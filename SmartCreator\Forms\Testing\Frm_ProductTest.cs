using SmartCreator.Entities.Accounting;
using SmartCreator.RJControls;
using SmartCreator.RJForms;
using SmartCreator.Services.Products;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace SmartCreator.Forms.Testing
{
    public partial class Frm_ProductTest : RJChildForm
    {
        private readonly ProductService _productService;
        private List<Entities.Accounting.Product> _testProducts;

        public Frm_ProductTest()
        {
            InitializeComponent();
            _productService = new ProductService();
            _testProducts = new List<Entities.Accounting.Product>();
            InitializeForm();
        }

        private void InitializeForm()
        {
            this.Text = "اختبار إدارة المنتجات";
            this.WindowState = FormWindowState.Maximized;
            
            // تهيئة الأحداث
            this.Load += Frm_ProductTest_Load;
            
            // تهيئة أحداث الأزرار
            btnCreateTestData.Click += BtnCreateTestData_Click;
            btnTestAdd.Click += BtnTestAdd_Click;
            btnTestUpdate.Click += BtnTestUpdate_Click;
            btnTestDelete.Click += BtnTestDelete_Click;
            btnTestSearch.Click += BtnTestSearch_Click;
            btnTestLowStock.Click += BtnTestLowStock_Click;
            btnClearTestData.Click += BtnClearTestData_Click;
            btnOpenProductForm.Click += BtnOpenProductForm_Click;
            btnRunAllTests.Click += BtnRunAllTests_Click;
            
            // تهيئة الجدول
            SetupDataGridView();
        }

        private void SetupDataGridView()
        {
            dgvTestResults.AutoGenerateColumns = false;
            dgvTestResults.AllowUserToAddRows = false;
            dgvTestResults.AllowUserToDeleteRows = false;
            dgvTestResults.ReadOnly = true;
            dgvTestResults.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            
            // إضافة الأعمدة
            dgvTestResults.Columns.Clear();
            
            dgvTestResults.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "TestName",
                HeaderText = "اسم الاختبار",
                Width = 200
            });
            
            dgvTestResults.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Status",
                HeaderText = "الحالة",
                Width = 100
            });
            
            dgvTestResults.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Result",
                HeaderText = "النتيجة",
                Width = 300
            });
            
            dgvTestResults.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Duration",
                HeaderText = "المدة (مللي ثانية)",
                Width = 150
            });
            
            dgvTestResults.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Timestamp",
                HeaderText = "وقت التنفيذ",
                Width = 150
            });
        }

        private void Frm_ProductTest_Load(object sender, EventArgs e)
        {
            AddTestResult("تم تحميل نموذج الاختبار", "نجح", "النموذج جاهز للاختبار", 0);
        }

        private async void BtnCreateTestData_Click(object sender, EventArgs e)
        {
            var startTime = DateTime.Now;
            try
            {
                ShowLoading(true);
                
                // إنشاء بيانات اختبار
                _testProducts = CreateTestProducts();
                
                int successCount = 0;
                foreach (var product in _testProducts)
                {
                    if (await _productService.AddProductAsync(product))
                    {
                        successCount++;
                    }
                }
                
                var duration = (DateTime.Now - startTime).TotalMilliseconds;
                AddTestResult("إنشاء بيانات الاختبار", "نجح", 
                    $"تم إنشاء {successCount} من {_testProducts.Count} منتج", (int)duration);
            }
            catch (Exception ex)
            {
                var duration = (DateTime.Now - startTime).TotalMilliseconds;
                AddTestResult("إنشاء بيانات الاختبار", "فشل", ex.Message, (int)duration);
            }
            finally
            {
                ShowLoading(false);
            }
        }

        private List<Entities.Accounting.Product> CreateTestProducts()
        {
            return new List<Entities.Accounting.Product>
            {
                new Entities.Accounting.Product
                {
                    Code = "TEST001",
                    Name = "منتج اختبار 1",
                    Description = "وصف منتج الاختبار الأول",
                    Price = 100.50m,
                    Stock = 50,
                    LowStockThreshold = 10,
                    Active = 1
                },
                new Entities.Accounting.Product
                {
                    Code = "TEST002",
                    Name = "منتج اختبار 2",
                    Description = "وصف منتج الاختبار الثاني",
                    Price = 75.25m,
                    Stock = 5, // مخزون منخفض
                    LowStockThreshold = 10,
                    Active = 1
                },
                new Entities.Accounting.Product
                {
                    Code = "TEST003",
                    Name = "منتج اختبار 3",
                    Description = "وصف منتج الاختبار الثالث",
                    Price = 200.00m,
                    Stock = 0, // نفد المخزون
                    LowStockThreshold = 5,
                    Active = 1
                },
                new Entities.Accounting.Product
                {
                    Code = "TEST004",
                    Name = "منتج اختبار 4",
                    Description = "وصف منتج الاختبار الرابع",
                    Price = 150.75m,
                    Stock = 100,
                    LowStockThreshold = 20,
                    Active = 0 // غير مفعل
                }
            };
        }

        private async void BtnTestAdd_Click(object sender, EventArgs e)
        {
            var startTime = DateTime.Now;
            try
            {
                var testProduct = new Entities.Accounting.Product
                {
                    Code = "TESTADD" + DateTime.Now.Ticks,
                    Name = "منتج اختبار إضافة",
                    Description = "اختبار إضافة منتج جديد",
                    Price = 99.99m,
                    Stock = 25,
                    LowStockThreshold = 5,
                    Active = 1
                };

                bool success = await _productService.AddProductAsync(testProduct);
                var duration = (DateTime.Now - startTime).TotalMilliseconds;
                
                AddTestResult("اختبار إضافة منتج", success ? "نجح" : "فشل", 
                    success ? "تم إضافة المنتج بنجاح" : "فشل في إضافة المنتج", (int)duration);
            }
            catch (Exception ex)
            {
                var duration = (DateTime.Now - startTime).TotalMilliseconds;
                AddTestResult("اختبار إضافة منتج", "فشل", ex.Message, (int)duration);
            }
        }

        private async void BtnTestUpdate_Click(object sender, EventArgs e)
        {
            var startTime = DateTime.Now;
            try
            {
                var products = await _productService.GetAllProductsAsync();
                var testProduct = products.FirstOrDefault(p => p.Code?.StartsWith("TEST") == true);
                
                if (testProduct == null)
                {
                    AddTestResult("اختبار تحديث منتج", "فشل", "لا توجد منتجات اختبار للتحديث", 0);
                    return;
                }

                var originalProduct = testProduct;
                testProduct.Name = "منتج محدث - " + DateTime.Now.ToString("HH:mm:ss");
                testProduct.Price += 10;
                testProduct.Stock += 5;

                bool success = await _productService.UpdateProductAsync(testProduct, originalProduct);
                var duration = (DateTime.Now - startTime).TotalMilliseconds;
                
                AddTestResult("اختبار تحديث منتج", success ? "نجح" : "فشل", 
                    success ? $"تم تحديث المنتج: {testProduct.Name}" : "فشل في تحديث المنتج", (int)duration);
            }
            catch (Exception ex)
            {
                var duration = (DateTime.Now - startTime).TotalMilliseconds;
                AddTestResult("اختبار تحديث منتج", "فشل", ex.Message, (int)duration);
            }
        }

        private async void BtnTestDelete_Click(object sender, EventArgs e)
        {
            var startTime = DateTime.Now;
            try
            {
                var products = await _productService.GetAllProductsAsync();
                var testProduct = products.FirstOrDefault(p => p.Code?.StartsWith("TESTADD") == true);
                
                if (testProduct == null)
                {
                    AddTestResult("اختبار حذف منتج", "فشل", "لا توجد منتجات اختبار للحذف", 0);
                    return;
                }

                bool success = await _productService.DeleteProductAsync(testProduct.Id);
                var duration = (DateTime.Now - startTime).TotalMilliseconds;
                
                AddTestResult("اختبار حذف منتج", success ? "نجح" : "فشل", 
                    success ? $"تم حذف المنتج: {testProduct.Name}" : "فشل في حذف المنتج", (int)duration);
            }
            catch (Exception ex)
            {
                var duration = (DateTime.Now - startTime).TotalMilliseconds;
                AddTestResult("اختبار حذف منتج", "فشل", ex.Message, (int)duration);
            }
        }

        private async void BtnTestSearch_Click(object sender, EventArgs e)
        {
            var startTime = DateTime.Now;
            try
            {
                var searchResults = await _productService.SearchProductsAsync("اختبار");
                var duration = (DateTime.Now - startTime).TotalMilliseconds;
                
                AddTestResult("اختبار البحث", "نجح", 
                    $"تم العثور على {searchResults.Count} منتج", (int)duration);
            }
            catch (Exception ex)
            {
                var duration = (DateTime.Now - startTime).TotalMilliseconds;
                AddTestResult("اختبار البحث", "فشل", ex.Message, (int)duration);
            }
        }

        private async void BtnTestLowStock_Click(object sender, EventArgs e)
        {
            var startTime = DateTime.Now;
            try
            {
                var lowStockProducts = await _productService.GetLowStockProductsAsync();
                var duration = (DateTime.Now - startTime).TotalMilliseconds;
                
                AddTestResult("اختبار المخزون المنخفض", "نجح", 
                    $"تم العثور على {lowStockProducts.Count} منتج يحتاج إعادة طلب", (int)duration);
            }
            catch (Exception ex)
            {
                var duration = (DateTime.Now - startTime).TotalMilliseconds;
                AddTestResult("اختبار المخزون المنخفض", "فشل", ex.Message, (int)duration);
            }
        }

        private async void BtnClearTestData_Click(object sender, EventArgs e)
        {
            var result = RJMessageBox.Show("هل تريد حذف جميع بيانات الاختبار؟", "تأكيد",
                MessageBoxButtons.YesNo, MessageBoxIcon.Question);
            
            if (result != DialogResult.Yes) return;

            var startTime = DateTime.Now;
            try
            {
                ShowLoading(true);
                
                var products = await _productService.GetAllProductsAsync();
                var testProducts = products.Where(p => p.Code?.StartsWith("TEST") == true).ToList();
                
                int deletedCount = 0;
                foreach (var product in testProducts)
                {
                    if (await _productService.DeleteProductAsync(product.Id))
                    {
                        deletedCount++;
                    }
                }
                
                var duration = (DateTime.Now - startTime).TotalMilliseconds;
                AddTestResult("مسح بيانات الاختبار", "نجح", 
                    $"تم حذف {deletedCount} من {testProducts.Count} منتج", (int)duration);
            }
            catch (Exception ex)
            {
                var duration = (DateTime.Now - startTime).TotalMilliseconds;
                AddTestResult("مسح بيانات الاختبار", "فشل", ex.Message, (int)duration);
            }
            finally
            {
                ShowLoading(false);
            }
        }

        private void BtnOpenProductForm_Click(object sender, EventArgs e)
        {
            var productForm = new Forms._Accounting.Product.Frm_Product();
            productForm.Show();
        }

        private async void BtnRunAllTests_Click(object sender, EventArgs e)
        {
            dgvTestResults.Rows.Clear();
            
            AddTestResult("بدء تشغيل جميع الاختبارات", "جاري التنفيذ", "تم بدء تشغيل جميع الاختبارات", 0);
            
            await Task.Delay(500); // توقف قصير
            
            BtnCreateTestData_Click(null, null);
            await Task.Delay(1000);
            
            BtnTestAdd_Click(null, null);
            await Task.Delay(500);
            
            BtnTestUpdate_Click(null, null);
            await Task.Delay(500);
            
            BtnTestSearch_Click(null, null);
            await Task.Delay(500);
            
            BtnTestLowStock_Click(null, null);
            await Task.Delay(500);
            
            BtnTestDelete_Click(null, null);
            await Task.Delay(500);
            
            AddTestResult("انتهاء جميع الاختبارات", "مكتمل", "تم تنفيذ جميع الاختبارات بنجاح", 0);
        }

        private void AddTestResult(string testName, string status, string result, int duration)
        {
            var row = new DataGridViewRow();
            row.CreateCells(dgvTestResults);
            
            row.Cells[0].Value = testName;
            row.Cells[1].Value = status;
            row.Cells[2].Value = result;
            row.Cells[3].Value = duration;
            row.Cells[4].Value = DateTime.Now.ToString("HH:mm:ss");
            
            // تلوين الصف حسب الحالة
            switch (status)
            {
                case "نجح":
                    row.DefaultCellStyle.BackColor = Color.LightGreen;
                    break;
                case "فشل":
                    row.DefaultCellStyle.BackColor = Color.LightCoral;
                    break;
                case "جاري التنفيذ":
                    row.DefaultCellStyle.BackColor = Color.LightYellow;
                    break;
                case "مكتمل":
                    row.DefaultCellStyle.BackColor = Color.LightBlue;
                    break;
            }
            
            dgvTestResults.Rows.Add(row);
            dgvTestResults.FirstDisplayedScrollingRowIndex = dgvTestResults.RowCount - 1;
        }

        private void ShowLoading(bool show)
        {
            if (show)
            {
                Cursor = Cursors.WaitCursor;
                pnlButtons.Enabled = false;
            }
            else
            {
                Cursor = Cursors.Default;
                pnlButtons.Enabled = true;
            }
        }
    }
}
