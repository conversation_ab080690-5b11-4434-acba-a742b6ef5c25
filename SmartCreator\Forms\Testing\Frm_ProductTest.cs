using SmartCreator.Entities.Accounting;
using SmartCreator.Forms.Accounting;
using SmartCreator.Properties;
using SmartCreator.RJControls;
using SmartCreator.RJForms;
using SmartCreator.Services;
using SmartCreator.Settings;
using System;
using System.Drawing;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace SmartCreator.Forms.Testing
{
    /// <summary>
    /// نموذج اختبار شامل لنظام إدارة المنتجات
    /// </summary>
    public partial class Frm_ProductTest : RJChildForm
    {
        private readonly ProductService _productService;
        private readonly InventoryService _inventoryService;

        public Frm_ProductTest()
        {
            InitializeComponent();
            _productService = new ProductService();
            _inventoryService = new InventoryService();
            
            InitializeForm();
        }

        private void InitializeForm()
        {
            this.Text = "اختبار نظام إدارة المنتجات";
            
            // تطبيق الخطوط المخصصة
            if (UIAppearance.Language_ar)
            {
                ApplyArabicFonts();
            }
        }

        private void ApplyArabicFonts()
        {
            var titleFont = Program.GetCustomFont(Resources.DroidKufi_Bold, 12, FontStyle.Bold);
            var buttonFont = Program.GetCustomFont(Resources.DroidKufi_Bold, 10, FontStyle.Bold);
            var labelFont = Program.GetCustomFont(Resources.DroidSansArabic, 10, FontStyle.Regular);

            lblTitle.Font = titleFont;
            
            // أزرار الاختبار
            btnTestProductManagement.Font = btnTestInventoryManagement.Font = 
            btnTestNotifications.Font = btnTestLowStock.Font = 
            btnTestSearch.Font = btnTestCRUD.Font = buttonFont;

            // التسميات
            lblProductTests.Font = lblInventoryTests.Font = 
            lblNotificationTests.Font = lblResults.Font = labelFont;
        }

        private async void BtnTestProductManagement_Click(object sender, EventArgs e)
        {
            try
            {
                AppendResult("🧪 بدء اختبار إدارة المنتجات...\n");
                
                var form = new Frm_Product();
                form.Show();
                
                AppendResult("✅ تم فتح نموذج إدارة المنتجات بنجاح\n");
                AppendResult("📋 يمكنك الآن اختبار:\n");
                AppendResult("   • عرض قائمة المنتجات\n");
                AppendResult("   • البحث في المنتجات\n");
                AppendResult("   • إضافة منتج جديد\n");
                AppendResult("   • تعديل منتج موجود\n");
                AppendResult("   • حذف منتج\n");
                AppendResult("   • تحديث المخزون\n");
                AppendResult("   • عرض المنتجات منخفضة المخزون\n\n");
            }
            catch (Exception ex)
            {
                AppendResult($"❌ خطأ في اختبار إدارة المنتجات: {ex.Message}\n\n");
            }
        }

        private async void BtnTestInventoryManagement_Click(object sender, EventArgs e)
        {
            try
            {
                AppendResult("🧪 بدء اختبار إدارة المخزون...\n");
                
                // إنشاء منتج تجريبي للاختبار
                var testProduct = new Product
                {
                    Code = "TEST001",
                    Name = "منتج اختبار المخزون",
                    Price = 100,
                    Stock = 50,
                    LowStockThreshold = 10,
                    Description = "منتج للاختبار فقط",
                    Active = 1
                };

                AppendResult("📦 إنشاء منتج تجريبي...\n");
                var addResult = await _productService.AddProductAsync(testProduct);
                
                if (addResult)
                {
                    AppendResult("✅ تم إنشاء المنتج التجريبي بنجاح\n");
                    
                    // اختبار تحديث المخزون
                    AppendResult("📈 اختبار زيادة المخزون...\n");
                    await _inventoryService.UpdateStockAsync(testProduct.Id, 20, "اختبار زيادة المخزون", true);
                    
                    AppendResult("📉 اختبار تقليل المخزون...\n");
                    await _inventoryService.UpdateStockAsync(testProduct.Id, 15, "اختبار تقليل المخزون", false);
                    
                    AppendResult("🎯 اختبار تعيين مخزون محدد...\n");
                    await _inventoryService.SetStockAsync(testProduct.Id, 5, "اختبار تعيين مخزون منخفض");
                    
                    AppendResult("✅ تم اختبار جميع عمليات المخزون بنجاح\n\n");
                }
                else
                {
                    AppendResult("❌ فشل في إنشاء المنتج التجريبي\n\n");
                }
            }
            catch (Exception ex)
            {
                AppendResult($"❌ خطأ في اختبار إدارة المخزون: {ex.Message}\n\n");
            }
        }

        private async void BtnTestNotifications_Click(object sender, EventArgs e)
        {
            try
            {
                AppendResult("🧪 بدء اختبار نظام الإشعارات...\n");
                
                // إنشاء منتج بمخزون منخفض
                var lowStockProduct = new Product
                {
                    Code = "LOW001",
                    Name = "منتج مخزون منخفض",
                    Price = 50,
                    Stock = 2,
                    LowStockThreshold = 10,
                    Description = "منتج لاختبار الإشعارات",
                    Active = 1
                };

                AppendResult("📦 إنشاء منتج بمخزون منخفض...\n");
                var addResult = await _productService.AddProductAsync(lowStockProduct);
                
                if (addResult)
                {
                    AppendResult("✅ تم إنشاء المنتج بنجاح\n");
                    AppendResult("🔔 يجب أن تظهر إشعارات المخزون المنخفض\n");
                    
                    // اختبار تحديث المخزون إلى صفر
                    AppendResult("📉 تقليل المخزون إلى صفر...\n");
                    await _inventoryService.SetStockAsync(lowStockProduct.Id, 0, "اختبار نفاد المخزون");
                    
                    AppendResult("🚨 يجب أن تظهر إشعارات نفاد المخزون\n\n");
                }
                else
                {
                    AppendResult("❌ فشل في إنشاء المنتج\n\n");
                }
            }
            catch (Exception ex)
            {
                AppendResult($"❌ خطأ في اختبار الإشعارات: {ex.Message}\n\n");
            }
        }

        private async void BtnTestLowStock_Click(object sender, EventArgs e)
        {
            try
            {
                AppendResult("🧪 بدء اختبار المنتجات منخفضة المخزون...\n");
                
                var lowStockProducts = await _inventoryService.GetLowStockProductsAsync();
                
                AppendResult($"📊 تم العثور على {lowStockProducts.Count} منتج يحتاج إعادة طلب:\n");
                
                foreach (var product in lowStockProducts)
                {
                    AppendResult($"   • {product.Name} - المخزون: {product.Stock} - الحد الأدنى: {product.LowStockThreshold}\n");
                }
                
                AppendResult("\n");
            }
            catch (Exception ex)
            {
                AppendResult($"❌ خطأ في اختبار المنتجات منخفضة المخزون: {ex.Message}\n\n");
            }
        }

        private async void BtnTestSearch_Click(object sender, EventArgs e)
        {
            try
            {
                AppendResult("🧪 بدء اختبار البحث في المنتجات...\n");
                
                // اختبار البحث بكلمات مختلفة
                var searchTerms = new[] { "اختبار", "TEST", "منتج" };
                
                foreach (var term in searchTerms)
                {
                    AppendResult($"🔍 البحث عن: '{term}'\n");
                    var results = await _productService.SearchProductsAsync(term);
                    AppendResult($"   النتائج: {results.Count} منتج\n");
                }
                
                AppendResult("✅ تم اختبار البحث بنجاح\n\n");
            }
            catch (Exception ex)
            {
                AppendResult($"❌ خطأ في اختبار البحث: {ex.Message}\n\n");
            }
        }

        private async void BtnTestCRUD_Click(object sender, EventArgs e)
        {
            try
            {
                AppendResult("🧪 بدء اختبار العمليات الأساسية (CRUD)...\n");
                
                // إنشاء منتج جديد
                var newProduct = new Product
                {
                    Code = "CRUD001",
                    Name = "منتج اختبار CRUD",
                    Price = 75,
                    Stock = 30,
                    LowStockThreshold = 5,
                    Description = "منتج لاختبار العمليات الأساسية",
                    Active = 1
                };

                AppendResult("➕ اختبار الإضافة (Create)...\n");
                var createResult = await _productService.AddProductAsync(newProduct);
                
                if (createResult)
                {
                    AppendResult("✅ تم إنشاء المنتج بنجاح\n");
                    
                    // قراءة المنتج
                    AppendResult("📖 اختبار القراءة (Read)...\n");
                    var readProduct = await _productService.GetProductByIdAsync(newProduct.Id);
                    
                    if (readProduct != null)
                    {
                        AppendResult("✅ تم قراءة المنتج بنجاح\n");
                        
                        // تحديث المنتج
                        AppendResult("✏️ اختبار التحديث (Update)...\n");
                        readProduct.Name = "منتج محدث";
                        readProduct.Price = 85;
                        
                        var updateResult = await _productService.UpdateProductAsync(readProduct);
                        
                        if (updateResult)
                        {
                            AppendResult("✅ تم تحديث المنتج بنجاح\n");
                            
                            // حذف المنتج
                            AppendResult("🗑️ اختبار الحذف (Delete)...\n");
                            var deleteResult = await _productService.DeleteProductAsync(readProduct.Id);
                            
                            if (deleteResult)
                            {
                                AppendResult("✅ تم حذف المنتج بنجاح\n");
                                AppendResult("🎉 تم اختبار جميع العمليات الأساسية بنجاح!\n\n");
                            }
                            else
                            {
                                AppendResult("❌ فشل في حذف المنتج\n\n");
                            }
                        }
                        else
                        {
                            AppendResult("❌ فشل في تحديث المنتج\n\n");
                        }
                    }
                    else
                    {
                        AppendResult("❌ فشل في قراءة المنتج\n\n");
                    }
                }
                else
                {
                    AppendResult("❌ فشل في إنشاء المنتج\n\n");
                }
            }
            catch (Exception ex)
            {
                AppendResult($"❌ خطأ في اختبار العمليات الأساسية: {ex.Message}\n\n");
            }
        }

        private void BtnClearResults_Click(object sender, EventArgs e)
        {
            txtResults.Text = "";
        }

        private void AppendResult(string message)
        {
            txtResults.AppendText(message);
            txtResults.ScrollToCaret();
            Application.DoEvents();
        }

        private void Frm_ProductTest_Load(object sender, EventArgs e)
        {
            AppendResult("🚀 مرحباً بك في نظام اختبار إدارة المنتجات\n");
            AppendResult("📝 اختر أحد الاختبارات من الأزرار أعلاه\n\n");
        }
    }
}
