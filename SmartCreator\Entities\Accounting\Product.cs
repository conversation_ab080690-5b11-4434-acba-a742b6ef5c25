﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SmartCreator.Entities.Accounting
{
    public class Product : BaseEntity
    {
        [DisplayName("الكود")]
        public string Code { get; set; } = string.Empty;

        [DisplayName("اسم المنتج")]
        public string Name { get; set; } = string.Empty;

        [DisplayName("السعر")]
        public decimal Price { get; set; }

        [DisplayName("المخزون الحالي")]
        public int Stock { get; set; }

        [DisplayName("الحد الأدنى للمخزون")]
        public int LowStockThreshold { get; set; } = 10;

        [DisplayName("الوصف")]
        public string Description { get; set; } = string.Empty;

        [DisplayName("ح/المبيعات")]
        public int? Account_IncomeId { get; set; } = null;

        [DisplayName("ح/مردود المبيعات")]
        public int? Account_ExpenseId { get; set; } = null;

        [DisplayName("وحدة القياس")]
        public int? Product_UomId { get; set; } = null;

        public int Active { get; set; } = 1;

        [DisplayName("مفعل")]
        public bool Str_Active
        {
            get
            {
                return Convert.ToBoolean(Active);
            }
        }

        // خصائص محسوبة للمخزون
        [DisplayName("حالة المخزون")]
        public string StockStatus
        {
            get
            {
                if (Stock <= 0)
                    return "نفد المخزون";
                else if (Stock <= LowStockThreshold)
                    return "مخزون منخفض";
                else
                    return "متوفر";
            }
        }

        [DisplayName("يحتاج إعادة طلب")]
        public bool NeedsReorder
        {
            get
            {
                return Stock <= LowStockThreshold;
            }
        }

        // خصائص إضافية للتتبع
        public DateTime? LastStockUpdate { get; set; }
        public string LastUpdateBy { get; set; } = string.Empty;
        public string StockUpdateReason { get; set; } = string.Empty;
    }
}
