﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Drawing.Design;
using System.Drawing.Drawing2D;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace SmartCreator.RJControls
{
    [DesignerCategory("Component")]
    [ToolboxItem(true)]
    [ToolboxBitmap(typeof(Panel))]
    [Description("RJ Panel - لوحة مخصصة مع حواف منحنية وتدرجات لونية")]
    public class RJPanel : Panel
    {
        /// <summary>
        /// This control doesn't have many additional customization properties,
        /// just set the background color according to the theme set by the appearance settings,
        /// and be able to set a radius to the edge of the control.
        /// </summary>
        ///

        #region -> Fields
        private bool customizable; // Gets or sets if the appearance colors are customizable
        private int borderRadius; // Gets or sets the border radius
        private bool ឭ;
        private float gradientAngle = 90F; // Gradient angle
        private System.Drawing.Color gradientTopColor = System.Drawing.Color.White; // Top gradient color
        private System.Drawing.Color gradientBottomColor = System.Drawing.Color.White; // Bottom gradient color
        #endregion
        //
        // Summary:
        //     Gets or sets a value indicating whether right-to-left mirror placement is turned
        //     on. Default value is false.
        [Description("Indicates whether right-to-left mirror placement is turned on. ")]
        [DefaultValue(false)]
        [Browsable(true)]
        [Category("RJ Code Advance")]
        public bool RightToLeftLayout
        {
            get
            {
                return ឭ;
            }
            set
            {
                if (ឭ != value)
                {
                    ឭ = value;
                    if (base.IsHandleCreated)
                    {
                        RecreateHandle();
                    }
                }
            }
        }

        #region -> Properties
        [Category("RJ Code Advance")]
        [Description("Gets or sets whether the control's appearance colors are customizable")]
        public bool Customizable
        {
            get { return customizable; }
            set { customizable = value; }
        }

        [Category("RJ Code Advance")]
        [Description("Gets or sets the border radius")]
        public int BorderRadius
        {
            get { return borderRadius; }
            set
            {
                borderRadius = value;
                this.Invalidate(); // Redraw the control to update the appearance of the control.
            }
        }

        [Category("RJ Code Advance")]
        [Description("Gets or sets the gradient angle")]
        public float GradientAngle
        {
            get { return gradientAngle; }
            set
            {
                gradientAngle = value;
                this.Invalidate();
            }
        }

        [Category("RJ Code Advance")]
        [Description("Gets or sets the top gradient color")]
        public System.Drawing.Color GradientTopColor
        {
            get { return gradientTopColor; }
            set
            {
                gradientTopColor = value;
                this.Invalidate();
            }
        }

        [Category("RJ Code Advance")]
        [Description("Gets or sets the bottom gradient color")]
        public System.Drawing.Color GradientBottomColor
        {
            get { return gradientBottomColor; }
            set
            {
                gradientBottomColor = value;
                this.Invalidate();
            }
        }
        #endregion

        #region -> Private methods
        private void ApplyAppearanceSettings()
        {// Apply appearance settings
            if (customizable == false)
            {
                this.BackColor = Settings.UIAppearance.ItemBackgroundColor;
            }
        }
        #endregion

        #region -> Overridden methods
        protected override void OnHandleCreated(EventArgs e)
        {
            base.OnHandleCreated(e);
            ApplyAppearanceSettings();
        }
        protected override void OnPaint(PaintEventArgs e)
        {
            base.OnPaint(e);
            Utils.RoundedControl.RegionAndSmoothed(this, borderRadius, e.Graphics);
        }
        #endregion

    }
}
