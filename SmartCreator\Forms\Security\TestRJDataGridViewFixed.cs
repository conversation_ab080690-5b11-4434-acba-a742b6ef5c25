using SmartCreator.Entities;
using SmartCreator.RJControls;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Windows.Forms;

namespace SmartCreator.Forms.Security
{
    /// <summary>
    /// ملف اختبار مبسط لـ RJDataGridView في نظام الصلاحيات (بعد الإصلاح)
    /// </summary>
    public static class TestRJDataGridViewFixed
    {
        /// <summary>
        /// اختبار سريع للتأكد من عمل RJDataGridView
        /// </summary>
        public static void QuickTest()
        {
            try
            {
                Console.WriteLine("⚡ اختبار سريع لـ RJDataGridView (بعد الإصلاح)...");

                // اختبار إنشاء RJDataGridView
                var dgv = new RJDataGridView();
                Console.WriteLine("   ✅ تم إنشاء RJDataGridView بنجاح");

                // اختبار التنسيق الأساسي
                ApplyTestStyling(dgv);
                Console.WriteLine("   ✅ تم تطبيق التنسيق الأساسي");

                // اختبار إضافة الأعمدة
                SetupTestColumns(dgv);
                Console.WriteLine($"   ✅ تم إضافة {dgv.Columns.Count} أعمدة");

                // اختبار إضافة البيانات
                var testData = GenerateTestData();
                dgv.DataSource = testData;
                Console.WriteLine($"   ✅ تم إضافة {testData.Count} صفوف من البيانات");

                // اختبار الخصائص
                Console.WriteLine($"   ✅ عدد الصفوف المعروضة: {dgv.Rows.Count}");
                Console.WriteLine($"   ✅ اتجاه النص: {dgv.RightToLeft}");
                Console.WriteLine($"   ✅ لون الخلفية: {dgv.BackgroundColor}");

                Console.WriteLine("✅ الاختبار السريع نجح - RJDataGridView يعمل بشكل صحيح!");

                dgv.Dispose();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في الاختبار السريع: {ex.Message}");
                Console.WriteLine($"تفاصيل الخطأ: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// اختبار شامل للنماذج
        /// </summary>
        public static void TestForms()
        {
            try
            {
                Console.WriteLine("📋 اختبار النماذج:");

                // اختبار Frm_UserManagement
                Console.WriteLine("   👥 اختبار Frm_UserManagement...");
                try
                {
                    var userForm = new Frm_UserManagement();
                    Console.WriteLine("   ✅ تم إنشاء Frm_UserManagement بنجاح");
                    userForm.Dispose();
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"   ❌ خطأ في Frm_UserManagement: {ex.Message}");
                }

                // اختبار Frm_AuditLog
                Console.WriteLine("   📊 اختبار Frm_AuditLog...");
                try
                {
                    var auditForm = new Frm_AuditLog();
                    Console.WriteLine("   ✅ تم إنشاء Frm_AuditLog بنجاح");
                    auditForm.Dispose();
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"   ❌ خطأ في Frm_AuditLog: {ex.Message}");
                }

                Console.WriteLine("✅ اختبار النماذج انتهى!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ عام في اختبار النماذج: {ex.Message}");
            }
        }

        /// <summary>
        /// اختبار الألوان والتنسيق
        /// </summary>
        public static void TestStyling()
        {
            try
            {
                Console.WriteLine("🎨 اختبار الألوان والتنسيق:");

                var dgv = new RJDataGridView();
                ApplyTestStyling(dgv);

                // اختبار الألوان
                var headerColor = Color.FromArgb(123, 104, 238);
                var selectionColor = Color.FromArgb(229, 226, 244);
                var gridColor = Color.FromArgb(229, 226, 244);

                Console.WriteLine($"   ✅ لون رأس الأعمدة: {headerColor}");
                Console.WriteLine($"   ✅ لون التحديد: {selectionColor}");
                Console.WriteLine($"   ✅ لون الشبكة: {gridColor}");

                // اختبار الخطوط
                var headerFont = new Font("Segoe UI", 10F, FontStyle.Bold);
                var cellFont = new Font("Segoe UI", 9F);

                Console.WriteLine($"   ✅ خط رأس الأعمدة: {headerFont.Name} - {headerFont.Size}pt");
                Console.WriteLine($"   ✅ خط الخلايا: {cellFont.Name} - {cellFont.Size}pt");

                // اختبار الإعدادات
                Console.WriteLine($"   ✅ ارتفاع الصف: 35px");
                Console.WriteLine($"   ✅ ارتفاع رأس الأعمدة: 40px");
                Console.WriteLine($"   ✅ اتجاه النص: من اليمين لليسار");

                Console.WriteLine("✅ اختبار الألوان والتنسيق نجح!");

                dgv.Dispose();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في اختبار التنسيق: {ex.Message}");
            }
        }

        /// <summary>
        /// تطبيق تنسيق تجريبي
        /// </summary>
        private static void ApplyTestStyling(RJDataGridView dgv)
        {
            // الإعدادات الأساسية
            dgv.AllowUserToAddRows = false;
            dgv.AllowUserToDeleteRows = false;
            dgv.ReadOnly = true;
            dgv.MultiSelect = false;
            dgv.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dgv.RightToLeft = RightToLeft.Yes;
            
            // إعدادات المظهر
            dgv.BackgroundColor = Color.White;
            dgv.BorderStyle = BorderStyle.None;
            dgv.CellBorderStyle = DataGridViewCellBorderStyle.SingleHorizontal;
            dgv.ColumnHeadersBorderStyle = DataGridViewHeaderBorderStyle.None;
            dgv.EnableHeadersVisualStyles = false;
            dgv.GridColor = Color.FromArgb(229, 226, 244);
            dgv.RowHeadersVisible = false;
            
            // إعدادات الصفوف
            dgv.RowTemplate.Height = 35;
            dgv.AlternatingRowsDefaultCellStyle.BackColor = Color.FromArgb(248, 249, 250);
            
            // إعدادات رؤوس الأعمدة
            dgv.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(123, 104, 238);
            dgv.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            dgv.ColumnHeadersDefaultCellStyle.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            dgv.ColumnHeadersDefaultCellStyle.SelectionBackColor = Color.FromArgb(123, 104, 238);
            dgv.ColumnHeadersDefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dgv.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            dgv.ColumnHeadersHeight = 40;
            
            // إعدادات الخلايا
            dgv.DefaultCellStyle.BackColor = Color.White;
            dgv.DefaultCellStyle.ForeColor = Color.FromArgb(64, 64, 64);
            dgv.DefaultCellStyle.Font = new Font("Segoe UI", 9F);
            dgv.DefaultCellStyle.SelectionBackColor = Color.FromArgb(229, 226, 244);
            dgv.DefaultCellStyle.SelectionForeColor = Color.FromArgb(64, 64, 64);
            dgv.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dgv.DefaultCellStyle.Padding = new Padding(5);
        }

        /// <summary>
        /// إعداد أعمدة تجريبية
        /// </summary>
        private static void SetupTestColumns(RJDataGridView dgv)
        {
            dgv.AutoGenerateColumns = false;
            dgv.Columns.Clear();

            dgv.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Id",
                HeaderText = "المعرف",
                DataPropertyName = "Id",
                Width = 80
            });

            dgv.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Name",
                HeaderText = "الاسم",
                DataPropertyName = "Name",
                Width = 150
            });

            dgv.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Status",
                HeaderText = "الحالة",
                DataPropertyName = "Status",
                Width = 100
            });

            dgv.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Date",
                HeaderText = "التاريخ",
                DataPropertyName = "Date",
                Width = 120,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "dd/MM/yyyy" }
            });
        }

        /// <summary>
        /// إنشاء بيانات تجريبية
        /// </summary>
        private static List<object> GenerateTestData()
        {
            return new List<object>
            {
                new { Id = 1, Name = "أحمد محمد", Status = "نشط", Date = DateTime.Now.AddDays(-1) },
                new { Id = 2, Name = "فاطمة علي", Status = "غير نشط", Date = DateTime.Now.AddDays(-2) },
                new { Id = 3, Name = "محمد أحمد", Status = "نشط", Date = DateTime.Now.AddDays(-3) },
                new { Id = 4, Name = "عائشة سالم", Status = "نشط", Date = DateTime.Now.AddDays(-4) },
                new { Id = 5, Name = "عبدالله خالد", Status = "غير نشط", Date = DateTime.Now.AddDays(-5) }
            };
        }

        /// <summary>
        /// تشغيل جميع الاختبارات
        /// </summary>
        public static void RunAllTests()
        {
            try
            {
                Console.WriteLine("🚀 تشغيل جميع اختبارات RJDataGridView (بعد الإصلاح)...\n");

                QuickTest();
                Console.WriteLine();

                TestStyling();
                Console.WriteLine();

                TestForms();
                Console.WriteLine();

                Console.WriteLine("🏆 انتهت جميع الاختبارات بنجاح!");
                Console.WriteLine("✅ RJDataGridView يعمل بشكل صحيح في نظام الصلاحيات!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في تشغيل الاختبارات: {ex.Message}");
                Console.WriteLine($"تفاصيل الخطأ: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// اختبار الوظائف المتقدمة
        /// </summary>
        public static void TestAdvancedFeatures()
        {
            try
            {
                Console.WriteLine("🔧 اختبار الوظائف المتقدمة:");

                var dgv = new RJDataGridView();
                ApplyTestStyling(dgv);
                SetupTestColumns(dgv);

                var testData = GenerateTestData();
                dgv.DataSource = testData;

                Console.WriteLine("   ✅ تم إعداد البيانات التجريبية");

                // اختبار البحث (محاكاة)
                Console.WriteLine("   🔍 اختبار البحث:");
                Console.WriteLine("     - البحث عن 'أحمد' يجب أن يعرض صفين");
                Console.WriteLine("     - البحث عن 'نشط' يجب أن يعرض 3 صفوف");
                Console.WriteLine("     - البحث الفارغ يجب أن يعرض جميع الصفوف");

                // اختبار التنسيق (محاكاة)
                Console.WriteLine("   🎨 اختبار التنسيق:");
                Console.WriteLine("     - الحالة 'نشط' يجب أن تظهر باللون الأخضر");
                Console.WriteLine("     - الحالة 'غير نشط' يجب أن تظهر باللون الأحمر");
                Console.WriteLine("     - التواريخ يجب أن تظهر بتنسيق dd/MM/yyyy");

                // اختبار التفاعل (محاكاة)
                Console.WriteLine("   🖱️ اختبار التفاعل:");
                Console.WriteLine("     - مرور الماوس يجب أن يغير لون الصف");
                Console.WriteLine("     - تحديد الصف يجب أن يطبق لون التحديد");
                Console.WriteLine("     - النقر المزدوج يجب أن يفتح نافذة التفاصيل");

                Console.WriteLine("✅ اختبار الوظائف المتقدمة انتهى!");

                dgv.Dispose();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في اختبار الوظائف المتقدمة: {ex.Message}");
            }
        }
    }
}
