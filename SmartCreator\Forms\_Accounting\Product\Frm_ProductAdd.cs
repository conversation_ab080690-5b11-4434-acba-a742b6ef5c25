using SmartCreator.Entities.Accounting;
using SmartCreator.RJControls;
using SmartCreator.RJForms;
using SmartCreator.Services.Products;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace SmartCreator.Forms._Accounting.Product
{
    public partial class Frm_ProductAdd : RJChildForm
    {
        private readonly ProductService _productService;

        public Frm_ProductAdd()
        {
            InitializeComponent();
            _productService = new ProductService();
            InitializeForm();
        }

        private void InitializeForm()
        {
            this.Text = "إضافة منتج جديد";
            this.Size = new Size(800, 600);
            this.StartPosition = FormStartPosition.CenterParent;
            
            // تهيئة الأحداث
            this.Load += Frm_ProductAdd_Load;
            
            // تهيئة أحداث الأزرار
            btnSave.Click += BtnSave_Click;
            btnCancel.Click += BtnCancel_Click;
            btnClear.Click += BtnClear_Click;
            
            // تهيئة أحداث التحقق
            txtCode.Leave += TxtCode_Leave;
            txtName.Leave += TxtName_Leave;
            txtPrice.Leave += TxtPrice_Leave;
            txtStock.Leave += TxtStock_Leave;
            txtLowStockThreshold.Leave += TxtLowStockThreshold_Leave;
            
            // تهيئة القيم الافتراضية
            SetDefaultValues();
        }

        private void SetDefaultValues()
        {
            txtLowStockThreshold.Texts = "10";
            chkActive.Checked = true;
            txtStock.Texts = "0";
            txtPrice.Texts = "0.00";
        }

        private void Frm_ProductAdd_Load(object sender, EventArgs e)
        {
            txtCode.Focus();
        }

        private async void BtnSave_Click(object sender, EventArgs e)
        {
            if (!ValidateInput())
                return;

            try
            {
                var product = CreateProductFromInput();
                
                btnSave.Enabled = false;
                btnSave.Text = "جاري الحفظ...";
                
                bool success = await _productService.AddProductAsync(product);
                
                if (success)
                {
                    this.DialogResult = DialogResult.OK;
                    this.Close();
                }
            }
            catch (Exception ex)
            {
                RJMessageBox.Show($"خطأ في حفظ المنتج: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                btnSave.Enabled = true;
                btnSave.Text = "حفظ";
            }
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        private void BtnClear_Click(object sender, EventArgs e)
        {
            ClearForm();
        }

        private void ClearForm()
        {
            txtCode.Texts = "";
            txtName.Texts = "";
            txtDescription.Texts = "";
            txtPrice.Texts = "0.00";
            txtStock.Texts = "0";
            txtLowStockThreshold.Texts = "10";
            chkActive.Checked = true;
            
            txtCode.Focus();
        }

        private bool ValidateInput()
        {
            // التحقق من الكود
            if (string.IsNullOrWhiteSpace(txtCode.Texts))
            {
                RJMessageBox.Show("يجب إدخال كود المنتج", "تحذير",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtCode.Focus();
                return false;
            }

            // التحقق من الاسم
            if (string.IsNullOrWhiteSpace(txtName.Texts))
            {
                RJMessageBox.Show("يجب إدخال اسم المنتج", "تحذير",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtName.Focus();
                return false;
            }

            // التحقق من السعر
            if (!decimal.TryParse(txtPrice.Texts, out decimal price) || price < 0)
            {
                RJMessageBox.Show("يجب إدخال سعر صحيح", "تحذير",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtPrice.Focus();
                return false;
            }

            // التحقق من المخزون
            if (!int.TryParse(txtStock.Texts, out int stock) || stock < 0)
            {
                RJMessageBox.Show("يجب إدخال كمية مخزون صحيحة", "تحذير",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtStock.Focus();
                return false;
            }

            // التحقق من الحد الأدنى للمخزون
            if (!int.TryParse(txtLowStockThreshold.Texts, out int threshold) || threshold < 0)
            {
                RJMessageBox.Show("يجب إدخال حد أدنى صحيح للمخزون", "تحذير",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtLowStockThreshold.Focus();
                return false;
            }

            return true;
        }

        private Entities.Accounting.Product CreateProductFromInput()
        {
            return new Entities.Accounting.Product
            {
                Code = txtCode.Texts.Trim(),
                Name = txtName.Texts.Trim(),
                Description = txtDescription.Texts.Trim(),
                Price = decimal.Parse(txtPrice.Texts),
                Stock = int.Parse(txtStock.Texts),
                LowStockThreshold = int.Parse(txtLowStockThreshold.Texts),
                Active = chkActive.Checked ? 1 : 0
            };
        }

        private void TxtCode_Leave(object sender, EventArgs e)
        {
            if (!string.IsNullOrWhiteSpace(txtCode.Texts))
            {
                txtCode.Texts = txtCode.Texts.Trim().ToUpper();
            }
        }

        private void TxtName_Leave(object sender, EventArgs e)
        {
            if (!string.IsNullOrWhiteSpace(txtName.Texts))
            {
                txtName.Texts = txtName.Texts.Trim();
            }
        }

        private void TxtPrice_Leave(object sender, EventArgs e)
        {
            if (decimal.TryParse(txtPrice.Texts, out decimal price))
            {
                txtPrice.Texts = price.ToString("F2");
            }
        }

        private void TxtStock_Leave(object sender, EventArgs e)
        {
            if (int.TryParse(txtStock.Texts, out int stock))
            {
                txtStock.Texts = stock.ToString();
            }
        }

        private void TxtLowStockThreshold_Leave(object sender, EventArgs e)
        {
            if (int.TryParse(txtLowStockThreshold.Texts, out int threshold))
            {
                txtLowStockThreshold.Texts = threshold.ToString();
            }
        }
    }
}
