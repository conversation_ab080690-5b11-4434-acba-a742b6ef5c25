using SmartCreator.Models;
using SmartCreator.Entities;
using SmartCreator.Services;
using SmartCreator.RJControls;
using SmartCreator.Helpers;
using System;
using System.Drawing;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace SmartCreator.Forms.Security
{
    /// <summary>
    /// نموذج تغيير كلمة المرور
    /// </summary>
    public partial class Frm_ChangePassword : Form
    {
        private readonly UserManagementService _userService;
        private readonly SmartCreator.Entities.User _user;

        public Frm_ChangePassword(UserManagementService userService, SmartCreator.Entities.User user)
        {
            _userService = userService;
            _user = user;
            InitializeComponent();
            SetupForm();
        }



        /// <summary>
        /// إعداد النموذج
        /// </summary>
        private void SetupForm()
        {
            // تحديث العنوان بناءً على المستخدم
            if (_user != null)
            {
                lblTitle.Text = $"تغيير كلمة مرور المستخدم: {_user.Username}";
            }

            // التركيز على كلمة المرور الحالية
            txtCurrentPassword.Focus();
        }

        /// <summary>
        /// التحقق من قوة كلمة المرور الجديدة
        /// </summary>
        private void TxtNewPassword_TextChanged(object sender, EventArgs e)
        {
            if (txtNewPassword.Text.Length > 0)
            {
                var strength = SecurityHelper.CheckPasswordStrength(txtNewPassword.Text);
                var description = SecurityHelper.GetPasswordStrengthDescription(strength);
                var color = SecurityHelper.GetPasswordStrengthColor(strength);

                lblPasswordStrength.Text = $"قوة كلمة المرور: {description}";
                lblPasswordStrength.ForeColor = color;
                lblPasswordStrength.Visible = true;
            }
            else
            {
                lblPasswordStrength.Visible = false;
            }

            // التحقق من التطابق إذا كان هناك نص في تأكيد كلمة المرور
            if (txtConfirmPassword.Text.Length > 0)
            {
                TxtConfirmPassword_TextChanged(sender, e);
            }
        }

        /// <summary>
        /// التحقق من تطابق كلمة المرور
        /// </summary>
        private void TxtConfirmPassword_TextChanged(object sender, EventArgs e)
        {
            if (txtConfirmPassword.Text.Length > 0)
            {
                if (txtNewPassword.Text == txtConfirmPassword.Text)
                {
                    lblPasswordMatch.Text = "✅ كلمة المرور متطابقة";
                    lblPasswordMatch.ForeColor = Color.Green;
                }
                else
                {
                    lblPasswordMatch.Text = "❌ كلمة المرور غير متطابقة";
                    lblPasswordMatch.ForeColor = Color.Red;
                }
                lblPasswordMatch.Visible = true;
            }
            else
            {
                lblPasswordMatch.Visible = false;
            }
        }

        /// <summary>
        /// حفظ كلمة المرور الجديدة
        /// </summary>
        private async void BtnSave_Click(object sender, EventArgs e)
        {
            if (!ValidateForm()) return;

            try
            {
                btnSave.Enabled = false;
                btnSave.Text = "جاري الحفظ...";

                // التحقق من كلمة المرور الحالية
                var isCurrentPasswordValid = await _userService.ValidatePasswordAsync(_user.Id, txtCurrentPassword.Text);
                if (!isCurrentPasswordValid)
                {
                    MessageBox.Show("كلمة المرور الحالية غير صحيحة", "خطأ في التحقق",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtCurrentPassword.Focus();
                    return;
                }

                // تغيير كلمة المرور
                var success = await _userService.ChangePasswordAsync(_user.Id, txtNewPassword.Text, Global_Variable.CurrentUser?.Id ?? 1);

                if (success)
                {
                    MessageBox.Show("تم تغيير كلمة المرور بنجاح", "نجح التغيير",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);

                    this.DialogResult = DialogResult.OK;
                    this.Close();
                }
                else
                {
                    MessageBox.Show("فشل في تغيير كلمة المرور", "خطأ",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تغيير كلمة المرور: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                btnSave.Enabled = true;
                btnSave.Text = "💾 حفظ";
            }
        }

        /// <summary>
        /// إلغاء العملية
        /// </summary>
        private void BtnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        /// <summary>
        /// التحقق من صحة النموذج
        /// </summary>
        private bool ValidateForm()
        {
            if (string.IsNullOrWhiteSpace(txtCurrentPassword.Text))
            {
                MessageBox.Show("كلمة المرور الحالية مطلوبة", "تحذير",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtCurrentPassword.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(txtNewPassword.Text))
            {
                MessageBox.Show("كلمة المرور الجديدة مطلوبة", "تحذير",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtNewPassword.Focus();
                return false;
            }

            if (txtNewPassword.Text.Length < 6)
            {
                MessageBox.Show("كلمة المرور الجديدة يجب أن تكون 6 أحرف على الأقل", "تحذير",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtNewPassword.Focus();
                return false;
            }

            if (txtNewPassword.Text != txtConfirmPassword.Text)
            {
                MessageBox.Show("كلمة المرور الجديدة غير متطابقة", "تحذير",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtConfirmPassword.Focus();
                return false;
            }

            if (txtCurrentPassword.Text == txtNewPassword.Text)
            {
                MessageBox.Show("كلمة المرور الجديدة يجب أن تكون مختلفة عن الحالية", "تحذير",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtNewPassword.Focus();
                return false;
            }

            return true;
        }
    }
}
