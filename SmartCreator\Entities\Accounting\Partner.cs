﻿using SmartCreator.Data.CustomORM;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SmartCreator.Entities.Accounting
{
    /// <summary>
    /// نموذج الشركاء الموحد (موظفين، عملاء، موردين، جهات اتصال)
    /// </summary>
    [Table("Partners")]
    public class Partner : BaseEntity
    {

        [Column("Code")]
        [MaxLength(50)]
        [Index(Name = "IX_Partner_Code", IsUnique = true)]
        public string Code { get; set; } = string.Empty;

        [Column("Name")]
        [Required]
        [MaxLength(200)]
        //[Index(Name = "IX_Partner_Name")]
        public string Name { get; set; } = string.Empty;

        [Column("NameEn")]
        [MaxLength(200)]
        public string? NameEn { get; set; }

        [Column("PartnerType")]
        [Required]
        public PartnerType PartnerType { get; set; } = PartnerType.Customer;

        [Column("Email")]
        [MaxLength(150)]
        //[Index(Name = "IX_Partner_Email")]
        public string? Email { get; set; }

        [Column("Phone")]
        [MaxLength(50)]
        public string? Phone { get; set; }

        [Column("Mobile")]
        [MaxLength(50)]
        public string? Mobile { get; set; }

        //[Column("Fax")]
        //[MaxLength(50)]
        //public string? Fax { get; set; }

        //[Column("Website")]
        //[MaxLength(200)]
        //public string? Website { get; set; }

        [Column("Address")]
        [MaxLength(500)]
        public string? Address { get; set; }

        //[Column("City")]
        //[MaxLength(100)]
        //public string? City { get; set; }

        //[Column("Country")]
        //[MaxLength(100)]
        //public string? Country { get; set; }

        //[Column("PostalCode")]
        //[MaxLength(20)]
        //public string? PostalCode { get; set; }

        //[Column("TaxNumber")]
        //[MaxLength(50)]
        //[Index(Name = "IX_Partner_TaxNumber")]
        //public string? TaxNumber { get; set; }

        //[Column("CommercialRegister")]
        //[MaxLength(50)]
        //public string? CommercialRegister { get; set; }

        //[Column("BankName")]
        //[MaxLength(100)]
        //public string? BankName { get; set; }

        //[Column("BankAccount")]
        //[MaxLength(50)]
        //public string? BankAccount { get; set; }

        //[Column("IBAN")]
        //[MaxLength(50)]
        //public string? IBAN { get; set; }

        //[Column("CreditLimit")]
        //[DefaultValue(0)]
        //public decimal CreditLimit { get; set; } = 0;

        [Column("PaymentTerms")]
        public int PaymentTerms { get; set; } = 0; // بالأيام

        [Column("Discount")]
        [DefaultValue(0)]
        public decimal Discount { get; set; } = 0; // نسبة الخصم

        [Column("IsActive")]
        [DefaultValue(true)]
        public bool IsActive { get; set; } = true;

        [Column("Notes")]
        [MaxLength(1000)]
        public string? Notes { get; set; }

 
        [Column("ContactPerson")]
        [MaxLength(200)]
        public string? ContactPerson { get; set; }

        [Column("ContactTitle")]
        [MaxLength(100)]
        public string? ContactTitle { get; set; }

        //[Column("Department")]
        //[MaxLength(100)]
        //public string? Department { get; set; }

        //[Column("Position")]
        //[MaxLength(100)]
        //public string? Position { get; set; }

        //[Column("Salary")]
        //[DefaultValue(0)]
        //public decimal Salary { get; set; } = 0; // للموظفين

        //[Column("HireDate")]
        //public DateTime? HireDate { get; set; } // للموظفين

        //[Column("BirthDate")]
        //public DateTime? BirthDate { get; set; }

        //[Column("NationalId")]
        //[MaxLength(50)]
        //public string? NationalId { get; set; }

        //[Column("PassportNumber")]
        //[MaxLength(50)]
        //public string? PassportNumber { get; set; }

        //[Column("EmergencyContact")]
        //[MaxLength(200)]
        //public string? EmergencyContact { get; set; }

        //[Column("EmergencyPhone")]
        //[MaxLength(50)]
        //public string? EmergencyPhone { get; set; }

        [Column("AccountId")]
        [ForeignKey("Accounts", "Id")]
        public int? AccountId { get; set; }

        [Column("ParentId")]
        [ForeignKey("Partners", "Id")]
        public int? ParentId { get; set; }

        [Column("ImagePath")]
        [MaxLength(500)]
        public string? ImagePath { get; set; }

        //[Column("AttachmentsPath")]
        //[MaxLength(1000)]
        //public string? AttachmentsPath { get; set; }

        // خصائص محسوبة
        [NotMapped]
        public string DisplayName => !string.IsNullOrEmpty(Name) ? Name : Code;

        [NotMapped]
        public string FullContact => $"{Phone}{(!string.IsNullOrEmpty(Phone) && !string.IsNullOrEmpty(Mobile) ? " / " : "")}{Mobile}";

        [NotMapped]
        public string TypeText => PartnerType switch
        {
            PartnerType.Customer => "عميل",
            PartnerType.Supplier => "مورد",
            PartnerType.Employee => "موظف",
            PartnerType.Contact => "جهة اتصال",
            PartnerType.Bank => "بنك",
            PartnerType.Government => "جهة حكومية",
            _ => "غير محدد"
        };

        [NotMapped]
        public string StatusText => IsActive ? "نشط" : "غير نشط";

        //[NotMapped]
        //public int Age => BirthDate.HasValue ? DateTime.Now.Year - BirthDate.Value.Year : 0;

        //[NotMapped]
        //public int ServiceYears => HireDate.HasValue ? DateTime.Now.Year - HireDate.Value.Year : 0;

        // علاقات
        [NotMapped]
        public Account? Account { get; set; }

        [NotMapped]
        public Partner? Parent { get; set; }

        [NotMapped]
        public List<Partner> Children { get; set; } = new();

        /// <summary>
        /// توليد كود تلقائي للشريك
        /// </summary>
        public void GenerateCode()
        {
            if (string.IsNullOrEmpty(Code))
            {
                var prefix = PartnerType switch
                {
                    PartnerType.Customer => "CUS",
                    PartnerType.Supplier => "SUP",
                    PartnerType.Employee => "EMP",
                    PartnerType.Contact => "CON",
                    PartnerType.Bank => "BNK",
                    PartnerType.Government => "GOV",
                    _ => "PTR"
                };

                Code = $"{prefix}{DateTime.Now:yyyyMMdd}{DateTime.Now:HHmmss}";
            }
        }

        /// <summary>
        /// التحقق من صحة البيانات
        /// </summary>
        public (bool IsValid, List<string> Errors) Validate()
        {
            var errors = new List<string>();

            if (string.IsNullOrWhiteSpace(Name))
                errors.Add("اسم الشريك مطلوب");

            if (Name?.Length > 200)
                errors.Add("اسم الشريك لا يجب أن يتجاوز 200 حرف");

            if (!string.IsNullOrEmpty(Email) && !IsValidEmail(Email))
                errors.Add("البريد الإلكتروني غير صحيح");

            //if (CreditLimit < 0)
            //    errors.Add("حد الائتمان لا يمكن أن يكون سالب");

            if (PaymentTerms < 0)
                errors.Add("شروط الدفع لا يمكن أن تكون سالبة");

            if (Discount < 0 || Discount > 100)
                errors.Add("نسبة الخصم يجب أن تكون بين 0 و 100");

            //if (PartnerType == PartnerType.Employee && Salary < 0)
            //    errors.Add("الراتب لا يمكن أن يكون سالب");

            return (errors.Count == 0, errors);
        }

        private bool IsValidEmail(string email)
        {
            try
            {
                var addr = new System.Net.Mail.MailAddress(email);
                return addr.Address == email;
            }
            catch
            {
                return false;
            }
        }
    }

    /// <summary>
    /// أنواع الشركاء
    /// </summary>
    public enum PartnerType
    {
        Customer = 1,    // عميل
        Supplier = 2,    // مورد
        Employee = 3,    // موظف
        Contact = 4,     // جهة اتصال
        Bank = 5,        // بنك
        Government = 6   // جهة حكومية
    }

}
