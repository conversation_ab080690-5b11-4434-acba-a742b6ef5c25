using SmartCreator.Helpers;
using SmartCreator.Entities;
using SmartCreator.Models;
using SmartCreator.Services.Security;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace SmartCreator
{
    /// <summary>
    /// ملف اختبار للتأكد من أن SecurityHelper يعمل بعد الإصلاحات
    /// </summary>
    public class TestSecurityHelperFixed
    {
        /// <summary>
        /// اختبار شامل لـ SecurityHelper بعد الإصلاحات
        /// </summary>
        public static async Task TestFixedSecurityHelperAsync()
        {
            try
            {
                Console.WriteLine("🔧 اختبار SecurityHelper بعد الإصلاحات...\n");

                // 1. اختبار بدون مستخدم حالي (null safety)
                await TestWithNullUser();
                Console.WriteLine();

                // 2. اختبار مع مستخدم صحيح
                await TestWithValidUser();
                Console.WriteLine();

                // 3. اختبار LogActivityAsync مباشرة
                await TestLogActivityDirectly();
                Console.WriteLine();

                // 4. اختبار جميع وظائف SecurityHelper
                TestAllSecurityHelperFunctions();
                Console.WriteLine();

                Console.WriteLine("🎉 جميع اختبارات SecurityHelper نجحت بعد الإصلاحات!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في اختبار SecurityHelper: {ex.Message}");
                Console.WriteLine($"تفاصيل الخطأ: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// اختبار مع مستخدم null (null safety)
        /// </summary>
        private static async Task TestWithNullUser()
        {
            Console.WriteLine("🔍 اختبار مع مستخدم null:");

            // تأكد من أن CurrentUser هو null
            Global_Variable.CurrentUser = null;

            try
            {
                // يجب أن يعمل بدون خطأ حتى مع null user
                await SecurityHelper.LogActivityAsync("اختبار null", "النظام", "اختبار مع مستخدم null");
                Console.WriteLine("   ✅ LogActivityAsync يعمل مع null user");

                var hasPermission = SecurityHelper.HasPermission(PermissionCodes.SYSTEM_ADMIN);
                Console.WriteLine($"   ✅ HasPermission مع null user: {hasPermission} (متوقع: false)");

                var isExpired = SecurityHelper.IsSessionExpired();
                Console.WriteLine($"   ✅ IsSessionExpired مع null user: {isExpired} (متوقع: true)");

                Console.WriteLine("✅ اختبار null user نجح!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في اختبار null user: {ex.Message}");
            }
        }

        /// <summary>
        /// اختبار مع مستخدم صحيح
        /// </summary>
        private static async Task TestWithValidUser()
        {
            Console.WriteLine("👤 اختبار مع مستخدم صحيح:");

            try
            {
                // إنشاء مستخدم صحيح
                Global_Variable.CurrentUser = new User
                {
                    Id = 1,
                    Username = "test_admin",
                    FullName = "مدير اختبار",
                    Email = "<EMAIL>",
                    IsActive = true,
                    IsLocked = false,
                    FailedLoginAttempts = 0,
                    Permissions = new List<string>
                    {
                        PermissionCodes.SYSTEM_ADMIN,
                        PermissionCodes.MANAGE_USERS,
                        PermissionCodes.VIEW_AUDIT_LOGS
                    }
                };

                Console.WriteLine($"   ✅ تم إنشاء مستخدم: {Global_Variable.CurrentUser.Username}");

                // اختبار LogActivityAsync
                await SecurityHelper.LogActivityAsync("تسجيل دخول", "النظام", "دخول ناجح للنظام");
                Console.WriteLine("   ✅ LogActivityAsync يعمل مع مستخدم صحيح");

                // اختبار الصلاحيات
                var hasSystemAdmin = SecurityHelper.HasPermission(PermissionCodes.SYSTEM_ADMIN);
                Console.WriteLine($"   ✅ HasPermission (SYSTEM_ADMIN): {hasSystemAdmin} (متوقع: true)");

                var hasManageUsers = SecurityHelper.HasPermission(PermissionCodes.MANAGE_USERS);
                Console.WriteLine($"   ✅ HasPermission (MANAGE_USERS): {hasManageUsers} (متوقع: true)");

                var hasUnauthorized = SecurityHelper.HasPermission("UNAUTHORIZED_PERMISSION");
                Console.WriteLine($"   ✅ HasPermission (غير مصرح): {hasUnauthorized} (متوقع: true لأنه SYSTEM_ADMIN)");

                // اختبار الجلسة
                var isExpired = SecurityHelper.IsSessionExpired();
                Console.WriteLine($"   ✅ IsSessionExpired: {isExpired} (متوقع: false)");

                Console.WriteLine("✅ اختبار المستخدم الصحيح نجح!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في اختبار المستخدم الصحيح: {ex.Message}");
            }
        }

        /// <summary>
        /// اختبار LogActivityAsync مباشرة
        /// </summary>
        private static async Task TestLogActivityDirectly()
        {
            Console.WriteLine("📝 اختبار LogActivityAsync مباشرة:");

            try
            {
                // اختبار مع معاملات مختلفة
                await SecurityHelper.LogActivityAsync("إنشاء مستخدم", "إدارة المستخدمين", "تم إنشاء مستخدم جديد");
                Console.WriteLine("   ✅ LogActivityAsync مع معاملات أساسية");

                await SecurityHelper.LogActivityAsync("حذف ملف", "النظام", "تم حذف ملف مؤقت", "Warning", true);
                Console.WriteLine("   ✅ LogActivityAsync مع جميع المعاملات");

                await SecurityHelper.LogActivityAsync("خطأ في قاعدة البيانات", "النظام", "فشل الاتصال", "Error", false);
                Console.WriteLine("   ✅ LogActivityAsync مع خطأ");

                Console.WriteLine("✅ اختبار LogActivityAsync المباشر نجح!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في اختبار LogActivityAsync: {ex.Message}");
            }
        }

        /// <summary>
        /// اختبار جميع وظائف SecurityHelper
        /// </summary>
        private static void TestAllSecurityHelperFunctions()
        {
            Console.WriteLine("🛠️ اختبار جميع وظائف SecurityHelper:");

            try
            {
                // اختبار كلمة المرور
                var passwords = new[] { "123", "password", "MyStr0ng@Pass!" };
                foreach (var password in passwords)
                {
                    var strength = SecurityHelper.CheckPasswordStrength(password);
                    var description = SecurityHelper.GetPasswordStrengthDescription(strength);
                    Console.WriteLine($"   ✅ كلمة المرور '{password}': {description}");
                }

                // اختبار التشفير
                var originalText = "نص تجريبي للتشفير";
                var encrypted = SecurityHelper.EncryptText(originalText);
                var decrypted = SecurityHelper.DecryptText(encrypted);
                var isCorrect = originalText == decrypted;
                Console.WriteLine($"   ✅ التشفير وفك التشفير: {isCorrect}");

                // اختبار الصلاحيات المطلوبة للوحدات
                var modules = new[] { "المحاسبة", "الحسابات", "التقارير" };
                foreach (var module in modules)
                {
                    var permissions = SecurityHelper.GetRequiredPermissionsForModule(module);
                    Console.WriteLine($"   ✅ الوحدة '{module}': {permissions.Count} صلاحية مطلوبة");
                }

                // اختبار قفل الحساب
                var lockedUser = new User { FailedLoginAttempts = 6 };
                var isLocked = SecurityHelper.IsAccountLockedDueToFailedAttempts(lockedUser);
                Console.WriteLine($"   ✅ قفل الحساب (6 محاولات): {isLocked}");

                Console.WriteLine("✅ اختبار جميع الوظائف نجح!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في اختبار الوظائف: {ex.Message}");
            }
        }

        /// <summary>
        /// اختبار تسجيل الخروج
        /// </summary>
        public static async Task TestLogoutAsync()
        {
            Console.WriteLine("🚪 اختبار تسجيل الخروج:");

            try
            {
                // تأكد من وجود مستخدم
                if (Global_Variable.CurrentUser == null)
                {
                    Global_Variable.CurrentUser = new User
                    {
                        Id = 1,
                        Username = "test_user",
                        FullName = "مستخدم اختبار"
                    };
                }

                Console.WriteLine($"   المستخدم قبل الخروج: {Global_Variable.CurrentUser.Username}");

                // تسجيل الخروج
                await SecurityHelper.LogoutUserAsync();

                var isNull = Global_Variable.CurrentUser == null;
                Console.WriteLine($"   ✅ تم تسجيل الخروج: {isNull}");

                Console.WriteLine("✅ اختبار تسجيل الخروج نجح!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في اختبار تسجيل الخروج: {ex.Message}");
            }
        }

        /// <summary>
        /// تشغيل جميع الاختبارات
        /// </summary>
        public static async Task RunAllFixedTestsAsync()
        {
            Console.WriteLine("🎯 تشغيل جميع اختبارات SecurityHelper المحدثة...\n");

            await TestFixedSecurityHelperAsync();
            Console.WriteLine();

            await TestLogoutAsync();
            Console.WriteLine();

            Console.WriteLine("🏆 انتهت جميع الاختبارات بنجاح!");
        }

        /// <summary>
        /// اختبار سريع للتأكد من عدم وجود أخطاء
        /// </summary>
        public static async Task QuickTestAsync()
        {
            try
            {
                Console.WriteLine("⚡ اختبار سريع...");

                // اختبار أساسي
                Global_Variable.CurrentUser = new User { Id = 1, Username = "quick_test" };
                await SecurityHelper.LogActivityAsync("اختبار سريع", "النظام", "اختبار للتأكد من عدم وجود أخطاء");
                
                var hasPermission = SecurityHelper.HasPermission(PermissionCodes.SYSTEM_ADMIN);
                var strength = SecurityHelper.CheckPasswordStrength("Test123!");
                
                Console.WriteLine("✅ الاختبار السريع نجح - لا توجد أخطاء!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في الاختبار السريع: {ex.Message}");
            }
        }
    }
}
