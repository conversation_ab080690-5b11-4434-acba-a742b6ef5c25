using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using SmartCreator.Entities.Accounting;
using SmartCreator.RJControls;
using static SmartCreator.Entities.Accounting.JournalEntryTypeInfo;
using static SmartCreator.Entities.Accounting.JournalEntryStatusInfo;
using SmartCreator.RJForms;
using FontAwesome.Sharp;
using SmartCreator.Service;
using SmartCreator.Services.Accounting;
using SmartCreator.Services.Security;
using SmartCreator.Settings;

namespace SmartCreator.Forms.Accounting
{
    /// <summary>
    /// نموذج إضافة/تعديل القيد المحاسبي المحسن
    /// </summary>
    public partial class Frm_AddEditJournalEntry_Enhanced : RJChildForm
    {
        #region المتغيرات

        private readonly JournalEntryService _journalEntryService;
        private readonly AccountService _accountService;
        private readonly UserActivityService _activityService;

        private JournalEntry _currentEntry;
        private List<JournalEntryDetail> _entryDetails;
        private List<Account> _accounts;
        private bool _isEditMode = false;
        private bool _isLoading = false;

        // الأحداث
        public event EventHandler<JournalEntry> EntryAdded;
        public event EventHandler<JournalEntry> EntryUpdated;

        // إحصائيات
        private decimal _totalDebit = 0;
        private decimal _totalCredit = 0;
        private bool _isBalanced = false;

        #endregion

        #region البناء

        /// <summary>
        /// Constructor افتراضي للـ Designer
        /// </summary>
        public Frm_AddEditJournalEntry_Enhanced()
        {
            // إنشاء الخدمات بـ constructors افتراضية للـ Designer
            _journalEntryService = new JournalEntryService();
            _accountService = new AccountService();
            _activityService = new UserActivityService();

            _isEditMode = false;
            _currentEntry = new JournalEntry();
            _entryDetails = new List<JournalEntryDetail>();

            InitializeComponent();

            // تجنب تحميل البيانات في وضع التصميم
            if (!DesignMode)
            {
                SetupForm();
                LoadDataAsync();
            }
        }

        /// <summary>
        /// بناء للإضافة
        /// </summary>
        public Frm_AddEditJournalEntry_Enhanced(JournalEntryService journalEntryService,
            AccountService accountService, UserActivityService activityService)
        {
            _journalEntryService = journalEntryService;
            _accountService = accountService;
            _activityService = activityService;

            _isEditMode = false;
            _currentEntry = new JournalEntry();
            _entryDetails = new List<JournalEntryDetail>();

            InitializeComponent();
            SetupForm();
            LoadDataAsync();
        }

        /// <summary>
        /// بناء للتعديل
        /// </summary>
        public Frm_AddEditJournalEntry_Enhanced(JournalEntryService journalEntryService,
            AccountService accountService, UserActivityService activityService, JournalEntry entry)
        {
            _journalEntryService = journalEntryService;
            _accountService = accountService;
            _activityService = activityService;

            _isEditMode = true;
            _currentEntry = entry;

            InitializeComponent();
            SetupForm();
            LoadDataAsync();
        }

        #endregion

        #region إعداد النموذج

        /// <summary>
        /// إعداد النموذج
        /// </summary>
        private void SetupForm()
        {
            // إعداد خصائص RJChildForm
            this.Text = _isEditMode ? $"تعديل القيد - {_currentEntry.EntryNumber}" : "إضافة قيد جديد";
            this.Caption = _isEditMode ? $"تعديل القيد - {_currentEntry.EntryNumber}" : "إضافة قيد جديد";
            this.FormIcon = _isEditMode ? FontAwesome.Sharp.IconChar.Edit : FontAwesome.Sharp.IconChar.Plus;
            this.IsChildForm = true;
            this.Size = new Size(1200, 800);
            this.StartPosition = FormStartPosition.CenterScreen;

            // العناصر موجودة بالفعل في ملف Designer - لا حاجة لإضافتها في runtime

            // إعداد جدول التفاصيل
            SetupDetailsGrid();

            // إعداد القوائم المنسدلة
            SetupComboBoxes();

            // تطبيق إعدادات المظهر
            ApplyThemeSettings();

            // إعداد البيانات الافتراضية
            if (!_isEditMode)
            {
                SetupDefaultData();
            }
        }

        /// <summary>
        /// إعداد جدول التفاصيل
        /// </summary>
        private void SetupDetailsGrid()
        {
            dgvDetails.AutoGenerateColumns = false;
            dgvDetails.AllowUserToAddRows = false;
            dgvDetails.AllowUserToDeleteRows = false;
            dgvDetails.ReadOnly = false;
            dgvDetails.SelectionMode = DataGridViewSelectionMode.FullRowSelect;

            // إعداد الأعمدة
            dgvDetails.Columns.Clear();
            dgvDetails.Columns.AddRange(new DataGridViewColumn[]
            {
                new DataGridViewTextBoxColumn
                {
                    Name = "AccountCode",
                    HeaderText = "رمز الحساب",
                    Width = 120,
                    ReadOnly = true
                },
                new DataGridViewTextBoxColumn
                {
                    Name = "AccountName",
                    HeaderText = "اسم الحساب",
                    Width = 250,
                    ReadOnly = true
                },
                new DataGridViewTextBoxColumn
                {
                    Name = "Description",
                    HeaderText = "البيان",
                    Width = 200
                },
                new DataGridViewTextBoxColumn
                {
                    Name = "DebitAmount",
                    HeaderText = "مدين",
                    Width = 120,
                    DefaultCellStyle = new DataGridViewCellStyle
                    {
                        Format = "N2",
                        Alignment = DataGridViewContentAlignment.MiddleRight
                    }
                },
                new DataGridViewTextBoxColumn
                {
                    Name = "CreditAmount",
                    HeaderText = "دائن",
                    Width = 120,
                    DefaultCellStyle = new DataGridViewCellStyle
                    {
                        Format = "N2",
                        Alignment = DataGridViewContentAlignment.MiddleRight
                    }
                },
                new DataGridViewButtonColumn
                {
                    Name = "SelectAccount",
                    HeaderText = "اختيار حساب",
                    Text = "اختيار",
                    UseColumnTextForButtonValue = true,
                    Width = 100
                },
                new DataGridViewButtonColumn
                {
                    Name = "DeleteRow",
                    HeaderText = "حذف",
                    Text = "حذف",
                    UseColumnTextForButtonValue = true,
                    Width = 80
                }
            });

            // أحداث الجدول
            dgvDetails.CellContentClick += DgvDetails_CellContentClick;
            dgvDetails.CellValueChanged += DgvDetails_CellValueChanged;
            dgvDetails.CellEndEdit += DgvDetails_CellEndEdit;
        }

        /// <summary>
        /// إعداد القوائم المنسدلة
        /// </summary>
        private void SetupComboBoxes()
        {
            // إعداد قائمة أنواع القيود
            cmbEntryType.Items.Clear();
            foreach (JournalEntryType type in Enum.GetValues(typeof(JournalEntryType)))
            {
                cmbEntryType.Items.Add(new { Value = type, Text = GetArabicName(type) });
            }
            cmbEntryType.DisplayMember = "Text";
            cmbEntryType.ValueMember = "Value";
            cmbEntryType.SelectedIndex = 0;

            // إعداد قائمة حالات القيود
            cmbEntryStatus.Items.Clear();
            foreach (JournalEntryStatus status in Enum.GetValues(typeof(JournalEntryStatus)))
            {
                cmbEntryStatus.Items.Add(new { Value = status, Text = GetArabicName(status) });
            }
            cmbEntryStatus.DisplayMember = "Text";
            cmbEntryStatus.ValueMember = "Value";
            cmbEntryStatus.SelectedIndex = 0;
        }

        /// <summary>
        /// تطبيق إعدادات المظهر
        /// </summary>
        private void ApplyThemeSettings()
        {
            this.BackColor = UIAppearance.BackgroundColor;

            var font = new Font("Droid Arabic Kufi", 9F, FontStyle.Regular);
            this.Font = font;

            foreach (Control control in this.Controls)
            {
                ApplyFontToControl(control, font);
            }
        }

        /// <summary>
        /// تطبيق الخط على العنصر وعناصره الفرعية
        /// </summary>
        private void ApplyFontToControl(Control control, Font font)
        {
            control.Font = font;
            foreach (Control childControl in control.Controls)
            {
                ApplyFontToControl(childControl, font);
            }
        }

        /// <summary>
        /// إضافة العناصر إلى منطقة العميل في RJChildForm
        /// </summary>
        private void AddControlsToClientArea()
        {
            // الحصول على منطقة العميل من RJChildForm
            var clientArea = this.Controls.Find("pnlClientArea", true).FirstOrDefault() as Panel;
            if (clientArea != null)
            {
                // إضافة العناصر الرئيسية إلى منطقة العميل
                clientArea.Controls.Add(pnlTop);
                clientArea.Controls.Add(pnlMiddle);
                clientArea.Controls.Add(pnlBottom);

                // تحديث ترتيب العناصر
                pnlTop.BringToFront();
                pnlBottom.BringToFront();
                pnlMiddle.BringToFront();
            }
        }

        /// <summary>
        /// إعداد البيانات الافتراضية
        /// </summary>
        private void SetupDefaultData()
        {
            try
            {
                // تعيين التاريخ الحالي
                dtpEntryDate.Value = DateTime.Now;

                // إنشاء رقم قيد جديد
                txtEntryNumber.Texts = GenerateNewEntryNumber();

                // إضافة سطرين افتراضيين
                AddNewDetailRow();
                AddNewDetailRow();

                // تحديث الإحصائيات
                UpdateStatistics();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في إعداد البيانات الافتراضية: {ex.Message}");
            }
        }

        /// <summary>
        /// توليد رقم قيد جديد
        /// </summary>
        private string GenerateNewEntryNumber()
        {
            try
            {
                var year = DateTime.Now.Year;
                var month = DateTime.Now.Month;
                return $"JE-{year:0000}-{month:00}-{DateTime.Now.Ticks % 10000:0000}";
            }
            catch
            {
                return $"JE-{DateTime.Now.Ticks % 100000:00000}";
            }
        }

        #endregion

        #region تحميل البيانات

        /// <summary>
        /// تحميل البيانات بشكل غير متزامن
        /// </summary>
        private async void LoadDataAsync()
        {
            try
            {
                _isLoading = true;

                // تحميل الحسابات
                _accounts = await Task.Run(() => _accountService.GetAllAccounts());

                // إذا كان في وضع التعديل، تحميل تفاصيل القيد
                if (_isEditMode && _currentEntry != null)
                {
                    await LoadEntryDetails();
                    PopulateFormData();
                }

                _isLoading = false;
            }
            catch (Exception ex)
            {
                _isLoading = false;
                RJMessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تحميل تفاصيل القيد
        /// </summary>
        private async Task LoadEntryDetails()
        {
            try
            {
                _entryDetails = await Task.Run(() => _journalEntryService.GetJournalEntryDetails(_currentEntry.Id));
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تحميل تفاصيل القيد: {ex.Message}");
            }
        }

        /// <summary>
        /// ملء بيانات النموذج
        /// </summary>
        private void PopulateFormData()
        {
            try
            {
                if (_currentEntry == null) return;

                txtEntryNumber.Texts = _currentEntry.EntryNumber;
                dtpEntryDate.Value = _currentEntry.Date;
                txtDescription.Texts = _currentEntry.Description;

                // تعيين النوع والحالة
                for (int i = 0; i < cmbEntryType.Items.Count; i++)
                {
                    var item = (dynamic)cmbEntryType.Items[i];
                    if (item.Value.Equals(_currentEntry.Type))
                    {
                        cmbEntryType.SelectedIndex = i;
                        break;
                    }
                }

                for (int i = 0; i < cmbEntryStatus.Items.Count; i++)
                {
                    var item = (dynamic)cmbEntryStatus.Items[i];
                    if (item.Value.Equals(_currentEntry.Status))
                    {
                        cmbEntryStatus.SelectedIndex = i;
                        break;
                    }
                }

                // ملء تفاصيل القيد
                PopulateDetailsGrid();

                // تحديث الإحصائيات
                UpdateStatistics();
            }
            catch (Exception ex)
            {
                RJMessageBox.Show($"خطأ في ملء بيانات النموذج: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// ملء جدول التفاصيل
        /// </summary>
        private void PopulateDetailsGrid()
        {
            try
            {
                dgvDetails.Rows.Clear();

                if (_entryDetails == null) return;

                foreach (var detail in _entryDetails)
                {
                    var account = _accounts?.FirstOrDefault(a => a.Id == detail.AccountId);

                    var row = new DataGridViewRow();
                    row.CreateCells(dgvDetails);

                    row.Cells["AccountCode"].Value = account?.Code ?? "";
                    row.Cells["AccountName"].Value = account?.Name ?? "";
                    row.Cells["Description"].Value = detail.Description;
                    row.Cells["DebitAmount"].Value = detail.DebitAmount;
                    row.Cells["CreditAmount"].Value = detail.CreditAmount;

                    row.Tag = detail;
                    dgvDetails.Rows.Add(row);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في ملء جدول التفاصيل: {ex.Message}");
            }
        }

        #endregion

        #region أحداث الأزرار

        /// <summary>
        /// إضافة سطر جديد
        /// </summary>
        private void BtnAddRow_Click(object sender, EventArgs e)
        {
            try
            {
                AddNewDetailRow();
                UpdateStatistics();
            }
            catch (Exception ex)
            {
                RJMessageBox.Show($"خطأ في إضافة السطر: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// حذف السطر المحدد
        /// </summary>
        private void BtnDeleteRow_Click(object sender, EventArgs e)
        {
            try
            {
                if (dgvDetails.SelectedRows.Count == 0)
                {
                    RJMessageBox.Show("يرجى تحديد سطر للحذف", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                var result = RJMessageBox.Show("هل أنت متأكد من حذف السطر المحدد؟",
                    "تأكيد الحذف", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    dgvDetails.Rows.RemoveAt(dgvDetails.SelectedRows[0].Index);
                    UpdateStatistics();
                }
            }
            catch (Exception ex)
            {
                RJMessageBox.Show($"خطأ في حذف السطر: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// مسح جميع الأسطر
        /// </summary>
        private void BtnClearAll_Click(object sender, EventArgs e)
        {
            try
            {
                if (dgvDetails.Rows.Count == 0) return;

                var result = RJMessageBox.Show("هل أنت متأكد من مسح جميع الأسطر؟",
                    "تأكيد المسح", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    dgvDetails.Rows.Clear();
                    UpdateStatistics();
                }
            }
            catch (Exception ex)
            {
                RJMessageBox.Show($"خطأ في مسح الأسطر: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// حفظ القيد
        /// </summary>
        private void BtnSave_Click(object sender, EventArgs e)
        {
            try
            {
                SaveEntry(false);
            }
            catch (Exception ex)
            {
                RJMessageBox.Show($"خطأ في الحفظ: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// حفظ وترحيل القيد
        /// </summary>
        private void BtnSaveAndPost_Click(object sender, EventArgs e)
        {
            try
            {
                SaveEntry(true);
            }
            catch (Exception ex)
            {
                RJMessageBox.Show($"خطأ في الحفظ والترحيل: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// إلغاء العملية
        /// </summary>
        private void BtnCancel_Click(object sender, EventArgs e)
        {
            try
            {
                this.DialogResult = DialogResult.Cancel;
                this.Close();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في الإلغاء: {ex.Message}");
            }
        }

        #endregion

        #region أحداث جدول التفاصيل

        /// <summary>
        /// النقر على محتوى الخلية
        /// </summary>
        private void DgvDetails_CellContentClick(object sender, DataGridViewCellEventArgs e)
        {
            try
            {
                if (e.RowIndex < 0) return;

                var columnName = dgvDetails.Columns[e.ColumnIndex].Name;

                if (columnName == "SelectAccount")
                {
                    SelectAccountForRow(e.RowIndex);
                }
                else if (columnName == "DeleteRow")
                {
                    DeleteDetailRow(e.RowIndex);
                }
            }
            catch (Exception ex)
            {
                RJMessageBox.Show($"خطأ في معالجة النقر: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تغيير قيمة الخلية
        /// </summary>
        private void DgvDetails_CellValueChanged(object sender, DataGridViewCellEventArgs e)
        {
            try
            {
                if (e.RowIndex < 0 || _isLoading) return;

                UpdateStatistics();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في تغيير القيمة: {ex.Message}");
            }
        }

        /// <summary>
        /// انتهاء تحرير الخلية
        /// </summary>
        private void DgvDetails_CellEndEdit(object sender, DataGridViewCellEventArgs e)
        {
            try
            {
                if (e.RowIndex < 0) return;

                var columnName = dgvDetails.Columns[e.ColumnIndex].Name;
                var row = dgvDetails.Rows[e.RowIndex];

                // التحقق من صحة المبالغ
                if (columnName == "DebitAmount" || columnName == "CreditAmount")
                {
                    ValidateAmountCell(row, columnName);
                }

                UpdateStatistics();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في انتهاء التحرير: {ex.Message}");
            }
        }

        #endregion

        #region طرق مساعدة

        /// <summary>
        /// إضافة سطر تفاصيل جديد
        /// </summary>
        private void AddNewDetailRow()
        {
            var row = new DataGridViewRow();
            row.CreateCells(dgvDetails);

            row.Cells["AccountCode"].Value = "";
            row.Cells["AccountName"].Value = "";
            row.Cells["Description"].Value = "";
            row.Cells["DebitAmount"].Value = 0.00m;
            row.Cells["CreditAmount"].Value = 0.00m;

            dgvDetails.Rows.Add(row);
        }

        /// <summary>
        /// اختيار حساب للسطر
        /// </summary>
        private void SelectAccountForRow(int rowIndex)
        {
            try
            {
                var accountSelector = new Frm_AccountSelector(_accountService);
                accountSelector.AccountSelected += (s, account) =>
                {
                    var row = dgvDetails.Rows[rowIndex];
                    row.Cells["AccountCode"].Value = account.Code;
                    row.Cells["AccountName"].Value = account.Name;
                    row.Tag = account;
                };

                accountSelector.ShowDialog();
            }
            catch (Exception ex)
            {
                RJMessageBox.Show($"خطأ في اختيار الحساب: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// حذف سطر تفاصيل
        /// </summary>
        private void DeleteDetailRow(int rowIndex)
        {
            try
            {
                var result = RJMessageBox.Show("هل أنت متأكد من حذف هذا السطر؟",
                    "تأكيد الحذف", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    dgvDetails.Rows.RemoveAt(rowIndex);
                    UpdateStatistics();
                }
            }
            catch (Exception ex)
            {
                RJMessageBox.Show($"خطأ في حذف السطر: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// التحقق من صحة خلية المبلغ
        /// </summary>
        private void ValidateAmountCell(DataGridViewRow row, string columnName)
        {
            try
            {
                var cellValue = row.Cells[columnName].Value?.ToString();
                if (string.IsNullOrEmpty(cellValue)) return;

                if (!decimal.TryParse(cellValue, out decimal amount) || amount < 0)
                {
                    RJMessageBox.Show("يرجى إدخال مبلغ صحيح", "خطأ في البيانات",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    row.Cells[columnName].Value = 0.00m;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في التحقق من المبلغ: {ex.Message}");
            }
        }

        /// <summary>
        /// تحديث الإحصائيات
        /// </summary>
        private void UpdateStatistics()
        {
            try
            {
                _totalDebit = 0;
                _totalCredit = 0;

                foreach (DataGridViewRow row in dgvDetails.Rows)
                {
                    if (decimal.TryParse(row.Cells["DebitAmount"].Value?.ToString(), out decimal debit))
                        _totalDebit += debit;

                    if (decimal.TryParse(row.Cells["CreditAmount"].Value?.ToString(), out decimal credit))
                        _totalCredit += credit;
                }

                lblTotalDebit.Text = $"مدين: {_totalDebit:N2}";
                lblTotalCredit.Text = $"دائن: {_totalCredit:N2}";

                var balance = _totalDebit - _totalCredit;
                lblBalance.Text = $"الفرق: {Math.Abs(balance):N2}";

                _isBalanced = Math.Abs(balance) < 0.01m;
                lblBalanceStatus.Text = _isBalanced ? "✓ متزن" : "✗ غير متزن";
                lblBalanceStatus.ForeColor = _isBalanced ?
                    Color.FromArgb(40, 167, 69) :
                    Color.FromArgb(220, 53, 69);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في تحديث الإحصائيات: {ex.Message}");
            }
        }

        /// <summary>
        /// حفظ القيد
        /// </summary>
        private void SaveEntry(bool postEntry)
        {
            try
            {
                // التحقق من صحة البيانات
                if (!ValidateEntry()) return;

                // إنشاء كائن القيد
                var entry = CreateJournalEntryFromForm();

                if (postEntry)
                {
                    entry.Status = JournalEntryStatus.Posted;
                }

                // محاكاة الحفظ
                var message = _isEditMode ? "تم تحديث القيد بنجاح" : "تم إضافة القيد بنجاح";
                if (postEntry) message += " وترحيله";

                RJMessageBox.Show(message, "نجح الحفظ",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);

                // إثارة الأحداث
                if (_isEditMode)
                    EntryUpdated?.Invoke(this, entry);
                else
                    EntryAdded?.Invoke(this, entry);

                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في حفظ القيد: {ex.Message}");
            }
        }

        /// <summary>
        /// التحقق من صحة القيد
        /// </summary>
        private bool ValidateEntry()
        {
            if (string.IsNullOrWhiteSpace(txtDescription.Texts))
            {
                RJMessageBox.Show("يرجى إدخال بيان القيد", "بيانات ناقصة",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }

            if (dgvDetails.Rows.Count < 2)
            {
                RJMessageBox.Show("يجب أن يحتوي القيد على حسابين على الأقل", "بيانات ناقصة",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }

            if (!_isBalanced)
            {
                RJMessageBox.Show("القيد غير متوازن - يجب أن يساوي إجمالي المدين إجمالي الدائن", "قيد غير متوازن",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }

            return true;
        }

        /// <summary>
        /// إنشاء كائن القيد من النموذج
        /// </summary>
        private JournalEntry CreateJournalEntryFromForm()
        {
            var entry = new JournalEntry
            {
                Id = _currentEntry?.Id ?? 0,
                EntryNumber = txtEntryNumber.Texts,
                Date = dtpEntryDate.Value,
                Description = txtDescription.Texts,
                Type = (JournalEntryType)((dynamic)cmbEntryType.SelectedItem).Value,
                Status = (JournalEntryStatus)((dynamic)cmbEntryStatus.SelectedItem).Value,
                TotalDebit = _totalDebit,
                TotalCredit = _totalCredit,
                Details = new List<JournalEntryDetail>()
            };

            // إضافة التفاصيل
            for (int i = 0; i < dgvDetails.Rows.Count; i++)
            {
                var row = dgvDetails.Rows[i];
                var account = row.Tag as Account;

                if (account != null)
                {
                    var detail = new JournalEntryDetail
                    {
                        JournalEntryId = entry.Id,
                        AccountId = account.Id,
                        Description = row.Cells["Description"].Value?.ToString() ?? "",
                        DebitAmount = decimal.Parse(row.Cells["DebitAmount"].Value?.ToString() ?? "0"),
                        CreditAmount = decimal.Parse(row.Cells["CreditAmount"].Value?.ToString() ?? "0"),
                        LineNumber = i + 1
                    };

                    entry.Details.Add(detail);
                }
            }

            return entry;
        }

        #endregion
    }
}
