﻿using Dapper;
using SmartCreator.Data;
using SmartCreator.Models;
using SmartCreator.Utils;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Data.SQLite;
using System.IO;
using System.Linq;
using System.Text;
using System.Windows.Forms;

namespace SmartCreator.db
{
    public partial class FormConnection : RJForms.RJChildForm
    {
        private readonly int index;
        private readonly Connections_Db curentDb=null;
        bool firstload = true;
        
        public FormConnection(Connections_Db db =null)
        {
            InitializeComponent();
            //grid.DefaultCellStyle.SelectionBackColor = (header.BackColor = Config.Theme);
            this.comboBox1.Items.AddRange(new object[8] { "SQLlite", "SQL Server", "SQL Server Compact", "MySql", "MariaDb", "PostgreSql", "Oracle", "Firebird" });
            this.comboBox2.Items.AddRange(new object[8] { "SQLlite", "SQL Server", "SQL Server Compact", "MySql", "MariaDb", "PostgreSql", "Oracle", "Firebird" });

            comboBox1.SelectedIndex = 0;
            comboBox2.SelectedIndex = 0;
            //this.index = index;
            this.curentDb = db;
            Loaddata();
            firstload = false;
        }

        private void Loaddata()
        {
            if (curentDb != null)
            {
                lblTitle.Text = "Edit " + curentDb.Name;
                txt_name.Text =curentDb.Name;
                CheckBox_default.Check = Convert.ToBoolean(curentDb.Default);
               
                RenderCS(curentDb.Connection_string.ToString());
                comboBox1.SelectedItem = curentDb.Type.ToString();
                comboBox2.SelectedIndex = comboBox1.SelectedIndex;
            }
            //if (index >= 0)
            //{
            //    lblTitle.Text = "Edit " + Connections._connections[index]["name"];
            //    RenderCS(Connections._connections[index]["connection_string"].ToString());
            //    comboBox1.SelectedItem = Connections._connections[index]["type"].ToString();
            //    comboBox2.SelectedIndex = comboBox1.SelectedIndex;
            //}
        }

        public string GetConnectionString()
        {
            StringBuilder stringBuilder = new StringBuilder();
            foreach (DataGridViewRow item in (IEnumerable)grid.Rows)
            {
                stringBuilder.Append(item.Cells[0].Value?.ToString() + "=" + item.Cells[1].Value?.ToString() + ";");
            }

            return stringBuilder.ToString();
        }

        private void btnConnectionstring_Click(object sender, EventArgs e)
        {
            if (grid.RowCount > 0 && MessageBox.Show("This will overwrite the current string.", Application.ProductName, MessageBoxButtons.YesNoCancel) != DialogResult.Yes)
            {
                return;
            }

            try
            {
                RenderCS(Clipboard.GetText());
            }
            catch (Exception)
            {
            }
        }

        private void RenderCS(string cs)
        {
            try
            {
                grid.Rows.Clear();
                foreach (KeyValuePair<string, string> item in Parse(cs))
                {
                    int num = grid.Rows.Add(item.Key, item.Value);
                    grid.Rows[num].Cells[0].ReadOnly = true;
                }
            }
            catch (Exception)
            {
                MessageBox.Show("Invlid Connection String.");
            }
        }

        public  Dictionary<string, string> Parse(string cs)
        {
            try
            {
                Dictionary<string, string> dictionary = new Dictionary<string, string>();
                string[] array = cs.Split(';');
                for (int i = 0; i < array.Length; i++)
                {
                    string[] array2 = array[i].Trim().Split('=');
                    if (array2.Length == 2)
                    {
                        dictionary.Add(array2[0], array2[1]);
                    }
                }

                return dictionary;
            }
            catch (Exception)
            {
                throw;
            }
        }
       
        private void btnSave_Click(object sender, EventArgs e)
        {

            if (comboBox2.Text == "SQLlite")
                save_new_sqlite_database();

            if (comboBox2.Text == "SQL Server")
                save_new_sqlServer_database();

            if (comboBox2.Text == "SQL Server Compact")
                save_new_sqlServer_database();

            if (comboBox2.Text == "MySql")
                save_new_sqlite_database();

            if (comboBox2.Text == "MariaDb")
                save_new_sqlServer_database();

            if (comboBox2.Text == "PostgreSql")
                save_new_sqlServer_database();

            //if (comboBox2.Text == "MariaDb")
            //    save_new_sqlServer_database();

            Close();
        }

        private void comboBox1_SelectionChangeCommitted(object sender, EventArgs e)
        {
            return;
            if (firstload)
                return;
            if (grid.RowCount > 0 && MessageBox.Show("This will overwrite the current string.", Application.ProductName, MessageBoxButtons.YesNoCancel) != DialogResult.Yes)
            {
                Loaddata();
                return;
            }

            Dictionary<string, string> dictionary = new Dictionary<string, string>();
            //var connectionString3 = @"Data Source=db\localDB21.db";
            string _localDB_path = "";
            if (comboBox1.SelectedIndex == 0)
                _localDB_path = Create_New_localDB() + ";";

            dictionary.Add("SQLlite", "Data Source=" + _localDB_path);
            dictionary.Add("SQL Server", "Server=;Database=;Trusted_Connection=True;");
            dictionary.Add("SQL Server Compact", "Data Source=MyData.sdf;Persist Security Info=False;");
            dictionary.Add("MySql", "Server=localhost;Database=;Uid=root;Pwd=;SslMode=none;");
            dictionary.Add("MariaDb", "Server=localhost;Database=;Uid=root;Pwd=;SslMode=none;");
            dictionary.Add("PostgreSql", "Server=127.0.0.1;Port=5432;Database=;User Id=;Password=;");
            dictionary.Add("Oracle", "Data Source=;Integrated Security=yes;");
            dictionary.Add("Firebird", "User=SYSDBA;Password=masterkey;Database=SampleDatabase.fdb;DataSource=localhost;Port=3050;Dialect=3;Charset=NONE;Role=;Connection lifetime=15;Pooling=true;MinPoolSize=0;MaxPoolSize=50;Packet Size=8192;ServerType = 0; ");
            
            var f = dictionary[comboBox1.SelectedItem.ToString()];
            RenderCS(f);
        }

        #region  sqlite Database check

        public void Get_Path_Database2()
        { 
            var rb = Smart_DataAccess.Get_default_Connections_Db();
            if (rb != null)
            {
                if (rb.Mk_code == Global_Variable.Mk_resources.RB_code || rb.Mk_code == Global_Variable.Mk_resources.RB_SN || rb.Mk_sn == Global_Variable.Mk_resources.RB_SN)
                {
                    Sql_DataAccess.connection_string = utils.Get_LocalDB_ConnectionString(rb.FileName);
                    Sql_DataAccess.DefualtDBType = rb.Type;
                    Sql_DataAccess.username_db = rb.Username_db;
                    Sql_DataAccess.password_db = rb.Password_db;
                    Sql_DataAccess.db_File_Name = rb.FileName;
                    Global_Variable.Mk_Router.localDB_path = $"{utils.Get_Database_Directory()}\\{rb.FileName}";
                    Global_Variable.Mk_Router.localDB_fileName = rb.FileName;
                    if (rb.Type == "SQLlite") 
                        Check_file_db_found(rb); 
                }
            }
            else
            { 
                Connections_Db New_db = Create_New_localDB(new Connections_Db());
                New_db.Type = "SQLlite";
                New_db.Default = 1;
                Sql_DataAccess.Add_Edit_Connection_string(New_db, false, true);

                Sql_DataAccess.connection_string = New_db.Connection_string;
                Sql_DataAccess.DefualtDBType = New_db.Type;
                Sql_DataAccess.username_db = New_db.Username_db;
                Sql_DataAccess.password_db = New_db.Password_db;
                Sql_DataAccess.db_File_Name = New_db.FileName;

                Global_Variable.Mk_Router.localDB_path = New_db.LocalDB_path;
                Global_Variable.Mk_Router.localDB_fileName = New_db.FileName;
            }
        }
        public void Get_Path_Database3()
        {
            //var rb2 = Smart_DataAccess.Get_default_Connections_Db();
            //MessageBox.Show($"last db name =  {rb2.FileName}");
            var rb = new Connections_Db
            {
                //FileName=$"db_91D709CCF83D",
                FileName = $"db_{Global_Variable.Mk_resources.RB_SN}.db",
                Mk_sn = Global_Variable.Mk_resources.RB_SN,
                Soft_id = Global_Variable.Mk_resources.RB_Soft_id,
                Mk_code = Global_Variable.Mk_resources.RB_code,
                LocalDB_path = $"{utils.Get_Database_Directory()}\\db_{Global_Variable.Mk_resources.RB_SN}.db",
                Connection_string = $"{utils.Get_LocalDB_ConnectionString($"db_{Global_Variable.Mk_resources.RB_SN}.db")}",
                Type = "SQLlite",
                Name = "SQLlite Connection",
                Username_db = "admin",
                Password_db = "admin@1212Smart",
            };

            Sql_DataAccess.connection_string = rb.Connection_string;
            Sql_DataAccess.DefualtDBType = rb.Type;
            Sql_DataAccess.username_db = rb.Username_db;
            Sql_DataAccess.password_db = rb.Password_db;
            Sql_DataAccess.db_File_Name = rb.FileName;
            Global_Variable.Mk_Router.localDB_path = rb.LocalDB_path;
            Global_Variable.Mk_Router.localDB_fileName = rb.FileName;

            if (rb.Type == "SQLlite")
                Check_LocalDB_File_byScript(rb);
            //Check_file_db_found(rb);




            //if (rb != null)
            //{
            //    if (rb.Mk_code == Global_Variable.Mk_resources.RB_code || rb.Mk_code == Global_Variable.Mk_resources.RB_SN || rb.Mk_sn == Global_Variable.Mk_resources.RB_SN)
            //    {
            //        Sql_DataAccess.connection_string = utils.Get_LocalDB_ConnectionString(rb.FileName);
            //        Sql_DataAccess.DefualtDBType = rb.Type;
            //        Sql_DataAccess.username_db = rb.Username_db;
            //        Sql_DataAccess.password_db = rb.Password_db;
            //        Sql_DataAccess.db_File_Name = rb.FileName;
            //        Global_Variable.Mk_Router.localDB_path = $"{utils.Get_Database_Directory()}\\{rb.FileName}";
            //        Global_Variable.Mk_Router.localDB_fileName = rb.FileName;
            //        if (rb.Type == "SQLlite") 
            //            Check_file_db_found(rb); 
            //    }
            //}
            //else
            //{
            //    Connections_Db New_db = Create_New_localDB(new Connections_Db());
            //    New_db.Type = "SQLlite";
            //    New_db.Default = 1;
            //    Sql_DataAccess.Add_Edit_Connection_string(New_db, false, true);

            //    Sql_DataAccess.connection_string = New_db.Connection_string;
            //    Sql_DataAccess.DefualtDBType = New_db.Type;
            //    Sql_DataAccess.username_db = New_db.Username_db;
            //    Sql_DataAccess.password_db = New_db.Password_db;
            //    Sql_DataAccess.db_File_Name = New_db.FileName;

            //    Global_Variable.Mk_Router.localDB_path = New_db.LocalDB_path;
            //    Global_Variable.Mk_Router.localDB_fileName = New_db.FileName;
            //}
        }
         
        public string Create_New_localDB()
        {
            string pathNew = Get_Next_Name_DB();
            string fileName = "";
            try
            {
                fileName = Path.GetFileName(pathNew);
                string folder = Directory.GetCurrentDirectory() + "\\db";
                CreateNewSqliteDatabase f = new CreateNewSqliteDatabase(fileName, folder, false);


                //Global_Variable.Mk_Router.localDB_path = "db\\" + fileName;
                //Global_Variable.Mk_Router.localDB_fileName = fileName;
            }
            catch { }
            return "db\\" + fileName;
            //return pathNew;
        }
       
        public string Get_Next_Name_DB()
        {
            int maxAttempts = 200;
            //conn.Connection_string = utils.Get_LocalDB_ConnectionString(conn.FileName);
            //conn.LocalDB_path = $"{utils.Get_Database_Directory()}\\{conn.FileName}";

            string fileName = "localDB.db";
            //string fileName = utils.Get_LocalDB_ConnectionString("localDB.db");
            //string fileName = $"Data Source={Directory.GetParent(Application.StartupPath)}\\dbs\\localDB.db;";
            //string fileName = Directory.GetCurrentDirectory() + "\\db\\localDB.db";
            //string fileName = Directory.GetCurrentDirectory() + "\\db\\localDB.db";
            string folder = $"{utils.Get_Database_Directory()}";
            //string folder = $"{Directory.GetParent(Application.StartupPath)}\\dbs";
            //string folder = Directory.GetCurrentDirectory() + "\\db";

            // get filename base and extension
            var fileBase = Path.GetFileNameWithoutExtension(fileName);
            var ext = Path.GetExtension(fileName);
            // build hash set of filenames for performance
            var files = new HashSet<string>(Directory.GetFiles(folder));

            for (var index = 1; index < maxAttempts; index++)
            {
                // first try with the original filename, else try incrementally adding an index
                var name = (index == 0)
                    ? fileName
                    : String.Format("{0}{1}{2}", fileBase, index, ext);

                // check if exists
                var fullPath = Path.Combine(folder, name);
                if (files.Contains(fullPath))
                    continue;

                // try to create the file
                try
                {
                    return fullPath;
                    //return new FileStream(fullPath, FileMode.CreateNew, FileAccess.Write);
                }
                catch (DirectoryNotFoundException) { throw; }
                catch (DriveNotFoundException) { throw; }
                catch (IOException)
                {
                }
            }

            RJMessageBox.Show("Could not create unique filename in " + maxAttempts + " attempts");
            return "";
            //throw new Exception("Could not create unique filename in " + maxAttempts + " attempts");
        }
        private void Check_file_db_found(Connections_Db conn = null)
        {
            string folderDb = $"{utils.Get_Database_Directory()}";
            conn.LocalDB_path = $"{utils.Get_Database_Directory()}\\{conn.FileName}";
            conn.Connection_string = utils.Get_LocalDB_ConnectionString(conn.FileName);
            var files = new HashSet<string>(Directory.GetFiles(folderDb));
            var fullPath = conn.LocalDB_path;

            //================== new way  add db ================
            //clss_CreateNewDatabase ff = new clss_CreateNewDatabase();
            //ff.create_default_db(conn); 
            //if (files.Contains(fullPath) == false)
            //    ff.create_default_db(conn);


            Check_LocalDB_File_byScript(conn);
        }
        public void Check_SmartDB_File()
        {
            Run_sqlite_Maintain();



            Check_SmartDB_If_Not_EXISTS();

            string folderDb = $"{utils.Get_Database_Directory()}";
            string Smartfile = $"{folderDb}\\Smart.db";

            if (!File.Exists(Smartfile))
            {
                //string ScriptDB = "BEGIN TRANSACTION;\r\nCREATE TABLE IF NOT EXISTS \"Alert_SellingPoint\" (\r\n\t\"Id\"\tINTEGER NOT NULL,\r\n\t\"SpCode\"\tTEXT,\r\n\t\"ProfileName\"\tTEXT,\r\n\t\"Is_Alert\"\tINTEGER DEFAULT 0,\r\n\t\"Count_Soon\"\tINTEGER DEFAULT 75,\r\n\t\"Count_Finsh\"\tINTEGER DEFAULT 95,\r\n\t\"Rb\"\tTEXT,\r\n\t\"Rb_Sn\"\tTEXT,\r\n\tPRIMARY KEY(\"Id\" AUTOINCREMENT),\r\n\tCONSTRAINT \"UC_Alert_SellingPoint_SpCode_Rb_ProfileName\" UNIQUE(\"SpCode\",\"Rb\",\"ProfileName\")\r\n);\r\nCREATE TABLE IF NOT EXISTS \"BatchCard\" (\r\n\t\"Id\"\tINTEGER NOT NULL UNIQUE,\r\n\t\"BatchNumber\"\tINTEGER NOT NULL,\r\n\t\"AddedDate\"\tTEXT,\r\n\t\"Count\"\tINTEGER NOT NULL,\r\n\t\"Sn_from\"\tINTEGER NOT NULL,\r\n\t\"Sn_to\"\tINTEGER NOT NULL,\r\n\t\"ProfileName\"\tTEXT,\r\n\t\"SpCode\"\tTEXT,\r\n\t\"SpName\"\tTEXT,\r\n\t\"Server\"\tINTEGER NOT NULL DEFAULT (0),\r\n\t\"Count_waiting\"\tINTEGER NOT NULL DEFAULT (0),\r\n\t\"Count_active\"\tINTEGER NOT NULL DEFAULT (0),\r\n\t\"Count_finshed\"\tINTEGER NOT NULL DEFAULT (0),\r\n\t\"Count_deleteServer\"\tINTEGER NOT NULL DEFAULT (0),\r\n\t\"Rb\"\tTEXT NOT NULL,\r\n\t\"BatchType\"\tINTEGER DEFAULT 0,\r\n\t\"Count_FoundServer\"\tINTEGER DEFAULT 0,\r\n\t\"Rb_Sn\"\tTEXT,\r\n\tUNIQUE(\"BatchNumber\",\"Rb\"),\r\n\tPRIMARY KEY(\"Id\" AUTOINCREMENT)\r\n);\r\nCREATE TABLE IF NOT EXISTS \"CardsTemplate\" (\r\n\t\"id\"\tINTEGER NOT NULL,\r\n\t\"name\"\tTEXT,\r\n\t\"type\"\tTEXT,\r\n\t\"values\"\tTEXT,\r\n\t\"rb\"\tTEXT,\r\n\t\"rb_Sn\"\tTEXT,\r\n\tPRIMARY KEY(\"id\" AUTOINCREMENT),\r\n\tUNIQUE(\"name\",\"rb\")\r\n);\r\nCREATE TABLE IF NOT EXISTS \"Comm_SellingPoint\" (\r\n\t\"Id\"\tINTEGER NOT NULL,\r\n\t\"SpCode\"\tTEXT,\r\n\t\"ProfileName\"\tTEXT,\r\n\t\"Is_percentage\"\tINTEGER DEFAULT 0,\r\n\t\"Percentage\"\tDOUBLE DEFAULT 0,\r\n\t\"PercentageType\"\tINTEGER DEFAULT 0,\r\n\t\"Rb\"\tTEXT,\r\n\t\"Rb_Sn\"\tTEXT,\r\n\tPRIMARY KEY(\"Id\" AUTOINCREMENT),\r\n\tUNIQUE(\"SpCode\",\"ProfileName\",\"Rb\")\r\n);\r\nCREATE TABLE IF NOT EXISTS \"Connections_Db\" (\r\n\t\"Id\"\tINTEGER NOT NULL UNIQUE,\r\n\t\"Soft_id\"\tTEXT NOT NULL,\r\n\t\"Mk_sn\"\tTEXT NOT NULL,\r\n\t\"Mk_code\"\tTEXT NOT NULL,\r\n\t\"Comment\"\tTEXT,\r\n\t\"LocalDB_path\"\tTEXT DEFAULT 'db\\\\localDB1.db',\r\n\t\"FileName\"\tTEXT DEFAULT 'localDB1.db' UNIQUE,\r\n\t\"Resources\"\tTEXT,\r\n\t\"Default\"\tINTEGER DEFAULT 1,\r\n\t\"Type\"\tTEXT DEFAULT 'SQLlite',\r\n\t\"Connection_string\"\tTEXT DEFAULT 'Data Sourc=localDB1.db;',\r\n\t\"Name\"\tTEXT DEFAULT 'SQLlite',\r\n\t\"Username_db\"\tTEXT,\r\n\t\"Password_db\"\tTEXT,\r\n\tPRIMARY KEY(\"Id\" AUTOINCREMENT),\r\n\tUNIQUE(\"Mk_code\",\"FileName\")\r\n);\r\nCREATE TABLE IF NOT EXISTS \"HSLocalProfile\" (\r\n\t\"Id\"\tINTEGER UNIQUE,\r\n\t\"Name\"\tTEXT,\r\n\t\"UptimeLimit\"\tINTEGER NOT NULL DEFAULT (0),\r\n\t\"Validity\"\tINTEGER NOT NULL DEFAULT (0),\r\n\t\"TransferLimit\"\tINTEGER NOT NULL DEFAULT (0),\r\n\t\"Price\"\tDOUBLE NOT NULL DEFAULT (0),\r\n\t\"Is_percentage\"\tINTEGER NOT NULL DEFAULT (0),\r\n\t\"Percentage\"\tDOUBLE NOT NULL DEFAULT (0),\r\n\t\"PercentageType\"\tINTEGER NOT NULL DEFAULT (0),\r\n\t\"Price_Display\"\tTEXT,\r\n\t\"Add_Smart_Scripts\"\tINTEGER NOT NULL DEFAULT (0),\r\n\t\"Save_time\"\tINTEGER NOT NULL DEFAULT (0),\r\n\t\"Save_download\"\tINTEGER NOT NULL DEFAULT (0),\r\n\t\"Save_session\"\tINTEGER NOT NULL DEFAULT (0),\r\n\t\"ByDayOrHour\"\tINTEGER NOT NULL DEFAULT (0),\r\n\t\"Link_hotspot_profile\"\tTEXT,\r\n\t\"Rb\"\tTEXT,\r\n\tPRIMARY KEY(\"Id\" AUTOINCREMENT),\r\n\tCONSTRAINT \"UC_HSLocalProfile_Name_Rb\" UNIQUE(\"Name\",\"Rb\")\r\n);\r\nCREATE TABLE IF NOT EXISTS \"Hotspot_Profile_Hotspot_local\" (\r\n\t\"id\"\tINTEGER NOT NULL UNIQUE,\r\n\t\"Name\"\tTEXT NOT NULL,\r\n\t\"Price_Sales\"\tINTEGER DEFAULT 0,\r\n\t\"Price_display\"\tINTEGER DEFAULT 0,\r\n\t\"Validity\"\tINTEGER DEFAULT 0,\r\n\t\"uptimeLimit\"\tINTEGER DEFAULT 0,\r\n\t\"transferLimit\"\tINTEGER DEFAULT 0,\r\n\t\"rb\"\tTEXT NOT NULL,\r\n\t\"link_hotspot_profile\"\tTEXT,\r\n\t\"add_Smart_Scripts\"\tINTEGER DEFAULT 1,\r\n\t\"Save_time\"\tINTEGER DEFAULT 1,\r\n\t\"Save_download\"\tINTEGER DEFAULT 1,\r\n\t\"Save_session\"\tINTEGER DEFAULT 1,\r\n\t\"ByDayOrHour\"\tINTEGER DEFAULT 0,\r\n\t\"Is_percentage\"\tINTEGER DEFAULT 0,\r\n\t\"Percentage\"\tfloat DEFAULT 0,\r\n\t\"PercentageType\"\tINTEGER DEFAULT 0,\r\n\tUNIQUE(\"Name\",\"rb\"),\r\n\tPRIMARY KEY(\"id\" AUTOINCREMENT)\r\n);\r\nCREATE TABLE IF NOT EXISTS \"Hotspot_Source_Profile\" (\r\n\t\"id\"\tINTEGER NOT NULL UNIQUE,\r\n\t\"Name\"\tTEXT NOT NULL,\r\n\t\"rb\"\tTEXT NOT NULL,\r\n\t\"Delet_fromServer\"\tINTEGER NOT NULL DEFAULT 0,\r\n\t\"idHX\"\tTEXT NOT NULL,\r\n\tUNIQUE(\"Name\",\"rb\"),\r\n\tPRIMARY KEY(\"id\" AUTOINCREMENT)\r\n);\r\nCREATE TABLE IF NOT EXISTS \"My_Sequence\" (\r\n\t\"Id\"\tINTEGER UNIQUE,\r\n\t\"Name\"\tTEXT,\r\n\t\"Seq\"\tINTEGER NOT NULL DEFAULT (0),\r\n\t\"Rb\"\tTEXT,\r\n\tPRIMARY KEY(\"Id\" AUTOINCREMENT),\r\n\tCONSTRAINT \"UC_My_Sequence_Name_Rb\" UNIQUE(\"Name\",\"Rb\")\r\n);\r\nCREATE TABLE IF NOT EXISTS \"NumberPrintCard\" (\r\n\t\"NumberPrint\"\tINTEGER,\r\n\t\"Id\"\tINTEGER NOT NULL,\r\n\t\"BatchNumber\"\tINTEGER NOT NULL,\r\n\t\"AddedDate\"\tTEXT,\r\n\t\"Count\"\tINTEGER,\r\n\t\"Sn_from\"\tINTEGER,\r\n\t\"Sn_to\"\tINTEGER,\r\n\t\"ProfileName\"\tTEXT,\r\n\t\"SpCode\"\tTEXT,\r\n\t\"SpName\"\tTEXT,\r\n\t\"Server\"\tINTEGER DEFAULT (0),\r\n\t\"Count_waiting\"\tINTEGER DEFAULT (0),\r\n\t\"Count_active\"\tINTEGER DEFAULT (0),\r\n\t\"Count_finshed\"\tINTEGER DEFAULT (0),\r\n\t\"Count_deleteServer\"\tINTEGER DEFAULT (0),\r\n\t\"Count_FoundServer\"\tINTEGER DEFAULT (0),\r\n\t\"Rb\"\tTEXT,\r\n\t\"BatchType\"\tINTEGER DEFAULT (0),\r\n\t\"RB_Sn\"\tTEXT,\r\n\tPRIMARY KEY(\"Id\" AUTOINCREMENT)\r\n);\r\nCREATE TABLE IF NOT EXISTS \"SellingPoint\" (\r\n\t\"Id\"\tINTEGER NOT NULL UNIQUE,\r\n\t\"Code\"\tTEXT NOT NULL,\r\n\t\"UserName\"\tTEXT NOT NULL,\r\n\t\"Prefixes\"\tTEXT,\r\n\t\"Suffixes\"\tTEXT,\r\n\t\"Address\"\tTEXT,\r\n\t\"Is_percentage\"\tINTEGER NOT NULL DEFAULT (0),\r\n\t\"Percentage\"\tDOUBLE NOT NULL DEFAULT (0),\r\n\t\"PercentageType\"\tINTEGER NOT NULL DEFAULT (0),\r\n\t\"UseAccounting\"\tINTEGER DEFAULT 0,\r\n\t\"Phone\"\tTEXT,\r\n\t\"Rb\"\tTEXT,\r\n\t\"Rb_Sn\"\tTEXT,\r\n\t\"Is_percentage_Custom\"\tINTEGER DEFAULT 0,\r\n\t\"Is_Alert\"\tINTEGER DEFAULT 0,\r\n\t\"Count_Soon\"\tINTEGER DEFAULT 70,\r\n\t\"Count_Finsh\"\tINTEGER DEFAULT 95,\r\n\t\"Is_Alert_Custom\"\tINTEGER DEFAULT 0,\r\n\tPRIMARY KEY(\"Id\" AUTOINCREMENT)\r\n);\r\nCREATE TABLE IF NOT EXISTS \"Setting_Mk_Login_Saved\" (\r\n\t\"id\"\tINTEGER NOT NULL UNIQUE,\r\n\t\"values\"\tTEXT NOT NULL,\r\n\t\"active\"\tINTEGER DEFAULT 0,\r\n\tPRIMARY KEY(\"id\" AUTOINCREMENT)\r\n);\r\nCREATE TABLE IF NOT EXISTS \"Setting_SaveState_Forms_Variables\" (\r\n\t\"id\"\tINTEGER NOT NULL,\r\n\t\"name\"\tTEXT,\r\n\t\"type\"\tTEXT,\r\n\t\"values\"\tTEXT,\r\n\t\"rb\"\tTEXT,\r\n\tPRIMARY KEY(\"id\" AUTOINCREMENT),\r\n\tUNIQUE(\"name\",\"rb\")\r\n);\r\nCREATE TABLE IF NOT EXISTS \"UmLimitation\" (\r\n\t\"Id\"\tINTEGER UNIQUE,\r\n\t\"IdHX\"\tTEXT NOT NULL,\r\n\t\"Sn\"\tINTEGER NOT NULL,\r\n\t\"Name\"\tTEXT NOT NULL,\r\n\t\"Sn_Name\"\tTEXT NOT NULL,\r\n\t\"DownloadLimit\"\tINTEGER NOT NULL DEFAULT (0),\r\n\t\"UploadLimit\"\tTEXT NOT NULL DEFAULT (0),\r\n\t\"TransferLimit\"\tINTEGER NOT NULL DEFAULT (0),\r\n\t\"UptimeLimit\"\tINTEGER NOT NULL DEFAULT (0),\r\n\t\"RateLimit\"\tTEXT,\r\n\t\"Download_tx\"\tTEXT,\r\n\t\"Upload_rx\"\tTEXT,\r\n\t\"GroupName\"\tTEXT,\r\n\t\"DownloadPrice\"\tINTEGER NOT NULL DEFAULT (0),\r\n\t\"UploadPrice\"\tINTEGER NOT NULL DEFAULT (0),\r\n\t\"UptimePrice\"\tINTEGER NOT NULL DEFAULT (0),\r\n\t\"Rb\"\tTEXT NOT NULL,\r\n\t\"DeleteFromServer\"\tINTEGER NOT NULL DEFAULT 0,\r\n\tPRIMARY KEY(\"Id\" AUTOINCREMENT),\r\n\tCONSTRAINT \"UC_UmLimitation_Sn_Name_Rb\" UNIQUE(\"Sn_Name\",\"Rb\")\r\n);\r\nCREATE TABLE IF NOT EXISTS \"UmProfile\" (\r\n\t\"NameForUser\"\tTEXT,\r\n\t\"Id\"\tINTEGER NOT NULL UNIQUE,\r\n\t\"IdHX\"\tTEXT NOT NULL,\r\n\t\"Sn\"\tINTEGER NOT NULL,\r\n\t\"Sn_Name\"\tTEXT NOT NULL,\r\n\t\"Name\"\tTEXT NOT NULL DEFAULT (0),\r\n\t\"Validity\"\tINTEGER NOT NULL DEFAULT (0),\r\n\t\"Price\"\tDOUBLE NOT NULL DEFAULT (0),\r\n\t\"Price_Disply\"\tTEXT DEFAULT (0),\r\n\t\"Is_percentage\"\tINTEGER NOT NULL DEFAULT (0),\r\n\t\"Percentage\"\tDOUBLE NOT NULL DEFAULT (0),\r\n\t\"PercentageType\"\tINTEGER NOT NULL DEFAULT (0),\r\n\t\"SharedUsers\"\tTEXT,\r\n\t\"DeleteFromServer\"\tINTEGER NOT NULL DEFAULT 0,\r\n\t\"Rb\"\tTEXT NOT NULL,\r\n\tPRIMARY KEY(\"Id\" AUTOINCREMENT),\r\n\tCONSTRAINT \"UC_UmProfile_Sn_Name_Rb\" UNIQUE(\"Sn_Name\",\"Rb\")\r\n);\r\nCREATE TABLE IF NOT EXISTS \"UmProfile_Limtition\" (\r\n\t\"Id\"\tINTEGER UNIQUE,\r\n\t\"IdHX\"\tTEXT NOT NULL,\r\n\t\"Sn_Profile_Limitation\"\tTEXT NOT NULL,\r\n\t\"Profile\"\tTEXT,\r\n\t\"Limitation\"\tTEXT,\r\n\t\"From_time\"\tTEXT,\r\n\t\"Till_time\"\tTEXT,\r\n\t\"Weekdays\"\tTEXT,\r\n\t\"Rb\"\tTEXT,\r\n\t\"DeleteFromServer\"\tINTEGER NOT NULL DEFAULT (0),\r\n\t\"Sn\"\tINTEGER NOT NULL,\r\n\tPRIMARY KEY(\"Id\" AUTOINCREMENT),\r\n\tCONSTRAINT \"UC_UmProfile_Limtition_Sn_Profile_Limitation_Rb\" UNIQUE(\"Sn_Profile_Limitation\",\"Rb\")\r\n);\r\nCREATE TABLE IF NOT EXISTS \"UserManager_Customer\" (\r\n\t\"id\"\tINTEGER NOT NULL UNIQUE,\r\n\t\"idHX\"\tTEXT,\r\n\t\"name\"\tTEXT NOT NULL,\r\n\t\"rb\"\tTEXT NOT NULL,\r\n\t\"Delet_fromServer\"\tINTEGER DEFAULT 0,\r\n\tPRIMARY KEY(\"id\" AUTOINCREMENT),\r\n\tUNIQUE(\"name\",\"rb\")\r\n);\r\nCREATE TABLE IF NOT EXISTS \"UserManager_SourceProfile_UserManager\" (\r\n\t\"id\"\tINTEGER NOT NULL UNIQUE,\r\n\t\"idHX\"\tTEXT,\r\n\t\"Name\"\tTEXT NOT NULL,\r\n\t\"Validity\"\tINTEGER,\r\n\t\"Price\"\tINTEGER,\r\n\t\"NameForUser\"\tTEXT,\r\n\t\"SharedUsers\"\tTEXT,\r\n\t\"rb\"\tTEXT NOT NULL,\r\n\t\"Delet_fromServer\"\tINTEGER NOT NULL DEFAULT 0,\r\n\tUNIQUE(\"Name\",\"rb\"),\r\n\tPRIMARY KEY(\"id\" AUTOINCREMENT)\r\n);\r\nCREATE TABLE IF NOT EXISTS \"UserManager_Source_Profile_Limtition\" (\r\n\t\"id\"\tINTEGER NOT NULL UNIQUE,\r\n\t\"idHX\"\tTEXT,\r\n\t\"profile\"\tTEXT,\r\n\t\"limitation\"\tTEXT,\r\n\t\"from_time\"\tTEXT DEFAULT '00:00:00',\r\n\t\"till_time\"\tTEXT DEFAULT '23:29:29',\r\n\t\"weekdays\"\tTEXT,\r\n\t\"rb\"\tTEXT,\r\n\t\"Delet_fromServer\"\tINTEGER NOT NULL DEFAULT 0,\r\n\tPRIMARY KEY(\"id\" AUTOINCREMENT),\r\n\tUNIQUE(\"profile\",\"limitation\",\"rb\")\r\n);\r\nCREATE TABLE IF NOT EXISTS \"UserManager_Source_limitation\" (\r\n\t\"id\"\tINTEGER NOT NULL UNIQUE,\r\n\t\"idHX\"\tTEXT,\r\n\t\"Name\"\tTEXT NOT NULL,\r\n\t\"downloadLimit\"\tINTEGER,\r\n\t\"uploadLimit\"\tINTEGER,\r\n\t\"transferLimit\"\tINTEGER,\r\n\t\"uptimeLimit\"\tINTEGER,\r\n\t\"rateLimit\"\tINTEGER,\r\n\t\"download_tx\"\tTEXT,\r\n\t\"upload_rx\"\tTEXT,\r\n\t\"groupName\"\tTEXT,\r\n\t\"downloadPrice\"\tINTEGER,\r\n\t\"uploadPrice\"\tINTEGER,\r\n\t\"uptimePrice\"\tINTEGER,\r\n\t\"rb\"\tTEXT NOT NULL,\r\n\t\"Delet_fromServer\"\tINTEGER NOT NULL DEFAULT 0,\r\n\tUNIQUE(\"Name\",\"rb\"),\r\n\tPRIMARY KEY(\"id\" AUTOINCREMENT)\r\n);\r\nCREATE TABLE IF NOT EXISTS \"routers\" (\r\n\t\"id\"\tINTEGER NOT NULL UNIQUE,\r\n\t\"soft_id\"\tTEXT NOT NULL,\r\n\t\"mk_sn\"\tTEXT NOT NULL,\r\n\t\"mk_code\"\tTEXT NOT NULL UNIQUE,\r\n\t\"comment\"\tTEXT,\r\n\t\"localDB_path\"\tTEXT DEFAULT 'db\\\\localDB1.db',\r\n\t\"fileName\"\tTEXT DEFAULT 'localDB1.db',\r\n\t\"Resources\"\tTEXT,\r\n\t\"default\"\tINTEGER DEFAULT 1,\r\n\t\"type\"\tTEXT DEFAULT 'SQLlite',\r\n\t\"connection_string\"\tTEXT DEFAULT 'Data Sourc=localDB1.db;',\r\n\t\"name\"\tTEXT DEFAULT 'SQLlite',\r\n\t\"username_db\"\tTEXT,\r\n\t\"password_db\"\tTEXT,\r\n\tPRIMARY KEY(\"id\" AUTOINCREMENT)\r\n);\r\nCREATE INDEX IF NOT EXISTS \"idx_batchcard_batchnumber\" ON \"BatchCard\" (\r\n\t\"BatchNumber\"\r\n);\r\nCREATE INDEX IF NOT EXISTS \"idx_batchcard_profilename\" ON \"BatchCard\" (\r\n\t\"ProfileName\"\r\n);\r\nCREATE INDEX IF NOT EXISTS \"idx_batchcard_server\" ON \"BatchCard\" (\r\n\t\"Server\"\r\n);\r\nCOMMIT;\r\n";
                string ScriptDB = "BEGIN TRANSACTION;\r\nCREATE TABLE IF NOT EXISTS \"Account\" (\r\n\t\"Id\"\tINTEGER NOT NULL UNIQUE,\r\n\t\"Code\"\tTEXT NOT NULL,\r\n\t\"Name\"\tTEXT NOT NULL,\r\n\t\"AccountType\"\tTEXT NOT NULL DEFAULT 1,\r\n\t\"Str_AccountType\"\tTEXT,\r\n\tPRIMARY KEY(\"Id\" AUTOINCREMENT)\r\n);\r\nCREATE TABLE IF NOT EXISTS \"AccountMove\" (\r\n\t\"Id\"\tINTEGER NOT NULL UNIQUE,\r\n\t\"Parent\"\tINTEGER,\r\n\t\"ParentId\"\tINTEGER,\r\n\t\"Sequence\"\tINTEGER,\r\n\t\"Date\"\tTEXT,\r\n\t\"Ref\"\tTEXT,\r\n\t\"debit\"\tINTEGER,\r\n\t\"credit\"\tINTEGER,\r\n\t\"ProductId\"\tINTEGER,\r\n\t\"Product_uomId\"\tINTEGER,\r\n\t\"Quantity\"\tINTEGER,\r\n\t\"Price_unit\"\tINTEGER,\r\n\t\"Price_total\"\tINTEGER,\r\n\t\"Discount\"\tINTEGER,\r\n\t\"PartnerId\"\tINTEGER,\r\n\t\"AccountId\"\tINTEGER,\r\n\tPRIMARY KEY(\"Id\" AUTOINCREMENT)\r\n);\r\nCREATE TABLE IF NOT EXISTS \"Company\" (\r\n\t\"Id\"\tINTEGER NOT NULL UNIQUE,\r\n\t\"Code\"\tTEXT,\r\n\t\"Name\"\tTEXT,\r\n\t\"Name_eng\"\tTEXT,\r\n\t\"Display_name\"\tTEXT,\r\n\t\"Active\"\tINTEGER DEFAULT 1,\r\n\t\"Description\"\tTEXT,\r\n\t\"Image\"\tBLOB,\r\n\t\"Address\"\tTEXT,\r\n\t\"Phone\"\tTEXT,\r\n\t\"Email\"\tTEXT,\r\n\t\"Currency_name\"\tTEXT,\r\n\t\"Currency_symbol\"\tTEXT,\r\n\t\"Currency_rounding\"\tTEXT,\r\n\t\"Currency_decimal_places\"\tTEXT,\r\n\t\"Created\"\tTEXT,\r\n\t\"Updated\"\tTEXT,\r\n\t\"Partner_type\"\tINTEGER,\r\n\t\"Str_Partner_type\"\tTEXT,\r\n\t\"Rb\"\tTEXT,\r\n\tPRIMARY KEY(\"Id\" AUTOINCREMENT)\r\n);\r\nCREATE TABLE IF NOT EXISTS \"Partner\" (\r\n\t\"Id\"\tINTEGER NOT NULL UNIQUE,\r\n\t\"Code\"\tTEXT,\r\n\t\"Name\"\tTEXT,\r\n\t\"Display_name\"\tTEXT,\r\n\t\"Active\"\tINTEGER,\r\n\t\"Description\"\tTEXT,\r\n\t\"Image\"\tBLOB,\r\n\t\"Address\"\tTEXT,\r\n\t\"Phone\"\tTEXT,\r\n\t\"Email\"\tTEXT,\r\n\t\"Currency_name\"\tTEXT,\r\n\t\"Currency_symbol\"\tTEXT,\r\n\t\"Currency_rounding\"\tTEXT,\r\n\t\"Currency_decimal_places\"\tTEXT,\r\n\t\"Created\"\tTEXT,\r\n\t\"Updated\"\tTEXT,\r\n\t\"Partner_type\"\tINTEGER,\r\n\t\"Str_Partner_type\"\tTEXT,\r\n\t\"Rb\"\tTEXT,\r\n\tPRIMARY KEY(\"Id\" AUTOINCREMENT)\r\n);\r\nCREATE TABLE IF NOT EXISTS \"Product\" (\r\n\t\"Id\"\tINTEGER NOT NULL UNIQUE,\r\n\t\"Code\"\tTEXT,\r\n\t\"Name\"\tTEXT,\r\n\t\"Name_eng\"\tTEXT,\r\n\t\"Description\"\tTEXT,\r\n\t\"Image\"\tBLOB,\r\n\t\"Price\"\tDOUBLE DEFAULT 0,\r\n\t\"Product_uomId\"\tINTEGER,\r\n\t\"Account_IncomeId\"\tINTEGER,\r\n\t\"Account_ExpenseId\"\tINTEGER,\r\n\t\"Active\"\tINTEGER DEFAULT 1,\r\n\t\"Available_In_Pos\"\tINTEGER,\r\n\t\"Rb\"\tTEXT,\r\n\tPRIMARY KEY(\"Id\" AUTOINCREMENT)\r\n);\r\nCREATE TABLE IF NOT EXISTS \"ProductUoM\" (\r\n\t\"Id\"\tINTEGER NOT NULL UNIQUE,\r\n\t\"Name\"\tTEXT,\r\n\t\"Rb\"\tTEXT,\r\n\tPRIMARY KEY(\"Id\" AUTOINCREMENT)\r\n);\r\nCOMMIT;\r\n";
                using (var con = new SQLiteConnection(utils.Get_SmartDB_ConnectionString()))
                {
                    try
                    {
                        con.Open();
                        //var sqLiteTransaction = con.BeginTransaction();
                        int effectRows = con.Execute(ScriptDB);
                        //CreateDefultTemplate();
                        //sqLiteTransaction.Commit();
                    }
                    catch (Exception ex) { MessageBox.Show("Check_SmartDB_File\n" + ex.Message); }
                }
            }
        }
        public void Check_SmartDB_If_Not_EXISTS()
        {
            try
            {
                //string folderDb = $"{utils.Get_Database_Directory()}";
                //string Smartfile = $"{folderDb}\\Smart.db";

                    //string ScriptDB = "BEGIN TRANSACTION;\r\nCREATE TABLE IF NOT EXISTS \"Alert_SellingPoint\" (\r\n\t\"Id\"\tINTEGER NOT NULL,\r\n\t\"SpCode\"\tTEXT,\r\n\t\"ProfileName\"\tTEXT,\r\n\t\"Is_Alert\"\tINTEGER DEFAULT 0,\r\n\t\"Count_Soon\"\tINTEGER DEFAULT 75,\r\n\t\"Count_Finsh\"\tINTEGER DEFAULT 95,\r\n\t\"Rb\"\tTEXT,\r\n\t\"Rb_Sn\"\tTEXT,\r\n\tPRIMARY KEY(\"Id\" AUTOINCREMENT),\r\n\tCONSTRAINT \"UC_Alert_SellingPoint_SpCode_Rb_ProfileName\" UNIQUE(\"SpCode\",\"Rb\",\"ProfileName\")\r\n);\r\nCREATE TABLE IF NOT EXISTS \"BatchCard\" (\r\n\t\"Id\"\tINTEGER NOT NULL UNIQUE,\r\n\t\"BatchNumber\"\tINTEGER NOT NULL,\r\n\t\"AddedDate\"\tTEXT,\r\n\t\"Count\"\tINTEGER NOT NULL,\r\n\t\"Sn_from\"\tINTEGER NOT NULL,\r\n\t\"Sn_to\"\tINTEGER NOT NULL,\r\n\t\"ProfileName\"\tTEXT,\r\n\t\"SpCode\"\tTEXT,\r\n\t\"SpName\"\tTEXT,\r\n\t\"Server\"\tINTEGER NOT NULL DEFAULT (0),\r\n\t\"Count_waiting\"\tINTEGER NOT NULL DEFAULT (0),\r\n\t\"Count_active\"\tINTEGER NOT NULL DEFAULT (0),\r\n\t\"Count_finshed\"\tINTEGER NOT NULL DEFAULT (0),\r\n\t\"Count_deleteServer\"\tINTEGER NOT NULL DEFAULT (0),\r\n\t\"Rb\"\tTEXT NOT NULL,\r\n\t\"BatchType\"\tINTEGER DEFAULT 0,\r\n\t\"Count_FoundServer\"\tINTEGER DEFAULT 0,\r\n\t\"Rb_Sn\"\tTEXT,\r\n\tUNIQUE(\"BatchNumber\",\"Rb\"),\r\n\tPRIMARY KEY(\"Id\" AUTOINCREMENT)\r\n);\r\nCREATE TABLE IF NOT EXISTS \"CardsTemplate\" (\r\n\t\"id\"\tINTEGER NOT NULL,\r\n\t\"name\"\tTEXT,\r\n\t\"type\"\tTEXT,\r\n\t\"values\"\tTEXT,\r\n\t\"rb\"\tTEXT,\r\n\t\"rb_Sn\"\tTEXT,\r\n\tPRIMARY KEY(\"id\" AUTOINCREMENT),\r\n\tUNIQUE(\"name\",\"rb\")\r\n);\r\nCREATE TABLE IF NOT EXISTS \"Comm_SellingPoint\" (\r\n\t\"Id\"\tINTEGER NOT NULL,\r\n\t\"SpCode\"\tTEXT,\r\n\t\"ProfileName\"\tTEXT,\r\n\t\"Is_percentage\"\tINTEGER DEFAULT 0,\r\n\t\"Percentage\"\tDOUBLE DEFAULT 0,\r\n\t\"PercentageType\"\tINTEGER DEFAULT 0,\r\n\t\"Rb\"\tTEXT,\r\n\t\"Rb_Sn\"\tTEXT,\r\n\tPRIMARY KEY(\"Id\" AUTOINCREMENT),\r\n\tUNIQUE(\"SpCode\",\"ProfileName\",\"Rb\")\r\n);\r\nCREATE TABLE IF NOT EXISTS \"Connections_Db\" (\r\n\t\"Id\"\tINTEGER NOT NULL UNIQUE,\r\n\t\"Soft_id\"\tTEXT NOT NULL,\r\n\t\"Mk_sn\"\tTEXT NOT NULL,\r\n\t\"Mk_code\"\tTEXT NOT NULL,\r\n\t\"Comment\"\tTEXT,\r\n\t\"LocalDB_path\"\tTEXT DEFAULT 'db\\\\localDB1.db',\r\n\t\"FileName\"\tTEXT DEFAULT 'localDB1.db' UNIQUE,\r\n\t\"Resources\"\tTEXT,\r\n\t\"Default\"\tINTEGER DEFAULT 1,\r\n\t\"Type\"\tTEXT DEFAULT 'SQLlite',\r\n\t\"Connection_string\"\tTEXT DEFAULT 'Data Sourc=localDB1.db;',\r\n\t\"Name\"\tTEXT DEFAULT 'SQLlite',\r\n\t\"Username_db\"\tTEXT,\r\n\t\"Password_db\"\tTEXT,\r\n\tPRIMARY KEY(\"Id\" AUTOINCREMENT),\r\n\tUNIQUE(\"Mk_code\",\"FileName\")\r\n);\r\nCREATE TABLE IF NOT EXISTS \"HSLocalProfile\" (\r\n\t\"Id\"\tINTEGER UNIQUE,\r\n\t\"Name\"\tTEXT,\r\n\t\"UptimeLimit\"\tINTEGER NOT NULL DEFAULT (0),\r\n\t\"Validity\"\tINTEGER NOT NULL DEFAULT (0),\r\n\t\"TransferLimit\"\tINTEGER NOT NULL DEFAULT (0),\r\n\t\"Price\"\tDOUBLE NOT NULL DEFAULT (0),\r\n\t\"Is_percentage\"\tINTEGER NOT NULL DEFAULT (0),\r\n\t\"Percentage\"\tDOUBLE NOT NULL DEFAULT (0),\r\n\t\"PercentageType\"\tINTEGER NOT NULL DEFAULT (0),\r\n\t\"Price_Display\"\tTEXT,\r\n\t\"Add_Smart_Scripts\"\tINTEGER NOT NULL DEFAULT (0),\r\n\t\"Save_time\"\tINTEGER NOT NULL DEFAULT (0),\r\n\t\"Save_download\"\tINTEGER NOT NULL DEFAULT (0),\r\n\t\"Save_session\"\tINTEGER NOT NULL DEFAULT (0),\r\n\t\"ByDayOrHour\"\tINTEGER NOT NULL DEFAULT (0),\r\n\t\"Link_hotspot_profile\"\tTEXT,\r\n\t\"Rb\"\tTEXT,\r\n\tPRIMARY KEY(\"Id\" AUTOINCREMENT),\r\n\tCONSTRAINT \"UC_HSLocalProfile_Name_Rb\" UNIQUE(\"Name\",\"Rb\")\r\n);\r\nCREATE TABLE IF NOT EXISTS \"Hotspot_Profile_Hotspot_local\" (\r\n\t\"id\"\tINTEGER NOT NULL UNIQUE,\r\n\t\"Name\"\tTEXT NOT NULL,\r\n\t\"Price_Sales\"\tINTEGER DEFAULT 0,\r\n\t\"Price_display\"\tINTEGER DEFAULT 0,\r\n\t\"Validity\"\tINTEGER DEFAULT 0,\r\n\t\"uptimeLimit\"\tINTEGER DEFAULT 0,\r\n\t\"transferLimit\"\tINTEGER DEFAULT 0,\r\n\t\"rb\"\tTEXT NOT NULL,\r\n\t\"link_hotspot_profile\"\tTEXT,\r\n\t\"add_Smart_Scripts\"\tINTEGER DEFAULT 1,\r\n\t\"Save_time\"\tINTEGER DEFAULT 1,\r\n\t\"Save_download\"\tINTEGER DEFAULT 1,\r\n\t\"Save_session\"\tINTEGER DEFAULT 1,\r\n\t\"ByDayOrHour\"\tINTEGER DEFAULT 0,\r\n\t\"Is_percentage\"\tINTEGER DEFAULT 0,\r\n\t\"Percentage\"\tfloat DEFAULT 0,\r\n\t\"PercentageType\"\tINTEGER DEFAULT 0,\r\n\tUNIQUE(\"Name\",\"rb\"),\r\n\tPRIMARY KEY(\"id\" AUTOINCREMENT)\r\n);\r\nCREATE TABLE IF NOT EXISTS \"Hotspot_Source_Profile\" (\r\n\t\"id\"\tINTEGER NOT NULL UNIQUE,\r\n\t\"Name\"\tTEXT NOT NULL,\r\n\t\"rb\"\tTEXT NOT NULL,\r\n\t\"Delet_fromServer\"\tINTEGER NOT NULL DEFAULT 0,\r\n\t\"idHX\"\tTEXT NOT NULL,\r\n\tUNIQUE(\"Name\",\"rb\"),\r\n\tPRIMARY KEY(\"id\" AUTOINCREMENT)\r\n);\r\nCREATE TABLE IF NOT EXISTS \"My_Sequence\" (\r\n\t\"Id\"\tINTEGER UNIQUE,\r\n\t\"Name\"\tTEXT,\r\n\t\"Seq\"\tINTEGER NOT NULL DEFAULT (0),\r\n\t\"Rb\"\tTEXT,\r\n\tPRIMARY KEY(\"Id\" AUTOINCREMENT),\r\n\tCONSTRAINT \"UC_My_Sequence_Name_Rb\" UNIQUE(\"Name\",\"Rb\")\r\n);\r\nCREATE TABLE IF NOT EXISTS \"NumberPrintCard\" (\r\n\t\"NumberPrint\"\tINTEGER,\r\n\t\"Id\"\tINTEGER NOT NULL,\r\n\t\"BatchNumber\"\tINTEGER NOT NULL,\r\n\t\"AddedDate\"\tTEXT,\r\n\t\"Count\"\tINTEGER,\r\n\t\"Sn_from\"\tINTEGER,\r\n\t\"Sn_to\"\tINTEGER,\r\n\t\"ProfileName\"\tTEXT,\r\n\t\"SpCode\"\tTEXT,\r\n\t\"SpName\"\tTEXT,\r\n\t\"Server\"\tINTEGER DEFAULT (0),\r\n\t\"Count_waiting\"\tINTEGER DEFAULT (0),\r\n\t\"Count_active\"\tINTEGER DEFAULT (0),\r\n\t\"Count_finshed\"\tINTEGER DEFAULT (0),\r\n\t\"Count_deleteServer\"\tINTEGER DEFAULT (0),\r\n\t\"Count_FoundServer\"\tINTEGER DEFAULT (0),\r\n\t\"Rb\"\tTEXT,\r\n\t\"BatchType\"\tINTEGER DEFAULT (0),\r\n\t\"RB_Sn\"\tTEXT,\r\n\tPRIMARY KEY(\"Id\" AUTOINCREMENT)\r\n);\r\nCREATE TABLE IF NOT EXISTS \"SellingPoint\" (\r\n\t\"Id\"\tINTEGER NOT NULL UNIQUE,\r\n\t\"Code\"\tTEXT NOT NULL,\r\n\t\"UserName\"\tTEXT NOT NULL,\r\n\t\"Prefixes\"\tTEXT,\r\n\t\"Suffixes\"\tTEXT,\r\n\t\"Address\"\tTEXT,\r\n\t\"Is_percentage\"\tINTEGER NOT NULL DEFAULT (0),\r\n\t\"Percentage\"\tDOUBLE NOT NULL DEFAULT (0),\r\n\t\"PercentageType\"\tINTEGER NOT NULL DEFAULT (0),\r\n\t\"UseAccounting\"\tINTEGER DEFAULT 0,\r\n\t\"Phone\"\tTEXT,\r\n\t\"Rb\"\tTEXT,\r\n\t\"Rb_Sn\"\tTEXT,\r\n\t\"Is_percentage_Custom\"\tINTEGER DEFAULT 0,\r\n\t\"Is_Alert\"\tINTEGER DEFAULT 0,\r\n\t\"Count_Soon\"\tINTEGER DEFAULT 70,\r\n\t\"Count_Finsh\"\tINTEGER DEFAULT 95,\r\n\t\"Is_Alert_Custom\"\tINTEGER DEFAULT 0,\r\n\tPRIMARY KEY(\"Id\" AUTOINCREMENT)\r\n);\r\nCREATE TABLE IF NOT EXISTS \"Setting_Mk_Login_Saved\" (\r\n\t\"id\"\tINTEGER NOT NULL UNIQUE,\r\n\t\"values\"\tTEXT NOT NULL,\r\n\t\"active\"\tINTEGER DEFAULT 0,\r\n\tPRIMARY KEY(\"id\" AUTOINCREMENT)\r\n);\r\nCREATE TABLE IF NOT EXISTS \"Setting_SaveState_Forms_Variables\" (\r\n\t\"id\"\tINTEGER NOT NULL,\r\n\t\"name\"\tTEXT,\r\n\t\"type\"\tTEXT,\r\n\t\"values\"\tTEXT,\r\n\t\"rb\"\tTEXT,\r\n\tPRIMARY KEY(\"id\" AUTOINCREMENT),\r\n\tUNIQUE(\"name\",\"rb\")\r\n);\r\nCREATE TABLE IF NOT EXISTS \"UmLimitation\" (\r\n\t\"Id\"\tINTEGER UNIQUE,\r\n\t\"IdHX\"\tTEXT NOT NULL,\r\n\t\"Sn\"\tINTEGER NOT NULL,\r\n\t\"Name\"\tTEXT NOT NULL,\r\n\t\"Sn_Name\"\tTEXT NOT NULL,\r\n\t\"DownloadLimit\"\tINTEGER NOT NULL DEFAULT (0),\r\n\t\"UploadLimit\"\tTEXT NOT NULL DEFAULT (0),\r\n\t\"TransferLimit\"\tINTEGER NOT NULL DEFAULT (0),\r\n\t\"UptimeLimit\"\tINTEGER NOT NULL DEFAULT (0),\r\n\t\"RateLimit\"\tTEXT,\r\n\t\"Download_tx\"\tTEXT,\r\n\t\"Upload_rx\"\tTEXT,\r\n\t\"GroupName\"\tTEXT,\r\n\t\"DownloadPrice\"\tINTEGER NOT NULL DEFAULT (0),\r\n\t\"UploadPrice\"\tINTEGER NOT NULL DEFAULT (0),\r\n\t\"UptimePrice\"\tINTEGER NOT NULL DEFAULT (0),\r\n\t\"Rb\"\tTEXT NOT NULL,\r\n\t\"DeleteFromServer\"\tINTEGER NOT NULL DEFAULT 0,\r\n\tPRIMARY KEY(\"Id\" AUTOINCREMENT),\r\n\tCONSTRAINT \"UC_UmLimitation_Sn_Name_Rb\" UNIQUE(\"Sn_Name\",\"Rb\")\r\n);\r\nCREATE TABLE IF NOT EXISTS \"UmProfile\" (\r\n\t\"NameForUser\"\tTEXT,\r\n\t\"Id\"\tINTEGER NOT NULL UNIQUE,\r\n\t\"IdHX\"\tTEXT NOT NULL,\r\n\t\"Sn\"\tINTEGER NOT NULL,\r\n\t\"Sn_Name\"\tTEXT NOT NULL,\r\n\t\"Name\"\tTEXT NOT NULL DEFAULT (0),\r\n\t\"Validity\"\tINTEGER NOT NULL DEFAULT (0),\r\n\t\"Price\"\tDOUBLE NOT NULL DEFAULT (0),\r\n\t\"Price_Disply\"\tTEXT DEFAULT (0),\r\n\t\"Is_percentage\"\tINTEGER NOT NULL DEFAULT (0),\r\n\t\"Percentage\"\tDOUBLE NOT NULL DEFAULT (0),\r\n\t\"PercentageType\"\tINTEGER NOT NULL DEFAULT (0),\r\n\t\"SharedUsers\"\tTEXT,\r\n\t\"DeleteFromServer\"\tINTEGER NOT NULL DEFAULT 0,\r\n\t\"Rb\"\tTEXT NOT NULL,\r\n\tPRIMARY KEY(\"Id\" AUTOINCREMENT),\r\n\tCONSTRAINT \"UC_UmProfile_Sn_Name_Rb\" UNIQUE(\"Sn_Name\",\"Rb\")\r\n);\r\nCREATE TABLE IF NOT EXISTS \"UmProfile_Limtition\" (\r\n\t\"Id\"\tINTEGER UNIQUE,\r\n\t\"IdHX\"\tTEXT NOT NULL,\r\n\t\"Sn_Profile_Limitation\"\tTEXT NOT NULL,\r\n\t\"Profile\"\tTEXT,\r\n\t\"Limitation\"\tTEXT,\r\n\t\"From_time\"\tTEXT,\r\n\t\"Till_time\"\tTEXT,\r\n\t\"Weekdays\"\tTEXT,\r\n\t\"Rb\"\tTEXT,\r\n\t\"DeleteFromServer\"\tINTEGER NOT NULL DEFAULT (0),\r\n\t\"Sn\"\tINTEGER NOT NULL,\r\n\tPRIMARY KEY(\"Id\" AUTOINCREMENT),\r\n\tCONSTRAINT \"UC_UmProfile_Limtition_Sn_Profile_Limitation_Rb\" UNIQUE(\"Sn_Profile_Limitation\",\"Rb\")\r\n);\r\nCREATE TABLE IF NOT EXISTS \"UserManager_Customer\" (\r\n\t\"id\"\tINTEGER NOT NULL UNIQUE,\r\n\t\"idHX\"\tTEXT,\r\n\t\"name\"\tTEXT NOT NULL,\r\n\t\"rb\"\tTEXT NOT NULL,\r\n\t\"Delet_fromServer\"\tINTEGER DEFAULT 0,\r\n\tPRIMARY KEY(\"id\" AUTOINCREMENT),\r\n\tUNIQUE(\"name\",\"rb\")\r\n);\r\nCREATE TABLE IF NOT EXISTS \"UserManager_SourceProfile_UserManager\" (\r\n\t\"id\"\tINTEGER NOT NULL UNIQUE,\r\n\t\"idHX\"\tTEXT,\r\n\t\"Name\"\tTEXT NOT NULL,\r\n\t\"Validity\"\tINTEGER,\r\n\t\"Price\"\tINTEGER,\r\n\t\"NameForUser\"\tTEXT,\r\n\t\"SharedUsers\"\tTEXT,\r\n\t\"rb\"\tTEXT NOT NULL,\r\n\t\"Delet_fromServer\"\tINTEGER NOT NULL DEFAULT 0,\r\n\tUNIQUE(\"Name\",\"rb\"),\r\n\tPRIMARY KEY(\"id\" AUTOINCREMENT)\r\n);\r\nCREATE TABLE IF NOT EXISTS \"UserManager_Source_Profile_Limtition\" (\r\n\t\"id\"\tINTEGER NOT NULL UNIQUE,\r\n\t\"idHX\"\tTEXT,\r\n\t\"profile\"\tTEXT,\r\n\t\"limitation\"\tTEXT,\r\n\t\"from_time\"\tTEXT DEFAULT '00:00:00',\r\n\t\"till_time\"\tTEXT DEFAULT '23:29:29',\r\n\t\"weekdays\"\tTEXT,\r\n\t\"rb\"\tTEXT,\r\n\t\"Delet_fromServer\"\tINTEGER NOT NULL DEFAULT 0,\r\n\tPRIMARY KEY(\"id\" AUTOINCREMENT),\r\n\tUNIQUE(\"profile\",\"limitation\",\"rb\")\r\n);\r\nCREATE TABLE IF NOT EXISTS \"UserManager_Source_limitation\" (\r\n\t\"id\"\tINTEGER NOT NULL UNIQUE,\r\n\t\"idHX\"\tTEXT,\r\n\t\"Name\"\tTEXT NOT NULL,\r\n\t\"downloadLimit\"\tINTEGER,\r\n\t\"uploadLimit\"\tINTEGER,\r\n\t\"transferLimit\"\tINTEGER,\r\n\t\"uptimeLimit\"\tINTEGER,\r\n\t\"rateLimit\"\tINTEGER,\r\n\t\"download_tx\"\tTEXT,\r\n\t\"upload_rx\"\tTEXT,\r\n\t\"groupName\"\tTEXT,\r\n\t\"downloadPrice\"\tINTEGER,\r\n\t\"uploadPrice\"\tINTEGER,\r\n\t\"uptimePrice\"\tINTEGER,\r\n\t\"rb\"\tTEXT NOT NULL,\r\n\t\"Delet_fromServer\"\tINTEGER NOT NULL DEFAULT 0,\r\n\tUNIQUE(\"Name\",\"rb\"),\r\n\tPRIMARY KEY(\"id\" AUTOINCREMENT)\r\n);\r\nCREATE TABLE IF NOT EXISTS \"routers\" (\r\n\t\"id\"\tINTEGER NOT NULL UNIQUE,\r\n\t\"soft_id\"\tTEXT NOT NULL,\r\n\t\"mk_sn\"\tTEXT NOT NULL,\r\n\t\"mk_code\"\tTEXT NOT NULL UNIQUE,\r\n\t\"comment\"\tTEXT,\r\n\t\"localDB_path\"\tTEXT DEFAULT 'db\\\\localDB1.db',\r\n\t\"fileName\"\tTEXT DEFAULT 'localDB1.db',\r\n\t\"Resources\"\tTEXT,\r\n\t\"default\"\tINTEGER DEFAULT 1,\r\n\t\"type\"\tTEXT DEFAULT 'SQLlite',\r\n\t\"connection_string\"\tTEXT DEFAULT 'Data Sourc=localDB1.db;',\r\n\t\"name\"\tTEXT DEFAULT 'SQLlite',\r\n\t\"username_db\"\tTEXT,\r\n\t\"password_db\"\tTEXT,\r\n\tPRIMARY KEY(\"id\" AUTOINCREMENT)\r\n);\r\nCREATE INDEX IF NOT EXISTS \"idx_batchcard_batchnumber\" ON \"BatchCard\" (\r\n\t\"BatchNumber\"\r\n);\r\nCREATE INDEX IF NOT EXISTS \"idx_batchcard_profilename\" ON \"BatchCard\" (\r\n\t\"ProfileName\"\r\n);\r\nCREATE INDEX IF NOT EXISTS \"idx_batchcard_server\" ON \"BatchCard\" (\r\n\t\"Server\"\r\n);\r\nCOMMIT;\r\n";
                    string ScriptDB = "BEGIN TRANSACTION;\r\nCREATE TABLE IF NOT EXISTS \"Alert_SellingPoint\" (\r\n\t\"Id\"\tINTEGER NOT NULL,\r\n\t\"SpCode\"\tTEXT,\r\n\t\"ProfileName\"\tTEXT,\r\n\t\"Is_Alert\"\tINTEGER DEFAULT 0,\r\n\t\"Count_Soon\"\tINTEGER DEFAULT 75,\r\n\t\"Count_Finsh\"\tINTEGER DEFAULT 95,\r\n\t\"Rb\"\tTEXT,\r\n\t\"Rb_Sn\"\tTEXT,\r\n\tPRIMARY KEY(\"Id\" AUTOINCREMENT),\r\n\tCONSTRAINT \"UC_Alert_SellingPoint_SpCode_Rb_ProfileName\" UNIQUE(\"SpCode\",\"Rb\",\"ProfileName\")\r\n);\r\nCREATE TABLE IF NOT EXISTS \"BatchCard\" (\r\n\t\"Id\"\tINTEGER NOT NULL UNIQUE,\r\n\t\"BatchNumber\"\tINTEGER NOT NULL,\r\n\t\"AddedDate\"\tTEXT,\r\n\t\"Count\"\tINTEGER NOT NULL,\r\n\t\"Sn_from\"\tINTEGER NOT NULL,\r\n\t\"Sn_to\"\tINTEGER NOT NULL,\r\n\t\"ProfileName\"\tTEXT,\r\n\t\"SpCode\"\tTEXT,\r\n\t\"SpName\"\tTEXT,\r\n\t\"Server\"\tINTEGER NOT NULL DEFAULT (0),\r\n\t\"Count_waiting\"\tINTEGER NOT NULL DEFAULT (0),\r\n\t\"Count_active\"\tINTEGER NOT NULL DEFAULT (0),\r\n\t\"Count_finshed\"\tINTEGER NOT NULL DEFAULT (0),\r\n\t\"Count_deleteServer\"\tINTEGER NOT NULL DEFAULT (0),\r\n\t\"Rb\"\tTEXT NOT NULL,\r\n\t\"BatchType\"\tINTEGER DEFAULT 0,\r\n\t\"Count_FoundServer\"\tINTEGER DEFAULT 0,\r\n\t\"Rb_Sn\"\tTEXT,\r\n\tUNIQUE(\"BatchNumber\",\"Rb\"),\r\n\tPRIMARY KEY(\"Id\" AUTOINCREMENT)\r\n);\r\nCREATE TABLE IF NOT EXISTS \"CardsTemplate\" (\r\n\t\"id\"\tINTEGER NOT NULL,\r\n\t\"name\"\tTEXT,\r\n\t\"type\"\tTEXT,\r\n\t\"values\"\tTEXT,\r\n\t\"rb\"\tTEXT,\r\n\t\"rb_Sn\"\tTEXT,\r\n\tPRIMARY KEY(\"id\" AUTOINCREMENT),\r\n\tUNIQUE(\"name\",\"rb\")\r\n);\r\nCREATE TABLE IF NOT EXISTS \"Comm_SellingPoint\" (\r\n\t\"Id\"\tINTEGER NOT NULL,\r\n\t\"SpCode\"\tTEXT,\r\n\t\"ProfileName\"\tTEXT,\r\n\t\"Is_percentage\"\tINTEGER DEFAULT 0,\r\n\t\"Percentage\"\tDOUBLE DEFAULT 0,\r\n\t\"PercentageType\"\tINTEGER DEFAULT 0,\r\n\t\"Rb\"\tTEXT,\r\n\t\"Rb_Sn\"\tTEXT,\r\n\tPRIMARY KEY(\"Id\" AUTOINCREMENT),\r\n\tUNIQUE(\"SpCode\",\"ProfileName\",\"Rb\")\r\n);\r\nCREATE TABLE IF NOT EXISTS \"Connections_Db\" (\r\n\t\"Id\"\tINTEGER NOT NULL UNIQUE,\r\n\t\"Soft_id\"\tTEXT NOT NULL,\r\n\t\"Mk_sn\"\tTEXT NOT NULL,\r\n\t\"Mk_code\"\tTEXT NOT NULL,\r\n\t\"Comment\"\tTEXT,\r\n\t\"LocalDB_path\"\tTEXT DEFAULT 'db\\\\localDB1.db',\r\n\t\"FileName\"\tTEXT DEFAULT 'localDB1.db' UNIQUE,\r\n\t\"Resources\"\tTEXT,\r\n\t\"Default\"\tINTEGER DEFAULT 1,\r\n\t\"Type\"\tTEXT DEFAULT 'SQLlite',\r\n\t\"Connection_string\"\tTEXT DEFAULT 'Data Sourc=localDB1.db;',\r\n\t\"Name\"\tTEXT DEFAULT 'SQLlite',\r\n\t\"Username_db\"\tTEXT,\r\n\t\"Password_db\"\tTEXT,\r\n\tPRIMARY KEY(\"Id\" AUTOINCREMENT),\r\n\tUNIQUE(\"Mk_code\",\"FileName\")\r\n);\r\nCREATE TABLE IF NOT EXISTS \"HSLocalProfile\" (\r\n\t\"Id\"\tINTEGER UNIQUE,\r\n\t\"Name\"\tTEXT,\r\n\t\"UptimeLimit\"\tINTEGER NOT NULL DEFAULT (0),\r\n\t\"Validity\"\tINTEGER NOT NULL DEFAULT (0),\r\n\t\"TransferLimit\"\tINTEGER NOT NULL DEFAULT (0),\r\n\t\"Price\"\tDOUBLE NOT NULL DEFAULT (0),\r\n\t\"Is_percentage\"\tINTEGER NOT NULL DEFAULT (0),\r\n\t\"Percentage\"\tDOUBLE NOT NULL DEFAULT (0),\r\n\t\"PercentageType\"\tINTEGER NOT NULL DEFAULT (0),\r\n\t\"Price_Display\"\tTEXT,\r\n\t\"Add_Smart_Scripts\"\tINTEGER NOT NULL DEFAULT (0),\r\n\t\"Save_time\"\tINTEGER NOT NULL DEFAULT (0),\r\n\t\"Save_download\"\tINTEGER NOT NULL DEFAULT (0),\r\n\t\"Save_session\"\tINTEGER NOT NULL DEFAULT (0),\r\n\t\"ByDayOrHour\"\tINTEGER NOT NULL DEFAULT (0),\r\n\t\"Link_hotspot_profile\"\tTEXT,\r\n\t\"Rb\"\tTEXT,\r\n\tPRIMARY KEY(\"Id\" AUTOINCREMENT),\r\n\tCONSTRAINT \"UC_HSLocalProfile_Name_Rb\" UNIQUE(\"Name\",\"Rb\")\r\n);\r\nCREATE TABLE IF NOT EXISTS \"Hotspot_Profile_Hotspot_local\" (\r\n\t\"id\"\tINTEGER NOT NULL UNIQUE,\r\n\t\"Name\"\tTEXT NOT NULL,\r\n\t\"Price_Sales\"\tINTEGER DEFAULT 0,\r\n\t\"Price_display\"\tINTEGER DEFAULT 0,\r\n\t\"Validity\"\tINTEGER DEFAULT 0,\r\n\t\"uptimeLimit\"\tINTEGER DEFAULT 0,\r\n\t\"transferLimit\"\tINTEGER DEFAULT 0,\r\n\t\"rb\"\tTEXT NOT NULL,\r\n\t\"link_hotspot_profile\"\tTEXT,\r\n\t\"add_Smart_Scripts\"\tINTEGER DEFAULT 1,\r\n\t\"Save_time\"\tINTEGER DEFAULT 1,\r\n\t\"Save_download\"\tINTEGER DEFAULT 1,\r\n\t\"Save_session\"\tINTEGER DEFAULT 1,\r\n\t\"ByDayOrHour\"\tINTEGER DEFAULT 0,\r\n\t\"Is_percentage\"\tINTEGER DEFAULT 0,\r\n\t\"Percentage\"\tfloat DEFAULT 0,\r\n\t\"PercentageType\"\tINTEGER DEFAULT 0,\r\n\tUNIQUE(\"Name\",\"rb\"),\r\n\tPRIMARY KEY(\"id\" AUTOINCREMENT)\r\n);\r\nCREATE TABLE IF NOT EXISTS \"Hotspot_Source_Profile\" (\r\n\t\"id\"\tINTEGER NOT NULL UNIQUE,\r\n\t\"Name\"\tTEXT NOT NULL,\r\n\t\"rb\"\tTEXT NOT NULL,\r\n\t\"Delet_fromServer\"\tINTEGER NOT NULL DEFAULT 0,\r\n\t\"idHX\"\tTEXT NOT NULL,\r\n\tUNIQUE(\"Name\",\"rb\"),\r\n\tPRIMARY KEY(\"id\" AUTOINCREMENT)\r\n);\r\nCREATE TABLE IF NOT EXISTS \"My_Sequence\" (\r\n\t\"Id\"\tINTEGER UNIQUE,\r\n\t\"Name\"\tTEXT,\r\n\t\"Seq\"\tINTEGER NOT NULL DEFAULT (0),\r\n\t\"Rb\"\tTEXT,\r\n\tPRIMARY KEY(\"Id\" AUTOINCREMENT),\r\n\tCONSTRAINT \"UC_My_Sequence_Name_Rb\" UNIQUE(\"Name\",\"Rb\")\r\n);\r\nCREATE TABLE IF NOT EXISTS \"NumberPrintCard\" (\r\n\t\"NumberPrint\"\tINTEGER,\r\n\t\"Id\"\tINTEGER NOT NULL,\r\n\t\"BatchNumber\"\tINTEGER NOT NULL,\r\n\t\"AddedDate\"\tTEXT,\r\n\t\"Count\"\tINTEGER,\r\n\t\"Sn_from\"\tINTEGER,\r\n\t\"Sn_to\"\tINTEGER,\r\n\t\"ProfileName\"\tTEXT,\r\n\t\"SpCode\"\tTEXT,\r\n\t\"SpName\"\tTEXT,\r\n\t\"Server\"\tINTEGER DEFAULT (0),\r\n\t\"Count_waiting\"\tINTEGER DEFAULT (0),\r\n\t\"Count_active\"\tINTEGER DEFAULT (0),\r\n\t\"Count_finshed\"\tINTEGER DEFAULT (0),\r\n\t\"Count_deleteServer\"\tINTEGER DEFAULT (0),\r\n\t\"Count_FoundServer\"\tINTEGER DEFAULT (0),\r\n\t\"Rb\"\tTEXT,\r\n\t\"BatchType\"\tINTEGER DEFAULT (0),\r\n\t\"RB_Sn\"\tTEXT,\r\n\tPRIMARY KEY(\"Id\" AUTOINCREMENT)\r\n);\r\nCREATE TABLE IF NOT EXISTS \"SellingPoint\" (\r\n\t\"Id\"\tINTEGER NOT NULL UNIQUE,\r\n\t\"Code\"\tTEXT NOT NULL,\r\n\t\"UserName\"\tTEXT NOT NULL,\r\n\t\"Prefixes\"\tTEXT,\r\n\t\"Suffixes\"\tTEXT,\r\n\t\"Address\"\tTEXT,\r\n\t\"Is_percentage\"\tINTEGER NOT NULL DEFAULT (0),\r\n\t\"Percentage\"\tDOUBLE NOT NULL DEFAULT (0),\r\n\t\"PercentageType\"\tINTEGER NOT NULL DEFAULT (0),\r\n\t\"UseAccounting\"\tINTEGER DEFAULT 0,\r\n\t\"Phone\"\tTEXT,\r\n\t\"Rb\"\tTEXT,\r\n\t\"Rb_Sn\"\tTEXT,\r\n\t\"Is_percentage_Custom\"\tINTEGER DEFAULT 0,\r\n\t\"Is_Alert\"\tINTEGER DEFAULT 0,\r\n\t\"Count_Soon\"\tINTEGER DEFAULT 70,\r\n\t\"Count_Finsh\"\tINTEGER DEFAULT 95,\r\n\t\"Is_Alert_Custom\"\tINTEGER DEFAULT 0,\r\n\tPRIMARY KEY(\"Id\" AUTOINCREMENT)\r\n);\r\nCREATE TABLE IF NOT EXISTS \"Setting_Mk_Login_Saved\" (\r\n\t\"id\"\tINTEGER NOT NULL UNIQUE,\r\n\t\"values\"\tTEXT NOT NULL,\r\n\t\"active\"\tINTEGER DEFAULT 0,\r\n\tPRIMARY KEY(\"id\" AUTOINCREMENT)\r\n);\r\nCREATE TABLE IF NOT EXISTS \"Setting_SaveState_Forms_Variables\" (\r\n\t\"id\"\tINTEGER NOT NULL,\r\n\t\"name\"\tTEXT,\r\n\t\"type\"\tTEXT,\r\n\t\"values\"\tTEXT,\r\n\t\"rb\"\tTEXT,\r\n\tPRIMARY KEY(\"id\" AUTOINCREMENT),\r\n\tUNIQUE(\"name\",\"rb\")\r\n);\r\nCREATE TABLE IF NOT EXISTS \"UmLimitation\" (\r\n\t\"Id\"\tINTEGER UNIQUE,\r\n\t\"IdHX\"\tTEXT NOT NULL,\r\n\t\"Sn\"\tINTEGER NOT NULL,\r\n\t\"Name\"\tTEXT NOT NULL,\r\n\t\"Sn_Name\"\tTEXT NOT NULL,\r\n\t\"DownloadLimit\"\tINTEGER NOT NULL DEFAULT (0),\r\n\t\"UploadLimit\"\tTEXT NOT NULL DEFAULT (0),\r\n\t\"TransferLimit\"\tINTEGER NOT NULL DEFAULT (0),\r\n\t\"UptimeLimit\"\tINTEGER NOT NULL DEFAULT (0),\r\n\t\"RateLimit\"\tTEXT,\r\n\t\"Download_tx\"\tTEXT,\r\n\t\"Upload_rx\"\tTEXT,\r\n\t\"GroupName\"\tTEXT,\r\n\t\"DownloadPrice\"\tINTEGER NOT NULL DEFAULT (0),\r\n\t\"UploadPrice\"\tINTEGER NOT NULL DEFAULT (0),\r\n\t\"UptimePrice\"\tINTEGER NOT NULL DEFAULT (0),\r\n\t\"Rb\"\tTEXT NOT NULL,\r\n\t\"DeleteFromServer\"\tINTEGER NOT NULL DEFAULT 0,\r\n\tPRIMARY KEY(\"Id\" AUTOINCREMENT),\r\n\tCONSTRAINT \"UC_UmLimitation_Sn_Name_Rb\" UNIQUE(\"Sn_Name\",\"Rb\")\r\n);\r\nCREATE TABLE IF NOT EXISTS \"UmProfile\" (\r\n\t\"NameForUser\"\tTEXT,\r\n\t\"Id\"\tINTEGER NOT NULL UNIQUE,\r\n\t\"IdHX\"\tTEXT NOT NULL,\r\n\t\"Sn\"\tINTEGER NOT NULL,\r\n\t\"Sn_Name\"\tTEXT NOT NULL,\r\n\t\"Name\"\tTEXT NOT NULL DEFAULT (0),\r\n\t\"Validity\"\tINTEGER NOT NULL DEFAULT (0),\r\n\t\"Price\"\tDOUBLE NOT NULL DEFAULT (0),\r\n\t\"Price_Disply\"\tTEXT DEFAULT (0),\r\n\t\"Is_percentage\"\tINTEGER NOT NULL DEFAULT (0),\r\n\t\"Percentage\"\tDOUBLE NOT NULL DEFAULT (0),\r\n\t\"PercentageType\"\tINTEGER NOT NULL DEFAULT (0),\r\n\t\"SharedUsers\"\tTEXT,\r\n\t\"DeleteFromServer\"\tINTEGER NOT NULL DEFAULT 0,\r\n\t\"Rb\"\tTEXT NOT NULL,\r\n\tPRIMARY KEY(\"Id\" AUTOINCREMENT),\r\n\tCONSTRAINT \"UC_UmProfile_Sn_Name_Rb\" UNIQUE(\"Sn_Name\",\"Rb\")\r\n);\r\nCREATE TABLE IF NOT EXISTS \"UmProfile_Limtition\" (\r\n\t\"Id\"\tINTEGER UNIQUE,\r\n\t\"IdHX\"\tTEXT NOT NULL,\r\n\t\"Sn_Profile_Limitation\"\tTEXT NOT NULL,\r\n\t\"Profile\"\tTEXT,\r\n\t\"Limitation\"\tTEXT,\r\n\t\"From_time\"\tTEXT,\r\n\t\"Till_time\"\tTEXT,\r\n\t\"Weekdays\"\tTEXT,\r\n\t\"Rb\"\tTEXT,\r\n\t\"DeleteFromServer\"\tINTEGER NOT NULL DEFAULT (0),\r\n\t\"Sn\"\tINTEGER NOT NULL,\r\n\tPRIMARY KEY(\"Id\" AUTOINCREMENT),\r\n\tCONSTRAINT \"UC_UmProfile_Limtition_Sn_Profile_Limitation_Rb\" UNIQUE(\"Sn_Profile_Limitation\",\"Rb\")\r\n);\r\nCREATE TABLE IF NOT EXISTS \"UserManager_Customer\" (\r\n\t\"id\"\tINTEGER NOT NULL UNIQUE,\r\n\t\"idHX\"\tTEXT,\r\n\t\"name\"\tTEXT NOT NULL,\r\n\t\"rb\"\tTEXT NOT NULL,\r\n\t\"Delet_fromServer\"\tINTEGER DEFAULT 0,\r\n\tPRIMARY KEY(\"id\" AUTOINCREMENT),\r\n\tUNIQUE(\"name\",\"rb\")\r\n);\r\nCREATE TABLE IF NOT EXISTS \"UserManager_SourceProfile_UserManager\" (\r\n\t\"id\"\tINTEGER NOT NULL UNIQUE,\r\n\t\"idHX\"\tTEXT,\r\n\t\"Name\"\tTEXT NOT NULL,\r\n\t\"Validity\"\tINTEGER,\r\n\t\"Price\"\tINTEGER,\r\n\t\"NameForUser\"\tTEXT,\r\n\t\"SharedUsers\"\tTEXT,\r\n\t\"rb\"\tTEXT NOT NULL,\r\n\t\"Delet_fromServer\"\tINTEGER NOT NULL DEFAULT 0,\r\n\tUNIQUE(\"Name\",\"rb\"),\r\n\tPRIMARY KEY(\"id\" AUTOINCREMENT)\r\n);\r\nCREATE TABLE IF NOT EXISTS \"UserManager_Source_Profile_Limtition\" (\r\n\t\"id\"\tINTEGER NOT NULL UNIQUE,\r\n\t\"idHX\"\tTEXT,\r\n\t\"profile\"\tTEXT,\r\n\t\"limitation\"\tTEXT,\r\n\t\"from_time\"\tTEXT DEFAULT '00:00:00',\r\n\t\"till_time\"\tTEXT DEFAULT '23:29:29',\r\n\t\"weekdays\"\tTEXT,\r\n\t\"rb\"\tTEXT,\r\n\t\"Delet_fromServer\"\tINTEGER NOT NULL DEFAULT 0,\r\n\tPRIMARY KEY(\"id\" AUTOINCREMENT),\r\n\tUNIQUE(\"profile\",\"limitation\",\"rb\")\r\n);\r\nCREATE TABLE IF NOT EXISTS \"UserManager_Source_limitation\" (\r\n\t\"id\"\tINTEGER NOT NULL UNIQUE,\r\n\t\"idHX\"\tTEXT,\r\n\t\"Name\"\tTEXT NOT NULL,\r\n\t\"downloadLimit\"\tINTEGER,\r\n\t\"uploadLimit\"\tINTEGER,\r\n\t\"transferLimit\"\tINTEGER,\r\n\t\"uptimeLimit\"\tINTEGER,\r\n\t\"rateLimit\"\tINTEGER,\r\n\t\"download_tx\"\tTEXT,\r\n\t\"upload_rx\"\tTEXT,\r\n\t\"groupName\"\tTEXT,\r\n\t\"downloadPrice\"\tINTEGER,\r\n\t\"uploadPrice\"\tINTEGER,\r\n\t\"uptimePrice\"\tINTEGER,\r\n\t\"rb\"\tTEXT NOT NULL,\r\n\t\"Delet_fromServer\"\tINTEGER NOT NULL DEFAULT 0,\r\n\tUNIQUE(\"Name\",\"rb\"),\r\n\tPRIMARY KEY(\"id\" AUTOINCREMENT)\r\n);\r\nCREATE TABLE IF NOT EXISTS \"routers\" (\r\n\t\"id\"\tINTEGER NOT NULL UNIQUE,\r\n\t\"soft_id\"\tTEXT NOT NULL,\r\n\t\"mk_sn\"\tTEXT NOT NULL,\r\n\t\"mk_code\"\tTEXT NOT NULL UNIQUE,\r\n\t\"comment\"\tTEXT,\r\n\t\"localDB_path\"\tTEXT DEFAULT 'db\\\\localDB1.db',\r\n\t\"fileName\"\tTEXT DEFAULT 'localDB1.db',\r\n\t\"Resources\"\tTEXT,\r\n\t\"default\"\tINTEGER DEFAULT 1,\r\n\t\"type\"\tTEXT DEFAULT 'SQLlite',\r\n\t\"connection_string\"\tTEXT DEFAULT 'Data Sourc=localDB1.db;',\r\n\t\"name\"\tTEXT DEFAULT 'SQLlite',\r\n\t\"username_db\"\tTEXT,\r\n\t\"password_db\"\tTEXT,\r\n\tPRIMARY KEY(\"id\" AUTOINCREMENT)\r\n);\r\nCREATE INDEX IF NOT EXISTS \"idx_batchcard_batchnumber\" ON \"BatchCard\" (\r\n\t\"BatchNumber\"\r\n);\r\nCREATE INDEX IF NOT EXISTS \"idx_batchcard_profilename\" ON \"BatchCard\" (\r\n\t\"ProfileName\"\r\n);\r\nCREATE INDEX IF NOT EXISTS \"idx_batchcard_server\" ON \"BatchCard\" (\r\n\t\"Server\"\r\n);\r\nCREATE TABLE IF NOT EXISTS \"Account\" (\r\n\t\"Id\"\tINTEGER NOT NULL UNIQUE,\r\n\t\"Code\"\tTEXT NOT NULL,\r\n\t\"Name\"\tTEXT NOT NULL,\r\n\t\"AccountType\"\tTEXT NOT NULL DEFAULT 1,\r\n\t\"Rb\"\tTEXT,\r\n\tPRIMARY KEY(\"Id\" AUTOINCREMENT)\r\n);\r\nCREATE TABLE IF NOT EXISTS \"AccountMove\" (\r\n\t\"Id\"\tINTEGER NOT NULL UNIQUE,\r\n\t\"Parent\"\tINTEGER,\r\n\t\"ParentId\"\tINTEGER,\r\n\t\"Sequence\"\tINTEGER,\r\n\t\"Date\"\tTEXT,\r\n\t\"Ref\"\tTEXT,\r\n\t\"debit\"\tINTEGER,\r\n\t\"credit\"\tINTEGER,\r\n\t\"ProductId\"\tINTEGER,\r\n\t\"Product_uomId\"\tINTEGER,\r\n\t\"Quantity\"\tINTEGER,\r\n\t\"Price_unit\"\tINTEGER,\r\n\t\"Price_total\"\tINTEGER,\r\n\t\"Discount\"\tINTEGER,\r\n\t\"PartnerId\"\tINTEGER,\r\n\t\"AccountId\"\tINTEGER,\r\n\t\"Rb\"\tTEXT,\r\n\tPRIMARY KEY(\"Id\" AUTOINCREMENT)\r\n);\r\nCREATE TABLE IF NOT EXISTS \"Company\" (\r\n\t\"Id\"\tINTEGER NOT NULL UNIQUE,\r\n\t\"Code\"\tTEXT,\r\n\t\"Name\"\tTEXT,\r\n\t\"Display_name\"\tTEXT,\r\n\t\"Active\"\tINTEGER DEFAULT 1,\r\n\t\"Description\"\tTEXT,\r\n\t\"Image\"\tTEXT,\r\n\t\"Address\"\tTEXT,\r\n\t\"Phone\"\tTEXT,\r\n\t\"Email\"\tTEXT,\r\n\t\"Currency_name\"\tTEXT,\r\n\t\"Currency_symbol\"\tTEXT,\r\n\t\"Currency_rounding\"\tTEXT,\r\n\t\"Currency_decimal_places\"\tTEXT,\r\n\t\"Created\"\tTEXT,\r\n\t\"Updated\"\tTEXT,\r\n\t\"Partner_type\"\tINTEGER,\r\n\t\"Rb\"\tTEXT,\r\n\tPRIMARY KEY(\"Id\" AUTOINCREMENT)\r\n);\r\nCREATE TABLE IF NOT EXISTS \"Partner\" (\r\n\t\"Id\"\tINTEGER NOT NULL UNIQUE,\r\n\t\"Code\"\tTEXT,\r\n\t\"Name\"\tTEXT,\r\n\t\"Display_name\"\tTEXT,\r\n\t\"Active\"\tINTEGER,\r\n\t\"Description\"\tTEXT,\r\n\t\"Image\"\tTEXT,\r\n\t\"Address\"\tTEXT,\r\n\t\"Phone\"\tTEXT,\r\n\t\"Email\"\tTEXT,\r\n\t\"Currency_name\"\tTEXT,\r\n\t\"Currency_symbol\"\tTEXT,\r\n\t\"Currency_rounding\"\tTEXT,\r\n\t\"Currency_decimal_places\"\tTEXT,\r\n\t\"Created\"\tTEXT,\r\n\t\"Updated\"\tTEXT,\r\n\t\"Partner_type\"\tINTEGER,\r\n\t\"Rb\"\tTEXT,\r\n\tPRIMARY KEY(\"Id\" AUTOINCREMENT)\r\n);\r\nCREATE TABLE IF NOT EXISTS \"Product\" (\r\n\t\"Id\"\tINTEGER NOT NULL UNIQUE,\r\n\t\"Code\"\tTEXT,\r\n\t\"Name\"\tTEXT,\r\n\t\"Name_eng\"\tTEXT,\r\n\t\"Description\"\tTEXT,\r\n\t\"Image\"\tTEXT,\r\n\t\"Price\"\tDOUBLE DEFAULT 0,\r\n\t\"Product_UomCode\"\tTEXT,\r\n\t\"Account_IncomeCode\"\tTEXT,\r\n\t\"Account_ExpenseCode\"\tTEXT,\r\n\t\"Active\"\tINTEGER DEFAULT 1,\r\n\t\"Available_In_Pos\"\tINTEGER,\r\n\t\"Rb\"\tTEXT,\r\n\tPRIMARY KEY(\"Id\" AUTOINCREMENT)\r\n);\r\nCREATE TABLE IF NOT EXISTS \"ProductUoM\" (\r\n\t\"Id\"\tINTEGER NOT NULL UNIQUE,\r\n\t\"Code\"\tTEXT,\r\n\t\"Name\"\tTEXT,\r\n\t\"Rb\"\tTEXT,\r\n\tPRIMARY KEY(\"Id\" AUTOINCREMENT)\r\n);\r\n\r\n\r\nCOMMIT;\r\n";
                    using (var con = new SQLiteConnection(utils.Get_SmartDB_ConnectionString()))
                    {
                        try
                        {
                            con.Open();
                            int effectRows = con.Execute(ScriptDB);
                        }
                        catch (Exception ex) { MessageBox.Show("Check_SmartDB_File\n" + ex.Message); }
                    }
                //}
            }
            catch { }
        }
        public void Run_sqlite_Maintain()
        {
            //DROP TABLE [IF EXISTS] [schema_name.]table_name;
            try
            {
                //string folderDb = $"{utils.Get_Database_Directory()}";
                //string Smartfile = $"{folderDb}\\Smart.db";   DROP TABLE IF EXISTS TABLE_NAME;

                //string ScriptDB = @"DROP TABLE IF EXISTS Account;
                //                    DROP TABLE IF EXISTS AccountMove;
                //                    DROP TABLE IF EXISTS Company;
                //                    DROP TABLE IF EXISTS Partner;
                //                    DROP TABLE IF EXISTS Product;
                //                    DROP TABLE IF EXISTS ProductUoM;
                //                    ";


                //SqliteRunCommand(ScriptDB);

                //SqliteRunCommand("ALTER TABLE Company ADD COLUMN Name_eng text;");
                //SqliteRunCommand("ALTER TABLE Company ADD COLUMN Mobail text; ");
                SqliteRunCommand("ALTER TABLE AccountMove ADD COLUMN Move_type text; ");

                //string addNewColum = "ALTER TABLE Company\r\nADD COLUMN Name_eng text; " +
                //    "ALTER TABLE Company\r\nADD COLUMN Mobail text;  ";

                //using (var con = new SQLiteConnection(utils.Get_SmartDB_ConnectionString()))
                //{
                //    try
                //    {
                //        con.Open();
                //        //var sqLiteTransaction = con.BeginTransaction();
                //        //int effectRows = con.Execute(ScriptDB);
                //        int effectRows = con.Execute(addNewColum);
                //        //CreateDefultTemplate();
                //        //sqLiteTransaction.Commit();
                //    }
                //    catch (Exception ex) { MessageBox.Show("Check_SmartDB_File\n" + ex.Message); }
                //}
            }
            catch { }
        }

        private void SqliteRunCommand(string command )
        {
            try
            {
                using (var con = new SQLiteConnection(utils.Get_SmartDB_ConnectionString()))
                {
                    try
                    {
                        con.Open();
                        int effectRows = con.Execute(command);
                    }
                    catch (Exception ex) { /*MessageBox.Show("Check_SmartDB_File\n" + ex.Message);*/ }
                }
            }
            catch { }
        }
        public void CreateDefultTemplate()
        {
            try
            {
                SourceCardsTemplate sourceCardsTemplate2 = new SourceCardsTemplate();
                if (sourceCardsTemplate2.CreateDefaultTemplate(true))
                {
                }
            }
            catch { }
        }
        public void Check_LocalDB_File_byScript(Connections_Db conn)
        {
            //string folderDb = $"{utils.Get_Database_Directory()}";
            string Smartfile = $"{utils.Get_Database_Directory()}\\{conn.FileName}";


            if (!File.Exists(Smartfile))
            {
                string ScriptDB = "BEGIN TRANSACTION;\r\nCREATE TABLE IF NOT EXISTS HSUser\r\n(\r\n\t\"ProfileHotspot\" TEXT ,\r\n\t\"Limitbytestotal\" INTEGER  DEFAULT (0) ,\r\n\t\"LimitUptime\" INTEGER  DEFAULT (0) ,\r\n\t\"Server\" TEXT ,\r\n\t\"Descr\" TEXT ,\r\n\t\"SmartValidatiy_Add\" INTEGER  DEFAULT (1) ,\r\n\t\"SmartValidatiy_ByDayOrHour\" INTEGER  DEFAULT (0) ,\r\n\t\"SmartValidatiy_timeSave\" INTEGER  DEFAULT (1) ,\r\n\t\"SmartValidatiy_sizeSave\" INTEGER  DEFAULT (1) ,\r\n\t\"SmartValidatiy_sessionSave\" INTEGER  DEFAULT (1) ,\r\n\t\"First_mac\" INTEGER  DEFAULT (0) ,\r\n\t\"SN\" INTEGER  NOT NULL ,\r\n\t\"UserName\" TEXT  NOT NULL ,\r\n\t\"Password\" TEXT ,\r\n\t\"ProfileName\" TEXT ,\r\n\t\"TotalPrice\" DOUBLE  DEFAULT (0) ,\r\n\t\"Price_Disply\" TEXT  DEFAULT (0) ,\r\n\t\"BatchCardId\" INTEGER,\r\n\t\"BatchType\" INTEGER  DEFAULT (0) ,\r\n\t\"NumberPrint\" INTEGER,\r\n\t\"SpName\" TEXT ,\r\n\t\"RegDate\" TEXT,\r\n\t\"FirsLogin\" TEXT,\r\n\t\"Comment\" TEXT ,\r\n\t\"LastSynDb\" TEXT,\r\n\t\"CountProfile\" INTEGER  DEFAULT (0) ,\r\n\t\"CountSession\" INTEGER  DEFAULT (0) ,\r\n\t\"PageNumber\" INTEGER,\r\n\t\"Sn_Name\" TEXT  NOT NULL  PRIMARY KEY ,\r\n\t\"DeleteFromServer\" INTEGER  DEFAULT (0) ,\r\n\t\"ValidityLimit\" INTEGER  DEFAULT (0) ,\r\n\t\"UptimeLimit\" INTEGER  DEFAULT (0) ,\r\n\t\"TransferLimit\" INTEGER  DEFAULT (0) ,\r\n\t\"UptimeUsed\" INTEGER  DEFAULT (0) ,\r\n\t\"DownloadUsed\" INTEGER  DEFAULT (0) ,\r\n\t\"UploadUsed\" INTEGER  DEFAULT (0) ,\r\n\t\"ProfileTillTime\" TEXT,\r\n\t\"ProfileTimeLeft\" INTEGER  DEFAULT (0) ,\r\n\t\"ProfileTransferLeft\" INTEGER  DEFAULT (0) ,\r\n\t\"ProfileValidity\" INTEGER  DEFAULT (0) ,\r\n\t\"Price\" DOUBLE  DEFAULT (0) ,\r\n\t\"Percentage\" DOUBLE  DEFAULT (0) ,\r\n\t\"PercentageType\" INTEGER  DEFAULT (0) ,\r\n\t\"Disabled\" INTEGER  DEFAULT (0) ,\r\n\t\"Status\" INTEGER  DEFAULT (0) ,\r\n\t\"SpCode\" TEXT ,\r\n\t\"Email\" TEXT ,\r\n\t\"NasPortId\" TEXT ,\r\n\t\"MkId\" TEXT ,\r\n\t\"AddedDb\" TEXT,\r\n\t\"IdHX\" TEXT ,\r\n\t\"CallerMac\" TEXT ,\r\n\t\"Group\" TEXT ,\r\n\t\"Attributes\" TEXT ,\r\n\t\"Sn_Archive\" INTEGER\r\n) WITHOUT ROWID;\r\nCREATE TABLE IF NOT EXISTS HsPyment\r\n(\r\n\t\"Id\" INTEGER  NOT NULL  PRIMARY KEY  AUTOINCREMENT ,\r\n\t\"DeleteFromServer\" INTEGER  DEFAULT (0) ,\r\n\t\"Fk_Sn_Name\" TEXT ,\r\n\t\"MainProfile\" INTEGER ,\r\n\t\"UserName\" TEXT  NOT NULL ,\r\n\t\"ProfileName\" TEXT ,\r\n\t\"Price\" DOUBLE  DEFAULT (0) ,\r\n\t\"Percentage\" DOUBLE  DEFAULT (0) ,\r\n\t\"PercentageType\" INTEGER  DEFAULT (0) ,\r\n\t\"TotalPrice\" DOUBLE  DEFAULT (0) ,\r\n\t\"ProfileValidity\" INTEGER  DEFAULT (0) ,\r\n\t\"ProfileUptimeLimit\" INTEGER  DEFAULT (0) ,\r\n\t\"ProfileTransferLimit\" INTEGER  DEFAULT (0) ,\r\n\t\"AddedDate\" TEXT,\r\n\t\"AddedDb\" TEXT,\r\n\t\"LastSynDb\" TEXT,\r\n\t\"MkId\" TEXT ,\r\n\t\"state\" TEXT \r\n\r\n,FOREIGN KEY(\"Fk_Sn_Name\") REFERENCES \"HSUser\"(\"Sn_Name\") ON  DELETE CASCADE\r\n,CONSTRAINT \"UC_HsPyment_UserName_Price_ProfileName_AddedDb_MkId\" UNIQUE(\"UserName\",\"Price\",\"ProfileName\",\"AddedDb\",\"MkId\")\r\n);\r\nCREATE TABLE IF NOT EXISTS HsSession\r\n(\r\n\t\"Id\" INTEGER  NOT NULL  PRIMARY KEY  AUTOINCREMENT ,\r\n\t\"Status\" INTEGER ,\r\n\t\"Fk_Sn_Name\" TEXT ,\r\n\t\"contents\" TEXT ,\r\n\t\"TillTime_inSecond\" INTEGER ,\r\n\t\"UserName\" TEXT  NOT NULL ,\r\n\t\"NasPortId\" TEXT ,\r\n\t\"CallingStationId\" TEXT ,\r\n\t\"IpUser\" TEXT ,\r\n\t\"IpRouter\" TEXT ,\r\n\t\"FromTime\" TEXT,\r\n\t\"TillTime\" TEXT,\r\n\t\"UpTime\" INTEGER  DEFAULT (0) ,\r\n\t\"BytesDownload\" INTEGER  DEFAULT (0) ,\r\n\t\"BytesUpload\" INTEGER  DEFAULT (0) ,\r\n\t\"DeleteFromServer\" INTEGER  DEFAULT (0) ,\r\n\t\"AddedDb\" TEXT,\r\n\t\"LastSynDb\" TEXT,\r\n\t\"MkId\" TEXT \r\n\r\n,FOREIGN KEY(\"Fk_Sn_Name\") REFERENCES \"HSUser\"(\"Sn_Name\") ON  DELETE CASCADE\r\n,CONSTRAINT \"UC_HsSession_TillTime_inSecond_UserName\" UNIQUE(\"TillTime_inSecond\",\"UserName\")\r\n);\r\nCREATE TABLE IF NOT EXISTS UmPyment\r\n(\r\n\t\"IdHX\" TEXT ,\r\n\t\"Sn\" INTEGER ,\r\n\t\"Sn_Name\" TEXT  NOT NULL  PRIMARY KEY ,\r\n\t\"DeleteFromServer\" INTEGER  DEFAULT (0) ,\r\n\t\"Fk_Sn_Name\" TEXT ,\r\n\t\"UserName\" TEXT  NOT NULL ,\r\n\t\"ProfileName\" TEXT ,\r\n\t\"Price\" DOUBLE  DEFAULT (0) ,\r\n\t\"Percentage\" DOUBLE  DEFAULT (0) ,\r\n\t\"PercentageType\" INTEGER  DEFAULT (0) ,\r\n\t\"TotalPrice\" DOUBLE  DEFAULT (0) ,\r\n\t\"ProfileValidity\" INTEGER  DEFAULT (0) ,\r\n\t\"ProfileUptimeLimit\" INTEGER  DEFAULT (0) ,\r\n\t\"ProfileTransferLimit\" INTEGER  DEFAULT (0) ,\r\n\t\"AddedDate\" TEXT,\r\n\t\"AddedDb\" TEXT,\r\n\t\"LastSynDb\" TEXT,\r\n\t\"MkId\" TEXT ,\r\n\t\"state\" TEXT \r\n,FOREIGN KEY(\"Fk_Sn_Name\") REFERENCES \"UmUser\"(\"Sn_Name\") ON  DELETE CASCADE\r\n) WITHOUT ROWID;\r\nCREATE TABLE IF NOT EXISTS UmSession\r\n(\r\n\t\"Sn\" INTEGER ,\r\n\t\"IdHX\" TEXT ,\r\n\t\"Active\" INTEGER  DEFAULT (0) ,\r\n\t\"Sn_Name\" TEXT  NOT NULL  PRIMARY KEY ,\r\n\t\"Status\" INTEGER ,\r\n\t\"Fk_Sn_Name\" TEXT ,\r\n\t\"UserName\" TEXT  NOT NULL ,\r\n\t\"NasPortId\" TEXT ,\r\n\t\"CallingStationId\" TEXT ,\r\n\t\"IpUser\" TEXT ,\r\n\t\"IpRouter\" TEXT ,\r\n\t\"FromTime\" TEXT,\r\n\t\"TillTime\" TEXT,\r\n\t\"UpTime\" INTEGER  DEFAULT (0) ,\r\n\t\"BytesDownload\" INTEGER  DEFAULT (0) ,\r\n\t\"BytesUpload\" INTEGER  DEFAULT (0) ,\r\n\t\"DeleteFromServer\" INTEGER  DEFAULT (0) ,\r\n\t\"AddedDb\" TEXT,\r\n\t\"LastSynDb\" TEXT,\r\n\t\"MkId\" TEXT \r\n,FOREIGN KEY(\"Fk_Sn_Name\") REFERENCES \"UmUser\"(\"Sn_Name\") ON  DELETE CASCADE\r\n) WITHOUT ROWID;\r\nCREATE TABLE IF NOT EXISTS UmUser\r\n(\r\n\t\"Radius\" TEXT ,\r\n\t\"CustomerName\" TEXT ,\r\n\t\"FirstName\" TEXT ,\r\n\t\"LastName\" TEXT ,\r\n\t\"Phone\" TEXT ,\r\n\t\"Location\" TEXT ,\r\n\t\"SharedUsers\" TEXT ,\r\n\t\"LastSeenAt\" TEXT,\r\n\t\"ActiveSessions\" INTEGER,\r\n\t\"SN\" INTEGER  NOT NULL ,\r\n\t\"UserName\" TEXT  NOT NULL ,\r\n\t\"Password\" TEXT ,\r\n\t\"ProfileName\" TEXT ,\r\n\t\"TotalPrice\" DOUBLE  DEFAULT (0) ,\r\n\t\"Price_Disply\" TEXT  DEFAULT (0) ,\r\n\t\"BatchCardId\" INTEGER,\r\n\t\"BatchType\" INTEGER  DEFAULT (0) ,\r\n\t\"NumberPrint\" INTEGER,\r\n\t\"SpName\" TEXT ,\r\n\t\"RegDate\" TEXT,\r\n\t\"FirsLogin\" TEXT,\r\n\t\"Comment\" TEXT ,\r\n\t\"LastSynDb\" TEXT,\r\n\t\"CountProfile\" INTEGER  DEFAULT (0) ,\r\n\t\"CountSession\" INTEGER  DEFAULT (0) ,\r\n\t\"PageNumber\" INTEGER,\r\n\t\"Sn_Name\" TEXT  NOT NULL  PRIMARY KEY ,\r\n\t\"DeleteFromServer\" INTEGER  DEFAULT (0) ,\r\n\t\"ValidityLimit\" INTEGER  DEFAULT (0) ,\r\n\t\"UptimeLimit\" INTEGER  DEFAULT (0) ,\r\n\t\"TransferLimit\" INTEGER  DEFAULT (0) ,\r\n\t\"UptimeUsed\" INTEGER  DEFAULT (0) ,\r\n\t\"DownloadUsed\" INTEGER  DEFAULT (0) ,\r\n\t\"UploadUsed\" INTEGER  DEFAULT (0) ,\r\n\t\"ProfileTillTime\" TEXT,\r\n\t\"ProfileTimeLeft\" INTEGER  DEFAULT (0) ,\r\n\t\"ProfileTransferLeft\" INTEGER  DEFAULT (0) ,\r\n\t\"ProfileValidity\" INTEGER  DEFAULT (0) ,\r\n\t\"Price\" DOUBLE  DEFAULT (0) ,\r\n\t\"Percentage\" DOUBLE  DEFAULT (0) ,\r\n\t\"PercentageType\" INTEGER  DEFAULT (0) ,\r\n\t\"Disabled\" INTEGER  DEFAULT (0) ,\r\n\t\"Status\" INTEGER  DEFAULT (0) ,\r\n\t\"SpCode\" TEXT ,\r\n\t\"Email\" TEXT ,\r\n\t\"NasPortId\" TEXT ,\r\n\t\"MkId\" TEXT ,\r\n\t\"AddedDb\" TEXT,\r\n\t\"IdHX\" TEXT ,\r\n\t\"CallerMac\" TEXT ,\r\n\t\"Group\" TEXT ,\r\n\t\"Attributes\" TEXT ,\r\n\t\"Sn_Archive\" INTEGER\r\n) WITHOUT ROWID;\r\nCREATE INDEX HSUser_idx_02eb6568 ON HSUser(SN DESC);\r\nCREATE INDEX HSUser_idx_e07cf9bb ON HSUser(DeleteFromServer, BatchCardId);\r\nCREATE INDEX HSUser_idx_e43b0e22 ON HSUser(Status, DeleteFromServer, SN DESC);\r\nCREATE INDEX HSUser_idx_f28dcfa3 ON HSUser(DeleteFromServer, BatchCardId, Status);\r\nCREATE INDEX UmPyment_idx_811fb391 ON UmPyment(Fk_Sn_Name);\r\nCREATE INDEX UmPyment_idx_c6d9b548 ON UmPyment(AddedDate);\r\nCREATE INDEX UmSession_idx_001b4956 ON UmSession(DeleteFromServer);\r\nCREATE INDEX UmSession_idx_180135e3 ON UmSession(FromTime);\r\nCREATE INDEX UmSession_idx_811fb391 ON UmSession(Fk_Sn_Name);\r\nCREATE INDEX UmSession_idx_adaa03a3 ON UmSession(NasPortId, FromTime);\r\nCREATE INDEX UmSession_idx_bb530fdb ON UmSession(DeleteFromServer, Sn);\r\nCREATE INDEX UmUser_idx_001b4956 ON UmUser(DeleteFromServer);\r\nCREATE INDEX UmUser_idx_0056e50e ON UmUser(SpCode);\r\nCREATE INDEX UmUser_idx_02eb6568 ON UmUser(SN DESC);\r\nCREATE INDEX UmUser_idx_07fd7236 ON UmUser(Status, SpCode);\r\nCREATE INDEX UmUser_idx_188647fd ON UmUser(NasPortId, FirsLogin);\r\nCREATE INDEX UmUser_idx_49fb733a ON UmUser(DeleteFromServer, SN DESC);\r\nCREATE INDEX UmUser_idx_9e0ac6a9 ON UmUser(BatchCardId);\r\nCREATE INDEX UmUser_idx_d5970c3d ON UmUser(FirsLogin);\r\nCREATE INDEX UmUser_idx_e07cf9bb ON UmUser(DeleteFromServer, BatchCardId);\r\nCREATE INDEX UmUser_idx_e7c209d4 ON UmUser(NasPortId);\r\nCREATE INDEX UmUser_idx_f28dcfa3 ON UmUser(DeleteFromServer, BatchCardId, Status);\r\nCOMMIT;\r\n";
                using (var con = new SQLiteConnection(utils.Get_LocalDB_ConnectionString(conn.FileName)))
                {
                    try
                    {
                        con.Open();
                        //var sqLiteTransaction = con.BeginTransaction();
                        int effectRows = con.Execute(ScriptDB);
                        //CreateDefultTemplate();

                        //sqLiteTransaction.Commit();
                    }
                    catch (Exception ex) {  MessageBox.Show("Check_LocalDB_File \n" + ex.Message); }
                }
            }
        }

        public bool Check_LocalDB_File_Found(Connections_Db conn)
        {
            //string folderDb = $"{utils.Get_Database_Directory()}";
            string Smartfile = $"{utils.Get_Database_Directory()}\\{conn.FileName}";


            if (!File.Exists(Smartfile))
            {
                //string ScriptDB = "BEGIN TRANSACTION;\r\nCREATE TABLE IF NOT EXISTS HSUser\r\n(\r\n\t\"ProfileHotspot\" TEXT ,\r\n\t\"Limitbytestotal\" INTEGER  DEFAULT (0) ,\r\n\t\"LimitUptime\" INTEGER  DEFAULT (0) ,\r\n\t\"Server\" TEXT ,\r\n\t\"Descr\" TEXT ,\r\n\t\"SmartValidatiy_Add\" INTEGER  DEFAULT (1) ,\r\n\t\"SmartValidatiy_ByDayOrHour\" INTEGER  DEFAULT (0) ,\r\n\t\"SmartValidatiy_timeSave\" INTEGER  DEFAULT (1) ,\r\n\t\"SmartValidatiy_sizeSave\" INTEGER  DEFAULT (1) ,\r\n\t\"SmartValidatiy_sessionSave\" INTEGER  DEFAULT (1) ,\r\n\t\"First_mac\" INTEGER  DEFAULT (0) ,\r\n\t\"SN\" INTEGER  NOT NULL ,\r\n\t\"UserName\" TEXT  NOT NULL ,\r\n\t\"Password\" TEXT ,\r\n\t\"ProfileName\" TEXT ,\r\n\t\"TotalPrice\" DOUBLE  DEFAULT (0) ,\r\n\t\"Price_Disply\" TEXT  DEFAULT (0) ,\r\n\t\"BatchCardId\" INTEGER,\r\n\t\"BatchType\" INTEGER  DEFAULT (0) ,\r\n\t\"NumberPrint\" INTEGER,\r\n\t\"SpName\" TEXT ,\r\n\t\"RegDate\" TEXT,\r\n\t\"FirsLogin\" TEXT,\r\n\t\"Comment\" TEXT ,\r\n\t\"LastSynDb\" TEXT,\r\n\t\"CountProfile\" INTEGER  DEFAULT (0) ,\r\n\t\"CountSession\" INTEGER  DEFAULT (0) ,\r\n\t\"PageNumber\" INTEGER,\r\n\t\"Sn_Name\" TEXT  NOT NULL  PRIMARY KEY ,\r\n\t\"DeleteFromServer\" INTEGER  DEFAULT (0) ,\r\n\t\"ValidityLimit\" INTEGER  DEFAULT (0) ,\r\n\t\"UptimeLimit\" INTEGER  DEFAULT (0) ,\r\n\t\"TransferLimit\" INTEGER  DEFAULT (0) ,\r\n\t\"UptimeUsed\" INTEGER  DEFAULT (0) ,\r\n\t\"DownloadUsed\" INTEGER  DEFAULT (0) ,\r\n\t\"UploadUsed\" INTEGER  DEFAULT (0) ,\r\n\t\"ProfileTillTime\" TEXT,\r\n\t\"ProfileTimeLeft\" INTEGER  DEFAULT (0) ,\r\n\t\"ProfileTransferLeft\" INTEGER  DEFAULT (0) ,\r\n\t\"ProfileValidity\" INTEGER  DEFAULT (0) ,\r\n\t\"Price\" DOUBLE  DEFAULT (0) ,\r\n\t\"Percentage\" DOUBLE  DEFAULT (0) ,\r\n\t\"PercentageType\" INTEGER  DEFAULT (0) ,\r\n\t\"Disabled\" INTEGER  DEFAULT (0) ,\r\n\t\"Status\" INTEGER  DEFAULT (0) ,\r\n\t\"SpCode\" TEXT ,\r\n\t\"Email\" TEXT ,\r\n\t\"NasPortId\" TEXT ,\r\n\t\"MkId\" TEXT ,\r\n\t\"AddedDb\" TEXT,\r\n\t\"IdHX\" TEXT ,\r\n\t\"CallerMac\" TEXT ,\r\n\t\"Group\" TEXT ,\r\n\t\"Attributes\" TEXT ,\r\n\t\"Sn_Archive\" INTEGER\r\n) WITHOUT ROWID;\r\nCREATE TABLE IF NOT EXISTS HsPyment\r\n(\r\n\t\"Id\" INTEGER  NOT NULL  PRIMARY KEY  AUTOINCREMENT ,\r\n\t\"DeleteFromServer\" INTEGER  DEFAULT (0) ,\r\n\t\"Fk_Sn_Name\" TEXT ,\r\n\t\"MainProfile\" INTEGER ,\r\n\t\"UserName\" TEXT  NOT NULL ,\r\n\t\"ProfileName\" TEXT ,\r\n\t\"Price\" DOUBLE  DEFAULT (0) ,\r\n\t\"Percentage\" DOUBLE  DEFAULT (0) ,\r\n\t\"PercentageType\" INTEGER  DEFAULT (0) ,\r\n\t\"TotalPrice\" DOUBLE  DEFAULT (0) ,\r\n\t\"ProfileValidity\" INTEGER  DEFAULT (0) ,\r\n\t\"ProfileUptimeLimit\" INTEGER  DEFAULT (0) ,\r\n\t\"ProfileTransferLimit\" INTEGER  DEFAULT (0) ,\r\n\t\"AddedDate\" TEXT,\r\n\t\"AddedDb\" TEXT,\r\n\t\"LastSynDb\" TEXT,\r\n\t\"MkId\" TEXT ,\r\n\t\"state\" TEXT \r\n\r\n,FOREIGN KEY(\"Fk_Sn_Name\") REFERENCES \"HSUser\"(\"Sn_Name\") ON  DELETE CASCADE\r\n,CONSTRAINT \"UC_HsPyment_UserName_Price_ProfileName_AddedDb_MkId\" UNIQUE(\"UserName\",\"Price\",\"ProfileName\",\"AddedDb\",\"MkId\")\r\n);\r\nCREATE TABLE IF NOT EXISTS HsSession\r\n(\r\n\t\"Id\" INTEGER  NOT NULL  PRIMARY KEY  AUTOINCREMENT ,\r\n\t\"Status\" INTEGER ,\r\n\t\"Fk_Sn_Name\" TEXT ,\r\n\t\"contents\" TEXT ,\r\n\t\"TillTime_inSecond\" INTEGER ,\r\n\t\"UserName\" TEXT  NOT NULL ,\r\n\t\"NasPortId\" TEXT ,\r\n\t\"CallingStationId\" TEXT ,\r\n\t\"IpUser\" TEXT ,\r\n\t\"IpRouter\" TEXT ,\r\n\t\"FromTime\" TEXT,\r\n\t\"TillTime\" TEXT,\r\n\t\"UpTime\" INTEGER  DEFAULT (0) ,\r\n\t\"BytesDownload\" INTEGER  DEFAULT (0) ,\r\n\t\"BytesUpload\" INTEGER  DEFAULT (0) ,\r\n\t\"DeleteFromServer\" INTEGER  DEFAULT (0) ,\r\n\t\"AddedDb\" TEXT,\r\n\t\"LastSynDb\" TEXT,\r\n\t\"MkId\" TEXT \r\n\r\n,FOREIGN KEY(\"Fk_Sn_Name\") REFERENCES \"HSUser\"(\"Sn_Name\") ON  DELETE CASCADE\r\n,CONSTRAINT \"UC_HsSession_TillTime_inSecond_UserName\" UNIQUE(\"TillTime_inSecond\",\"UserName\")\r\n);\r\nCREATE TABLE IF NOT EXISTS UmPyment\r\n(\r\n\t\"IdHX\" TEXT ,\r\n\t\"Sn\" INTEGER ,\r\n\t\"Sn_Name\" TEXT  NOT NULL  PRIMARY KEY ,\r\n\t\"DeleteFromServer\" INTEGER  DEFAULT (0) ,\r\n\t\"Fk_Sn_Name\" TEXT ,\r\n\t\"UserName\" TEXT  NOT NULL ,\r\n\t\"ProfileName\" TEXT ,\r\n\t\"Price\" DOUBLE  DEFAULT (0) ,\r\n\t\"Percentage\" DOUBLE  DEFAULT (0) ,\r\n\t\"PercentageType\" INTEGER  DEFAULT (0) ,\r\n\t\"TotalPrice\" DOUBLE  DEFAULT (0) ,\r\n\t\"ProfileValidity\" INTEGER  DEFAULT (0) ,\r\n\t\"ProfileUptimeLimit\" INTEGER  DEFAULT (0) ,\r\n\t\"ProfileTransferLimit\" INTEGER  DEFAULT (0) ,\r\n\t\"AddedDate\" TEXT,\r\n\t\"AddedDb\" TEXT,\r\n\t\"LastSynDb\" TEXT,\r\n\t\"MkId\" TEXT ,\r\n\t\"state\" TEXT \r\n,FOREIGN KEY(\"Fk_Sn_Name\") REFERENCES \"UmUser\"(\"Sn_Name\") ON  DELETE CASCADE\r\n) WITHOUT ROWID;\r\nCREATE TABLE IF NOT EXISTS UmSession\r\n(\r\n\t\"Sn\" INTEGER ,\r\n\t\"IdHX\" TEXT ,\r\n\t\"Active\" INTEGER  DEFAULT (0) ,\r\n\t\"Sn_Name\" TEXT  NOT NULL  PRIMARY KEY ,\r\n\t\"Status\" INTEGER ,\r\n\t\"Fk_Sn_Name\" TEXT ,\r\n\t\"UserName\" TEXT  NOT NULL ,\r\n\t\"NasPortId\" TEXT ,\r\n\t\"CallingStationId\" TEXT ,\r\n\t\"IpUser\" TEXT ,\r\n\t\"IpRouter\" TEXT ,\r\n\t\"FromTime\" TEXT,\r\n\t\"TillTime\" TEXT,\r\n\t\"UpTime\" INTEGER  DEFAULT (0) ,\r\n\t\"BytesDownload\" INTEGER  DEFAULT (0) ,\r\n\t\"BytesUpload\" INTEGER  DEFAULT (0) ,\r\n\t\"DeleteFromServer\" INTEGER  DEFAULT (0) ,\r\n\t\"AddedDb\" TEXT,\r\n\t\"LastSynDb\" TEXT,\r\n\t\"MkId\" TEXT \r\n,FOREIGN KEY(\"Fk_Sn_Name\") REFERENCES \"UmUser\"(\"Sn_Name\") ON  DELETE CASCADE\r\n) WITHOUT ROWID;\r\nCREATE TABLE IF NOT EXISTS UmUser\r\n(\r\n\t\"Radius\" TEXT ,\r\n\t\"CustomerName\" TEXT ,\r\n\t\"FirstName\" TEXT ,\r\n\t\"LastName\" TEXT ,\r\n\t\"Phone\" TEXT ,\r\n\t\"Location\" TEXT ,\r\n\t\"SharedUsers\" TEXT ,\r\n\t\"LastSeenAt\" TEXT,\r\n\t\"ActiveSessions\" INTEGER,\r\n\t\"SN\" INTEGER  NOT NULL ,\r\n\t\"UserName\" TEXT  NOT NULL ,\r\n\t\"Password\" TEXT ,\r\n\t\"ProfileName\" TEXT ,\r\n\t\"TotalPrice\" DOUBLE  DEFAULT (0) ,\r\n\t\"Price_Disply\" TEXT  DEFAULT (0) ,\r\n\t\"BatchCardId\" INTEGER,\r\n\t\"BatchType\" INTEGER  DEFAULT (0) ,\r\n\t\"NumberPrint\" INTEGER,\r\n\t\"SpName\" TEXT ,\r\n\t\"RegDate\" TEXT,\r\n\t\"FirsLogin\" TEXT,\r\n\t\"Comment\" TEXT ,\r\n\t\"LastSynDb\" TEXT,\r\n\t\"CountProfile\" INTEGER  DEFAULT (0) ,\r\n\t\"CountSession\" INTEGER  DEFAULT (0) ,\r\n\t\"PageNumber\" INTEGER,\r\n\t\"Sn_Name\" TEXT  NOT NULL  PRIMARY KEY ,\r\n\t\"DeleteFromServer\" INTEGER  DEFAULT (0) ,\r\n\t\"ValidityLimit\" INTEGER  DEFAULT (0) ,\r\n\t\"UptimeLimit\" INTEGER  DEFAULT (0) ,\r\n\t\"TransferLimit\" INTEGER  DEFAULT (0) ,\r\n\t\"UptimeUsed\" INTEGER  DEFAULT (0) ,\r\n\t\"DownloadUsed\" INTEGER  DEFAULT (0) ,\r\n\t\"UploadUsed\" INTEGER  DEFAULT (0) ,\r\n\t\"ProfileTillTime\" TEXT,\r\n\t\"ProfileTimeLeft\" INTEGER  DEFAULT (0) ,\r\n\t\"ProfileTransferLeft\" INTEGER  DEFAULT (0) ,\r\n\t\"ProfileValidity\" INTEGER  DEFAULT (0) ,\r\n\t\"Price\" DOUBLE  DEFAULT (0) ,\r\n\t\"Percentage\" DOUBLE  DEFAULT (0) ,\r\n\t\"PercentageType\" INTEGER  DEFAULT (0) ,\r\n\t\"Disabled\" INTEGER  DEFAULT (0) ,\r\n\t\"Status\" INTEGER  DEFAULT (0) ,\r\n\t\"SpCode\" TEXT ,\r\n\t\"Email\" TEXT ,\r\n\t\"NasPortId\" TEXT ,\r\n\t\"MkId\" TEXT ,\r\n\t\"AddedDb\" TEXT,\r\n\t\"IdHX\" TEXT ,\r\n\t\"CallerMac\" TEXT ,\r\n\t\"Group\" TEXT ,\r\n\t\"Attributes\" TEXT ,\r\n\t\"Sn_Archive\" INTEGER\r\n) WITHOUT ROWID;\r\nCREATE INDEX HSUser_idx_02eb6568 ON HSUser(SN DESC);\r\nCREATE INDEX HSUser_idx_e07cf9bb ON HSUser(DeleteFromServer, BatchCardId);\r\nCREATE INDEX HSUser_idx_e43b0e22 ON HSUser(Status, DeleteFromServer, SN DESC);\r\nCREATE INDEX HSUser_idx_f28dcfa3 ON HSUser(DeleteFromServer, BatchCardId, Status);\r\nCREATE INDEX UmPyment_idx_811fb391 ON UmPyment(Fk_Sn_Name);\r\nCREATE INDEX UmPyment_idx_c6d9b548 ON UmPyment(AddedDate);\r\nCREATE INDEX UmSession_idx_001b4956 ON UmSession(DeleteFromServer);\r\nCREATE INDEX UmSession_idx_180135e3 ON UmSession(FromTime);\r\nCREATE INDEX UmSession_idx_811fb391 ON UmSession(Fk_Sn_Name);\r\nCREATE INDEX UmSession_idx_adaa03a3 ON UmSession(NasPortId, FromTime);\r\nCREATE INDEX UmSession_idx_bb530fdb ON UmSession(DeleteFromServer, Sn);\r\nCREATE INDEX UmUser_idx_001b4956 ON UmUser(DeleteFromServer);\r\nCREATE INDEX UmUser_idx_0056e50e ON UmUser(SpCode);\r\nCREATE INDEX UmUser_idx_02eb6568 ON UmUser(SN DESC);\r\nCREATE INDEX UmUser_idx_07fd7236 ON UmUser(Status, SpCode);\r\nCREATE INDEX UmUser_idx_188647fd ON UmUser(NasPortId, FirsLogin);\r\nCREATE INDEX UmUser_idx_49fb733a ON UmUser(DeleteFromServer, SN DESC);\r\nCREATE INDEX UmUser_idx_9e0ac6a9 ON UmUser(BatchCardId);\r\nCREATE INDEX UmUser_idx_d5970c3d ON UmUser(FirsLogin);\r\nCREATE INDEX UmUser_idx_e07cf9bb ON UmUser(DeleteFromServer, BatchCardId);\r\nCREATE INDEX UmUser_idx_e7c209d4 ON UmUser(NasPortId);\r\nCREATE INDEX UmUser_idx_f28dcfa3 ON UmUser(DeleteFromServer, BatchCardId, Status);\r\nCOMMIT;\r\n";
                //using (var con = new SQLiteConnection(utils.Get_LocalDB_ConnectionString(conn.FileName)))
                //{
                //    try
                //    {
                //        con.Open();
                //        //var sqLiteTransaction = con.BeginTransaction();
                //        int effectRows = con.Execute(ScriptDB);
                //        //sqLiteTransaction.Commit();
                //    }
                //    catch (Exception ex) { MessageBox.Show("Check_LocalDB_File \n" + ex.Message); }
                //}
            }
            return false;
        }

        public void Check_ArchiveDB_File()
        {
            string folderDb = $"{utils.Get_Database_Directory()}";
            string Archivefile = $"{folderDb}\\CardsArchive.db";

            if (!File.Exists(Archivefile))
            {
                string ScriptDB = "BEGIN TRANSACTION;\r\nCREATE TABLE IF NOT EXISTS \"BatchArtchive\" (\r\n\t\"Id\"\tINTEGER NOT NULL,\r\n\t\"BatchNumber\"\tINTEGER NOT NULL,\r\n\t\"ProfileName\"\tTEXT,\r\n\t\"AddedDate\"\tTEXT,\r\n\t\"Count\"\tINTEGER,\r\n\t\"Sn_from\"\tINTEGER,\r\n\t\"Sn_to\"\tINTEGER,\r\n\t\"Count_Page\"\tINTEGER DEFAULT (0),\r\n\t\"Count_waiting\"\tINTEGER DEFAULT (0),\r\n\t\"Count_active\"\tINTEGER DEFAULT (0),\r\n\t\"Count_DeleteFormArchive\"\tINTEGER DEFAULT (0),\r\n\t\"Rb\"\tTEXT,\r\n\tPRIMARY KEY(\"Id\" AUTOINCREMENT)\r\n);\r\nCREATE TABLE IF NOT EXISTS \"CardsArtchive\" (\r\n\t\"Id\"\tINTEGER NOT NULL,\r\n\t\"SN\"\tINTEGER NOT NULL,\r\n\t\"UserName\"\tTEXT NOT NULL,\r\n\t\"Password\"\tTEXT,\r\n\t\"ProfileName\"\tTEXT,\r\n\t\"BatchCardId\"\tINTEGER,\r\n\t\"PageNumber\"\tINTEGER,\r\n\t\"RegDate\"\tTEXT,\r\n\t\"Status\"\tINTEGER DEFAULT (0),\r\n\t\"Rb\"\tTEXT,\r\n\tPRIMARY KEY(\"Id\" AUTOINCREMENT)\r\n);\r\nCREATE INDEX IF NOT EXISTS \"idx_CardsArtchive_UserName\" ON \"CardsArtchive\" (\r\n\t\"UserName\"\r\n);\r\nCOMMIT;\r\n";
                using (var con = new SQLiteConnection(utils.Get_CardsArchive_ConnectionString()))  
                {
                    try
                    {
                        con.Open();
                        //var sqLiteTransaction = con.BeginTransaction();
                        int effectRows = con.Execute(ScriptDB);
                        //sqLiteTransaction.Commit();
                    }
                    catch (Exception ex) { MessageBox.Show("Check_ArchiveDB_File \n" + ex.Message); }
                }
            }
        }
        public void Check_Accounting_File()
        {
            string folderDb = $"{utils.Get_Database_Directory()}";
            string Archivefile = $"{folderDb}\\Accounting.db";

            if (!File.Exists(Archivefile))
            {
                string ScriptDB = "BEGIN TRANSACTION;\r\nCREATE TABLE IF NOT EXISTS \"Accounts\" (\r\n\t\"Id\"\tINTEGER,\r\n\t\"Code\"\tINTEGER,\r\n\t\"Name\"\tTEXT,\r\n\t\"NameEnglish\"\tTEXT,\r\n\t\"Type\"\tINTEGER,\r\n\t\"Nature\"\tINTEGER,\r\n\t\"ParentId\"\tTEXT,\r\n\t\"Level\"\tINTEGER,\r\n\t\"IsParent\"\tINTEGER,\r\n\t\"IsActive\"\tINTEGER,\r\n\t\"Balance\"\tINTEGER,\r\n\t\"Description\"\tTEXT,\r\n\t\"CreatedDate\"\tTEXT,\r\n\t\"UpdatedDate\"\tTEXT\r\n, \"Rb\"\tTEXT);\r\nCREATE TABLE IF NOT EXISTS \"Company\" (\r\n\t\"Id\"\tINTEGER NOT NULL UNIQUE,\r\n\t\"Code\"\tTEXT,\r\n\t\"Name\"\tTEXT,\r\n\t\"Display_name\"\tTEXT,\r\n\t\"Active\"\tINTEGER DEFAULT 1,\r\n\t\"Description\"\tTEXT,\r\n\t\"Image\"\tTEXT,\r\n\t\"Address\"\tTEXT,\r\n\t\"Phone\"\tTEXT,\r\n\t\"Email\"\tTEXT,\r\n\t\"Currency_name\"\tTEXT,\r\n\t\"Currency_symbol\"\tTEXT,\r\n\t\"Currency_rounding\"\tTEXT,\r\n\t\"Currency_decimal_places\"\tTEXT,\r\n\t\"Created\"\tTEXT,\r\n\t\"Updated\"\tTEXT,\r\n\t\"Partner_type\"\tINTEGER,\r\n\t\"Rb\"\tTEXT,\r\n\t\"Name_eng\"\tTEXT,\r\n\t\"Mobail\"\tTEXT,\r\n\tPRIMARY KEY(\"Id\" AUTOINCREMENT)\r\n);\r\nCREATE TABLE IF NOT EXISTS JournalEntries (\r\n                    Id INTEGER PRIMARY KEY AUTOINCREMENT,\r\n                    EntryNumber TEXT NOT NULL UNIQUE,\r\n                    Date DATETIME NOT NULL,\r\n                    Description TEXT NOT NULL,\r\n                    Reference TEXT,\r\n                    Type INTEGER NOT NULL,\r\n                    Status INTEGER DEFAULT 1,\r\n                    TotalDebit DECIMAL(15,2) DEFAULT 0,\r\n                    TotalCredit DECIMAL(15,2) DEFAULT 0,\r\n                    SourceId INTEGER,\r\n                    SourceType TEXT,\r\n                    CreatedBy TEXT,\r\n                    CreatedDate DATETIME DEFAULT CURRENT_TIMESTAMP,\r\n                    UpdatedBy TEXT,\r\n                    UpdatedDate DATETIME,\r\n                    ApprovedBy TEXT,\r\n                    ApprovedDate DATETIME,\r\n                    Notes TEXT\r\n                );\r\nCREATE TABLE IF NOT EXISTS JournalEntryDetails (\r\n                    Id INTEGER PRIMARY KEY AUTOINCREMENT,\r\n                    JournalEntryId INTEGER NOT NULL,\r\n                    AccountId INTEGER NOT NULL,\r\n                    DebitAmount DECIMAL(15,2) DEFAULT 0,\r\n                    CreditAmount DECIMAL(15,2) DEFAULT 0,\r\n                    Description TEXT,\r\n                    LineNumber INTEGER DEFAULT 1,\r\n                    FOREIGN KEY (JournalEntryId) REFERENCES JournalEntries(Id) ON DELETE CASCADE,\r\n                    FOREIGN KEY (AccountId) REFERENCES \"Accountss\"(Id)\r\n                );\r\nCREATE TABLE IF NOT EXISTS Partners (\r\n                    Id INTEGER PRIMARY KEY AUTOINCREMENT,\r\n                    Code TEXT NOT NULL UNIQUE,\r\n                    Name TEXT NOT NULL,\r\n                    NameEn TEXT,\r\n                    PartnerType INTEGER NOT NULL,\r\n                    Email TEXT,\r\n                    Phone TEXT,\r\n                    Mobile TEXT,\r\n                    Fax TEXT,\r\n                    Website TEXT,\r\n                    Address TEXT,\r\n                    City TEXT,\r\n                    Country TEXT,\r\n                    PostalCode TEXT,\r\n                    TaxNumber TEXT,\r\n                    CommercialRegister TEXT,\r\n                    BankName TEXT,\r\n                    BankAccount TEXT,\r\n                    IBAN TEXT,\r\n                    CreditLimit DECIMAL(15,2) DEFAULT 0,\r\n                    PaymentTerms INTEGER DEFAULT 0,\r\n                    Discount DECIMAL(5,2) DEFAULT 0,\r\n                    IsActive BOOLEAN DEFAULT 1,\r\n                    Notes TEXT,\r\n                    Tags TEXT,\r\n                    ContactPerson TEXT,\r\n                    ContactTitle TEXT,\r\n                    Department TEXT,\r\n                    Position TEXT,\r\n                    Salary DECIMAL(15,2) DEFAULT 0,\r\n                    HireDate DATETIME,\r\n                    BirthDate DATETIME,\r\n                    NationalId TEXT,\r\n                    PassportNumber TEXT,\r\n                    EmergencyContact TEXT,\r\n                    EmergencyPhone TEXT,\r\n                    AccountId INTEGER,\r\n                    ParentId INTEGER,\r\n                    ImagePath TEXT,\r\n                    AttachmentsPath TEXT,\r\n                    CreatedBy TEXT DEFAULT 'System',\r\n                    CreatedDate DATETIME DEFAULT CURRENT_TIMESTAMP,\r\n                    UpdatedDate DATETIME,\r\n                    UpdatedBy TEXT,\r\n                    IsDeleted BOOLEAN DEFAULT 0,\r\n                    DeletedDate DATETIME,\r\n                    DeletedBy TEXT,\r\n                    FOREIGN KEY (AccountId) REFERENCES \"Accountss\"(Id),\r\n                    FOREIGN KEY (ParentId) REFERENCES Partners(Id)\r\n                );\r\nCREATE TABLE IF NOT EXISTS Products (\r\n                    Id INTEGER PRIMARY KEY AUTOINCREMENT,\r\n                    Name TEXT NOT NULL,\r\n                    Price DECIMAL NOT NULL,\r\n                    Stock INTEGER DEFAULT 0,\r\n                    Description TEXT,\r\n                    CreatedDate DATETIME DEFAULT CURRENT_TIMESTAMP,\r\n\t\t\t\t\tRb TEXT\r\n                );\r\nCOMMIT;\r\n";
                using (var con = new SQLiteConnection(utils.Get_Accounting_ConnectionString()))
                {
                    try
                    {
                        con.Open();
                        //var sqLiteTransaction = con.BeginTransaction();
                        int effectRows = con.Execute(ScriptDB);
                        //sqLiteTransaction.Commit();
                    }
                    catch (Exception ex) { MessageBox.Show("Check_ArchiveDB_File \n" + ex.Message); }
                }
            }
        }

        public void Check_CardsArchive_File()
        {
            string folderDb = $"{utils.Get_Database_Directory()}";
            string Smartfile = $"{folderDb}\\CardsArchive.db";

            if (!File.Exists(Smartfile))
            {
                string ScriptDB = @"BEGIN TRANSACTION;
                        CREATE TABLE IF NOT EXISTS ""BatchArtchive"" (
	                        ""Id""	INTEGER NOT NULL,
	                        ""BatchNumber""	INTEGER NOT NULL,
	                        ""ProfileName""	TEXT,
	                        ""AddedDate""	TEXT,
	                        ""Count""	INTEGER,
	                        ""Sn_from""	INTEGER,
	                        ""Sn_to""	INTEGER,
	                        ""Count_Page""	INTEGER DEFAULT (0),
	                        ""Count_waiting""	INTEGER DEFAULT (0),
	                        ""Count_active""	INTEGER DEFAULT (0),
	                        ""Count_DeleteFormArchive""	INTEGER DEFAULT (0),
	                        ""Rb""	TEXT,
	                        PRIMARY KEY(""Id"" AUTOINCREMENT)
                        );
                        CREATE TABLE IF NOT EXISTS ""CardsArtchive"" (
	                        ""Id""	INTEGER NOT NULL,
	                        ""SN""	INTEGER NOT NULL,
	                        ""UserName""	TEXT NOT NULL,
	                        ""Password""	TEXT,
	                        ""ProfileName""	TEXT,
	                        ""BatchCardId""	INTEGER,
	                        ""PageNumber""	INTEGER,
	                        ""RegDate""	TEXT,
	                        ""Status""	INTEGER DEFAULT (0),
	                        ""Rb""	TEXT,
	                        PRIMARY KEY(""Id"" AUTOINCREMENT)
                        );
                        CREATE INDEX IF NOT EXISTS ""idx_CardsArtchive_UserName"" ON ""CardsArtchive"" (
	                        ""UserName""
                        );
                        COMMIT;
";
                using (var con = new SQLiteConnection(utils.Get_CardsArchive_ConnectionString()))
                {
                    try
                    {
                        con.Open();
                        //var sqLiteTransaction = con.BeginTransaction();
                        int effectRows = con.Execute(ScriptDB);
                        //sqLiteTransaction.Commit();
                    
                    }
                    catch (Exception ex) { MessageBox.Show("Check_SmartDB_File\n" + ex.Message); }
                }
            }
        }

        private void save_new_sqlite_database()
        {
            clss_CreateNewDatabase clss_CreateNewDatabase = new clss_CreateNewDatabase();
            Smart_DataAccess smart = new Smart_DataAccess();
            List<Connections_Db> listconnections = smart.Load<Connections_Db>($"select * from Connections_Db");
            //List<Connections_Db> listconnections = Sql_DataAccess.Get_List_Connections_Db();
            Connections_Db db = new Connections_Db();

            db.Default = Convert.ToInt32(CheckBox_default.Check);
            db.Default = ((listconnections.Count() == 0) ? 1 : db.Default);
            db.Type = comboBox2.Text;
            db.Connection_string = GetConnectionString();
            db.Name = txt_name.Text;
            db.Comment = "";
            db.Soft_id = Global_Variable.Mk_resources.RB_Soft_id;
            db.Mk_code = Global_Variable.Mk_resources.RB_code;
            db.Mk_sn = Global_Variable.Mk_resources.RB_SN;

            if (comboBox2.Text == "SQLlite")
            {
                db.FileName = Path.GetFileName(grid.Rows[0].Cells[1].Value?.ToString());
                db.LocalDB_path = (grid.Rows[0].Cells[1].Value?.ToString());
            }
            if (comboBox2.Text == "SQL Server Compact")
            {
                db.FileName = Path.GetFileName(grid.Rows[0].Cells[1].Value?.ToString());
                db.LocalDB_path = (grid.Rows[0].Cells[1].Value?.ToString());
            }
            
            if (curentDb != null)
            {
                //========== if null mean  edit connection_Db ============
                if (Sql_DataAccess.Add_Edit_Connection_string(db, true, true ? db.Default == 1 : false))
                {
                    Close();
                }
            }
            else
            {
                if (db != null)
                {
                    if (comboBox2.Text == "SQLlite")
                        clss_CreateNewDatabase.create_default_db(db);

                    if (comboBox2.Text == "SQL Server")
                        New_SQLServer_Connection();

                    if (comboBox2.Text == "SQL Server Compact")
                        save_new_sqlServer_database();

                    if (comboBox2.Text == "MySql" || comboBox2.Text == "MariaDb")
                    {
                        db = New_MySql_Database(db);
                        if (db.FileName != null)
                        {
                            clss_CreateNewDatabase.create_Schema_Table_db(db);
                            Sql_DataAccess.Add_Edit_Connection_string(db, false, CheckBox_default.Check);
                        }
                    }
                     

                    //if (comboBox2.Text == "PostgreSql")
                    //    save_new_sqlServer_database();
                    //clss_CreateNewDatabase.create_Schema_Table_db(db);
                    //if (clss_CreateNewDatabase.create_default_db(db))
                    //{
                    //    if (Sql_DataAccess.Add_Edit_Connection_string(db, false, true ? db.Default == 1 : false))
                    //    {
                    //        RJMessageBox.Show("تم انشاء قاعد البيانات بنجاح");
                    //    }
                    //}

                }
            }


        }
        private void save_new_sqlServer_database()
        {
            Smart_DataAccess smart_db = new Smart_DataAccess();
            List<Connections_Db> listconnections = smart_db.Load<Connections_Db>($"select * from Connections_Db");
            Connections_Db db = new Connections_Db();

            db.Default = Convert.ToInt32(CheckBox_default.Check);
            db.Default = ((listconnections.Count() == 0) ? 1 : db.Default);
            db.Type = comboBox2.Text;
            db.Connection_string = GetConnectionString();
            db.Name = txt_name.Text;
            db.Comment = "";
            db.Soft_id = Global_Variable.Mk_resources.RB_Soft_id;
            db.Mk_code = Global_Variable.Mk_resources.RB_code;
            db.Mk_sn = Global_Variable.Mk_resources.RB_SN;


            if (curentDb != null)
            {
                if (Sql_DataAccess.Add_Edit_Connection_string(db, true, true ? db.Default == 1 : false))
                {
                    Close();
                }
            }
            else
            {
                if (db != null)
                {
                    clss_CreateNewDatabase clss_CreateNewDatabase = new clss_CreateNewDatabase();
                    if (clss_CreateNewDatabase.create_Schema_Table_db(db))
                    {
                        if (Sql_DataAccess.Add_Edit_Connection_string(db, false, true ? db.Default == 1 : false))
                        {
                            RJMessageBox.Show("تم انشاء قاعد البيانات بنجاح");
                            //RJMessageBox.Show("database created.");
                        }
                    }

                    //using (var con = Sql_DataAccess.GetConnection(db.Connection_string, db.Type))
                    //{
                    //    try
                    //    {
                    //        con.Open();
                    //        con.CreateTableIfNotExists<BatchCard>();
                    //        con.CreateTableIfNotExists<SellingPoint>();
                    //        con.CreateTableIfNotExists<HSUser>();
                    //        con.CreateTableIfNotExists<UmUser>();
                    //        con.CreateTableIfNotExists<UmPyment>();
                    //        con.CreateTableIfNotExists<UmSession>();
                    //        RJMessageBox.Show("تم انشاء قاعد البيانات بنجاح");
                    //        //status = true;
                    //    }
                    //    catch (Exception ex) { RJMessageBox.Show(ex.Message); }
                    //}
                }
            }


        }

        public Connections_Db Create_New_localDB(Connections_Db conn = null)
        {
            string pathNew = Get_Next_Name_DB();
            var fileName = Path.GetFileName(pathNew);
           
            //string folder = Directory.GetCurrentDirectory() + "\\db";
            //string new_Connection_string = "Data Source=db\\" + fileName + "; Version = 3; New = True; Compress = True;";

            //string connection_string = "Data Source=" + AppContext.BaseDirectory + fileName+"; ";
            //string db_connection_string_path = AppContext.BaseDirectory + "Smart.db";


            conn.FileName = fileName;
            conn.Connection_string = utils.Get_LocalDB_ConnectionString(fileName);
            //conn.Connection_string = $"Data Source={Directory.GetParent(Application.StartupPath)}\\dbs\\{fileName};";
            //conn.Connection_string = $@"Data Source=db\{fileName};";
            //conn.Connection_string = new_Connection_string;
            conn.LocalDB_path = $"{Directory.GetParent(Application.StartupPath)}\\dbs\\{fileName}";
            //conn.LocalDB_path = "dbs\\" + fileName;
            conn.Soft_id = Global_Variable.Mk_resources.RB_Soft_id;
            conn.Mk_code = Global_Variable.Mk_resources.RB_code;
            conn.Mk_sn = Global_Variable.Mk_resources.RB_SN;
            conn.Type = "SQLlite";
            conn.Name = "SQLlite Connection";

            Sql_DataAccess.connection_string = conn.Connection_string;
            Sql_DataAccess.DefualtDBType = conn.Type;
            Sql_DataAccess.username_db = conn.Username_db;
            Sql_DataAccess.password_db = conn.Password_db;
            Sql_DataAccess.db_File_Name = conn.FileName;



            //clss_CreateNewDatabase ff = new clss_CreateNewDatabase();
            //ff.create_default_db(conn);


            Check_LocalDB_File_byScript(conn);


            Global_Variable.Mk_Router.localDB_path = conn.LocalDB_path;
            Global_Variable.Mk_Router.localDB_fileName = conn.FileName;
            Global_Variable.LocalPathDB = Global_Variable.Mk_Router.localDB_path;
            Global_Variable.LocalPathDB = Global_Variable.Mk_Router.localDB_path;

            return conn;
        }

        private void GenerateTable<T>()
        {
            List<TableClass> tables = new List<TableClass>();
            Type type = typeof(T);
            TableClass tc = new TableClass(type);
            tables.Add(tc);
            tables.Add(new TableClass(type));

            // Create SQL for each table
            foreach (TableClass table in tables)
            {
                string t = table.CreateTableScript().ToString();
            }








            // Total Hacked way to find FK relationships! Too lazy to fix right now
            //foreach (TableClass table in tables)
            //{
            //    foreach (KeyValuePair<String, Type> field in table.Fields)
            //    {
            //        foreach (TableClass t2 in tables)
            //        {
            //            if (field.Value.Name == t2.ClassName)
            //            {
            //                // We have a FK Relationship!
            //                Console.WriteLine("GO");
            //                Console.WriteLine("ALTER TABLE " + table.ClassName + " WITH NOCHECK");
            //                Console.WriteLine("ADD CONSTRAINT FK_" + field.Key + " FOREIGN KEY (" + field.Key + ") REFERENCES " + t2.ClassName + "(ID)");
            //                Console.WriteLine("GO");

            //            }
            //        }
            //    }
            //}
        }

        #endregion

        private void comboBox2_OnSelectedIndexChanged(object sender, EventArgs e)
        {
            if (firstload)
                return;
           
            Dictionary<string, string> dictionary = new Dictionary<string, string>();
            dictionary.Add("SQLlite", "Data Source=localDB.db");
            dictionary.Add("SQL Server", "Server=;Database=;Trusted_Connection=True;");
            dictionary.Add("SQL Server Compact", "Data Source=MyData.sdf;Persist Security Info=False;");
            dictionary.Add("MySql", "Server=localhost;Database=;Uid=root;Pwd=;SslMode=none;");
            dictionary.Add("MariaDb", "Server=localhost;Database=;Uid=root;Pwd=;SslMode=none;");
            dictionary.Add("PostgreSql", "Server=127.0.0.1;Port=5432;Database=;User Id=;Password=;");
            dictionary.Add("Oracle", "Data Source=;Integrated Security=yes;");
            dictionary.Add("Firebird", "User=SYSDBA;Password=masterkey;Database=SampleDatabase.fdb;DataSource=localhost;Port=3050;Dialect=3;Charset=NONE;Role=;Connection lifetime=15;Pooling=true;MinPoolSize=0;MaxPoolSize=50;Packet Size=8192;ServerType = 0; ");

            var f = dictionary[comboBox2.Text.ToString()];
            RenderCS(f);

            if (comboBox2.Text == "SQLlite")
                New_Sqlite_Connection();

            if (comboBox2.Text == "SQL Server")
                New_SQLServer_Connection();

            if (comboBox2.Text == "SQL Server Compact")
                New_SQLServer_Connection();

            if (comboBox2.Text == "MySql")
                New_MySql_Connection();

            if (comboBox2.Text == "MariaDb")
                New_MySql_Connection();

            //if (comboBox2.Text == "PostgreSql")
            //    New_Sql_Connection();

            //if (comboBox2.Text == "MariaDb")
            //    New_Sql_Connection();

            txt_name.Text = comboBox2.Text + "  Connection";
        }

        private void New_Sqlite_Connection()
        {
            string folder = Directory.GetCurrentDirectory() + "\\db";
            string pathNew = Get_Next_Name_DB();
            string fileName = Path.GetFileName(pathNew);
            New_DB_Generator = fileName;
            string fileWithlocalPath="db\\" + fileName;

            string connection = "Data Source=" + fileWithlocalPath;
            // render on grid
            RenderCS(connection);
        }

        string New_DB_Generator="";

        private void New_MySql_Connection()
        {
            Random rnd = new Random();
            string dbName = Get_Next_Name_DB();
            string fileName = "Smart" + Path.GetFileNameWithoutExtension(dbName) + rnd.Next(100, 200);

            New_DB_Generator = fileName;
            string connString = "Server=localhost;Database=" + fileName + ";Uid=root;Pwd=;SslMode=none;";

            RenderCS(connString);

        }
        private Connections_Db New_MySql_Database(Connections_Db db)
         {
            //bool status = false;
            //string dbName = "";
            //string connString = "server=localhost;userid=root;password=";
            ////string connString = "Server=localhost;Database=" + fileName + ";Uid=root;Pwd=;SslMode=none;";
            //for (int i = 0;i<grid.Rows.Count;i++)
            //{
            //   if( grid.Rows[i].Cells[0].Value?.ToString().ToLower()== "database")
            //    {
            //        dbName = grid.Rows[i].Cells[1].Value?.ToString();
            //    }
            //}
            //try
            //{
            //    MySqlConnection conn = new MySqlConnection(connString);
            //    conn.Open();
            //    MySqlCommand cmd = new MySqlCommand("create database " + dbName, conn);
            //    cmd.ExecuteNonQuery();
            //    connString = "Server=localhost;Database=" + dbName + ";Uid=root;Pwd=;SslMode=none;";
            //    //RJMessageBox.Show("Database  created successfully", fileName);
            //    conn.Close();
            //    //status = true;
            //    db.Connection_string = connString;
            //    db.FileName = dbName;
            //}
            //catch (Exception ex) { }

            return db;
        }


        private void New_SQLServer_Connection()
        {
            string dbName = Get_Next_Name_DB();
            string fileName = "Smart" + Path.GetFileNameWithoutExtension(dbName);
            New_DB_Generator = fileName;
            string connString = "Server=localhost;Database=" + fileName + ";Uid=root;Pwd=;SslMode=none;";

            RenderCS(connString);

        }
        private void New_SQLServer_Database()
        {
            //string dbName = Get_Next_Name_DB();
            //string fileName = "Smart" + Path.GetFileNameWithoutExtension(dbName);
            //New_DB_Generator = fileName;
            //string connString = "Server=;Database=;Trusted_Connection=True";
            ////Server=;Database=;Trusted_Connection=True;
            //try
            //{
            //    MySqlConnection conn = new MySqlConnection(connString);
            //    conn.Open();
            //    MySqlCommand cmd = new MySqlCommand("create database " + fileName, conn);
            //    cmd.ExecuteNonQuery();
            //    connString = "Server=localhost;Database=" + fileName + ";Uid=root;Pwd=;SslMode=none;";
            //    RJMessageBox.Show("Database  created successfully", fileName);
            //    conn.Close();
            //}
            //catch (Exception ex) { }


        }


        private void CheckBox_default_CheckedChanged(object sender, EventArgs e)
        {
            if(firstload) return;
        }
    }

    //public static class CsParser
    //{
    //    public static Dictionary<string, string> Parse(string cs)
    //    {
    //        try
    //        {
    //            Dictionary<string, string> dictionary = new Dictionary<string, string>();
    //            string[] array = cs.Split(';');
    //            for (int i = 0; i < array.Length; i++)
    //            {
    //                string[] array2 = array[i].Trim().Split('=');
    //                if (array2.Length == 2)
    //                {
    //                    dictionary.Add(array2[0], array2[1]);
    //                }
    //            }

    //            return dictionary;
    //        }
    //        catch (Exception)
    //        {
    //            throw;
    //        }
    //    }
    //}
}
