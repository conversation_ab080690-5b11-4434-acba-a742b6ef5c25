using System;
using System.Drawing;
using System.Windows.Forms;
using SmartCreator.RJControls;

namespace SmartCreator.Forms.Security
{
    /// <summary>
    /// نموذج اختبار بسيط يعمل بالتأكيد
    /// </summary>
    public partial class Frm_SimpleTest : Form
    {
        #region Constructor
        public Frm_SimpleTest()
        {
            InitializeComponent();
        }
        #endregion

        #region Event Handlers
        private void btnTest1_Click(object sender, EventArgs e)
        {
            RJMessageBox.Show("تم النقر على زر الاختبار الأول!", "اختبار", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void btnTest2_Click(object sender, EventArgs e)
        {
            RJMessageBox.Show("تم النقر على زر الاختبار الثاني!", "اختبار", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void btnClose_Click(object sender, EventArgs e)
        {
            this.Close();
        }
        #endregion
    }
}
