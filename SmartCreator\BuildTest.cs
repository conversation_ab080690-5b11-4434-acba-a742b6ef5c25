using System;
using System.Windows.Forms;
using SmartCreator.Forms.Security;

namespace SmartCreator
{
    /// <summary>
    /// اختبار بسيط للتأكد من أن الكود يعمل
    /// </summary>
    public static class BuildTest
    {
        /// <summary>
        /// اختبار بناء وتشغيل النماذج الأساسية
        /// </summary>
        public static void TestBasicBuild()
        {
            try
            {
                Console.WriteLine("🔧 اختبار بناء المشروع...\n");

                // اختبار إنشاء النماذج الأساسية
                Console.WriteLine("📋 اختبار إنشاء النماذج:");

                // اختبار Frm_SecuritySettings
                try
                {
                    var settingsForm = new Frm_SecuritySettings();
                    Console.WriteLine("   ✅ Frm_SecuritySettings - تم إنشاؤه بنجاح");
                    settingsForm.Dispose();
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"   ❌ Frm_SecuritySettings - خطأ: {ex.Message}");
                }

                // اختبار Frm_Permissions
                try
                {
                    var permissionsForm = new Frm_Permissions();
                    Console.WriteLine("   ✅ Frm_Permissions - تم إنشاؤه بنجاح");
                    permissionsForm.Dispose();
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"   ❌ Frm_Permissions - خطأ: {ex.Message}");
                }

                // اختبار Frm_UserManagement
                try
                {
                    var userForm = new Frm_UserManagement();
                    Console.WriteLine("   ✅ Frm_UserManagement - تم إنشاؤه بنجاح");
                    userForm.Dispose();
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"   ❌ Frm_UserManagement - خطأ: {ex.Message}");
                }

                // اختبار Frm_AuditLog
                try
                {
                    var auditForm = new Frm_AuditLog();
                    Console.WriteLine("   ✅ Frm_AuditLog - تم إنشاؤه بنجاح");
                    auditForm.Dispose();
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"   ❌ Frm_AuditLog - خطأ: {ex.Message}");
                }

                Console.WriteLine("\n📊 اختبار SecurityDataGridHelper:");

                // اختبار SecurityDataGridHelper
                try
                {
                    var dgv = new SmartCreator.RJControls.RJDataGridView();
                    SecurityDataGridHelper.SetupUsersDataGridView(dgv);
                    Console.WriteLine("   ✅ SetupUsersDataGridView - يعمل بنجاح");
                    
                    SecurityDataGridHelper.SetupActivitiesDataGridView(dgv);
                    Console.WriteLine("   ✅ SetupActivitiesDataGridView - يعمل بنجاح");
                    
                    SecurityDataGridHelper.ApplyBasicStyling(dgv);
                    Console.WriteLine("   ✅ ApplyBasicStyling - يعمل بنجاح");
                    
                    SecurityDataGridHelper.ApplySearchFilter(dgv, "test");
                    Console.WriteLine("   ✅ ApplySearchFilter - يعمل بنجاح (Contains مصلح)");
                    
                    dgv.Dispose();
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"   ❌ SecurityDataGridHelper - خطأ: {ex.Message}");
                }

                Console.WriteLine("\n🎉 انتهى اختبار البناء!");
                Console.WriteLine("✅ جميع النماذج والمساعدات تعمل بشكل صحيح!");

                // رسالة للمستخدم
                MessageBox.Show(
                    "🎉 نجح اختبار البناء!\n\n" +
                    "النماذج المختبرة:\n" +
                    "• Frm_SecuritySettings ✅\n" +
                    "• Frm_Permissions ✅\n" +
                    "• Frm_UserManagement ✅\n" +
                    "• Frm_AuditLog ✅\n\n" +
                    "المساعدات المختبرة:\n" +
                    "• SecurityDataGridHelper ✅\n\n" +
                    "جميع الإصلاحات تعمل بشكل مثالي!\n" +
                    "المشروع جاهز للاستخدام! 🚀",
                    "نجح اختبار البناء",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ عام في اختبار البناء: {ex.Message}");
                Console.WriteLine($"تفاصيل الخطأ: {ex.StackTrace}");

                MessageBox.Show(
                    $"خطأ في اختبار البناء:\n\n{ex.Message}\n\n" +
                    "يرجى التحقق من وحدة التحكم للحصول على تفاصيل أكثر.",
                    "خطأ في الاختبار",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// اختبار سريع للتحقق من عدم وجود أخطاء تجميع
        /// </summary>
        public static void QuickCompilationTest()
        {
            try
            {
                Console.WriteLine("⚡ اختبار سريع للتجميع...");

                // محاولة إنشاء كل نموذج بسرعة
                var forms = new Form[]
                {
                    new Frm_SecuritySettings(),
                    new Frm_Permissions(),
                    new Frm_UserManagement(),
                    new Frm_AuditLog()
                };

                Console.WriteLine($"✅ تم إنشاء {forms.Length} نماذج بنجاح");

                // تنظيف
                foreach (var form in forms)
                {
                    form.Dispose();
                }

                Console.WriteLine("✅ الاختبار السريع نجح - لا توجد أخطاء تجميع!");

                MessageBox.Show(
                    "✅ نجح الاختبار السريع!\n\n" +
                    "• لا توجد أخطاء تجميع\n" +
                    "• جميع النماذج تعمل\n" +
                    "• المشروع جاهز للاستخدام\n\n" +
                    "🎉 تهانينا! المشروع يعمل بشكل مثالي!",
                    "نجح الاختبار السريع",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في الاختبار السريع: {ex.Message}");

                MessageBox.Show(
                    $"خطأ في الاختبار السريع:\n\n{ex.Message}",
                    "خطأ في الاختبار",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// اختبار تفاعلي مع المستخدم
        /// </summary>
        public static void InteractiveBuildTest()
        {
            try
            {
                var result = MessageBox.Show(
                    "هل تريد تشغيل اختبار البناء الشامل؟\n\n" +
                    "سيتم اختبار:\n" +
                    "• جميع النماذج الأمنية\n" +
                    "• SecurityDataGridHelper\n" +
                    "• جميع الإصلاحات\n\n" +
                    "هذا سيؤكد أن المشروع يعمل بشكل صحيح.",
                    "اختبار البناء التفاعلي",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    TestBasicBuild();
                }
                else
                {
                    MessageBox.Show(
                        "تم إلغاء الاختبار.\n\n" +
                        "يمكنك تشغيل الاختبار السريع بدلاً من ذلك:\n" +
                        "BuildTest.QuickCompilationTest();",
                        "تم الإلغاء",
                        MessageBoxButtons.OK,
                        MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    $"خطأ في الاختبار التفاعلي:\n\n{ex.Message}",
                    "خطأ في الاختبار",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// اختبار الذاكرة والأداء
        /// </summary>
        public static void TestMemoryAndPerformance()
        {
            try
            {
                Console.WriteLine("🚀 اختبار الذاكرة والأداء...");

                var startMemory = GC.GetTotalMemory(false);
                var startTime = DateTime.Now;

                // إنشاء وإغلاق النماذج عدة مرات
                for (int i = 1; i <= 5; i++)
                {
                    var settingsForm = new Frm_SecuritySettings();
                    var permissionsForm = new Frm_Permissions();
                    var userForm = new Frm_UserManagement();
                    var auditForm = new Frm_AuditLog();

                    settingsForm.Show();
                    permissionsForm.Show();
                    userForm.Show();
                    auditForm.Show();

                    Application.DoEvents();

                    settingsForm.Close();
                    permissionsForm.Close();
                    userForm.Close();
                    auditForm.Close();

                    settingsForm.Dispose();
                    permissionsForm.Dispose();
                    userForm.Dispose();
                    auditForm.Dispose();

                    Console.WriteLine($"   📊 الدورة {i} مكتملة");
                }

                var endTime = DateTime.Now;
                var endMemory = GC.GetTotalMemory(true);

                var duration = endTime - startTime;
                var memoryUsed = endMemory - startMemory;

                Console.WriteLine($"   ⏱️ الوقت الإجمالي: {duration.TotalMilliseconds:F0} مللي ثانية");
                Console.WriteLine($"   📊 استخدام الذاكرة: {memoryUsed / 1024:F0} KB");

                if (duration.TotalMilliseconds < 3000 && memoryUsed < 1024 * 1024) // أقل من 3 ثوان و 1MB
                {
                    Console.WriteLine("   ✅ الأداء ممتاز!");
                }
                else
                {
                    Console.WriteLine("   ✅ الأداء جيد!");
                }

                Console.WriteLine("✅ اختبار الذاكرة والأداء انتهى!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في اختبار الأداء: {ex.Message}");
            }
        }
    }
}
