﻿using SmartCreator.Data;
using SmartCreator.Entities.Accounts;
using SmartCreator.Entities.UserManager;
using SmartCreator.Models;
using SmartCreator.Properties;
using SmartCreator.Settings;
using SmartCreator.Utils;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace SmartCreator.Forms.Accounting
{
    public partial class Form_UoM : RJForms.RJChildForm
    {
        Smart_DataAccess smart_DataAccess = null;
        ProductUoM productUoM = null;
        public Form_UoM()
        {
            InitializeComponent();
            smart_DataAccess = new Smart_DataAccess();
            this.Text = "وحدات القياس";

            if (UIAppearance.DGV_RTL == false)
            {
                dgv.RightToLeft = RightToLeft.No;
            }
            utils utils1 = new utils();
            utils1.Control_textSize1(this);

            this.Text = "Unit";
            if (UIAppearance.Language_ar)
            {
                this.Text = "وحدات القياس";
                //System.Drawing.Font title_font = btnRefresh.Font = Program.GetCustomFont(Resources.DroidSansArabic, 11, FontStyle.Bold);
                rjLabel5.Font  = Program.GetCustomFont(Resources.DroidSansArabic, 9, FontStyle.Regular);

                dgv.AllowUserToOrderColumns = true;
                dgv.ColumnHeadersDefaultCellStyle.Font = Program.GetCustomFont(Resources.DroidSansArabic, 9f, FontStyle.Regular);
                dgv.ColumnHeadersDefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
                dgv.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;

            }
            utils utils = new utils();
            utils.Control_textSize1(this);
        }

        private void Form_UoM_Load(object sender, EventArgs e)
        {
            getData();
        }

        private void getData()
        {
            try
            {
                Smart_DataAccess dataAccess = new Smart_DataAccess();
                var sp = dataAccess.Load<Entities.Accounts.ProductUoM>($"select * from ProductUoM where  Rb='{Global_Variable.Mk_resources.RB_SN}' ");
                dgv.DataSource = sp;

                //try { dgv.Columns["Id"].Visible = false; } catch { }
                try { dgv.Columns["Rb"].Visible = false; } catch { }
            }
            catch (Exception ex) { RJMessageBox.Show(ex.Message); }
        }
        public  bool add = false;   
        public  bool succes = false;
        private void btnAdd_Click(object sender, EventArgs e)
        {
            add = true;
            txt_Name.Enabled = true;
            btnCancel.Visible = true;
            btn_Save.Visible = true;
            btnEdit.Visible = false;
            txt_Name.Enabled = true;
            //addEdit();

        }

        private void addEdit()
        {
            try
            {
                Smart_DataAccess smart_DataAccess = new Smart_DataAccess();
                if (add)
                {
                    long foundsp = smart_DataAccess.Get_int_FromDB($"SELECT COUNT(*) FROM ProductUoM where Name='{txt_Name.Text}'  and  Rb='{Global_Variable.Mk_resources.RB_SN}';");

                    if (foundsp > 0)
                    {
                        RJMessageBox.Show("  الاسم موجود مسبقا");
                        return;
                    }
                    if (txt_Name.Text == "")
                    {
                        RJMessageBox.Show("ادخل الاسم");
                        return;
                    }

                    ProductUoM sp = new ProductUoM();
                    sp.Name = txt_Name.Text;
                    sp.Rb = Global_Variable.Mk_resources.RB_SN;

                    lock (Smart_DataAccess.Lock_object)
                    {
                        List<string> Fields = new List<string>();
                        string[] aFields = { "Name", "Rb" };

                        Fields.AddRange(aFields);


                        int new_sp = smart_DataAccess.InsertTable(Fields, sp, "ProductUoM");
                        if (new_sp > 0)
                        {
                            //smart_DataAccess.InsertTable<SellingPoint>(Fields, sp);
                            RJMessageBox.Show("تمت عمليه الاضافة");
                            succes = true;
                            txt_Name.Text = "";
                            txt_Name.Enabled = false;
                            btnCancel.Visible = false;
                            btn_Save.Visible = false;
                            //this.Close();
                            getData();
                            return;
                        }
                        RJMessageBox.Show("خطاء");
                    }
                }
                else
                {
                    Smart_DataAccess sql_DataAccess = new Smart_DataAccess();
                    List<string> Fields = new List<string>();
                    string[] aFields = { "Name" };
                    Fields.AddRange(aFields);
                    lock (Smart_DataAccess.Lock_object)
                    {
                        var data = new Entities.Accounts.Product
                        {
                            Id = productUoM.Id,
                            Name = txt_Name.Text,
                            Rb=productUoM.Rb,
                        };
                        string sqlquery = UtilsSql.GetUpdateSql<Entities.Accounts.ProductUoM>("ProductUoM", Fields, $" where Id=@Id and  Rb='{Global_Variable.Mk_resources.RB_SN}'");
                        int r = sql_DataAccess.UpateTable(data, sqlquery);
                    }
                }
                succes = true;
            }
            catch(Exception ex) { MessageBox.Show(ex.Message); }
            btn_Save.Visible = false;
            btnEdit.Visible = false;
            btnCancel.Visible = false;
            getData();

            //this.Close();
        }

        private void btnEdit_Click(object sender, EventArgs e)
        {
            add = false;
            btn_Save.Visible = true;
            btnCancel.Visible = true;
            btnAdd.Visible=false;
            btnEdit.Visible = false;
            txt_Name.Enabled = true;
            //addEdit();

        }

        private void dgv_CellClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                foreach (DataGridViewRow row in dgv.SelectedRows)
                {
                    productUoM = ((ProductUoM)row.DataBoundItem);
                    txt_Name.Text = productUoM.Name;
                    btnEdit.Visible=true;
                    btnAdd.Visible = false;
                    btnCancel.Visible = false;
                    btn_Save.Visible = false;
                    //txt_Name.Enabled = true;
                }
            }
        }

        private void btn_Save_Click(object sender, EventArgs e)
        {
            addEdit();

        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            btnEdit.Visible=false;
            btn_Save.Visible=false;
            btnAdd.Visible=true;
            txt_Name.Enabled=false;
            btnCancel.Visible = false;
            txt_Name.Text = "";
        }

        private void btnRefresh_Click(object sender, EventArgs e)
        {
            getData();
        }

        private void btnDelete_Click(object sender, EventArgs e)
        {
            if (RJMessageBox.Show("هل انت متاكد من حذف وحدة القياس", "تحذير", MessageBoxButtons.YesNo, MessageBoxIcon.Warning) == DialogResult.Yes)
            {

                Smart_DataAccess smart_DataAccess = new Smart_DataAccess();
                smart_DataAccess.DeleteById<Entities.Accounts.Product>(dgv.CurrentRow.Cells["Id"].Value.ToString(), "ProductUoM");
                getData();
            }
        }
    }
}
