using System;
using System.Windows.Forms;
using SmartCreator.RJControls;
using SmartCreator.Settings;

namespace SmartCreator.Forms.Testing
{
    /// <summary>
    /// اختبار سريع لفتح النموذج الرئيسي مع أزرار القيود المحاسبية
    /// </summary>
    public static class Quick_MainForm_Test
    {
        /// <summary>
        /// فتح النموذج الرئيسي مباشرة للاختبار
        /// </summary>
        public static void OpenMainFormWithJournalButtons()
        {
            try
            {
                Console.WriteLine("🚀 فتح النموذج الرئيسي مع أزرار القيود المحاسبية...\n");

                // تهيئة الإعدادات
                UIAppearance.Theme = UITheme.Light;
                UIAppearance.Language_ar = true;

                // إعداد التطبيق
                Application.EnableVisualStyles();
                Application.SetCompatibleTextRenderingDefault(false);

                Console.WriteLine("1️⃣ إنشاء النموذج الرئيسي...");
                
                // إنشاء وفتح النموذج الرئيسي
                var mainForm = new MainForm();
                
                Console.WriteLine("2️⃣ عرض النموذج الرئيسي...");
                
                // عرض رسالة ترحيب
                RJMessageBox.Show(
                    "🎉 مرحباً بك في النموذج الرئيسي المحسن!\n\n" +
                    "✅ تم إضافة أزرار القيود المحاسبية المحسنة:\n\n" +
                    "📍 ابحث عن لوحة \"القيود المحاسبية المحسنة\"\n" +
                    "   في أعلى يسار النموذج\n\n" +
                    "🔘 الأزرار المتاحة:\n" +
                    "   • إدارة القيود المحاسبية\n" +
                    "   • إضافة قيد جديد\n" +
                    "   • اختيار حساب\n" +
                    "   • 🏆 الاختبار النهائي\n" +
                    "   • 🎯 حل CS2001 الدائم\n\n" +
                    "🎯 جرب النقر على الأزرار لاختبار الوظائف!\n\n" +
                    "✨ جميع النماذج متكاملة مع RJChildForm\n" +
                    "✨ تصميم احترافي ومنظم\n" +
                    "✨ دعم كامل للغة العربية\n\n" +
                    "استمتع بالتجربة! 🚀",
                    "النموذج الرئيسي جاهز",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Information);

                // عرض النموذج
                mainForm.Show();
                
                Console.WriteLine("3️⃣ تم عرض النموذج الرئيسي بنجاح");
                Console.WriteLine("\n🏆 النموذج الرئيسي جاهز مع أزرار القيود المحاسبية!");
                Console.WriteLine("🎯 اختبر الأزرار للتأكد من عملها بمثالية!");

                // تشغيل حلقة الرسائل
                Application.Run(mainForm);

            }
            catch (Exception ex)
            {
                Console.WriteLine($"\n❌ خطأ في فتح النموذج الرئيسي: {ex.Message}");
                RJMessageBox.Show(
                    $"❌ خطأ في فتح النموذج الرئيسي:\n\n{ex.Message}\n\n" +
                    "🔧 تأكد من:\n" +
                    "• وجود جميع الملفات المطلوبة\n" +
                    "• عدم وجود أخطاء compilation\n" +
                    "• صحة using statements\n" +
                    "• تهيئة قاعدة البيانات",
                    "خطأ في النموذج الرئيسي",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// اختبار سريع للأزرار فقط
        /// </summary>
        public static void QuickButtonsTest()
        {
            try
            {
                RJMessageBox.Show(
                    "⚡ اختبار سريع للأزرار\n\n" +
                    "سيتم اختبار كل زر بسرعة:\n\n" +
                    "1️⃣ إدارة القيود ← Frm_JournalEntries_Enhanced\n" +
                    "2️⃣ إضافة قيد ← Frm_AddEditJournalEntry_Enhanced\n" +
                    "3️⃣ اختيار حساب ← Frm_AccountSelector\n" +
                    "4️⃣ الاختبار النهائي ← CS2001_Fixed_Final_Test\n" +
                    "5️⃣ حل CS2001 ← CS2001_Permanently_Fixed_Test\n\n" +
                    "🎯 النتائج المتوقعة:\n" +
                    "✅ فتح النماذج بنجاح\n" +
                    "✅ لا توجد أخطاء\n" +
                    "✅ تصميم احترافي\n" +
                    "✅ وظائف كاملة\n\n" +
                    "ابدأ الاختبار؟",
                    "اختبار سريع",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question) == DialogResult.Yes ? 
                    OpenMainFormWithJournalButtons() : 
                    Console.WriteLine("تم إلغاء الاختبار السريع");
            }
            catch (Exception ex)
            {
                RJMessageBox.Show($"خطأ في الاختبار السريع: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// نقطة الدخول الرئيسية
        /// </summary>
        [STAThread]
        public static void Main()
        {
            OpenMainFormWithJournalButtons();
        }
    }
}
