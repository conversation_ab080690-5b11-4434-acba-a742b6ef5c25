using System;
using System.Linq;
using System.Windows.Forms;
using SmartCreator.Forms.Accounting;
using SmartCreator.Forms.Testing;
using SmartCreator.RJControls;
using SmartCreator.Service;
using SmartCreator.Services.Accounting;
using SmartCreator.Services.Security;
using FontAwesome.Sharp;

namespace SmartCreator.Forms.Accounting
{
    /// <summary>
    /// تكامل نماذج القيود المحاسبية مع النموذج الرئيسي
    /// </summary>
    public static class JournalEntries_MainForm_Integration
    {
        /// <summary>
        /// إضافة أزرار نماذج القيود إلى النموذج الرئيسي
        /// </summary>
        public static void AddJournalEntriesButtonsToMainForm(Form mainForm)
        {
            try
            {
                // البحث عن لوحة الأزرار في النموذج الرئيسي
                var buttonsPanel = FindButtonsPanel(mainForm);
                if (buttonsPanel == null)
                {
                    RJMessageBox.Show("لم يتم العثور على لوحة الأزرار في النموذج الرئيسي", "خطأ",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // إنشاء أزرار نماذج القيود
                CreateJournalEntriesButtons(buttonsPanel);

                RJMessageBox.Show(
                    "✅ تم إضافة أزرار نماذج القيود المحاسبية بنجاح!\n\n" +
                    "الأزرار المضافة:\n" +
                    "• إدارة القيود المحاسبية\n" +
                    "• إضافة قيد جديد\n" +
                    "• اختيار حساب\n\n" +
                    "جميع النماذج متكاملة مع RJChildForm",
                    "تكامل مكتمل",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                RJMessageBox.Show($"خطأ في إضافة الأزرار: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// البحث عن لوحة الأزرار في النموذج الرئيسي
        /// </summary>
        private static Panel FindButtonsPanel(Form mainForm)
        {
            // البحث عن لوحة بأسماء محتملة
            var possibleNames = new[] { "pnlButtons", "panelButtons", "pnlMenu", "panelMenu", "pnlSidebar" };

            foreach (var name in possibleNames)
            {
                var panel = mainForm.Controls.Find(name, true).FirstOrDefault() as Panel;
                if (panel != null) return panel;
            }

            // إذا لم توجد، إنشاء لوحة جديدة
            var newPanel = new Panel
            {
                Name = "pnlJournalEntriesButtons",
                Size = new System.Drawing.Size(200, 400),
                Location = new System.Drawing.Point(10, 10),
                BackColor = System.Drawing.Color.FromArgb(42, 46, 50)
            };

            mainForm.Controls.Add(newPanel);
            return newPanel;
        }

        /// <summary>
        /// إنشاء أزرار نماذج القيود
        /// </summary>
        private static void CreateJournalEntriesButtons(Panel buttonsPanel)
        {
            var buttonHeight = 50;
            var buttonWidth = 180;
            var spacing = 10;
            var currentY = 10;

            // زر إدارة القيود
            var btnManageEntries = CreateStyledButton(
                "إدارة القيود المحاسبية",
                IconChar.FileInvoice,
                new System.Drawing.Point(10, currentY),
                new System.Drawing.Size(buttonWidth, buttonHeight)
            );
            btnManageEntries.Click += BtnManageEntries_Click;
            buttonsPanel.Controls.Add(btnManageEntries);
            currentY += buttonHeight + spacing;

            // زر إضافة قيد جديد
            var btnAddEntry = CreateStyledButton(
                "إضافة قيد جديد",
                IconChar.Plus,
                new System.Drawing.Point(10, currentY),
                new System.Drawing.Size(buttonWidth, buttonHeight)
            );
            btnAddEntry.Click += BtnAddEntry_Click;
            buttonsPanel.Controls.Add(btnAddEntry);
            currentY += buttonHeight + spacing;

            // زر اختيار حساب
            var btnSelectAccount = CreateStyledButton(
                "اختيار حساب",
                IconChar.Search,
                new System.Drawing.Point(10, currentY),
                new System.Drawing.Size(buttonWidth, buttonHeight)
            );
            btnSelectAccount.Click += BtnSelectAccount_Click;
            buttonsPanel.Controls.Add(btnSelectAccount);
            currentY += buttonHeight + spacing;

            // زر الاختبار النهائي الشامل
            var btnFinalTest = CreateStyledButton(
                "🏆 الاختبار النهائي الشامل",
                IconChar.Trophy,
                new System.Drawing.Point(10, currentY),
                new System.Drawing.Size(buttonWidth, buttonHeight)
            );
            btnFinalTest.BackColor = System.Drawing.Color.FromArgb(46, 204, 113);
            btnFinalTest.Click += (s, e) => CS2001_Fixed_Final_Test.RunFinalCompilationTest();
            buttonsPanel.Controls.Add(btnFinalTest);
            currentY += buttonHeight + spacing;

            // زر الاختبار الدائم (حل CS2001)
            var btnPermanentTest = CreateStyledButton(
                "🎯 الاختبار الدائم - حل CS2001",
                IconChar.CheckCircle,
                new System.Drawing.Point(10, currentY),
                new System.Drawing.Size(buttonWidth, buttonHeight)
            );
            btnPermanentTest.BackColor = System.Drawing.Color.FromArgb(155, 89, 182);
            btnPermanentTest.Click += (s, e) => CS2001_Permanently_Fixed_Test.RunPermanentFixTest();
            buttonsPanel.Controls.Add(btnPermanentTest);
        }

        /// <summary>
        /// إنشاء زر منسق
        /// </summary>
        private static RJButton CreateStyledButton(string text, IconChar icon, System.Drawing.Point location, System.Drawing.Size size)
        {
            var button = new RJButton
            {
                Text = text,
                IconChar = icon,
                IconColor = System.Drawing.Color.White,
                IconFont = IconFont.Auto,
                IconSize = 20,
                TextImageRelation = TextImageRelation.ImageBeforeText,
                Location = location,
                Size = size,
                BackColor = System.Drawing.Color.FromArgb(23, 162, 184),
                ForeColor = System.Drawing.Color.White,
                Font = new System.Drawing.Font("Droid Arabic Kufi", 10F, System.Drawing.FontStyle.Bold),
                BorderRadius = 8,
                BorderSize = 0,
                FlatStyle = FlatStyle.Flat,
                UseVisualStyleBackColor = false
            };

            button.FlatAppearance.BorderSize = 0;
            return button;
        }

        /// <summary>
        /// حدث النقر على زر إدارة القيود
        /// </summary>
        private static void BtnManageEntries_Click(object sender, EventArgs e)
        {
            try
            {
                // إنشاء الخدمات
                var journalService = new JournalEntryService();
                var accountService = new AccountService();
                var activityService = new UserActivityService();

                // فتح نموذج إدارة القيود
                var form = new Frm_JournalEntries_Enhanced(journalService, accountService, activityService);
                form.Show();
            }
            catch (Exception ex)
            {
                RJMessageBox.Show($"خطأ في فتح نموذج إدارة القيود: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// حدث النقر على زر إضافة قيد جديد
        /// </summary>
        private static void BtnAddEntry_Click(object sender, EventArgs e)
        {
            try
            {
                // إنشاء الخدمات
                var journalService = new JournalEntryService();
                var accountService = new AccountService();
                var activityService = new UserActivityService();

                // فتح نموذج إضافة قيد جديد
                var form = new Frm_AddEditJournalEntry_Enhanced(journalService, accountService, activityService);
                form.Show();
            }
            catch (Exception ex)
            {
                RJMessageBox.Show($"خطأ في فتح نموذج إضافة القيد: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// حدث النقر على زر اختيار حساب
        /// </summary>
        private static void BtnSelectAccount_Click(object sender, EventArgs e)
        {
            try
            {
                // إنشاء خدمة الحسابات
                var accountService = new AccountService();

                // فتح نموذج اختيار الحساب
                var form = new Frm_AccountSelector(accountService);
                form.AccountSelected += (s, account) =>
                {
                    RJMessageBox.Show(
                        $"تم اختيار الحساب:\n\nالرمز: {account.Code}\nالاسم: {account.Name}",
                        "حساب محدد",
                        MessageBoxButtons.OK,
                        MessageBoxIcon.Information);
                };
                form.ShowDialog();
            }
            catch (Exception ex)
            {
                RJMessageBox.Show($"خطأ في فتح نموذج اختيار الحساب: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// إضافة قائمة منسدلة للقيود المحاسبية
        /// </summary>
        public static void AddJournalEntriesMenuToMainForm(Form mainForm)
        {
            try
            {
                // البحث عن شريط القوائم
                var menuStrip = mainForm.Controls.OfType<MenuStrip>().FirstOrDefault();
                if (menuStrip == null)
                {
                    // إنشاء شريط قوائم جديد
                    menuStrip = new MenuStrip();
                    mainForm.Controls.Add(menuStrip);
                    mainForm.MainMenuStrip = menuStrip;
                }

                // إنشاء قائمة القيود المحاسبية
                var journalEntriesMenu = new ToolStripMenuItem("القيود المحاسبية");

                // إضافة عناصر القائمة
                var manageEntriesItem = new ToolStripMenuItem("إدارة القيود", null, BtnManageEntries_Click);
                var addEntryItem = new ToolStripMenuItem("إضافة قيد جديد", null, BtnAddEntry_Click);
                var selectAccountItem = new ToolStripMenuItem("اختيار حساب", null, BtnSelectAccount_Click);

                journalEntriesMenu.DropDownItems.AddRange(new ToolStripItem[]
                {
                    manageEntriesItem,
                    addEntryItem,
                    new ToolStripSeparator(),
                    selectAccountItem
                });

                menuStrip.Items.Add(journalEntriesMenu);

                RJMessageBox.Show(
                    "✅ تم إضافة قائمة القيود المحاسبية بنجاح!\n\n" +
                    "القائمة تحتوي على:\n" +
                    "• إدارة القيود\n" +
                    "• إضافة قيد جديد\n" +
                    "• اختيار حساب\n\n" +
                    "جميع النماذج متكاملة مع RJChildForm",
                    "قائمة مضافة",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                RJMessageBox.Show($"خطأ في إضافة القائمة: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// اختبار التكامل مع النموذج الرئيسي
        /// </summary>
        public static void TestMainFormIntegration()
        {
            try
            {
                RJMessageBox.Show(
                    "🧪 اختبار تكامل نماذج القيود مع النموذج الرئيسي\n\n" +
                    "📋 طرق التكامل المتاحة:\n\n" +
                    "1️⃣ AddJournalEntriesButtonsToMainForm(mainForm)\n" +
                    "   • إضافة أزرار للنماذج\n" +
                    "   • تصميم احترافي\n" +
                    "   • سهولة الوصول\n\n" +
                    "2️⃣ AddJournalEntriesMenuToMainForm(mainForm)\n" +
                    "   • إضافة قائمة منسدلة\n" +
                    "   • تنظيم أفضل\n" +
                    "   • واجهة منظمة\n\n" +
                    "🎯 الميزات:\n" +
                    "• تكامل مع RJChildForm\n" +
                    "• أيقونات مناسبة\n" +
                    "• تصميم متناسق\n" +
                    "• سهولة الاستخدام\n\n" +
                    "استخدم هذه الطرق في MainForm\n" +
                    "لإضافة النماذج المحسنة.",
                    "تكامل النموذج الرئيسي",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                RJMessageBox.Show($"خطأ في الاختبار: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
