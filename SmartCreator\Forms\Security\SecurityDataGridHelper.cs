using SmartCreator.RJControls;
using System;
using System.Drawing;
using System.Windows.Forms;

namespace SmartCreator.Forms.Security
{
    /// <summary>
    /// مساعد لتخصيص RJDataGridView في نظام الصلاحيات
    /// </summary>
    public static class SecurityDataGridHelper
    {
        #region - Colors and Styles

        // ألوان النظام
        public static readonly Color HeaderBackColor = Color.FromArgb(123, 104, 238);
        public static readonly Color HeaderForeColor = Color.White;
        public static readonly Color CellBackColor = Color.White;
        public static readonly Color CellForeColor = Color.FromArgb(64, 64, 64);
        public static readonly Color SelectionBackColor = Color.FromArgb(229, 226, 244);
        public static readonly Color SelectionForeColor = Color.FromArgb(64, 64, 64);
        public static readonly Color GridColor = Color.FromArgb(229, 226, 244);
        public static readonly Color AlternatingRowColor = Color.FromArgb(248, 249, 250);

        // خطوط النظام
        public static readonly Font HeaderFont = new Font("Segoe UI", 10F, FontStyle.Bold);
        public static readonly Font CellFont = new Font("Segoe UI", 9F);

        #endregion

        #region - DataGridView Setup Methods

        /// <summary>
        /// إعداد RJDataGridView للمستخدمين
        /// </summary>
        /// <param name="dgv">DataGridView المراد تخصيصه</param>
        public static void SetupUsersDataGridView(RJDataGridView dgv)
        {
            ApplyBasicStyling(dgv);

            // إعداد الأعمدة للمستخدمين
            dgv.Columns.Clear();

            dgv.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Id",
                HeaderText = "المعرف",
                DataPropertyName = "Id",
                Visible = false
            });

            dgv.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Username",
                HeaderText = "اسم المستخدم",
                DataPropertyName = "Username",
                Width = 120,
                MinimumWidth = 100
            });

            dgv.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "FullName",
                HeaderText = "الاسم الكامل",
                DataPropertyName = "FullName",
                Width = 150,
                MinimumWidth = 120
            });

            dgv.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Email",
                HeaderText = "البريد الإلكتروني",
                DataPropertyName = "Email",
                Width = 180,
                MinimumWidth = 150
            });

            dgv.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Role",
                HeaderText = "الدور",
                DataPropertyName = "Role",
                Width = 100,
                MinimumWidth = 80
            });

            var statusColumn = new DataGridViewTextBoxColumn
            {
                Name = "IsActive",
                HeaderText = "الحالة",
                DataPropertyName = "IsActive",
                Width = 80,
                MinimumWidth = 60
            };
            dgv.Columns.Add(statusColumn);

            dgv.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "LastLogin",
                HeaderText = "آخر دخول",
                DataPropertyName = "LastLogin",
                Width = 130,
                MinimumWidth = 110,
                DefaultCellStyle = new DataGridViewCellStyle
                {
                    Format = "dd/MM/yyyy HH:mm"
                }
            });

            dgv.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "CreatedAt",
                HeaderText = "تاريخ الإنشاء",
                DataPropertyName = "CreatedAt",
                Width = 130,
                MinimumWidth = 110,
                DefaultCellStyle = new DataGridViewCellStyle
                {
                    Format = "dd/MM/yyyy"
                }
            });

            // تخصيص عرض الحالة
            dgv.CellFormatting += (sender, e) =>
            {
                if (e.ColumnIndex == dgv.Columns["IsActive"].Index && e.Value != null)
                {
                    bool isActive = Convert.ToBoolean(e.Value);
                    e.Value = isActive ? "نشط" : "غير نشط";
                    e.CellStyle.ForeColor = isActive ? Color.FromArgb(40, 167, 69) : Color.FromArgb(220, 53, 69);
                    e.FormattingApplied = true;
                }
            };
        }

        /// <summary>
        /// إعداد RJDataGridView لسجل الأنشطة
        /// </summary>
        /// <param name="dgv">DataGridView المراد تخصيصه</param>
        public static void SetupActivitiesDataGridView(RJDataGridView dgv)
        {
            ApplyBasicStyling(dgv);

            // إعداد الأعمدة لسجل الأنشطة
            dgv.Columns.Clear();

            dgv.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Id",
                HeaderText = "المعرف",
                DataPropertyName = "Id",
                Visible = false
            });

            dgv.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Timestamp",
                HeaderText = "التوقيت",
                DataPropertyName = "Timestamp",
                Width = 140,
                MinimumWidth = 120,
                DefaultCellStyle = new DataGridViewCellStyle
                {
                    Format = "dd/MM/yyyy HH:mm:ss"
                }
            });

            dgv.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Username",
                HeaderText = "المستخدم",
                DataPropertyName = "Username",
                Width = 120,
                MinimumWidth = 100
            });

            dgv.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Module",
                HeaderText = "الوحدة",
                DataPropertyName = "Module",
                Width = 100,
                MinimumWidth = 80
            });

            dgv.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Action",
                HeaderText = "الإجراء",
                DataPropertyName = "Action",
                Width = 150,
                MinimumWidth = 120
            });

            var severityColumn = new DataGridViewTextBoxColumn
            {
                Name = "Severity",
                HeaderText = "المستوى",
                DataPropertyName = "Severity",
                Width = 80,
                MinimumWidth = 60
            };
            dgv.Columns.Add(severityColumn);

            dgv.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Description",
                HeaderText = "الوصف",
                DataPropertyName = "Description",
                Width = 200,
                MinimumWidth = 150,
                AutoSizeMode = DataGridViewAutoSizeColumnMode.Fill
            });

            dgv.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "IpAddress",
                HeaderText = "عنوان IP",
                DataPropertyName = "IpAddress",
                Width = 120,
                MinimumWidth = 100
            });

            // تخصيص عرض مستوى الخطورة
            dgv.CellFormatting += (sender, e) =>
            {
                if (e.ColumnIndex == dgv.Columns["Severity"].Index && e.Value != null)
                {
                    string severity = e.Value.ToString();
                    switch (severity.ToLower())
                    {
                        case "info":
                            e.Value = "معلومات";
                            e.CellStyle.ForeColor = Color.FromArgb(23, 162, 184);
                            break;
                        case "warning":
                            e.Value = "تحذير";
                            e.CellStyle.ForeColor = Color.FromArgb(255, 193, 7);
                            break;
                        case "error":
                            e.Value = "خطأ";
                            e.CellStyle.ForeColor = Color.FromArgb(220, 53, 69);
                            break;
                        case "success":
                            e.Value = "نجح";
                            e.CellStyle.ForeColor = Color.FromArgb(40, 167, 69);
                            break;
                        default:
                            e.Value = severity;
                            break;
                    }
                    e.FormattingApplied = true;
                }
            };
        }

        /// <summary>
        /// تطبيق التنسيق الأساسي على RJDataGridView
        /// </summary>
        /// <param name="dgv">DataGridView المراد تنسيقه</param>
        public static void ApplyBasicStyling(RJDataGridView dgv)
        {
            // الإعدادات الأساسية
            dgv.AllowUserToAddRows = false;
            dgv.AllowUserToDeleteRows = false;
            dgv.ReadOnly = true;
            dgv.MultiSelect = false;
            dgv.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dgv.RightToLeft = RightToLeft.Yes;

            // إعدادات المظهر
            dgv.BackgroundColor = Color.White;
            dgv.BorderStyle = BorderStyle.None;
            dgv.CellBorderStyle = DataGridViewCellBorderStyle.SingleHorizontal;
            dgv.ColumnHeadersBorderStyle = DataGridViewHeaderBorderStyle.None;
            dgv.EnableHeadersVisualStyles = false;
            dgv.GridColor = GridColor;
            dgv.RowHeadersVisible = false;

            // إعدادات الصفوف
            dgv.RowTemplate.Height = 35;
            dgv.AlternatingRowsDefaultCellStyle.BackColor = AlternatingRowColor;

            // إعدادات رؤوس الأعمدة
            dgv.ColumnHeadersDefaultCellStyle.BackColor = HeaderBackColor;
            dgv.ColumnHeadersDefaultCellStyle.ForeColor = HeaderForeColor;
            dgv.ColumnHeadersDefaultCellStyle.Font = HeaderFont;
            dgv.ColumnHeadersDefaultCellStyle.SelectionBackColor = HeaderBackColor;
            dgv.ColumnHeadersDefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dgv.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            dgv.ColumnHeadersHeight = 40;

            // إعدادات الخلايا
            dgv.DefaultCellStyle.BackColor = CellBackColor;
            dgv.DefaultCellStyle.ForeColor = CellForeColor;
            dgv.DefaultCellStyle.Font = CellFont;
            dgv.DefaultCellStyle.SelectionBackColor = SelectionBackColor;
            dgv.DefaultCellStyle.SelectionForeColor = SelectionForeColor;
            dgv.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dgv.DefaultCellStyle.Padding = new Padding(5);
        }

        /// <summary>
        /// إضافة تأثيرات التفاعل للـ DataGridView
        /// </summary>
        /// <param name="dgv">DataGridView المراد إضافة التأثيرات إليه</param>
        public static void AddInteractionEffects(RJDataGridView dgv)
        {
            // تأثير عند مرور الماوس
            dgv.CellMouseEnter += (sender, e) =>
            {
                if (e.RowIndex >= 0)
                {
                    dgv.Rows[e.RowIndex].DefaultCellStyle.BackColor = Color.FromArgb(240, 245, 249);
                }
            };

            dgv.CellMouseLeave += (sender, e) =>
            {
                if (e.RowIndex >= 0)
                {
                    dgv.Rows[e.RowIndex].DefaultCellStyle.BackColor =
                        e.RowIndex % 2 == 0 ? CellBackColor : AlternatingRowColor;
                }
            };

            // تأثير عند التحديد
            dgv.SelectionChanged += (sender, e) =>
            {
                foreach (DataGridViewRow row in dgv.Rows)
                {
                    if (row.Selected)
                    {
                        row.DefaultCellStyle.BackColor = SelectionBackColor;
                        row.DefaultCellStyle.ForeColor = SelectionForeColor;
                    }
                    else
                    {
                        row.DefaultCellStyle.BackColor =
                            row.Index % 2 == 0 ? CellBackColor : AlternatingRowColor;
                        row.DefaultCellStyle.ForeColor = CellForeColor;
                    }
                }
            };
        }

        /// <summary>
        /// تطبيق فلتر البحث على DataGridView
        /// </summary>
        /// <param name="dgv">DataGridView</param>
        /// <param name="searchText">نص البحث</param>
        public static void ApplySearchFilter(RJDataGridView dgv, string searchText)
        {
            if (string.IsNullOrWhiteSpace(searchText))
            {
                // إظهار جميع الصفوف
                foreach (DataGridViewRow row in dgv.Rows)
                {
                    row.Visible = true;
                }
                return;
            }

            // إخفاء الصفوف التي لا تحتوي على نص البحث
            foreach (DataGridViewRow row in dgv.Rows)
            {
                bool found = false;
                foreach (DataGridViewCell cell in row.Cells)
                {
                    if (cell.Value != null &&
                        cell.Value.ToString().ToLower().Contains(searchText.ToLower()))
                    {
                        found = true;
                        break;
                    }
                }
                row.Visible = found;
            }
        }

        /// <summary>
        /// تصدير بيانات DataGridView إلى CSV
        /// </summary>
        /// <param name="dgv">DataGridView</param>
        /// <param name="fileName">اسم الملف</param>
        public static void ExportToCSV(RJDataGridView dgv, string fileName)
        {
            try
            {
                using (var writer = new System.IO.StreamWriter(fileName, false, System.Text.Encoding.UTF8))
                {
                    // كتابة رؤوس الأعمدة
                    var headers = new string[dgv.Columns.Count];
                    for (int i = 0; i < dgv.Columns.Count; i++)
                    {
                        headers[i] = dgv.Columns[i].HeaderText;
                    }
                    writer.WriteLine(string.Join(",", headers));

                    // كتابة البيانات
                    foreach (DataGridViewRow row in dgv.Rows)
                    {
                        if (!row.IsNewRow && row.Visible)
                        {
                            var values = new string[dgv.Columns.Count];
                            for (int i = 0; i < dgv.Columns.Count; i++)
                            {
                                values[i] = row.Cells[i].Value?.ToString() ?? "";
                            }
                            writer.WriteLine(string.Join(",", values));
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تصدير البيانات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion
    }
}
