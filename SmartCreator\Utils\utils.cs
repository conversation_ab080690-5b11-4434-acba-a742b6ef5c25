﻿using FontAwesome.Sharp;
using iTextSharp.text.pdf;
using Newtonsoft.Json.Linq;
using Org.BouncyCastle.Asn1.Crmf;
using Org.BouncyCastle.Asn1.X509;
using SmartCreator.Entities;
using SmartCreator.Models;
using SmartCreator.Properties;
using SmartCreator.RJControls;
using SmartCreator.Service;
using SmartCreator.Settings;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SQLite;
using System.Drawing;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;
using System.Web.UI.WebControls;
using System.Windows.Forms;

namespace SmartCreator.Utils
{
    public class utils
    {
        public static float ScaleFactor = 1;
        public static float ScaleFactor_dgv = 1;
        public static float ScaleFactor_lbl = 1;
        public static float ScaleFactor_combo = 1;
        public static float ScaleFactor_dm = 1;
        public static float ScaleFactor_sideMenu = 1;
        public static Color Dgv_DarkColor = Color.DarkRed;
        //public static Color Dgv_DarkColor = ColorTranslator.FromHtml("#55ff55");

        public static Dictionary<string, object> Convert_DataTable_To_Dict(DataTable dt, int coulum_KeyIndex)
        {
            return dt.AsEnumerable().ToDictionary<DataRow, string, object>(row => row.Field<string>(coulum_KeyIndex), row => row.Field<object>(1));
        }
        public static string Base64Encode(string plainText)
        {
            var plainTextBytes = System.Text.Encoding.UTF8.GetBytes(plainText);
            return Convert.ToBase64String(plainTextBytes);

        }
        public static string Base64Decode(string base64EncodedData)
        {
            var base64EncodedBytes = System.Convert.FromBase64String(base64EncodedData);
            return System.Text.Encoding.UTF8.GetString(base64EncodedBytes);
        }
        public static string Get_Seconds_By_clock_Mode(double value)
        {
            string result = "";
            try
            {
                //    int total_seconds = Convert.ToInt32(value);
                //    int hours = total_seconds / 3600;
                //    int minutes = total_seconds / 60 % 60;
                //    int seconds = total_seconds % 60;
                //    string h = hours.ToString();

                //    if (hours < 10)
                //        h = "0" + hours;
                //    result = h + ":" + minutes.ToString("00") + ":" + seconds.ToString("00");
                //}
                //catch { }
                //return result;

                int total_seconds = Convert.ToInt32(value);
                int s = total_seconds % 60;
                total_seconds /= 60;
                int mins = total_seconds % 60;
                int hours = total_seconds / 60;
                string h = hours.ToString();
                if (hours < 10)
                    h = "0" + hours;
                result = h + ":" + mins.ToString("00") + ":" + s.ToString("00");
            }
            catch { }
            return result;
        }

        public static string GetTimeCard_By_clock_Mode(string TimeCard)
        {
            //MessageBox.Show("befor : " + TimeCard.ToString());
            string s = TimeCard;
            string orginal = TimeCard;
            string f = "";
            int NuberDay = 0;
            TimeCard = s;
            try
            {
                string hour = "00";
                string minut = "00";
                string second = "00";

                int w = s.IndexOf("w");
                if (w > 0)
                {
                    int i = Convert.ToInt32(s.Substring(0, w));
                    NuberDay = i * 7;

                    f = s.Substring(w, s.Length - w);
                    f = f.Remove(0, 1);
                    s = f;
                    hour = ((NuberDay * 24)).ToString();
                    TimeCard = hour + ":" + minut + ":" + second;

                }
                int d = s.IndexOf("d");
                if (d > 0)
                {
                    int i = Convert.ToInt32(s.Substring(0, d));
                    NuberDay = NuberDay + i;
                    f = s.Substring(d, s.Length - (s.Substring(0, d).Length));
                    f = f.Remove(0, 1);
                    s = f;
                    hour = ((NuberDay * 24)).ToString();
                    if (hour.Length == 1)
                        hour = "0" + hour;
                    TimeCard = hour + ":" + minut + ":" + second;
                }
                int h = s.IndexOf("h");
                if (h > 0)
                {
                    int i = Convert.ToInt32(s.Substring(0, h));
                    f = s.Substring(h, s.Length - (s.Substring(0, h).Length));
                    f = f.Remove(0, 1);
                    s = f;
                    hour = (((NuberDay * 24)) + i).ToString();
                    //hour = (Convert.ToInt16(i)).ToString();
                    if (hour.Length == 1)
                        hour = "0" + hour;
                    TimeCard = hour + ":" + minut + ":" + second;
                }
                int m = s.IndexOf("m");
                //MessageBox.Show("m befor for s : " + s.ToString());
                if (m > 0)
                {
                    double i = Convert.ToDouble(s.Substring(0, m));
                    //if (s.Substring(0, m).Length == 1)
                    f = s.Substring(m, s.Length - (s.Substring(0, m).Length));
                    //else if (s.Substring(0, m).Length == 2)
                    //    f = s.Substring(m, s.Length - 2);

                    f = f.Remove(0, 1);
                    s = f;

                    minut = i.ToString();
                    if (minut.Length == 1)
                        minut = "0" + minut;

                    TimeCard = hour + ":" + minut + ":" + second;
                }
                int scond = s.IndexOf("s");
                if (scond > 0)
                {
                    double i = Convert.ToDouble(s.Substring(0, scond));
                    second = i.ToString();

                    if (second.Length == 1)
                        second = "0" + second;

                    TimeCard = hour + ":" + minut + ":" + second;
                }
                if (s.Contains(":"))
                {
                    string[] split = s.Split(new string[] { ":" }, StringSplitOptions.None);
                    if (split[0] != "00")
                    {
                        hour = (((NuberDay * 24))).ToString();
                        hour = (Convert.ToInt16(split[0]) + Convert.ToInt16(hour)).ToString();
                        if (hour.Length == 0)
                            hour = "0" + hour;
                        TimeCard = hour + ":" + minut + ":" + second;
                    }
                    if (split[1] != "00")
                    {
                        minut = (Convert.ToInt16(split[1]) + Convert.ToInt16(minut)).ToString();
                        if (minut.Length == 0)
                            minut = "0" + minut;
                        TimeCard = hour + ":" + minut + ":" + second;
                    }
                    if (split[2] != "00")
                    {
                        second = (Convert.ToInt16(split[2]) + Convert.ToInt16(second)).ToString();
                        if (second.Length == 0)
                            second = "0" + second;
                    }
                }
                //MessageBox.Show("after : " + timeWithSecond.ToString());
                // ===================================================
                // 
            }
            catch { }
            return TimeCard;
        }


        public static double GetString_Time_in_Hour(string TimeCard)
        {
            // retrun double Value like  9.5  befor point houer  and after  minut
            string s = TimeCard;
            string f = "";
            int NuberDay = 0;
            double hour = 0;
            double minut = 0;
            try
            {
                int w = s.IndexOf("w");
                if (w > 0)
                {
                    int i = Convert.ToInt32(s.Substring(0, w));
                    NuberDay = i * 7;
                    f = s.Substring(w, s.Length - w);
                    f = f.Remove(0, 1);
                    s = f;
                    hour = ((NuberDay * 24));

                }

                int d = s.IndexOf("d");
                if (d > 0)
                {
                    int i = Convert.ToInt32(s.Substring(0, d));
                    NuberDay = NuberDay + i;
                    f = s.Substring(d, s.Length - (s.Substring(0, d).Length));
                    f = f.Remove(0, 1);
                    s = f;
                    hour = ((NuberDay * 24));
                }

                int h = s.IndexOf("h");
                if (h > 0)
                {
                    int i = Convert.ToInt32(s.Substring(0, h));
                    f = s.Substring(h, s.Length - (s.Substring(0, h).Length));
                    f = f.Remove(0, 1);
                    s = f;
                    hour = (((NuberDay * 24)) + i);
                }

                int m = s.IndexOf("m");
                if (m > 0)
                {
                    double i = Convert.ToDouble(s.Substring(0, m));
                    f = s.Substring(m, s.Length - (s.Substring(0, m).Length));
                    f = f.Remove(0, 1);
                    s = f;
                    minut = minut + i;
                }

                if (s.Contains(":"))
                {
                    string[] split = s.Split(new string[] { ":" }, StringSplitOptions.None);
                    if (split[0] != "00")
                    {
                        hour = (((NuberDay * 24)));
                        hour = (Convert.ToDouble(split[0]) + Convert.ToDouble(hour));
                    }
                    if (split[1] != "00")
                    {
                        minut = (Convert.ToDouble(split[1]) + Convert.ToDouble(minut));
                        hour = Convert.ToDouble(hour + "." + minut);
                    }
                }
            }
            catch { }
            return hour;

        }
        public static string ConvertSize_Get_InArabic_short(string size)
        {
            if (size != "" && size != "0")
            {
                string[] sizes = { "بايت", "كيلو", "ميجا", "جيجا" };
                double len = Convert.ToDouble(size);
                int order = 0;
                while (len >= 1024 && order + 1 < sizes.Length)
                {
                    order++;
                    len = len / 1024;
                }
                size = String.Format("{0:0.##}{1}", len, sizes[order]);
            }
            else
                size = "0";

            return size;
        }

        public static string ConvertSize_Get_InArabic(string size)
        {
            if (size != "" && size != "0")
            {
                string[] sizes = { " بايت ", " كيلوبايت", " ميجابايت", " جيجابايت" };
                double len = Convert.ToDouble(size);
                int order = 0;
                while (len >= 1024 && order + 1 < sizes.Length)
                {
                    order++;
                    len = len / 1024;
                }
                size = String.Format("{0:0.##}{1}", len, sizes[order]);
            }
            else
                size = "0";

            return size;
        }

        public static string ConvertSize_Get_InArabic_with_ZeroByte(string size)
        {
            if (size != "")
            {
                string[] sizes = { " بايت ", " كيلوبايت", " ميجابايت", " جيجابايت" };
                double len = Convert.ToDouble(size);
                int order = 0;
                while (len >= 1024 && order + 1 < sizes.Length)
                {
                    order++;
                    len = len / 1024;
                }
                size = String.Format("{0:0.##}{1}", len, sizes[order]);
            }
            else
                size = "0";

            return size;
        }
        public static string ConvertSize_Get_MB_or_GM(string size, int choice, bool unitShow)
        {
            if (choice == 2)
            {
                if (size != "")
                {
                    double len = Convert.ToDouble(size);
                    double mb = (((len / 1024) / 1024) / 1024);
                    if (mb < 1) mb = Math.Round(mb, 2);
                    size = (mb).ToString();
                    if (unitShow)
                        size = mb + " جيجا بايبت";
                }
                else
                    size = "0";
            }

            if (choice == 1)
            {
                string[] sizes = { " بايت ", " كيلوبايت", " ميجابايت", " جيجابايت" };
                if (size != "")
                {
                    double len = Convert.ToDouble(size);
                    double mb = (len / 1024) / 1024;
                    if (mb < 1) mb = Math.Round(mb, 2);
                    size = (mb).ToString();
                    if (unitShow)
                        size = mb + " ميجابايت";
                }
                else
                    size = "0";
            }

            if (choice == 0)
            {
                size = ConvertSize_Get_InArabic(size);
            }
            return size;
        }
        public static string ConvertSize_Get_MB_or_GM(string size, format_Size format_Size)
        {
            //عرض جيجا و ميجا
            //عرض الوحده بالميجا
            //عرض الوحده بالجيجا

            int choice = format_Size.unit_format;
            if (choice == 0)
            {
                if (format_Size.unit_show)
                    size = ConvertSize_Get_InArabic(size);
                else
                    ConvertSize_Get_InMB_without_Uint(size);
            }
            else if (choice == 2)
            {
                if (size != "")
                {
                    double len = Convert.ToDouble(size);
                    double mb = (((len / 1024) / 1024) / 1024);
                    if (mb < 1) mb = Math.Round(mb, 2);
                    size = (mb).ToString();
                    if (format_Size.unit_show)
                        size = mb + " جيجا بايبت";
                }
                else
                    size = "0";
            }

            else if (choice == 1)
            {
                string[] sizes = { " بايت ", " كيلوبايت", " ميجابايت", " جيجابايت" };
                if (size != "")
                {
                    double len = Convert.ToDouble(size);
                    double mb = (len / 1024) / 1024;
                    if (mb < 1) mb = Math.Round(mb, 2);
                    size = (mb).ToString();
                    if (format_Size.unit_show)
                        size = mb + " ميجابايت";
                }
                else
                    size = "0";
            }


            return size;
        }

        public static string ConvertSize_Get_En(string size)
        {
            //string size = "";
            try
            {
                if (size != "" && size != "0")
                {
                    string[] sizes = { " B ", " KB", " MB", " GB" };
                    double len = Convert.ToDouble(size);
                    int order = 0;
                    while (len >= 1024 && order + 1 < sizes.Length)
                    {
                        order++;
                        len = len / 1024;
                    }
                    size = String.Format("{0:0.##}{1}", len, sizes[order]);
                }
                else
                    size = "0";
            }
            catch { }
            return size;
        }
        public static string ConvertSize_Get_En_with_ZeroByte(string size)
        {
            if (size != "")
            {
                string[] sizes = { " B ", " KB", " MB", " GB" };
                double len = Convert.ToDouble(size);
                int order = 0;
                while (len >= 1024 && order + 1 < sizes.Length)
                {
                    order++;
                    len = len / 1024;
                }
                size = String.Format("{0:0.##}{1}", len, sizes[order]);
            }
            else
                size = "0";

            return size;
        }
        public static string ConvertSize_Get_Without_Uint(string size)
        {
            if (size != "" || size != "0")
            {
                string[] sizes = { "", "", "", "" };
                double len = Convert.ToDouble(size);
                int order = 0;
                while (len >= 1024 && order + 1 < sizes.Length)
                {
                    order++;
                    len = len / 1024;
                }
                size = String.Format("{0:0.##}{1}", len, sizes[order]);
            }
            else
                size = "0";

            return size;
        }


        public string ConvertSize(string size)
        {

            string[] sizes = { "B", "KB", "MB", "GB" };
            double len = Convert.ToDouble(size);
            int order = 0;
            while (len >= 1024 && order + 1 < sizes.Length)
            {
                order++;
                len = len / 1024;
            }
            size = String.Format("{0:0.##}{1}", len, sizes[order]);


            return size;
        }

        public static string Get_Days_in_WeeksDays_or_MonthsWeekDays(string value, format_Validity Validity)
        {
            //"بالايام\r\nبالاسابيع\r\nبالشهور"

            string result = value;
            int iDays = Convert.ToInt32(value);
            int choice = Validity.unit_format;
            if (choice == 1) // in week
            {

                int w = (iDays % 365) / 7;
                int d = iDays - (w * 7);
                string dt = "";
                if (d > 0)
                    dt = "," + d;

                if (Validity.unit_show)
                {
                    if (w <= 1)
                        result = w + dt + $" {Validity.week}";
                    else if (w > 1 && w < 11)
                        result = w + dt + " اسابيع";
                    else if (w > 10)
                        result = w + dt + " اسبوع";
                    else
                        result = value;
                }
                else
                {
                    result = w + dt + "";
                }

            }
            else if (choice == 2)
            {
                int m = (iDays % 365) / 30;
                int d = iDays - (m * 30);
                string dt = "";
                if (d > 0) dt = "," + d;

                if (Validity.unit_show)
                {
                    if (m <= 1)
                        result = m + dt + " أشهر";
                    else if (m > 1 && m < 11)
                        result = m + dt + " أشهر";
                    else if (m > 10)
                        result = m + dt + " شهر";
                    else
                        result = value;
                }
                else
                {
                    result = m + dt + "";
                }
            }
            else
            {
                if (Validity.unit_show)
                {
                    if (iDays < 11)
                        result = iDays + " " + "أيام";
                    else
                        result = iDays + " " + "يوم";
                }
                else result = iDays + "";
            }

            return result;
        }
        public static string Get_Days_in_WeeksDays_or_MonthsWeekDays(string value, int choice)
        {
            //"بالايام\r\nبالاسابيع\r\nبالشهور"

            string result = value;
            int iDays = Convert.ToInt32(value);
            //int choice = Validity.unit_format;
            if (choice == 1) // in week
            {

                int w = (iDays % 365) / 7;
                int d = iDays - (w * 7);
                string dt = "";
                if (d > 0) dt = "," + d;

                if (w <= 1)
                    result = w + dt + $" اسبوع";
                else if (w > 1 && w < 11)
                    result = w + dt + " اسابيع";
                else if (w > 10)
                    result = w + dt + " اسبوع";
                else
                    result = value;

            }
            else if (choice == 2)
            {
                int m = (iDays % 365) / 30;
                int d = iDays - (m * 30);
                string dt = "";
                if (d > 0) dt = "," + d;

                if (m <= 1)
                    result = m + dt + " أشهر";
                else if (m > 1 && m < 11)
                    result = m + dt + " أشهر";
                else if (m > 10)
                    result = m + dt + " شهر";
                else
                    result = value;
            }
            else
            {
                if (iDays < 11)
                    result = iDays + " " + "أيام";
                else
                    result = iDays + " " + "يوم";
            }

            return result;
        }

        public static string Get_Seconds_in_Houre_or_DaysHoure(string value, int choice, bool unitShow)
        {
            string result = "";
            int n = Convert.ToInt32(value);
            int day = n / (24 * 3600);

            n = n % (24 * 3600);
            int hour = n / 3600;

            n %= 3600;
            int minutes = n / 60;

            n %= 60;
            int seconds = n;

            if (choice == 0)
            {

                if (day > 0)
                {
                    if (day < 10 && day > 0)
                    {
                        result = day.ToString();
                        if (unitShow)
                            result = result + " أيام";
                    }
                    else if (day > 10)
                    {
                        result = day.ToString();
                        if (unitShow)
                            result = result + " يوم";
                    }
                }
                if (hour > 0)
                {
                    if (result != "")
                        result = result + ",";

                    if (hour <= 10 && hour > 0)
                    {
                        result = result + hour;
                        if (unitShow)
                            result = result + " ساعات";

                    }
                    else if (hour > 10)
                    {
                        result = result + hour;
                        if (unitShow)
                            result = result + " ساعة";
                    }
                }
                if (minutes > 0)
                {
                    if (result != "")
                        result = result + ",";
                    if (minutes <= 10 && minutes > 0)
                    {
                        result = result + minutes;
                        if (unitShow)
                            result = result + " دقائق";

                    }
                    else if (minutes > 10)
                    {
                        result = result + minutes;
                        if (unitShow)
                            result = result + minutes + " دقيقة";
                    }
                }
            }
            else if (choice == 1)
            {
                result = hour.ToString();
                if (unitShow)
                {
                    if (hour <= 10 && hour > 0)
                        result = result + " ساعات";
                    else if (hour > 10)
                    {
                        result = result + " ساعة";
                    }

                }

            }


            return result;
        }

        public static string Get_Seconds_in_Houre_or_DaysHoure(double value, format_Time Time)
        {
            // عرض الوحده بالساعات 0
            //1 عرض الوحده بالدقائق 
            //2  بالساعات والدقائق
            //3 بالايام و الساعات

            int choice = Time.unit_format;
            string result = "";
            int n = Convert.ToInt32(value);


            if (choice == 0)
            {
                int hours = (int)(value / 3600);
                int mins = (int)((value % 3600) / 60);
                int secs = (int)(value % 60);
                if (Time.unit_show)
                {
                    if (hours <= 10 && hours > 0)
                        result = hours + " ساعات";
                    else if (hours > 10)
                    {
                        result = hours + " ساعة";
                    }
                }
                else
                    result = hours + "";
            }
            else if (choice == 1)
            {
                int mins = (int)((value) / 60);
                if (Time.unit_show)
                {
                    if (mins <= 10 && mins > 0)
                        result = mins + " دقائق";
                    else if (mins > 10)
                    {
                        result = mins + " دقيقة";
                    }
                }
                else
                    result = mins + "";
            }
            else if (choice == 2)
            {
                int hours = (int)(value / 3600);
                int mins = (int)((value % 3600) / 60);
                int secs = (int)(value % 60);
                if (Time.unit_show)
                {
                    if (hours <= 10 && hours > 0)
                        result = hours + " ساعات";
                    else if (hours > 10)
                    {
                        result = hours + " ساعة";
                    }
                    if (mins > 0)
                    {
                        if (mins <= 10 && mins > 0)
                            result = result + ", " + mins + " دقائق";
                        else if (mins > 10)
                        {
                            result = result + ", " + mins + " دقيقة";
                        }
                    }
                }
                else
                {
                    result = hours + "";
                    if (mins > 0)
                        result = result + ":" + mins;

                }
            }



            else if (choice == 3)
            {
                int day = n / (24 * 3600);
                n = n % (24 * 3600);
                int hour = n / 3600;

                n %= 3600;
                int minutes = n / 60;

                n %= 60;
                int seconds = n;

                string result_withou_Uint = "";
                if (Time.unit_show)
                {
                    if (day > 0)
                    {
                        result_withou_Uint = day + "";
                        if (day < 10 && day > 0)
                        {
                            result = day.ToString();
                            if (Time.unit_show)
                                result = result + " أيام";
                        }
                        else if (day > 10)
                        {
                            result = day.ToString();
                            if (Time.unit_show)
                                result = result + " يوم";
                        }
                    }
                    if (hour > 0)
                    {
                        result_withou_Uint += "," + hour;
                        if (result != "")
                            result = result + ",";

                        if (hour <= 10 && hour > 0)
                        {
                            result = result + hour;
                            if (Time.unit_show)
                                result = result + " ساعات";

                        }
                        else if (hour > 10)
                        {
                            result = result + hour;
                            if (Time.unit_show)
                                result = result + " ساعة";
                        }
                    }
                }
                else
                {
                    result = result_withou_Uint;
                }



            }


            return result;
        }

        public static long GetTimeCard_InSeconds(string TimeCard)
        {

            string s = TimeCard;
            string f = "";
            int NuberDay = 0;
            TimeCard = s;
            long timeWithSecond = 0;
            try
            {
                int w = s.IndexOf("w");
                if (w > 0)
                {
                    int i = Convert.ToInt16(s.Substring(0, w));
                    NuberDay = i * 7;
                    //f = s.Substring(w, s.Length - i.ToString().Length+1);
                    f = s.Substring(w, (int)(s.Length - Math.Floor(Math.Log10(i) + 1)));
                    f = f.Remove(0, 1);
                    s = f;
                    timeWithSecond = timeWithSecond + (NuberDay * 24) * 60 * 60; //نحول الايام الى ثواني
                }
                int d = s.IndexOf("d");
                if (d > 0)
                {
                    int i = Convert.ToInt16(s.Substring(0, d));
                    NuberDay = NuberDay + i;
                    //if (s.Substring(0, d).Length == 1)
                    //    f = s.Substring(d, s.Length - 1);
                    //else if (s.Substring(0, d).Length == 2)
                    //    f = s.Substring(d, s.Length - 2);
                    f = s.Substring(d, (int)(s.Length - Math.Floor(Math.Log10(i) + 1)));

                    f = f.Remove(0, 1);
                    s = f;

                    timeWithSecond = timeWithSecond + (i * 24) * 60 * 60;  //نحول الايام الى ثواني
                }
                int h = s.IndexOf("h");
                if (h > 0)
                {
                    double i = Convert.ToDouble(s.Substring(0, h));

                    //if (s.Substring(0, h).Length == 1)
                    //    f = s.Substring(h, s.Length - 1);
                    //else if (s.Substring(0, h).Length == 2)
                    //    f = s.Substring(h, s.Length - 2);
                    f = s.Substring(h, (int)(s.Length - Math.Floor(Math.Log10(i) + 1)));
                    f = f.Remove(0, 1);
                    s = f;
                    timeWithSecond = timeWithSecond + ((long)(i) * 60 * 60); //الان نحول عدد الساعات الى ثواني
                }
                int m = s.IndexOf("m");
                if (m > 0)
                {
                    double i = Convert.ToDouble(s.Substring(0, m));
                    //if (s.Substring(0, m).Length == 1)
                    //    f = s.Substring(m, s.Length - 1);
                    //else if (s.Substring(0, m).Length == 2)
                    //    f = s.Substring(m, s.Length - 2);
                    f = s.Substring(m, (int)(s.Length - Math.Floor(Math.Log10(i) + 1)));
                    f = f.Remove(0, 1);
                    s = f;
                    timeWithSecond = timeWithSecond + (((long)(i) * 60));
                }
                int scond = s.IndexOf("s");
                if (scond > 0)
                {
                    double i = Convert.ToDouble(s.Substring(0, scond));
                    timeWithSecond = (long)(timeWithSecond + i);
                }
                if (s.Contains(":"))
                {
                    string[] split = s.Split(new string[] { ":" }, StringSplitOptions.None);
                    if (split[0] != "00")
                    {
                        timeWithSecond = (long)(timeWithSecond + (Convert.ToDouble(split[0]) * 60 * 60));
                    }
                    if (split[1] != "00")
                    {
                        timeWithSecond = (long)(timeWithSecond + (Convert.ToDouble(split[1]) * 60));
                    }
                    if (split[2] != "00")
                    {
                        timeWithSecond = (long)(timeWithSecond + (Convert.ToDouble(split[1])));
                    }
                }
            }
            catch (Exception e)
            {
                MessageBox.Show("GetTimeCard_InSeconds \n" + e.Message);
            }
            // ===================================================           
            return timeWithSecond;
        }

        public static string ConvertSize_Get_InMB(string size)
        {
            if (size != "")
            {
                string[] sizes = { " B", " KB", " MB" };
                double len = Convert.ToDouble(size);
                int order = 0;
                while (len >= 1024 && order + 1 < sizes.Length)
                {
                    order++;
                    len = len / 1024;
                }
                size = String.Format("{0:0.##}{1}", len, sizes[order]);
            }
            else
                size = "0";

            return size;
        }
        public static string ConvertSize_Get_InMB_without_Uint(string size)
        {
            if (size != "")
            {
                string[] sizes = { "", "", "" };
                double len = Convert.ToDouble(size);
                int order = 0;
                while (len >= 1024 && order + 1 < sizes.Length)
                {
                    order++;
                    len = len / 1024;
                }
                size = String.Format("{0:0.##}{1}", len, sizes[order]);
            }
            else
                size = "0";

            return size;
        }

        public static CMYKColor ConvertRgbToCmyk(int r, int g, int b)
        {
            float c;
            float m;
            float y;
            float k;
            float rf;
            float gf;
            float bf;

            rf = r / 255F;
            gf = g / 255F;
            bf = b / 255F;

            k = ClampCmyk(1 - Math.Max(Math.Max(rf, gf), bf));
            c = ClampCmyk((1 - rf - k) / (1 - k));
            m = ClampCmyk((1 - gf - k) / (1 - k));
            y = ClampCmyk((1 - bf - k) / (1 - k));

            return new CMYKColor(c, m, y, k);
        }

        private static float ClampCmyk(float value)
        {
            if (value < 0 || float.IsNaN(value))
            {
                value = 0;
            }

            return value;
        }

        public static int get_number_Days_form_validity_profile(string matchValidity)
        {
            int days = 0;
            try
            {
                if (matchValidity == "" || matchValidity == "00:00:00" || matchValidity == "0d" || matchValidity == "0")
                    days = 0;
                //result = "مفتوح";
                else
                {
                    string s = matchValidity;
                    string f = "";

                    int NuberDay = 0;

                    int w = s.IndexOf("w");
                    if (w > 0)
                    {
                        int ii = Convert.ToInt16(s.Substring(0, w));
                        NuberDay = ii * 7;

                        f = s.Substring(w, s.Length - w);
                        f = f.Remove(0, 1);
                        s = f;
                    }
                    int d = s.IndexOf("d");
                    if (d > 0)
                    {
                        int ii = Convert.ToInt32(s.Substring(0, d));
                        NuberDay = NuberDay + ii;

                        f = s.Substring(d, s.Length - 1);
                        f = f.Remove(0, 1);
                        s = f;
                    }
                    days = NuberDay;
                }


            }
            catch { }
            return days;
        }
        public static DateTime UnixTimeStampToDateTime(double unixTimeStamp)
        {
            // Unix timestamp is seconds past epoch
            DateTime dateTime = new DateTime(1970, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc);
            dateTime = dateTime.AddSeconds(unixTimeStamp).ToLocalTime();
            return dateTime;
        }
        public static Int32 DateTimeToUnixTimeStamp(DateTime dt)
        {
            Int32 unixTimestamp = 0;
            //try
            //{
            unixTimestamp = (int)dt.Subtract(new DateTime(1970, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc)).TotalSeconds;
            //}catch { }
            return unixTimestamp;
        }

        public static Int32 StringDatetimeToUnixTimeStamp(string time, string format = "MMM/dd/yyyy HH:mm:ss")
        {
            Int32 secondTime = 0; DateTime match_time;
            //try
            //{

            format = Global_Variable.Mk_resources.version <= 6 ? "MMM/dd/yyyy HH:mm:ss" : "yyyy-MM-dd HH:mm:ss";

            DateTime.TryParseExact(time, format, CultureInfo.InvariantCulture, DateTimeStyles.None, out match_time);

            secondTime = DateTimeToUnixTimeStamp(match_time.ToUniversalTime());
            //}catch { }
            return secondTime;
        }
        public static DateTime? String_To_Datetime_By_V_MK(string time)
        {
            //DateTime.TryParse(time, out DateTime dateTime);

            try
            {
                DateTime match_time;
                //string format = Global_Variable.Mk_resources.version <= 6 ? "MMM/dd/yyyy HH:mm:ss" : "dd-MM-yyyy HH:mm:ss";
                string format = Global_Variable.Mk_resources.version <= 6 ? "MMM/dd/yyyy HH:mm:ss" : "yyyy-MM-dd HH:mm:ss";
                DateTime.TryParseExact(time, format, CultureInfo.InvariantCulture, DateTimeStyles.None, out match_time);
                return match_time; //secondTime = DateTimeToUnixTimeStamp(match_time.ToUniversalTime());
            }
            catch { }
            return null;

        }
        public static DateTime? String_UTC_ToDatetime_By_V_MK(string time)
        {
            //DateTime.TryParse(time, out DateTime dateTime);

            try
            {
                DateTime match_time;
                string format = Global_Variable.Mk_resources.version <= 6 ? "MMM/dd/yyyy HH:mm:ss" : "dd-MM-yyyy HH:mm:ss";
                DateTime.TryParseExact(time, format, CultureInfo.InvariantCulture, DateTimeStyles.None, out match_time);
                return match_time.ToLocalTime(); //secondTime = DateTimeToUnixTimeStamp(match_time.ToUniversalTime());
            }
            catch { }
            return null;

        }
        public static DateTime String_To_Datetim(string time, string format = "")
        {
            DateTime match_time;
            if (format != "")
                DateTime.TryParseExact(time, format, CultureInfo.InvariantCulture, DateTimeStyles.None, out match_time);
            else
                DateTime.TryParse(time, out match_time);

            return match_time;
        }
        public static string Datetime_String_To_string_By_V_MK(string time)
        {
            DateTime match_time;
            string format = Global_Variable.Mk_resources.version <= 6 ? "MMM/dd/yyyy HH:mm:ss" : "dd-MM-yyyy HH:mm:ss";
            DateTime.TryParseExact(time, format, CultureInfo.InvariantCulture, DateTimeStyles.None, out match_time);
            return match_time.ToString("dd-MM-yyyy HH:mm:ss");
        }
        public static DateTime Datetime_To_DateTime_Format_DB(DateTime time)
        {
            return new DateTime(time.Year, time.Month, time.Day, time.Hour, time.Minute, time.Second);
            ////string format = Global_Variable.Mk_resources.version <= 6 ? "MMM/dd/yyyy HH:mm:ss" : "dd-MM-yyyy HH:mm:ss";
            //DateTime.TryParseExact(time, format, CultureInfo.InvariantCulture, DateTimeStyles.None, out match_time);
            //return match_time.ToString("dd-MM-yyyy HH:mm:ss");
        }

        public static void GetMonthBoundaries(int month, int year, out DateTime firstDayOfMonth, out DateTime lastDayOfMonth)
        {
            // Get the 1st day of the month (always day 1)
            DateTime first = new DateTime(year, month, 1);

            // Calculate the last day of the month
            DateTime last = first.AddMonths(1).AddSeconds(-1);

            // Set the out parameters
            firstDayOfMonth = first;
            lastDayOfMonth = last;
        }

        public static bool check_Filed_Intiger(string n)
        {
            //int numberChik;
            if ((int.TryParse(n, out int numberChik)))
            {
                //RJMessageBox.Show(" ادخل الرقم التسلسلي بشكل صحيح ");
                return true;
            }
            return false;
        }
        public static bool check_Filed_Intiger_with_Msg(string n, string msg = "ادخل الرقم التسلسلي بشكل صحيح")
        {
            //int numberChik;
            if ((int.TryParse(n, out int numberChik)) == false)
            {
                RJMessageBox.Show(msg);
                return false;
            }
            return true;
        }
        public static string Get_Root_Directory()
        {
            return $"{Directory.GetParent(Application.StartupPath)}";
        }
        public static string Get_Database_Directory()
        {
            string path = $"{Directory.GetParent(Application.StartupPath)}\\dbs";

            if (!System.IO.Directory.Exists(path))
                System.IO.Directory.CreateDirectory(path);

            return path;
        }
        public static string Get_Backup_Directory()
        {
            string path = $"{Directory.GetParent(Application.StartupPath)}\\ftp\\Backups";

            if (!System.IO.Directory.Exists(path))
                System.IO.Directory.CreateDirectory(path);

            return path;
        }

        public static string Get_TempCards_Pdf_Directory()
        {
            string path = $"{Directory.GetParent(Application.StartupPath)}\\tempCards\\pdf";
            if (!System.IO.Directory.Exists(path))
                System.IO.Directory.CreateDirectory(path);

            if (!System.IO.Directory.Exists(path + "\\Hotspot"))
                System.IO.Directory.CreateDirectory(path + "\\Hotspot");
            if (!System.IO.Directory.Exists(path + "\\UserManager"))
                System.IO.Directory.CreateDirectory(path + "\\UserManager");
            return path;
        }
        public static string Get_Report_Directory()
        {
            string path = $"{Directory.GetParent(Application.StartupPath)}\\tempCards\\Report";
            if (!System.IO.Directory.Exists(path))
                System.IO.Directory.CreateDirectory(path);

            return path;
        }
        public static string Get_TempCards_Script_Directory()
        {
            string path = $"{Directory.GetParent(Application.StartupPath)}\\tempCards\\script";

            if (!System.IO.Directory.Exists(path))
                System.IO.Directory.CreateDirectory(path);

            if (!System.IO.Directory.Exists(path + "\\Hotspot"))
                System.IO.Directory.CreateDirectory(path + "\\Hotspot");

            if (!System.IO.Directory.Exists(path + "\\UserManager"))
                System.IO.Directory.CreateDirectory(path + "\\UserManager");

            if (!System.IO.Directory.Exists(path + "\\UserManager\\batch"))
                System.IO.Directory.CreateDirectory(path + "\\UserManager\\batch");

            if (!System.IO.Directory.Exists(path + "\\Hotspot\\batch"))
                System.IO.Directory.CreateDirectory(path + "\\Hotspot\\batch");

            return path;
        }
        public static string Get_TempCards_Text_Directory()
        {
            string path = $"{Directory.GetParent(Application.StartupPath)}\\tempCards\\text";
            if (!System.IO.Directory.Exists(path))
                System.IO.Directory.CreateDirectory(path);

            if (!System.IO.Directory.Exists(path + "\\Hotspot"))
                System.IO.Directory.CreateDirectory(path + "\\Hotspot");
            if (!System.IO.Directory.Exists(path + "\\UserManager"))
                System.IO.Directory.CreateDirectory(path + "\\UserManager");
            return path;
        }
        public static string Get_TempCards_Excel_Directory()
        {
            string path = $"{Directory.GetParent(Application.StartupPath)}\\tempCards\\Excel";
            if (!System.IO.Directory.Exists(path))
                System.IO.Directory.CreateDirectory(path);

            if (!System.IO.Directory.Exists(path + "\\Hotspot"))
                System.IO.Directory.CreateDirectory(path + "\\Hotspot");
            if (!System.IO.Directory.Exists(path + "\\UserManager"))
                System.IO.Directory.CreateDirectory(path + "\\UserManager");

            return path;
        }
        public static string Get_CardsBack_Directory()
        {
            string path = $"{Directory.GetParent(Application.StartupPath)}\\CardsBack";
            if (!System.IO.Directory.Exists(path))
                System.IO.Directory.CreateDirectory(path);
            return path;
        }
        public static string Get_Ftp_Directory()
        {
            string path = $"{Directory.GetParent(Application.StartupPath)}\\ftp";
            if (!System.IO.Directory.Exists(path))
                System.IO.Directory.CreateDirectory(path);
            return path;
        }
        public static string Get_SmartDB_ConnectionString()
        {
            string cs = $"Data Source={Directory.GetParent(Application.StartupPath)}\\dbs\\Smart.db;";
            //string cs=$"Data Source={Directory.GetParent(Application.StartupPath)}\\dbs\\Smart.db;Password=@Smart#.Com;";
            return cs;
        }
        public static string Get_LocalDB_ConnectionString(string FileName)
        {
            return $"Data Source={Directory.GetParent(Application.StartupPath)}\\dbs\\{FileName};Password=@Smart#.Com;";
            //return $"Data Source={Directory.GetParent(Application.StartupPath)}\\dbs\\{FileName};";
        }
        public static string Get_LocalDB_ConnectionString_WithoutPass(string FileName)
        {
            return $"Data Source={Directory.GetParent(Application.StartupPath)}\\dbs\\{FileName};";
            //return $"Data Source={Directory.GetParent(Application.StartupPath)}\\dbs\\{FileName};";
        }
        public static string Get_CardsArchive_ConnectionString2()
        {
            return $"Data Source={Directory.GetParent(Application.StartupPath)}\\dbs\\CardsArchive.db;";
        }
        public static string Get_CardsArchive_ConnectionString()
        {
            return $"Data Source={Directory.GetParent(Application.StartupPath)}\\dbs\\CardsArchive.db;Password=@Smart#.Com;";

            //return $"Data Source={Directory.GetParent(Application.StartupPath)}\\dbs\\CardsArchive.db;Password=@Smart#.Com;";
        }

        public static string Get_Accounting_ConnectionString()
        {
            return Get_SmartDB_ConnectionString();

            //return $"Data Source={Directory.GetParent(Application.StartupPath)}\\dbs\\Accounting.db;Password=@Smart#.Com;";
            return $"Data Source={Directory.GetParent(Application.StartupPath)}\\dbs\\Accounting.db;";

            //return $"Data Source={Directory.GetParent(Application.StartupPath)}\\dbs\\CardsArchive.db;Password=@Smart#.Com;";
        }

        //float Graphics_dpi = CreateGraphics().DpiX;


        public static void Control_textSize(Control ctl)
        {
            return;
            if (Global_Variable.Graphics_dpi == 96f)
                return;

            //if(lbl==null)
            //    lbl = new HashSet<RJLabel>();

            //if (btn == null)
            //    btn = new HashSet<RJButton>();

            //List<RJButton> btn = new List<RJButton>();
            List<RJDataGridView> dgv = new List<RJDataGridView>();
            try
            {
                foreach (Control C in ctl.Controls)
                {
                    try
                    {
                        //if (C.GetType() == typeof(RJLabel))
                        //    lbls.Add((RJLabel)C);
                        //else if (C.GetType() == typeof(RJButton))
                        //    btns.Add((RJButton)C);
                        //else if (C.GetType() == typeof(RJDataGridView))
                        //    dgv.Add((RJDataGridView)C);


                        //if (C.Controls.Count > 0)
                        //    Control_textSize(C);


                        //System.Windows.Forms.Application.DoEvents();
                    }
                    catch
                    {
                    }
                }

                //foreach(RJLabel rJLabel in lbl)
                //{

                //}
                //foreach(RJButton rJButton in btn)
                //{
                //    RJMessageBox.Show("edit control  " + rJButton.Name);
                //    rJButton.IconSize = (int)(rJButton.IconSize + (rJButton.IconSize - (rJButton.IconSize * 96f / Global_Variable.Graphics_dpi))  );

                //}
                //foreach (RJLabel rJLabel in lbl)
                //{

                //}
            }
            catch
            {
            }

        }

        HashSet<RJLabel> lbls = null;
        HashSet<RJButton> btns = null;
        HashSet<RJDataGridView> dgvs = null;
        HashSet<RJMenuButton> menubtn = null;
        HashSet<RJMenuIcon> iconbtn = null;
        HashSet<FontAwesome.Sharp.IconButton> FontAwsnbtn = null;
        HashSet<FontAwesome.Sharp.IconMenuItem> FontAwsiconMenu = null;
        public void Control_textSize1(Control ctl)
        {
            if (Global_Variable.Graphics_dpi == 96f)
                return;

            if (lbls == null)
                lbls = new HashSet<RJLabel>();

            if (btns == null)
                btns = new HashSet<RJButton>();

            if (dgvs == null)
                dgvs = new HashSet<RJDataGridView>();
            if (menubtn == null)
                menubtn = new HashSet<RJMenuButton>();
            if (iconbtn == null)
                iconbtn = new HashSet<RJMenuIcon>();
            if (FontAwsnbtn == null)
                FontAwsnbtn = new HashSet<FontAwesome.Sharp.IconButton>();
            if (FontAwsiconMenu == null)
                FontAwsiconMenu = new HashSet<FontAwesome.Sharp.IconMenuItem>();
            try
            {
                foreach (Control C in ctl.Controls)
                {
                    try
                    {
                        if (C.GetType() == typeof(RJLabel))
                            lbls.Add((RJLabel)C);
                        else if (C.GetType() == typeof(RJButton))
                            btns.Add((RJButton)C);
                        else if (C.GetType() == typeof(RJDataGridView))
                            dgvs.Add((RJDataGridView)C);
                        else if (C.GetType() == typeof(RJMenuButton))
                            menubtn.Add((RJMenuButton)C);
                        else if (C.GetType() == typeof(RJMenuIcon))
                            iconbtn.Add((RJMenuIcon)C);
                        else if (C.GetType() == typeof(FontAwesome.Sharp.IconButton))
                            FontAwsnbtn.Add((FontAwesome.Sharp.IconButton)C);
                        //else if (C.GetType() == typeof(FontAwesome.Sharp.IconMenuItem))
                        //   FontAwsiconMenu.Add(C as FontAwesome.Sharp.IconMenuItem);

                        else if (C.GetType() == typeof(RJPanel) || C.GetType() == typeof(System.Windows.Forms.Panel) || C.GetType() == typeof(GroupBox)
                             || C.GetType() == typeof(FlowLayoutPanel) || C.GetType() == typeof(TableLayoutPanel))
                        {
                            Control_textSize_rebuld(C);
                        }

                        //System.Windows.Forms.Application.DoEvents();
                    }
                    catch
                    {
                    }
                }

                //foreach(RJLabel rJLabel in lbl)
                //{

                //}
                //foreach (RJButton rJButton in btns)
                //{
                //    //RJMessageBox.Show("edit control  " + rJButton.Name);
                //    rJButton.IconSize = (int)(rJButton.IconSize + (rJButton.IconSize - (rJButton.IconSize * 96f / Global_Variable.Graphics_dpi))  + 5);
                //}
                foreach (RJMenuButton rJButton in menubtn)
                {
                    //RJMessageBox.Show("edit control  " + rJButton.Name);
                    rJButton.IconSize = (int)(rJButton.IconSize + (rJButton.IconSize - (rJButton.IconSize * 96f / Global_Variable.Graphics_dpi)));
                }
                foreach (RJMenuIcon rJButton in iconbtn)
                {
                    //RJMessageBox.Show("edit control  " + rJButton.Name);
                    rJButton.IconSize = (int)(rJButton.IconSize + (rJButton.IconSize - (rJButton.IconSize * 96f / Global_Variable.Graphics_dpi)));
                }
                foreach (FontAwesome.Sharp.IconButton rJButton in btns)
                {
                    //RJMessageBox.Show("edit control  " + rJButton.Name);
                    rJButton.IconSize = (int)(rJButton.IconSize + (rJButton.IconSize - (rJButton.IconSize * 96f / Global_Variable.Graphics_dpi)));
                }
                foreach (RJDataGridView dgv in dgvs)
                {
                    //RJMessageBox.Show("edit control  " + rJButton.Name);
                    dgv.ColumnHeaderHeight = (int)(dgv.ColumnHeaderHeight + (dgv.ColumnHeaderHeight - (dgv.ColumnHeaderHeight * 96f / Global_Variable.Graphics_dpi)));
                    dgv.RowHeadersWidth = (int)(dgv.RowHeadersWidth + (dgv.RowHeadersWidth - (dgv.RowHeadersWidth * 96f / Global_Variable.Graphics_dpi)));
                    dgv.RowHeight = (int)(dgv.RowHeight + (dgv.RowHeight - (dgv.RowHeight * 96f / Global_Variable.Graphics_dpi)));

                }
                //foreach (RJLabel rJLabel in lbl)
                //{

                //}
            }
            catch
            {
            }

        }

        public void Control_textSize_rebuld(Control ctl)
        {
            try
            {
                foreach (Control C in ctl.Controls)
                {
                    try
                    {
                        if (C.GetType() == typeof(RJLabel))
                            lbls.Add((RJLabel)C);
                        else if (C.GetType() == typeof(RJButton))
                            btns.Add((RJButton)C);
                        else if (C.GetType() == typeof(RJDataGridView))
                            dgvs.Add((RJDataGridView)C);
                        else if (C.GetType() == typeof(RJMenuButton))
                            menubtn.Add((RJMenuButton)C);
                        else if (C.GetType() == typeof(RJMenuIcon))
                            iconbtn.Add((RJMenuIcon)C);

                        else if (C.GetType() == typeof(RJPanel) || C.GetType() == typeof(System.Windows.Forms.Panel) || C.GetType() == typeof(GroupBox)
                            || C.GetType() == typeof(FlowLayoutPanel) || C.GetType() == typeof(TableLayoutPanel))
                        {
                            Control_textSize_rebuld(C);
                        }

                        //System.Windows.Forms.Application.DoEvents();
                    }
                    catch
                    {
                    }
                }

                //foreach(RJLabel rJLabel in lbl)
                //{

                //}
                //foreach (RJButton rJButton in btn)
                //{
                //    RJMessageBox.Show("edit control  " + rJButton.Name);
                //    rJButton.IconSize = (int)(rJButton.IconSize + (rJButton.IconSize - (rJButton.IconSize * 96f / Global_Variable.Graphics_dpi)));

                //}
                //foreach (RJLabel rJLabel in lbl)
                //{

                //}
            }
            catch
            {
            }

        }

        public static void Control_textSize3(Control ctl)
        {
            //Global_Variable.Graphics_dpi= Control.CreateGraphics().DpiX;
            //Global_Variable
            //int curntDpi_Size = Control.CreateGraphics().DpiX;
            //int curntDpi_Size = (int)(btn_Save.IconSize * 96f / );

            //int iconSize = btn_Save.IconSize;

            //int newSize = iconSize + (iconSize - curntDpi_Size);
            //btn_Save.IconSize = newSize;
            ////RJMessageBox.Show( btn_Save.IconSize.ToString());
            //Global_Variable.Graphics_dpi

            //List<Control> x= ctl.GetType().GetProperties().Any(x => x.Name == "IconSize");

            try
            {
                foreach (FontAwesome.Sharp.Icon C in ctl.Controls)
                {
                    try
                    {

                        //if (C.GetType() != typeof(RJPanel) && C.GetType() != typeof(Panel) && C.GetType() != typeof(GroupBox) 
                        //    && C.GetType() != typeof(FlowLayoutPanel) && C.GetType() != typeof(TableLayoutPanel))
                        //{
                        //    if (C.GetType() == typeof(RJComboBox) || C.GetType() == typeof(RJTextBox))
                        //    {
                        //        //C.Font = new Font(C.Font.FontFamily, C.Font.Size * utils.ScaleFactor_combo, C.Font.Style);
                        //    }
                        //    //else if (C.GetType() == typeof(RJLabel) )
                        //    //    C.Font = new Font(C.Font.FontFamily, C.Font.Size * utils.ScaleFactor_lbl, C.Font.Style);
                        //    else
                        //        C.Font = new Font(C.Font.FontFamily, C.Font.Size * utils.ScaleFactor, C.Font.Style);
                        //}

                        //if (C.GetType() == typeof(FontAwesome.Sharp.Icon))
                        //{

                        //    int curntDpi_Size = (int)(C.IconSize * 96f / Global_Variable.Graphics_dpi);

                        //    //int iconSize = btn_Save.IconSize;

                        //}

                        //if (C.Controls.Count > 0)
                        //    Control_textSize(C);

                        System.Windows.Forms.Application.DoEvents();
                    }
                    catch
                    {
                    }
                }
            }
            catch
            {
            }
        }

        public static void Control_textSize2(Control ctl)
        {
            //if ( ScaleFactor == 1 ) 
            return;

            try
            {
                foreach (Control C in ctl.Controls)
                {
                    try
                    {
                        if (C.GetType() != typeof(RJPanel) && C.GetType() != typeof(System.Windows.Controls.Panel) && C.GetType() != typeof(GroupBox)
                            && C.GetType() != typeof(FlowLayoutPanel) && C.GetType() != typeof(TableLayoutPanel))
                        {
                            if (C.GetType() == typeof(RJComboBox) || C.GetType() == typeof(RJTextBox))
                            {
                                //C.Font = new Font(C.Font.FontFamily, C.Font.Size * utils.ScaleFactor_combo, C.Font.Style);
                            }
                            //else if (C.GetType() == typeof(RJLabel) )
                            //    C.Font = new Font(C.Font.FontFamily, C.Font.Size * utils.ScaleFactor_lbl, C.Font.Style);
                            else
                                C.Font = new Font(C.Font.FontFamily, C.Font.Size * utils.ScaleFactor, C.Font.Style);
                        }

                        if (C.Controls.Count > 0)
                            Control_textSize(C);

                        System.Windows.Forms.Application.DoEvents();
                    }
                    catch
                    {
                    }
                }
            }
            catch
            {
            }

        }

        public static int Control_Mesur_DPI(int size)
        {
            if (Global_Variable.Graphics_dpi == 96f)
                return size;
            return (int)(size + (size - (size * 96f / Global_Variable.Graphics_dpi)));
        }

        public static void dgv_textSize(RJDataGridView dgv)
        {
            //if (ScaleFactor == 1)
            return;

            try
            {
                dgv.Font = new Font(dgv.Font.FontFamily, dgv.Font.Size * utils.ScaleFactor_dgv, dgv.Font.Style);

                dgv.AllowUserToOrderColumns = true;
                dgv.ColumnHeadersDefaultCellStyle.Font = new Font(dgv.ColumnHeadersDefaultCellStyle.Font.FontFamily, dgv.ColumnHeadersDefaultCellStyle.Font.Size * utils.ScaleFactor_dgv, dgv.ColumnHeadersDefaultCellStyle.Font.Style);

                //dgv.ColumnHeadersDefaultCellStyle.Font = Program.GetCustomFont(Resources.DroidSansArabic,dgv.ColumnHeadersDefaultCellStyle.Font.Size * utils.ScaleFactor, FontStyle.Regular);
                //dgv.ColumnHeadersDefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
                //dgv.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
                //dgv.ColumnHeadersHeight = 40;

                dgv.DefaultCellStyle.Font = new Font(dgv.DefaultCellStyle.Font.FontFamily, dgv.DefaultCellStyle.Font.Size * utils.ScaleFactor_dgv, dgv.DefaultCellStyle.Font.Style);
                dgv.RowsDefaultCellStyle.Font = new Font(dgv.RowsDefaultCellStyle.Font.FontFamily, dgv.RowsDefaultCellStyle.Font.Size * utils.ScaleFactor_dgv, dgv.RowsDefaultCellStyle.Font.Style);

            }
            catch
            {
            }
        }
        public static void tollstrip_textSize(ToolStripMenuItem toolstrip)
        {
            //return;
            //if (ScaleFactor == 1)
            //    return;
            Color color;
            if (UIAppearance.Theme == UITheme.Dark)
                color = RJColors.DarkTextColor;
            else color = Color.Black;

            Font fn = Program.GetCustomFont(Resources.DroidKufi_Bold, 9 * utils.ScaleFactor_dgv, FontStyle.Bold);
            //toolstrip.Font = new Font(toolstrip.Font.FontFamily, toolstrip.Font.Size * utils.ScaleFactor_dgv, toolstrip.Font.Style);
            toolstrip.Font = fn;
            try
            {
                for (int i = 0; i <= toolstrip.DropDownItems.Count - 1; i++)
                {
                    //toolstrip.DropDownItems[i].Font = new Font(toolstrip.DropDownItems[i].Font.FontFamily, toolstrip.DropDownItems[i].Font.Size * utils.ScaleFactor_dgv, toolstrip.DropDownItems[i].Font.Style);
                    toolstrip.DropDownItems[i].Font = new Font("Segoe UI", 9 * utils.ScaleFactor_dgv, FontStyle.Regular);
                    toolstrip.DropDownItems[i].ForeColor = color;
                }
                //foreach (ToolStripMenuItem ts in toolstrip.DropDownItems)
                //{

                //    if (ts.GetType() == typeof(ToolStripMenuItem))
                //    {

                //        ts.Font = new Font(ts.Font.FontFamily, ts.Font.Size * utils.ScaleFactor_dgv, ts.Font.Style);
                //        ts.ForeColor =color;
                //    }
                //}
            }
            catch (Exception ex)
            {
                //MessageBox.Show(ex.Message);
            }
        }
        public static void item_Contrlol_textSize(Control C)
        {
            ////if (ScaleFactor == 1)
            //    return;
            try
            {
                if (C.GetType() == typeof(RJDropdownMenu))
                {
                    C.Font = new Font(C.Font.FontFamily, C.Font.Size * utils.ScaleFactor_dgv, C.Font.Style);
                    if (UIAppearance.Theme == UITheme.Dark)
                        C.ForeColor = RJColors.DarkTextColor;
                    else C.ForeColor = Color.Black;
                }
                else
                    C.Font = new Font(C.Font.FontFamily, C.Font.Size * utils.ScaleFactor, C.Font.Style);
            }
            catch
            {
            }
        }

        private async Task LogUserActivityAsync(string action, string details)
        {
            //try
            //{
            //    await _auditService.LogAsync(
            //        action: action,
            //        entityType: "UserInterface",
            //        entityId: null,
            //        entityName: "MainForm",
            //        oldValues: null,
            //        newValues: new { Details = details, SessionId = _sessionId },
            //        userId: _currentUser.Id,
            //        module: "UI",
            //        severity: AuditSeverity.Information,
            //        success: true,
            //        errorMessage: null);
            //}
            //catch (Exception ex)
            //{
            //    // Log error but don't show to user
            //    Console.WriteLine($"Error logging user activity: {ex.Message}");
            //}
        }



    }

    public class ListtoDataTableConverter
    {
        public DataTable ToDataTable<T>(List<T> items)
        {
            DataTable dataTable = new DataTable(typeof(T).Name);
            //Get all the properties
            PropertyInfo[] Props = typeof(T).GetProperties(BindingFlags.Public | BindingFlags.Instance);
            foreach (PropertyInfo prop in Props)
            {
                //Setting column names as Property names
                dataTable.Columns.Add(prop.Name, prop.PropertyType);
            }
            foreach (T item in items)
            {
                var values = new object[Props.Length];
                for (int i = 0; i < Props.Length; i++)
                {
                    //inserting property values to datatable rows
                    values[i] = Props[i].GetValue(item, null);
                }
                dataTable.Rows.Add(values);
            }
            //put a breakpoint here and check datatable
            return dataTable;
        }

         

    }
}
