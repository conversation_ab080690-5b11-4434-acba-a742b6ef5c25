# نظام إدارة المنتجات - SmartCreator

## نظرة عامة

نظام إدارة المنتجات هو جزء متكامل من نظام SmartCreator المحاسبي، يوفر إدارة شاملة للمنتجات والمخزون مع نظام إشعارات متقدم وتسجيل شامل للأنشطة.

## الميزات الرئيسية

### 🏪 إدارة المنتجات
- **إضافة منتجات جديدة** مع جميع البيانات الأساسية
- **تعديل المنتجات الموجودة** مع تتبع التغييرات
- **حذف المنتجات** مع تأكيد الأمان
- **البحث والفلترة المتقدمة** في قائمة المنتجات
- **عرض تفصيلي** لجميع بيانات المنتج

### 📦 إدارة المخزون
- **تحديث المخزون** (زيادة/نقصان/تعيين قيمة محددة)
- **تتبع حركات المخزون** مع تسجيل الأسباب
- **حساب المخزون المتاح** في الوقت الفعلي
- **تحديد الحد الأدنى للمخزون** لكل منتج
- **منع المخزون السالب** مع تحذيرات مناسبة

### 🔔 نظام الإشعارات
- **إشعارات المخزون المنخفض** عند الوصول للحد الأدنى
- **إشعارات نفاد المخزون** عند وصول المخزون للصفر
- **إشعارات تلقائية** عند تحديث المخزون
- **إدارة صلاحية الإشعارات** مع انتهاء تلقائي

### 📊 تسجيل الأنشطة
- **تسجيل جميع العمليات** (إضافة، تعديل، حذف، تحديث مخزون)
- **تتبع المستخدم والوقت** لكل عملية
- **حفظ القيم القديمة والجديدة** للمقارنة
- **تصنيف الأنشطة** حسب النوع والأهمية

## البنية التقنية

### الكلاسات الرئيسية

#### `Product` (Entities/Accounting/Product.cs)
```csharp
public class Product : BaseEntity
{
    public string Code { get; set; }           // كود المنتج
    public string Name { get; set; }           // اسم المنتج
    public decimal Price { get; set; }         // السعر
    public int Stock { get; set; }             // المخزون الحالي
    public int LowStockThreshold { get; set; } // الحد الأدنى للمخزون
    public string Description { get; set; }    // الوصف
    // خصائص إضافية...
}
```

#### `ProductService` (Services/ProductService.cs)
خدمة شاملة لإدارة المنتجات تتضمن:
- `GetAllProductsAsync()` - جلب جميع المنتجات
- `GetProductByIdAsync(int id)` - جلب منتج بالمعرف
- `AddProductAsync(Product product)` - إضافة منتج جديد
- `UpdateProductAsync(Product product)` - تحديث منتج
- `DeleteProductAsync(int productId)` - حذف منتج
- `SearchProductsAsync(string searchTerm)` - البحث في المنتجات

#### `InventoryService` (Services/InventoryService.cs)
خدمة متخصصة لإدارة المخزون تتضمن:
- `UpdateStockAsync()` - تحديث المخزون (زيادة/نقصان)
- `SetStockAsync()` - تعيين مخزون محدد
- `GetLowStockProductsAsync()` - جلب المنتجات منخفضة المخزون

### النماذج (Forms)

#### `Frm_Product` - النموذج الرئيسي
- عرض قائمة المنتجات في `RJDataGridView`
- أزرار الإدارة (إضافة، تعديل، حذف، تحديث)
- شريط بحث متقدم
- عرض إحصائيات المخزون

#### `Frm_InventoryUpdate` - نموذج تحديث المخزون
- خيارات متعددة للتحديث (زيادة/نقصان/تعيين)
- معاينة فورية للتغييرات
- تحذيرات للمخزون السالب أو المنخفض
- إدخال سبب التحديث

#### `Frm_ProductTest` - نموذج الاختبار الشامل
- اختبارات تلقائية لجميع العمليات
- عرض نتائج الاختبارات في الوقت الفعلي
- اختبار الإشعارات والتنبيهات

## التصميم والواجهة

### RJControls المستخدمة
- **RJPanel** - حاويات مخصصة مع حواف منحنية
- **RJButton** - أزرار مع أيقونات FontAwesome
- **RJTextBox** - صناديق نص مع علامات مائية
- **RJDataGridView** - جداول بيانات احترافية
- **RJLabel** - تسميات مخصصة
- **RJComboBox** - قوائم منسدلة مخصصة

### الخطوط المستخدمة
- **Droid Arabic Kufi** - للعناوين والأزرار
- **Droid Sans Arabic** - للنصوص العادية
- **Consolas** - لعرض نتائج الاختبارات

### نظام الألوان
- **أخضر** (#28a745) - العمليات الناجحة والإضافة
- **أزرق** (#7b68ee) - التعديل والعمليات العادية
- **أحمر** (#dc3545) - الحذف والتحذيرات
- **برتقالي** (#ffc107) - التنبيهات والمخزون المنخفض

## كيفية الاستخدام

### 1. فتح نظام إدارة المنتجات
```csharp
var productForm = new Frm_Product();
productForm.Show();
```

### 2. إضافة منتج جديد
1. انقر على زر "إضافة"
2. أدخل بيانات المنتج (الكود، الاسم، السعر، إلخ)
3. حدد الحسابات المحاسبية المرتبطة
4. انقر "حفظ"

### 3. تحديث المخزون
1. اختر المنتج من القائمة
2. انقر على "تحديث مخزون"
3. اختر نوع التحديث (زيادة/نقصان/تعيين)
4. أدخل الكمية والسبب
5. انقر "حفظ"

### 4. مراقبة المخزون المنخفض
- انقر على "مخزون منخفض" لعرض المنتجات التي تحتاج إعادة طلب
- ستظهر إشعارات تلقائية عند انخفاض المخزون

## الاختبارات

### تشغيل الاختبارات الشاملة
```csharp
var testForm = new Frm_ProductTest();
testForm.Show();
```

### أنواع الاختبارات المتاحة
1. **اختبار إدارة المنتجات** - فتح النموذج الرئيسي
2. **اختبار العمليات الأساسية** - CRUD operations
3. **اختبار إدارة المخزون** - تحديث وإدارة المخزون
4. **اختبار الإشعارات** - نظام التنبيهات
5. **اختبار البحث** - وظائف البحث والفلترة
6. **اختبار المخزون المنخفض** - عرض المنتجات التي تحتاج إعادة طلب

## قاعدة البيانات

### جداول مطلوبة
```sql
-- جدول المنتجات الأساسي
CREATE TABLE Product (
    Id INTEGER PRIMARY KEY,
    Code TEXT NOT NULL,
    Name TEXT NOT NULL,
    Price DECIMAL(10,2),
    Stock INTEGER DEFAULT 0,
    LowStockThreshold INTEGER DEFAULT 10,
    Description TEXT,
    Account_IncomeId INTEGER,
    Account_ExpenseId INTEGER,
    Product_UomId INTEGER,
    Active INTEGER DEFAULT 1,
    LastStockUpdate DATETIME,
    LastUpdateBy TEXT,
    StockUpdateReason TEXT,
    Rb TEXT
);

-- جدول حركات المخزون (اختياري)
CREATE TABLE StockMovements (
    Id INTEGER PRIMARY KEY,
    ProductId INTEGER,
    OldStock INTEGER,
    NewStock INTEGER,
    Quantity INTEGER,
    MovementType TEXT,
    Reason TEXT,
    CreatedBy TEXT,
    CreatedDate DATETIME,
    Rb TEXT
);
```

## المتطلبات

### التبعيات المطلوبة
- .NET Framework 4.7.2 أو أحدث
- FontAwesome.Sharp (للأيقونات)
- Dapper (لقاعدة البيانات)
- System.Data.SQLite (قاعدة البيانات)

### RJControls المطلوبة
جميع عناصر RJControls متوفرة في المشروع ولا تحتاج تثبيت إضافي.

## الصيانة والتطوير

### إضافة ميزات جديدة
1. أضف الخصائص الجديدة لكلاس `Product`
2. حدث `ProductService` لدعم العمليات الجديدة
3. عدل النماذج لعرض الميزات الجديدة
4. أضف اختبارات للميزات الجديدة

### حل المشاكل الشائعة
- **المنتجات لا تظهر**: تأكد من قيمة `Rb` في قاعدة البيانات
- **الإشعارات لا تعمل**: تحقق من خدمة `NotificationService`
- **أخطاء في التحديث**: تأكد من صحة بيانات المنتج

## الدعم

للحصول على المساعدة أو الإبلاغ عن مشاكل، يرجى مراجعة:
- ملفات الاختبار في `Forms/Testing/`
- سجلات الأنشطة في قاعدة البيانات
- رسائل الخطأ في `UserActivities` table

---

**تم تطوير هذا النظام كجزء من مشروع SmartCreator المحاسبي المتكامل**
