# نظام إدارة المنتجات - SmartCreator

## نظرة عامة
نظام شامل لإدارة المنتجات في تطبيق SmartCreator يوفر جميع العمليات الأساسية لإدارة المنتجات والمخزون مع إشعارات المخزون المنخفض وتسجيل الأنشطة.

## الميزات الرئيسية

### 1. إدارة المنتجات
- **إضافة منتجات جديدة**: إضافة منتجات بكافة التفاصيل المطلوبة
- **تعديل المنتجات**: تحديث بيانات المنتجات الموجودة
- **حذف المنتجات**: حذف منتج أو عدة منتجات
- **عرض تفاصيل المنتج**: عرض شامل لجميع معلومات المنتج

### 2. إدارة المخزون
- **تتبع المخزون الحالي**: عرض الكميات المتوفرة
- **الحد الأدنى للمخزون**: تحديد حد أدنى لكل منتج
- **إشعارات المخزون المنخفض**: تنبيهات تلقائية عند الوصول للحد الأدنى
- **حالة المخزون**: عرض حالة المخزون (متوفر، منخفض، نفد)

### 3. البحث والتصفية
- **البحث المتقدم**: البحث بالاسم، الكود، أو الوصف
- **تصفية المنتجات**: عرض المنتجات حسب معايير مختلفة
- **المنتجات منخفضة المخزون**: عرض المنتجات التي تحتاج إعادة طلب

### 4. التقارير والإحصائيات
- **إحصائيات شاملة**: عدد المنتجات، القيمة الإجمالية، المنتجات المفعلة
- **تقارير المخزون**: تقارير مفصلة عن حالة المخزون
- **تسجيل الأنشطة**: تتبع جميع العمليات المنفذة على المنتجات

## البنية التقنية

### الكلاسات الرئيسية

#### 1. Product Entity (`SmartCreator.Entities.Accounting.Product`)
```csharp
public class Product : BaseEntity
{
    public string Code { get; set; }           // كود المنتج
    public string Name { get; set; }           // اسم المنتج
    public decimal Price { get; set; }         // السعر
    public int Stock { get; set; }             // المخزون الحالي
    public int LowStockThreshold { get; set; } // الحد الأدنى للمخزون
    public string Description { get; set; }    // الوصف
    public int Active { get; set; }            // حالة التفعيل
    
    // خصائص محسوبة
    public string StockStatus { get; }         // حالة المخزون
    public bool NeedsReorder { get; }          // يحتاج إعادة طلب
    
    // خصائص التتبع
    public DateTime? LastStockUpdate { get; set; }
    public string LastUpdateBy { get; set; }
    public string StockUpdateReason { get; set; }
}
```

#### 2. ProductService (`SmartCreator.Services.Products.ProductService`)
خدمة شاملة تحتوي على جميع العمليات:
- `GetAllProductsAsync()`: الحصول على جميع المنتجات
- `GetProductByIdAsync(int id)`: الحصول على منتج بالمعرف
- `AddProductAsync(Product product)`: إضافة منتج جديد
- `UpdateProductAsync(Product product, Product oldProduct)`: تحديث منتج
- `DeleteProductAsync(int productId)`: حذف منتج
- `SearchProductsAsync(string searchTerm)`: البحث في المنتجات
- `GetLowStockProductsAsync()`: الحصول على المنتجات منخفضة المخزون

### النماذج (Forms)

#### 1. النموذج الرئيسي (`Frm_Product`)
- عرض جميع المنتجات في جدول تفاعلي
- أزرار الإدارة العلوية (إضافة، تعديل، عرض، حذف، طباعة)
- شريط البحث مع خيارات متعددة
- إحصائيات سفلية شاملة
- دعم التحديد المتعدد للحذف

#### 2. نموذج الإضافة (`Frm_ProductAdd`)
- نموذج لإضافة منتج جديد
- التحقق من صحة البيانات
- منع تكرار الأكواد
- قيم افتراضية ذكية

#### 3. نموذج التعديل (`Frm_ProductEdit`)
- تعديل بيانات المنتج الموجود
- عرض البيانات الحالية
- تتبع التغييرات
- إمكانية إعادة التعيين

#### 4. نموذج العرض (`Frm_ProductView`)
- عرض تفاصيل المنتج بشكل للقراءة فقط
- معلومات شاملة ومنظمة
- أزرار للتعديل والطباعة
- عرض حالة المخزون بألوان مميزة

#### 5. نموذج الاختبار (`Frm_ProductTest`)
- اختبار شامل لجميع وظائف النظام
- إنشاء بيانات اختبار تلقائية
- تشغيل اختبارات متعددة
- عرض نتائج الاختبارات مع الأوقات

## التصميم والواجهة

### المكونات المستخدمة
- **RJControls**: مكتبة التحكم المخصصة
- **RJChildForm**: النموذج الأساسي للوراثة
- **RJDataGridView**: جدول البيانات المخصص
- **RJButton, RJTextBox, RJLabel**: عناصر التحكم المخصصة
- **خطوط Droid**: خطوط عربية واضحة

### نظام الألوان
- **الخلفية الرئيسية**: رمادي داكن (#1E1E1E)
- **الأزرار**: ألوان متدرجة حسب الوظيفة
  - إضافة: أخضر (#4CAF50)
  - تعديل: أصفر (#FFC107)
  - عرض: أزرق (#2196F3)
  - حذف: أحمر (#F44336)
  - طباعة: رمادي (#607D8B)
- **حالة المخزون**: 
  - متوفر: أخضر فاتح
  - منخفض: برتقالي
  - نفد: أحمر

## قاعدة البيانات

### جدول Product
```sql
CREATE TABLE Product (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    Code TEXT UNIQUE,
    Name TEXT NOT NULL,
    Description TEXT,
    Price DECIMAL(10,2) DEFAULT 0,
    Stock INTEGER DEFAULT 0,
    LowStockThreshold INTEGER DEFAULT 10,
    Account_IncomeId INTEGER,
    Account_ExpenseId INTEGER,
    Product_UomId INTEGER,
    Active INTEGER DEFAULT 1,
    Rb TEXT,
    LastStockUpdate DATETIME,
    LastUpdateBy TEXT,
    StockUpdateReason TEXT,
    CreatedDate DATETIME DEFAULT CURRENT_TIMESTAMP,
    ModifiedDate DATETIME
);
```

## الإشعارات والتنبيهات

### إشعارات المخزون المنخفض
- **تلقائية**: تُرسل عند إضافة أو تحديث منتج
- **معايير الإرسال**: عندما يكون المخزون <= الحد الأدنى
- **محتوى الإشعار**: اسم المنتج، الكود، المخزون الحالي، الحد الأدنى
- **مدة الصلاحية**: 30 يوم

## تسجيل الأنشطة

### الأنشطة المسجلة
- إضافة منتج جديد
- تعديل بيانات منتج
- حذف منتج
- عرض منتج
- البحث في المنتجات
- عرض المنتجات منخفضة المخزون

### معلومات النشاط
- المستخدم المنفذ
- نوع العملية
- تفاصيل العملية
- البيانات القديمة والجديدة (للتحديث)
- وقت التنفيذ
- حالة النجاح/الفشل

## الاختبارات

### أنواع الاختبارات
1. **اختبار الإضافة**: إضافة منتجات جديدة
2. **اختبار التحديث**: تعديل بيانات المنتجات
3. **اختبار الحذف**: حذف المنتجات
4. **اختبار البحث**: البحث والتصفية
5. **اختبار المخزون المنخفض**: عرض المنتجات التي تحتاج إعادة طلب

### بيانات الاختبار
- منتجات متنوعة بحالات مخزون مختلفة
- منتجات مفعلة وغير مفعلة
- أسعار وكميات متنوعة
- أكواد فريدة للاختبار

## الاستخدام

### تشغيل النظام
1. فتح النموذج الرئيسي `Frm_Product`
2. استخدام أزرار الإدارة العلوية للعمليات المختلفة
3. البحث باستخدام شريط البحث
4. مراقبة الإحصائيات في الشريط السفلي

### إضافة منتج جديد
1. النقر على زر "إضافة"
2. ملء البيانات المطلوبة
3. التحقق من عدم تكرار الكود
4. حفظ المنتج

### تعديل منتج
1. تحديد المنتج من الجدول
2. النقر على زر "تعديل"
3. تعديل البيانات المطلوبة
4. حفظ التغييرات

### حذف منتج
1. تحديد منتج أو عدة منتجات
2. النقر على زر "حذف"
3. تأكيد عملية الحذف

## الصيانة والتطوير

### إضافة ميزات جديدة
- إضافة حقول جديدة في كلاس Product
- تحديث النماذج والخدمات
- إضافة اختبارات للميزات الجديدة

### تحسين الأداء
- فهرسة قاعدة البيانات
- تحسين استعلامات البحث
- تحميل البيانات بشكل تدريجي

### الأمان
- التحقق من صلاحيات المستخدم
- تشفير البيانات الحساسة
- تسجيل جميع العمليات

## المتطلبات التقنية

### البرمجيات المطلوبة
- .NET Framework 4.7.2 أو أحدث
- SQLite لقاعدة البيانات
- مكتبة RJControls المخصصة

### المكتبات المستخدمة
- System.Data.SQLite
- Dapper (اختياري)
- Newtonsoft.Json (للتسلسل)

## الدعم والمساعدة

### الأخطاء الشائعة
- **كود مكرر**: التأكد من عدم وجود كود مشابه
- **بيانات مفقودة**: التحقق من ملء الحقول المطلوبة
- **مشاكل الاتصال**: التحقق من قاعدة البيانات

### التواصل
- للدعم التقني: راجع وثائق النظام
- للتطوير: راجع كود المصدر والتعليقات
