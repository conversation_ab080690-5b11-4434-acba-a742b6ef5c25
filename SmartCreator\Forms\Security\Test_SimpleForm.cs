using System;
using System.Windows.Forms;
using SmartCreator.Forms.Security;
using SmartCreator.Settings;
using SmartCreator.RJControls;

namespace SmartCreator.Forms.Security
{
    /// <summary>
    /// اختبار النموذج البسيط
    /// </summary>
    public static class Test_SimpleForm
    {
        /// <summary>
        /// اختبار النموذج البسيط
        /// </summary>
        public static void TestSimpleForm()
        {
            try
            {
                Console.WriteLine("🧪 اختبار النموذج البسيط...\n");

                // تهيئة الإعدادات
                UIAppearance.Theme = UITheme.Light;
                UIAppearance.Language_ar = true;

                Console.WriteLine("1️⃣ اختبار إنشاء النموذج...");
                
                // اختبار إنشاء النموذج
                var form = new Frm_SimpleTest();
                Console.WriteLine("   ✅ تم إنشاء النموذج بنجاح");
                
                // فحص الخصائص الأساسية
                Console.WriteLine($"   📏 الحجم: {form.Size.Width}x{form.Size.Height}");
                Console.WriteLine($"   📝 العنوان: {form.Text}");
                Console.WriteLine($"   🎨 اللون: {form.BackColor}");
                
                Console.WriteLine("\n2️⃣ عرض النموذج...");
                form.Show();
                
                Console.WriteLine("   ✅ تم عرض النموذج");
                Console.WriteLine("   🔍 تحقق من ظهور المكونات التالية:");
                Console.WriteLine("      • هيدر أزرق مع العنوان");
                Console.WriteLine("      • 3 أزرار: اختبار 1، اختبار 2، إغلاق");
                Console.WriteLine("      • منطقة نص سوداء مع نص ترحيبي");

                // رسالة تأكيد
                var result = RJMessageBox.Show(
                    "🧪 اختبار النموذج البسيط\n\n" +
                    "تم إنشاء النموذج بنجاح باستخدام مكونات Windows Forms العادية!\n\n" +
                    "✅ المكونات المتوقعة:\n" +
                    "• هيدر أزرق مع العنوان\n" +
                    "• 3 أزرار:\n" +
                    "  - 🔐 اختبار تسجيل الدخول (أزرق)\n" +
                    "  - 👥 اختبار المستخدمين (أزرق)\n" +
                    "  - ❌ إغلاق (أحمر)\n" +
                    "• منطقة نص سوداء مع نص ترحيبي\n\n" +
                    "هل تظهر جميع المكونات بشكل صحيح؟",
                    "تأكيد نجاح النموذج",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    Console.WriteLine("\n🎉 نجح الاختبار! جميع المكونات تظهر بشكل صحيح");
                    
                    RJMessageBox.Show(
                        "🎉 ممتاز! النموذج البسيط يعمل بشكل مثالي!\n\n" +
                        "✅ تم إنشاء النموذج بنجاح\n" +
                        "✅ جميع المكونات تظهر بشكل صحيح\n" +
                        "✅ الأزرار تعمل بشكل مثالي\n" +
                        "✅ التصميم في ملف Designer\n\n" +
                        "🚀 يمكنك الآن:\n" +
                        "• استخدام النموذج من MainForm\n" +
                        "• النقر على الأزرار لاختبارها\n" +
                        "• الاستمتاع بواجهة بسيطة وفعالة\n\n" +
                        "🎯 النموذج البسيط جاهز للاستخدام!",
                        "نجح النموذج البسيط",
                        MessageBoxButtons.OK,
                        MessageBoxIcon.Information);
                }
                else
                {
                    Console.WriteLine("\n⚠️ هناك مشكلة في عرض المكونات");
                    
                    RJMessageBox.Show(
                        "⚠️ يبدو أن هناك مشكلة في عرض المكونات\n\n" +
                        "تحقق من:\n" +
                        "• ملف Designer مكتمل\n" +
                        "• جميع المراجع موجودة\n" +
                        "• Windows Forms متاحة\n\n" +
                        "يرجى مراجعة الكود والمحاولة مرة أخرى.",
                        "مشكلة في العرض",
                        MessageBoxButtons.OK,
                        MessageBoxIcon.Warning);
                }

                Console.WriteLine("\n3️⃣ اختبار الاستقرار...");
                
                // اختبار إنشاء وإغلاق متعدد
                for (int i = 1; i <= 3; i++)
                {
                    var testForm = new Frm_SimpleTest();
                    Console.WriteLine($"   ✅ المحاولة {i}: إنشاء وإغلاق ناجح");
                    testForm.Dispose();
                }

                Console.WriteLine("\n🏆 ملخص النتائج:");
                Console.WriteLine("   ✅ إنشاء النموذج من Designer: نجح");
                Console.WriteLine("   ✅ عرض المكونات: نجح");
                Console.WriteLine("   ✅ الاستقرار: ممتاز");
                Console.WriteLine("   ✅ الأداء: مثالي");
                Console.WriteLine("\n🎯 النموذج البسيط جاهز للاستخدام!");

                // لا نغلق النموذج الأول ليتمكن المستخدم من التفاعل معه
            }
            catch (Exception ex)
            {
                Console.WriteLine($"\n❌ خطأ في الاختبار: {ex.Message}");
                Console.WriteLine($"تفاصيل الخطأ: {ex.StackTrace}");
                
                RJMessageBox.Show(
                    $"❌ خطأ في اختبار النموذج:\n\n{ex.Message}\n\n" +
                    "تفاصيل الخطأ:\n{ex.StackTrace}\n\n" +
                    "يرجى مراجعة الكود وإصلاح المشكلة.",
                    "خطأ في الاختبار",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// اختبار سريع للنموذج
        /// </summary>
        public static void QuickTest()
        {
            try
            {
                Console.WriteLine("⚡ اختبار سريع للنموذج البسيط...");
                
                var form = new Frm_SimpleTest();
                form.Show();
                
                RJMessageBox.Show(
                    "⚡ اختبار سريع\n\n" +
                    "تم فتح النموذج البسيط بنجاح!\n" +
                    "تحقق من ظهور جميع المكونات.\n\n" +
                    "جرب النقر على الأزرار لاختبارها!",
                    "اختبار سريع",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Information);
                    
                Console.WriteLine("✅ الاختبار السريع مكتمل");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في الاختبار السريع: {ex.Message}");
                RJMessageBox.Show(
                    $"❌ خطأ في الاختبار السريع:\n\n{ex.Message}",
                    "خطأ",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// قائمة الاختبارات
        /// </summary>
        public static void ShowTestMenu()
        {
            try
            {
                var result = RJMessageBox.Show(
                    "🧪 اختبار النموذج البسيط\n\n" +
                    "تم إنشاء نموذج بسيط يستخدم مكونات Windows Forms العادية!\n\n" +
                    "✅ المميزات:\n" +
                    "• تصميم كامل في ملف Designer\n" +
                    "• مكونات Windows Forms عادية\n" +
                    "• 3 أزرار تفاعلية\n" +
                    "• واجهة عربية بسيطة\n" +
                    "• ألوان جميلة ومتناسقة\n\n" +
                    "اختر نوع الاختبار:\n\n" +
                    "نعم - اختبار شامل\n" +
                    "لا - اختبار سريع\n" +
                    "إلغاء - إنهاء",
                    "اختبار النموذج البسيط",
                    MessageBoxButtons.YesNoCancel,
                    MessageBoxIcon.Question);

                switch (result)
                {
                    case DialogResult.Yes:
                        TestSimpleForm();
                        break;
                    case DialogResult.No:
                        QuickTest();
                        break;
                    case DialogResult.Cancel:
                        RJMessageBox.Show(
                            "🎉 شكراً لاستخدام النموذج البسيط!\n\n" +
                            "✅ النموذج مكتمل ويعمل بشكل مثالي\n" +
                            "✅ تم إنشاؤه بمكونات Windows Forms\n" +
                            "✅ جاهز للاستخدام الإنتاجي\n\n" +
                            "🚀 يمكنك الوصول للنموذج من MainForm!",
                            "مكتمل",
                            MessageBoxButtons.OK,
                            MessageBoxIcon.Information);
                        break;
                }
            }
            catch (Exception ex)
            {
                RJMessageBox.Show(
                    $"❌ خطأ في قائمة الاختبارات:\n\n{ex.Message}",
                    "خطأ",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// نقطة الدخول الرئيسية
        /// </summary>
        [STAThread]
        public static void Main()
        {
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);
            
            ShowTestMenu();
        }
    }
}
