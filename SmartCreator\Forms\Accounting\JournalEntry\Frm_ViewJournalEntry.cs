using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using SmartCreator.Entities.Accounting;
using SmartCreator.Services.Accounting;
using SmartCreator.Service;
using SmartCreator.RJControls;
using SmartCreator.RJForms;
using SmartCreator.Utils;

namespace SmartCreator.Forms.Accounting
{
    /// <summary>
    /// نموذج عرض تفاصيل القيد المحاسبي
    /// </summary>
    public partial class Frm_ViewJournalEntry : RJChildForm
    {
        #region المتغيرات

        private readonly JournalEntryService _journalEntryService;
        private readonly AccountService _accountService;
        private readonly JournalEntry _journalEntry;
        private List<Account> _accounts;
        private List<JournalEntryDetail> _details;
        private bool _isLoading = false;

        #endregion

        #region البناء

        public Frm_ViewJournalEntry(JournalEntryService journalEntryService,
            AccountService accountService, JournalEntry journalEntry)
        {
            _journalEntryService = journalEntryService;
            _accountService = accountService;
            _journalEntry = journalEntry;
            _accounts = new List<Account>();
            _details = new List<JournalEntryDetail>();

            InitializeComponent();
            SetupForm();
            LoadDataAsync();

            utils utils = new utils();
            utils.Control_textSize1(this);

        }

        #endregion

        #region إعداد النموذج

        /// <summary>
        /// إعداد النموذج
        /// </summary>
        private void SetupForm()
        {
            this.Text = $"عرض القيد - {_journalEntry.EntryNumber}";
            lblTitle.Text = $"عرض القيد المحاسبي - {_journalEntry.EntryNumber}";

            // إعداد أعمدة جدول التفاصيل
            SetupDetailsGrid();

            // تعبئة بيانات القيد
            PopulateJournalEntryData();

            // تعطيل جميع عناصر الإدخال
            DisableInputControls();
        }

        /// <summary>
        /// إعداد أعمدة جدول التفاصيل
        /// </summary>
        private void SetupDetailsGrid()
        {
            dgvDetails.AutoGenerateColumns = false;
            dgvDetails.AllowUserToAddRows = false;
            dgvDetails.AllowUserToDeleteRows = false;
            dgvDetails.ReadOnly = true;
            dgvDetails.Columns.Clear();

            dgvDetails.Columns.AddRange(new DataGridViewColumn[]
            {
                new DataGridViewTextBoxColumn
                {
                    Name = "LineNumber",
                    HeaderText = "الرقم",
                    DataPropertyName = "LineNumber",
                    Width = 60,
                    ReadOnly = true
                },
                new DataGridViewTextBoxColumn
                {
                    Name = "AccountCode",
                    HeaderText = "كود الحساب",
                    Width = 120,
                    ReadOnly = true
                },
                new DataGridViewTextBoxColumn
                {
                    Name = "AccountName",
                    HeaderText = "اسم الحساب",
                    Width = 250,
                    ReadOnly = true
                },
                new DataGridViewTextBoxColumn
                {
                    Name = "Description",
                    HeaderText = "البيان",
                    DataPropertyName = "Description",
                    Width = 250,
                    ReadOnly = true
                },
                new DataGridViewTextBoxColumn
                {
                    Name = "DebitAmount",
                    HeaderText = "مدين",
                    DataPropertyName = "DebitAmount",
                    Width = 120,
                    ReadOnly = true,
                    DefaultCellStyle = new DataGridViewCellStyle
                    {
                        Format = "N2",
                        Alignment = DataGridViewContentAlignment.MiddleRight
                    }
                },
                new DataGridViewTextBoxColumn
                {
                    Name = "CreditAmount",
                    HeaderText = "دائن",
                    DataPropertyName = "CreditAmount",
                    Width = 120,
                    ReadOnly = true,
                    DefaultCellStyle = new DataGridViewCellStyle
                    {
                        Format = "N2",
                        Alignment = DataGridViewContentAlignment.MiddleRight
                    }
                }
            });
        }

        /// <summary>
        /// تعبئة بيانات القيد
        /// </summary>
        private void PopulateJournalEntryData()
        {
            if (_journalEntry == null) return;

            txtEntryNumber.Text = _journalEntry.EntryNumber;
            dtpDate.Value = _journalEntry.Date;
            txtDescription.Text = _journalEntry.Description;
            txtReference.Text = _journalEntry.Reference;
            txtType.Text = JournalEntryTypeInfo.GetArabicName(_journalEntry.Type);
            txtStatus.Text = JournalEntryStatusInfo.GetArabicName(_journalEntry.Status);
            txtCreatedBy.Text = _journalEntry.CreatedBy;
            txtCreatedDate.Text = _journalEntry.CreatedDate.ToString("yyyy-MM-dd HH:mm");

            if (_journalEntry.UpdatedBy != null)
            {
                txtUpdatedBy.Text = _journalEntry.UpdatedBy;
                txtUpdatedDate.Text = _journalEntry.UpdatedDate?.ToString("yyyy-MM-dd HH:mm");
            }

            if (_journalEntry.ApprovedBy != null)
            {
                txtApprovedBy.Text = _journalEntry.ApprovedBy;
                txtApprovedDate.Text = _journalEntry.ApprovedDate?.ToString("yyyy-MM-dd HH:mm");
            }

            // تلوين الحالة
            switch (_journalEntry.Status)
            {
                case JournalEntryStatus.Draft:
                    txtStatus.BackColor = Color.FromArgb(255, 248, 220);
                    txtStatus.ForeColor = Color.DarkOrange;
                    break;
                case JournalEntryStatus.Posted:
                    txtStatus.BackColor = Color.FromArgb(220, 255, 220);
                    txtStatus.ForeColor = Color.DarkGreen;
                    break;
                case JournalEntryStatus.Approved:
                    txtStatus.BackColor = Color.FromArgb(220, 220, 255);
                    txtStatus.ForeColor = Color.DarkBlue;
                    break;
                case JournalEntryStatus.Cancelled:
                    txtStatus.BackColor = Color.FromArgb(255, 220, 220);
                    txtStatus.ForeColor = Color.DarkRed;
                    break;
            }
        }

        /// <summary>
        /// تعطيل عناصر الإدخال
        /// </summary>
        private void DisableInputControls()
        {
            txtEntryNumber.Enabled = false;
            dtpDate.Enabled = false;
            txtDescription.Enabled = false;
            txtReference.Enabled = false;
            txtType.Enabled = false;
            txtStatus.Enabled = false;
            txtCreatedBy.Enabled = false;
            txtCreatedDate.Enabled = false;
            txtUpdatedBy.Enabled = false;
            txtUpdatedDate.Enabled = false;
            txtApprovedBy.Enabled = false;
            txtApprovedDate.Enabled = false;
        }

        #endregion

        #region تحميل البيانات

        /// <summary>
        /// تحميل البيانات
        /// </summary>
        private async void LoadDataAsync()
        {
            if (_isLoading) return;

            try
            {
                _isLoading = true;
                ShowLoadingIndicator(true);

                // تحميل الحسابات والتفاصيل بشكل متوازي
                var accountsTask = _accountService.GetActiveAccountsAsync();
                var detailsTask = _journalEntryService.GetJournalEntryDetailsAsync(_journalEntry.Id);

                await Task.WhenAll(accountsTask, detailsTask);

                _accounts = accountsTask.Result;
                _details = detailsTask.Result.ToList();

                // إضافة معلومات الحساب لكل تفصيل
                foreach (var detail in _details)
                {
                    var account = _accounts.FirstOrDefault(a => a.Id == detail.AccountId);
                    if (account != null)
                    {
                        detail.Account = account;
                    }
                }

                RefreshDetailsGrid();
                CalculateAndDisplayTotals();
            }
            catch (Exception ex)
            {
                RJMessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                _isLoading = false;
                ShowLoadingIndicator(false);
            }
        }

        /// <summary>
        /// عرض مؤشر التحميل
        /// </summary>
        private void ShowLoadingIndicator(bool show)
        {
            if (this.InvokeRequired)
            {
                this.Invoke(new Action<bool>(ShowLoadingIndicator), show);
                return;
            }

            lblStatus.Text = show ? "جاري التحميل..." : "جاهز";
            lblStatus.ForeColor = show ? Color.Orange : Color.Green;
        }

        /// <summary>
        /// تحديث جدول التفاصيل
        /// </summary>
        private void RefreshDetailsGrid()
        {
            if (this.InvokeRequired)
            {
                this.Invoke(new Action(RefreshDetailsGrid));
                return;
            }

            dgvDetails.DataSource = _details;

            // تحديث معلومات الحسابات
            foreach (DataGridViewRow row in dgvDetails.Rows)
            {
                if (row.DataBoundItem is JournalEntryDetail detail && detail.Account != null)
                {
                    row.Cells["AccountCode"].Value = detail.Account.Code;
                    row.Cells["AccountName"].Value = detail.Account.Name;
                }
            }
        }

        /// <summary>
        /// حساب وعرض الإجماليات
        /// </summary>
        private void CalculateAndDisplayTotals()
        {
            var totalDebit = _details.Sum(d => d.DebitAmount);
            var totalCredit = _details.Sum(d => d.CreditAmount);
            var difference = totalDebit - totalCredit;

            lblTotalDebit.Text = $"إجمالي المدين: {totalDebit:N2}";
            lblTotalCredit.Text = $"إجمالي الدائن: {totalCredit:N2}";
            lblDifference.Text = $"الفرق: {difference:N2}";

            // تلوين الفرق
            if (difference == 0)
            {
                lblDifference.ForeColor = Color.Green;
                lblBalance.Text = "القيد متوازن ✓";
                lblBalance.ForeColor = Color.Green;
            }
            else
            {
                lblDifference.ForeColor = Color.Red;
                lblBalance.Text = "القيد غير متوازن ✗";
                lblBalance.ForeColor = Color.Red;
            }
        }

        #endregion

        #region أحداث الأزرار

        /// <summary>
        /// طباعة القيد
        /// </summary>
        private void BtnPrint_Click(object sender, EventArgs e)
        {
            try
            {
                // يمكن إضافة منطق الطباعة هنا
                RJMessageBox.Show("سيتم إضافة وظيفة الطباعة قريباً", "معلومات",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                RJMessageBox.Show($"خطأ في الطباعة: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تصدير القيد
        /// </summary>
        private void BtnExport_Click(object sender, EventArgs e)
        {
            try
            {
                var saveDialog = new SaveFileDialog
                {
                    Filter = "PDF Files|*.pdf|Excel Files|*.xlsx",
                    FileName = $"القيد_{_journalEntry.EntryNumber}.pdf"
                };

                if (saveDialog.ShowDialog() == DialogResult.OK)
                {
                    // يمكن إضافة منطق التصدير هنا
                    RJMessageBox.Show("سيتم إضافة وظيفة التصدير قريباً", "معلومات",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                RJMessageBox.Show($"خطأ في التصدير: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// إغلاق النموذج
        /// </summary>
        private void BtnClose_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        #endregion

        private void Frm_ViewJournalEntry_SizeChanged(object sender, EventArgs e)
        {
            this.Refresh();
            pnlDetails.Refresh();
            pnlAudit.Refresh();
            pnlButtons.Refresh();
            pnlDetails.Refresh();
            pnlHeader.Refresh();
            pnlMain.Refresh();
            pnlStatus.Refresh();
            pnlTotals.Refresh();
        }
    }
}