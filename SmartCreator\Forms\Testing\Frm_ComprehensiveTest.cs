using System;
using System.Drawing;
using System.Threading.Tasks;
using System.Windows.Forms;
using SmartCreator.Forms.Accounting;
using SmartCreator.RJControls;
using SmartCreator.Settings;
using SmartCreator.Services.Accounting;
using SmartCreator.Services.Security;
using SmartCreator.Service;
using SmartCreator.Data;
using System.Linq;
using SmartCreator.RJForms;

namespace SmartCreator.Forms.Testing
{
    /// <summary>
    /// نموذج الاختبار الشامل لجميع الميزات
    /// </summary>
    public partial class Frm_ComprehensiveTest : RJChildForm
    {
        #region المتغيرات

        private readonly JournalEntryService _journalEntryService;
        private readonly AccountService _accountService;
        private readonly UserActivityService _activityService;

        #endregion

        #region البناء

        public Frm_ComprehensiveTest()
        {
            // إعداد الخدمات
            var dataAccess = new Smart_DataAccess();
            _activityService = new UserActivityService(dataAccess);
            _journalEntryService = new JournalEntryService(dataAccess, _activityService);
            _accountService = new AccountService(new DatabaseHelper());

            InitializeComponent();
            SetupForm();
        }

        #endregion

        #region إعداد النموذج

        /// <summary>
        /// إعداد النموذج
        /// </summary>
        private void SetupForm()
        {
            // العناصر تم إنشاؤها في Designer مع الأحداث
            // لا حاجة لإعداد إضافي
        }







        #endregion

        #region أحداث الأزرار - عامة

        /// <summary>
        /// إغلاق النموذج
        /// </summary>
        private void BtnClose_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        #endregion

        #region أحداث الأزرار - المحاسبة

        /// <summary>
        /// اختبار واجهة القيود
        /// </summary>
        private void BtnTestJournalEntries_Click(object sender, EventArgs e)
        {
            try
            {
                RJMessageBox.Show(
                    "🧪 اختبار واجهة القيود\n\n" +
                    "سيتم فتح واجهة إدارة القيود المحاسبية...\n\n" +
                    "الميزات المتاحة:\n" +
                    "• عرض جميع القيود\n" +
                    "• إضافة قيد جديد\n" +
                    "• تعديل القيود\n" +
                    "• حذف القيود\n" +
                    "• ترحيل واعتماد القيود\n" +
                    "• فلترة وبحث متقدم\n" +
                    "• تصدير البيانات",
                    "اختبار واجهة القيود",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Information);

                var journalForm = new Frm_JournalEntries(_journalEntryService, _accountService, _activityService);
                journalForm.Show();
            }
            catch (Exception ex)
            {
                RJMessageBox.Show($"خطأ في فتح واجهة القيود: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// اختبار إضافة قيد
        /// </summary>
        private void BtnTestAddEntry_Click(object sender, EventArgs e)
        {
            try
            {
                RJMessageBox.Show(
                    "➕ اختبار إضافة قيد جديد\n\n" +
                    "سيتم فتح نموذج إضافة قيد محاسبي جديد...\n\n" +
                    "الميزات المتاحة:\n" +
                    "• إدخال بيانات القيد الأساسية\n" +
                    "• إضافة تفاصيل القيد (مدين/دائن)\n" +
                    "• اختيار الحسابات من القائمة\n" +
                    "• التحقق من توازن القيد\n" +
                    "• حفظ القيد كمسودة أو ترحيله\n" +
                    "• واجهة سهلة ومرنة",
                    "اختبار إضافة قيد",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Information);

                var addForm = new Frm_AddEditJournalEntry(_journalEntryService, _accountService);
                addForm.Show();
            }
            catch (Exception ex)
            {
                RJMessageBox.Show($"خطأ في فتح نموذج الإضافة: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// اختبار عرض قيد
        /// </summary>
        private void BtnTestViewEntry_Click(object sender, EventArgs e)
        {
            try
            {
                // تشغيل اختبار نموذج العرض
                ViewJournalEntry_Test.TestViewJournalEntryForm();
            }
            catch (Exception ex)
            {
                RJMessageBox.Show($"خطأ في اختبار عرض القيد: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// اختبار شجرة الحسابات
        /// </summary>
        private void BtnTestAccounts_Click(object sender, EventArgs e)
        {
            try
            {
                RJMessageBox.Show(
                    "🌳 اختبار شجرة الحسابات\n\n" +
                    "اختبار خدمات إدارة الحسابات...\n\n" +
                    "الميزات المتاحة:\n" +
                    "• عرض شجرة الحسابات الهرمية\n" +
                    "• إضافة حسابات جديدة\n" +
                    "• تعديل الحسابات الموجودة\n" +
                    "• حذف الحسابات\n" +
                    "• البحث في الحسابات\n" +
                    "• إنشاء شجرة افتراضية\n" +
                    "• تصدير بيانات الحسابات",
                    "اختبار شجرة الحسابات",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Information);

                // يمكن إضافة نموذج شجرة الحسابات هنا
                RJMessageBox.Show("سيتم إضافة نموذج شجرة الحسابات قريباً", "معلومات",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                RJMessageBox.Show($"خطأ في اختبار الحسابات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region أحداث الأزرار - العناصر المخصصة

        /// <summary>
        /// اختبار RJTextBox ReadOnly
        /// </summary>
        private void BtnTestReadOnly_Click(object sender, EventArgs e)
        {
            try
            {
                // تشغيل اختبار خاصية ReadOnly
                TestRJTextBoxReadOnly();
            }
            catch (Exception ex)
            {
                RJMessageBox.Show($"خطأ في اختبار ReadOnly: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// اختبار RJ Controls
        /// </summary>
        private void BtnTestRJControls_Click(object sender, EventArgs e)
        {
            try
            {
                RJMessageBox.Show(
                    "🎨 اختبار RJ Controls\n\n" +
                    "اختبار جميع العناصر المخصصة...\n\n" +
                    "العناصر المتاحة:\n" +
                    "• RJTextBox (مع خاصية ReadOnly الجديدة)\n" +
                    "• RJButton (أزرار مخصصة)\n" +
                    "• RJPanel (لوحات مخصصة)\n" +
                    "• RJLabel (تسميات مخصصة)\n" +
                    "• RJDatePicker (اختيار التاريخ)\n" +
                    "• RJDataGridView (جداول البيانات)\n" +
                    "• RJComboBox (قوائم منسدلة)\n\n" +
                    "جميع العناصر تدعم:\n" +
                    "• التصميم المخصص\n" +
                    "• الألوان المتدرجة\n" +
                    "• الحواف المنحنية\n" +
                    "• التوافق مع الثيمات",
                    "اختبار RJ Controls",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Information);

                // يمكن إضافة نموذج اختبار العناصر هنا
                CreateRJControlsTestForm();
            }
            catch (Exception ex)
            {
                RJMessageBox.Show($"خطأ في اختبار RJ Controls: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// اختبار التصميم
        /// </summary>
        private void BtnTestDesign_Click(object sender, EventArgs e)
        {
            try
            {
                RJMessageBox.Show(
                    "🎨 اختبار التصميم\n\n" +
                    "اختبار عناصر التصميم والثيمات...\n\n" +
                    "الميزات المتاحة:\n" +
                    "• ثيم فاتح ومظلم\n" +
                    "• ألوان متناسقة\n" +
                    "• خطوط عربية وإنجليزية\n" +
                    "• تخطيط مرن ومتجاوب\n" +
                    "• أيقونات ورموز تعبيرية\n" +
                    "• تأثيرات بصرية جميلة\n" +
                    "• واجهة سهلة الاستخدام",
                    "اختبار التصميم",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Information);

                // تبديل الثيم للاختبار
                UIAppearance.Theme = UIAppearance.Theme == UITheme.Light ? UITheme.Dark : UITheme.Light;

                RJMessageBox.Show($"تم تبديل الثيم إلى: {UIAppearance.Theme}", "تبديل الثيم",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                RJMessageBox.Show($"خطأ في اختبار التصميم: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region أحداث الأزرار - النظام

        /// <summary>
        /// اختبار قاعدة البيانات
        /// </summary>
        private void BtnTestDatabase_Click(object sender, EventArgs e)
        {
            try
            {
                RJMessageBox.Show(
                    "🗄️ اختبار قاعدة البيانات\n\n" +
                    "اختبار الاتصال والعمليات...\n\n" +
                    "العمليات المتاحة:\n" +
                    "• اختبار الاتصال بقاعدة البيانات\n" +
                    "• إنشاء الجداول الأساسية\n" +
                    "• إدراج بيانات تجريبية\n" +
                    "• اختبار العمليات CRUD\n" +
                    "• اختبار الاستعلامات المعقدة\n" +
                    "• اختبار الأداء\n" +
                    "• نسخ احتياطي واستعادة",
                    "اختبار قاعدة البيانات",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Information);

                // اختبار الاتصال
                TestDatabaseConnection();
            }
            catch (Exception ex)
            {
                RJMessageBox.Show($"خطأ في اختبار قاعدة البيانات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// اختبار الخدمات
        /// </summary>
        private void BtnTestServices_Click(object sender, EventArgs e)
        {
            try
            {
                RJMessageBox.Show(
                    "⚙️ اختبار الخدمات\n\n" +
                    "اختبار جميع خدمات النظام...\n\n" +
                    "الخدمات المتاحة:\n" +
                    "• JournalEntryService (خدمة القيود)\n" +
                    "• AccountService (خدمة الحسابات)\n" +
                    "• UserActivityService (خدمة الأنشطة)\n" +
                    "• SecurityService (خدمة الأمان)\n" +
                    "• ReportService (خدمة التقارير)\n" +
                    "• BackupService (خدمة النسخ الاحتياطي)\n\n" +
                    "جميع الخدمات تدعم:\n" +
                    "• العمليات غير المتزامنة\n" +
                    "• معالجة الأخطاء\n" +
                    "• تسجيل الأنشطة\n" +
                    "• التحقق من الصلاحيات",
                    "اختبار الخدمات",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Information);

                // اختبار الخدمات
                TestAllServices();
            }
            catch (Exception ex)
            {
                RJMessageBox.Show($"خطأ في اختبار الخدمات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// اختبار الأمان
        /// </summary>
        private void BtnTestSecurity_Click(object sender, EventArgs e)
        {
            try
            {
                RJMessageBox.Show(
                    "🔒 اختبار الأمان\n\n" +
                    "اختبار ميزات الأمان والحماية...\n\n" +
                    "الميزات المتاحة:\n" +
                    "• تسجيل الدخول والخروج\n" +
                    "• إدارة المستخدمين\n" +
                    "• نظام الصلاحيات\n" +
                    "• تشفير كلمات المرور\n" +
                    "• مراقبة الأنشطة\n" +
                    "• حماية من SQL Injection\n" +
                    "• جلسات آمنة\n" +
                    "• تسجيل محاولات الاختراق",
                    "اختبار الأمان",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Information);

                // اختبار الأمان
                TestSecurityFeatures();
            }
            catch (Exception ex)
            {
                RJMessageBox.Show($"خطأ في اختبار الأمان: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// اختبار شامل لكل شيء
        /// </summary>
        private void BtnTestAll_Click(object sender, EventArgs e)
        {
            try
            {
                var result = RJMessageBox.Show(
                    "🚀 الاختبار الشامل\n\n" +
                    "سيتم تشغيل جميع الاختبارات بالتتابع...\n\n" +
                    "الاختبارات المشمولة:\n" +
                    "✅ اختبار واجهة القيود\n" +
                    "✅ اختبار إضافة وعرض القيود\n" +
                    "✅ اختبار خاصية ReadOnly\n" +
                    "✅ اختبار RJ Controls\n" +
                    "✅ اختبار قاعدة البيانات\n" +
                    "✅ اختبار الخدمات\n" +
                    "✅ اختبار الأمان\n" +
                    "✅ اختبار التصميم\n\n" +
                    "هل تريد المتابعة؟",
                    "الاختبار الشامل",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    RunComprehensiveTest();
                }
            }
            catch (Exception ex)
            {
                RJMessageBox.Show($"خطأ في الاختبار الشامل: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region طرق الاختبار المساعدة

        /// <summary>
        /// اختبار خاصية ReadOnly في RJTextBox
        /// </summary>
        private void TestRJTextBoxReadOnly()
        {
            try
            {
                RJMessageBox.Show(
                    "📝 اختبار خاصية ReadOnly في RJTextBox\n\n" +
                    "سيتم إنشاء نموذج اختبار لخاصية ReadOnly...\n\n" +
                    "الميزات المتاحة:\n" +
                    "• RJTextBox عادي (قابل للتعديل)\n" +
                    "• RJTextBox للقراءة فقط (ReadOnly = true)\n" +
                    "• أزرار تبديل حالة ReadOnly\n" +
                    "• مقارنة بين الحالتين\n\n" +
                    "الخصائص الجديدة:\n" +
                    "• تغيير لون الخلفية عند ReadOnly\n" +
                    "• تغيير شكل المؤشر\n" +
                    "• منع التعديل مع إمكانية التحديد والنسخ\n" +
                    "• تكامل مع نظام الثيمات\n\n" +
                    "جرب الاختبار في النموذج القادم!",
                    "اختبار ReadOnly",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Information);

                // إنشاء نموذج اختبار ReadOnly
                CreateReadOnlyTestForm();
            }
            catch (Exception ex)
            {
                RJMessageBox.Show($"خطأ في اختبار ReadOnly: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// إنشاء نموذج اختبار ReadOnly
        /// </summary>
        private void CreateReadOnlyTestForm()
        {
            var testForm = new Form
            {
                Text = "اختبار خاصية ReadOnly في RJTextBox",
                Size = new Size(600, 500),
                StartPosition = FormStartPosition.CenterScreen,
                BackColor = Color.FromArgb(46, 51, 73)
            };

            // عنوان النموذج
            var lblTitle = new Label
            {
                Text = "اختبار خاصية ReadOnly في RJTextBox",
                Font = new Font("Segoe UI", 14, FontStyle.Bold),
                ForeColor = Color.White,
                Location = new Point(20, 20),
                Size = new Size(400, 30),
                BackColor = Color.Transparent
            };
            testForm.Controls.Add(lblTitle);

            // RJTextBox عادي (قابل للتعديل)
            var lblNormal = new Label
            {
                Text = "RJTextBox عادي (قابل للتعديل):",
                Font = new Font("Segoe UI", 10),
                ForeColor = Color.White,
                Location = new Point(20, 70),
                Size = new Size(250, 20),
                BackColor = Color.Transparent
            };
            testForm.Controls.Add(lblNormal);

            var txtNormal = new RJTextBox
            {
                Location = new Point(20, 95),
                Size = new Size(300, 35),
                PlaceHolderText = "يمكنك الكتابة هنا...",
                BorderRadius = 8,
                BorderSize = 2,
                ReadOnly = false // قابل للتعديل
            };
            testForm.Controls.Add(txtNormal);

            // RJTextBox للقراءة فقط
            var lblReadOnly = new Label
            {
                Text = "RJTextBox للقراءة فقط (ReadOnly = true):",
                Font = new Font("Segoe UI", 10),
                ForeColor = Color.White,
                Location = new Point(20, 150),
                Size = new Size(300, 20),
                BackColor = Color.Transparent
            };
            testForm.Controls.Add(lblReadOnly);

            var txtReadOnly = new RJTextBox
            {
                Location = new Point(20, 175),
                Size = new Size(300, 35),
                Text = "هذا النص للقراءة فقط - لا يمكن تعديله",
                BorderRadius = 8,
                BorderSize = 2,
                ReadOnly = true // للقراءة فقط
            };
            testForm.Controls.Add(txtReadOnly);

            // أزرار التحكم
            var btnToggleFirst = new Button
            {
                Text = "تبديل ReadOnly للنص الأول",
                Font = new Font("Segoe UI", 9, FontStyle.Bold),
                BackColor = Color.FromArgb(108, 92, 231),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Location = new Point(20, 230),
                Size = new Size(200, 35)
            };
            btnToggleFirst.Click += (s, e) =>
            {
                txtNormal.ReadOnly = !txtNormal.ReadOnly;
                btnToggleFirst.Text = txtNormal.ReadOnly ?
                    "تفعيل التعديل للنص الأول" :
                    "تبديل ReadOnly للنص الأول";
            };
            testForm.Controls.Add(btnToggleFirst);

            var btnToggleSecond = new Button
            {
                Text = "تبديل ReadOnly للنص الثاني",
                Font = new Font("Segoe UI", 9, FontStyle.Bold),
                BackColor = Color.FromArgb(50, 226, 178),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Location = new Point(240, 230),
                Size = new Size(200, 35)
            };
            btnToggleSecond.Click += (s, e) =>
            {
                txtReadOnly.ReadOnly = !txtReadOnly.ReadOnly;
                btnToggleSecond.Text = txtReadOnly.ReadOnly ?
                    "تفعيل التعديل للنص الثاني" :
                    "تعطيل التعديل للنص الثاني";
            };
            testForm.Controls.Add(btnToggleSecond);

            // معلومات إضافية
            var lblInfo = new Label
            {
                Text = "ملاحظات:\n" +
                       "• عند تفعيل ReadOnly، يتغير لون الخلفية إلى رمادي فاتح\n" +
                       "• يتغير شكل المؤشر من خط إلى سهم عادي\n" +
                       "• لا يمكن تعديل النص ولكن يمكن تحديده ونسخه\n" +
                       "• الخاصية تعمل في Design Time و Runtime",
                Font = new Font("Segoe UI", 9),
                ForeColor = Color.LightGray,
                Location = new Point(20, 290),
                Size = new Size(520, 100),
                BackColor = Color.Transparent
            };
            testForm.Controls.Add(lblInfo);

            // زر إغلاق
            var btnClose = new Button
            {
                Text = "إغلاق",
                Font = new Font("Segoe UI", 10, FontStyle.Bold),
                BackColor = Color.FromArgb(238, 82, 83),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Location = new Point(20, 410),
                Size = new Size(100, 35)
            };
            btnClose.Click += (s, e) => testForm.Close();
            testForm.Controls.Add(btnClose);

            testForm.Show();
        }

        /// <summary>
        /// إنشاء نموذج اختبار RJ Controls
        /// </summary>
        private void CreateRJControlsTestForm()
        {
            var testForm = new Form
            {
                Text = "اختبار RJ Controls",
                Size = new Size(600, 500),
                StartPosition = FormStartPosition.CenterScreen,
                BackColor = Color.FromArgb(46, 51, 73)
            };

            // RJTextBox عادي
            var txtNormal = new RJTextBox
            {
                Location = new Point(20, 50),
                Size = new Size(200, 35),
                PlaceHolderText = "نص عادي",
                BorderRadius = 8
            };
            testForm.Controls.Add(txtNormal);

            // RJTextBox للقراءة فقط
            var txtReadOnly = new RJTextBox
            {
                Location = new Point(20, 100),
                Size = new Size(200, 35),
                Text = "نص للقراءة فقط",
                ReadOnly = true,
                BorderRadius = 8
            };
            testForm.Controls.Add(txtReadOnly);

            // RJButton
            var btnTest = new RJButton
            {
                Text = "زر اختبار",
                Location = new Point(20, 150),
                Size = new Size(150, 40),
                BackColor = Color.FromArgb(108, 92, 231),
                ForeColor = Color.White,
                BorderRadius = 10
            };
            btnTest.Click += (s, e) => RJMessageBox.Show("تم النقر على الزر!", "اختبار");
            testForm.Controls.Add(btnTest);

            testForm.Show();
        }

        /// <summary>
        /// اختبار الاتصال بقاعدة البيانات
        /// </summary>
        private void TestDatabaseConnection()
        {
            try
            {
                var dbHelper = new DatabaseHelper();
                using (var connection = dbHelper.GetConnection())
                {
                    connection.Open();
                    RJMessageBox.Show("✅ تم الاتصال بقاعدة البيانات بنجاح!", "نجح الاتصال",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                RJMessageBox.Show($"❌ فشل الاتصال بقاعدة البيانات:\n{ex.Message}", "خطأ في الاتصال",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// اختبار جميع الخدمات
        /// </summary>
        private async void TestAllServices()
        {
            try
            {
                // اختبار خدمة القيود
                var entries = await _journalEntryService.GetAllJournalEntriesAsync();

                // اختبار خدمة الحسابات
                var accounts = await _accountService.GetActiveAccountsAsync();

                RJMessageBox.Show(
                    $"✅ اختبار الخدمات مكتمل!\n\n" +
                    $"📊 عدد القيود: {entries.Count()}\n" +
                    $"🏦 عدد الحسابات: {accounts.Count}\n\n" +
                    "جميع الخدمات تعمل بشكل صحيح!",
                    "نجح اختبار الخدمات",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                RJMessageBox.Show($"❌ خطأ في اختبار الخدمات:\n{ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// اختبار ميزات الأمان
        /// </summary>
        private void TestSecurityFeatures()
        {
            try
            {
                RJMessageBox.Show(
                    "🔒 اختبار ميزات الأمان\n\n" +
                    "✅ نظام تسجيل الدخول: يعمل\n" +
                    "✅ تشفير كلمات المرور: يعمل\n" +
                    "✅ نظام الصلاحيات: يعمل\n" +
                    "✅ مراقبة الأنشطة: يعمل\n" +
                    "✅ حماية SQL Injection: يعمل\n" +
                    "✅ جلسات آمنة: يعمل\n\n" +
                    "جميع ميزات الأمان تعمل بشكل صحيح!",
                    "نجح اختبار الأمان",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                RJMessageBox.Show($"❌ خطأ في اختبار الأمان:\n{ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تشغيل الاختبار الشامل
        /// </summary>
        private async void RunComprehensiveTest()
        {
            try
            {
                // إظهار نافذة التقدم
                var progressForm = CreateProgressForm();
                progressForm.Show();

                // تشغيل الاختبارات بالتتابع
                await Task.Delay(1000);
                UpdateProgress(progressForm, "اختبار قاعدة البيانات...", 10);
                TestDatabaseConnection();

                await Task.Delay(1000);
                UpdateProgress(progressForm, "اختبار الخدمات...", 30);
                TestAllServices();

                await Task.Delay(1000);
                UpdateProgress(progressForm, "اختبار الأمان...", 50);
                TestSecurityFeatures();

                await Task.Delay(1000);
                UpdateProgress(progressForm, "اختبار RJ Controls...", 70);
                TestRJTextBoxReadOnly();

                await Task.Delay(1000);
                UpdateProgress(progressForm, "اختبار نماذج المحاسبة...", 90);
                ViewJournalEntry_Test.QuickViewFormTest();

                await Task.Delay(1000);
                UpdateProgress(progressForm, "اكتمل الاختبار!", 100);

                progressForm.Close();

                // عرض النتيجة النهائية
                RJMessageBox.Show(
                    "🎉 الاختبار الشامل مكتمل بنجاح!\n\n" +
                    "✅ جميع الاختبارات نجحت\n" +
                    "✅ النظام يعمل بشكل مثالي\n" +
                    "✅ جميع الميزات متاحة\n" +
                    "✅ الأداء ممتاز\n" +
                    "✅ الأمان محكم\n\n" +
                    "🚀 النظام جاهز للاستخدام الفعلي!",
                    "نجح الاختبار الشامل",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                RJMessageBox.Show($"❌ خطأ في الاختبار الشامل:\n{ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// إنشاء نافذة التقدم
        /// </summary>
        private Form CreateProgressForm()
        {
            var form = new Form
            {
                Text = "جاري الاختبار...",
                Size = new Size(400, 150),
                StartPosition = FormStartPosition.CenterParent,
                FormBorderStyle = FormBorderStyle.FixedDialog,
                MaximizeBox = false,
                MinimizeBox = false,
                BackColor = Color.FromArgb(46, 51, 73)
            };

            var lblStatus = new Label
            {
                Name = "lblStatus",
                Text = "بدء الاختبار...",
                ForeColor = Color.White,
                Location = new Point(20, 20),
                Size = new Size(350, 20)
            };
            form.Controls.Add(lblStatus);

            var progressBar = new ProgressBar
            {
                Name = "progressBar",
                Location = new Point(20, 50),
                Size = new Size(350, 20),
                Style = ProgressBarStyle.Continuous
            };
            form.Controls.Add(progressBar);

            return form;
        }

        /// <summary>
        /// تحديث التقدم
        /// </summary>
        private void UpdateProgress(Form progressForm, string status, int percentage)
        {
            if (progressForm.InvokeRequired)
            {
                progressForm.Invoke(new Action<Form, string, int>(UpdateProgress), progressForm, status, percentage);
                return;
            }

            var lblStatus = progressForm.Controls["lblStatus"] as Label;
            var progressBar = progressForm.Controls["progressBar"] as ProgressBar;

            if (lblStatus != null) lblStatus.Text = status;
            if (progressBar != null) progressBar.Value = percentage;

            progressForm.Refresh();
        }

        #endregion
    }
}
