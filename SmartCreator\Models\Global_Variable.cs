﻿//using ServiceStack.OrmLite;
using SmartCreator.Data;
using SmartCreator.Entities.Hotspot;
using SmartCreator.Entities.UserManager;
using SmartCreator.Forms;
using SmartCreator.Models.API;
using SmartCreator.Models.hotspot;
using SmartCreator.RJControls;
using SmartCreator.ViewModels;
using System.Collections.Generic;
using System.Data;
using System.Windows.Forms;

namespace SmartCreator.Models
{
    public class Global_Variable
    {
        //public Global_Variable()
        //{

        //}
        public static bool StartThreadProcessFromMK = false;
        //public static string Pc_Code = "";
        //public static string APPId = "Smart-8.13-Desktop";

        //public static bool IsSuccessStatusCode = false;
        public static float Graphics_dpi = 96f;
        public static Response_api Response_api=new Response_api();
        public static DataTable CountryList;
        public static string Pc_Code { get; set; }
        public static string Server_IP { get; set; }
        public static string Server_Username { get; set; }
        public static string Server_Password { get; set; }
        public static int Server_Port { get; set; } = 8728;
        public static int Server_Port_SSL { get; set; } = 8728;
        public static bool SSL_Use { get; set; } = false;
        public static bool RunOffline { get; set; } = false;
        public static bool Load_From_Last_load { get; set; } = false;
        public static bool load_by_DownloadDB { get; set; } = false;
        public static bool Ddiable_LoadSession { get; set; } = false;
        public static string LocalPathDB { get; set; } = "\\db\\localDB.db";
        public static void Update_Um_StatusBar(bool leftChange, bool RightChange, double progressBar_Value = -1, string ListSelected = "(0/0)", string lblDescription = "")
        {
            try
            {
                Uc_StatusBar.lblDescription.Invoke(
                     (MethodInvoker)delegate ()
                     {
                         if (leftChange)
                         {
                             Uc_StatusBar.lblSelected.Text = ListSelected;
                         }
                         if (RightChange)
                         {
                             //=== if prograss -1  not change value
                             if (progressBar_Value != -1)
                                 Uc_StatusBar.rjProgressBar1.Value = (int)progressBar_Value;
                             Uc_StatusBar.lblDescription.Text = lblDescription;
                         }
                     });
            }
            catch { }
        }
        public static void Update_Um_StatusBar_Prograss(string lblDescription, int progressBar_Value)
        {
            try
            {
                Uc_StatusBar.lblDescription.Invoke(
                          (MethodInvoker)delegate ()
                          {
                              Global_Variable.Uc_StatusBar.lblDescription.Text = lblDescription;
                              Global_Variable.Uc_StatusBar.rjProgressBar1.Value = progressBar_Value;

                              if (progressBar_Value == 0)
                                  Uc_StatusBar.rjProgressBar1.ShowValue = RJControls.TextPosition.None;
                              else
                                  Uc_StatusBar.rjProgressBar1.ShowValue = RJControls.TextPosition.Center;
                          });
            }
            catch { }

            //if (progressBar_Value == 0)
            //    Uc_StatusBar.rjProgressBar1.ShowValue = RJControls.TextPosition.None;
            //else
            //    Uc_StatusBar.rjProgressBar1.ShowValue = RJControls.TextPosition.Center;

        }
        public static void Update_StatusBar_StartSyn()
        {
            try
            {

                Uc_StatusBar.lblDescription.Invoke(
                          (MethodInvoker)delegate ()
                          {
                              Uc_StatusBar.rjPictureBox1.Visible = true;

                          });
            }
            catch { }

            //try
            //{
            //    Uc_StatusBar.rjPictureBox1.Visible = true;

            //}
            //catch { }
        }

        public static void Update_StatusBar_StopSyn()
        {
            try
            {

                Uc_StatusBar.lblDescription.Invoke(
                          (MethodInvoker)delegate ()
                          {
                              Uc_StatusBar.rjPictureBox1.Visible = false;

                          });
            }
            catch { }

            //try
            //{
            //    Uc_StatusBar.rjPictureBox1.Visible = true;

            //}
            //catch { }
        }


        //======================
        public static App_Info App_Info = new App_Info();
        public static UC_StatusBar_Info Uc_StatusBar = null;

        // إضافة المستخدم الحالي للنظام الأمني
        public static SmartCreator.Entities.User CurrentUser { get; set; }

        public static Form_LoingState Mk_Login_data { get; set; }

        public static Mk_Routers Mk_Router { get; set; }
        public static Mk_Resources Mk_resources { get; set; } = new Mk_Resources();
        public static List<UserManager_Customer> UM_Customer { get; set; }
        public static List<string> UM_Attribute { get; set; }= new List<string>();
        public static List<string> UM_Group { get; set; }=new List<string>();
        public static List<UmProfile> UM_Profile { get; set; }
        //public static List<UserManager_Profile_UserManager> UM_Profile { get; set; }
        //public static List<UserManager_SourceProfile_UserManager> Source_profile { get; set; }
        public static List<UmProfile> Source_profile { get; set; }
        public static List<UmProfile_Limtition> Source_profile_limtition { get; set; }
        //public static List<UmProfile_Limtition> Source_profile_limtition2 { get; set; }
        public static List<UmLimitation> Source_limtition { get; set; }
        //public static List<UmLimitation> Source_limtition2 { get; set; }


        public static List<SourceCardsUserManager_fromMK> Source_Users_UserManager { get; set; }
        public static List<SourceCardsHotspot_fromMK> Source_Users_HotSpot { get; set; }
        public static HashSet<string> Source_Users_UserManager_ForPrint { get; set; } = new HashSet<string>();
        public static HashSet<string> Source_Users_Hotspot_ForPrint { get; set; } = new HashSet<string>();
        public static List<SourcePymentUserManager_fromMK> Source_Pyment_UserManager { get; set; }
        public static List<SourceSessionUserManager_fromMK> Source_Session_UserManager { get; set; }

        public static List<Hotspot_Source_Profile> Source_HS_Profile { get; set; }
        public static List<HSLocalProfile> Hotspot_Profile { get; set; }

        public static List<Acive_Host_Users> Acive_Users { get; set; }
        public static List<Host_Users> Hosts_Users { get; set; }
        public static List<Neighbor_Device> Neighbor_Devices { get; set; }
        public static List<Dhcp_Lease> Dhcp_Leases { get; set; }

        //public static Dictionary<string, IOrmLiteDialectProvider> ProviderList = new Dictionary<string, IOrmLiteDialectProvider>()
        //{
        //    {"SQLlite", SqliteDialect.Provider},
        //    //{"SQL Server", SqlServerDialect.Provider},
        //    //{ "SQL Server Compact", SqlServerDialect.Provider },
        //    //{ "MySql", MySqlDialect.Provider},
        //    //{ "MariaDb", MySqlDialect.Provider},
        //    //{ "PostgreSql", PostgreSqlDialect.Provider },
        //    //{ "Oracle", OracleDialect.Provider},
        //    //{ "Firebird", FirebirdDialect.Provider}
        //};

        //public static OrmLiteConnectionFactory dbFactory = null;

        //public static OrmLiteConnectionFactory dbFactory = new OrmLiteConnectionFactory(Sql_DataAccess.connection_string, Sql_DataAccess.Get_Provider());

    }
}
