using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using SmartCreator.Entities.Accounting;
using SmartCreator.RJControls;
using static SmartCreator.Entities.Accounting.JournalEntryTypeInfo;
using static SmartCreator.Entities.Accounting.JournalEntryStatusInfo;
using SmartCreator.RJForms;
using FontAwesome.Sharp;
using SmartCreator.Service;
using SmartCreator.Services.Accounting;
using SmartCreator.Services.Security;
using SmartCreator.Settings;

namespace SmartCreator.Forms.Accounting
{
    /// <summary>
    /// نموذج إدارة القيود المحاسبية المحسن
    /// </summary>
    public partial class Frm_JournalEntries_Enhanced : RJChildForm
    {
        #region المتغيرات

        private readonly JournalEntryService _journalEntryService;
        private readonly AccountService _accountService;
        private readonly UserActivityService _activityService;

        private List<JournalEntry> _journalEntries;
        private List<JournalEntry> _filteredEntries;
        private JournalEntry _selectedEntry;
        private bool _isLoading = false;

        // إحصائيات
        private int _totalEntries = 0;
        private decimal _totalDebit = 0;
        private decimal _totalCredit = 0;

        #endregion

        #region البناء

        /// <summary>
        /// Constructor افتراضي للـ Designer
        /// </summary>
        public Frm_JournalEntries_Enhanced()
        {
            // إنشاء الخدمات بـ constructors افتراضية للـ Designer
            _journalEntryService = new JournalEntryService();
            _accountService = new AccountService();
            _activityService = new UserActivityService();

            _journalEntries = new List<JournalEntry>();
            _filteredEntries = new List<JournalEntry>();

            InitializeComponent();

            // تجنب تحميل البيانات في وضع التصميم
            if (!DesignMode)
            {
                SetupForm();
                LoadDataAsync();
            }
        }

        /// <summary>
        /// Constructor مع الخدمات المحقونة
        /// </summary>
        public Frm_JournalEntries_Enhanced(JournalEntryService journalEntryService,
            AccountService accountService, UserActivityService activityService)
        {
            _journalEntryService = journalEntryService;
            _accountService = accountService;
            _activityService = activityService;

            _journalEntries = new List<JournalEntry>();
            _filteredEntries = new List<JournalEntry>();

            InitializeComponent();
            SetupForm();
            LoadDataAsync();
        }

        #endregion

        #region إعداد النموذج

        /// <summary>
        /// إعداد النموذج
        /// </summary>
        private void SetupForm()
        {
            // إعداد خصائص RJChildForm
            this.Text = "إدارة القيود المحاسبية";
            this.Caption = "إدارة القيود المحاسبية";
            this.FormIcon = FontAwesome.Sharp.IconChar.FileInvoice;
            this.IsChildForm = true;
            this.WindowState = FormWindowState.Maximized;

            // العناصر موجودة بالفعل في ملف Designer - لا حاجة لإضافتها في runtime

            // إعداد جدول البيانات
            SetupDataGridView();

            // إعداد القوائم المنسدلة
            SetupComboBoxes();

            // إعداد القائمة المنبثقة
            SetupContextMenu();

            // تطبيق إعدادات المظهر
            ApplyThemeSettings();
        }

        /// <summary>
        /// إعداد جدول البيانات
        /// </summary>
        private void SetupDataGridView()
        {
            dgvJournalEntries.AutoGenerateColumns = false;
            dgvJournalEntries.AllowUserToAddRows = false;
            dgvJournalEntries.AllowUserToDeleteRows = false;
            dgvJournalEntries.ReadOnly = true;
            dgvJournalEntries.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dgvJournalEntries.MultiSelect = true;

            // إعداد الأعمدة
            dgvJournalEntries.Columns.Clear();
            dgvJournalEntries.Columns.AddRange(new DataGridViewColumn[]
            {
                new DataGridViewCheckBoxColumn
                {
                    Name = "Select",
                    HeaderText = "اختيار",
                    Width = 50,
                    ReadOnly = false
                },
                new DataGridViewTextBoxColumn
                {
                    Name = "EntryNumber",
                    HeaderText = "رقم القيد",
                    DataPropertyName = "EntryNumber",
                    Width = 120
                },
                new DataGridViewTextBoxColumn
                {
                    Name = "Date",
                    HeaderText = "التاريخ",
                    DataPropertyName = "Date",
                    Width = 100,
                    DefaultCellStyle = new DataGridViewCellStyle { Format = "yyyy-MM-dd" }
                },
                new DataGridViewTextBoxColumn
                {
                    Name = "Description",
                    HeaderText = "البيان",
                    DataPropertyName = "Description",
                    Width = 300
                },
                new DataGridViewTextBoxColumn
                {
                    Name = "Type",
                    HeaderText = "النوع",
                    Width = 100
                },
                new DataGridViewTextBoxColumn
                {
                    Name = "Status",
                    HeaderText = "الحالة",
                    Width = 100
                },
                new DataGridViewTextBoxColumn
                {
                    Name = "TotalDebit",
                    HeaderText = "إجمالي المدين",
                    DataPropertyName = "TotalDebit",
                    Width = 120,
                    DefaultCellStyle = new DataGridViewCellStyle
                    {
                        Format = "N2",
                        Alignment = DataGridViewContentAlignment.MiddleRight
                    }
                },
                new DataGridViewTextBoxColumn
                {
                    Name = "TotalCredit",
                    HeaderText = "إجمالي الدائن",
                    DataPropertyName = "TotalCredit",
                    Width = 120,
                    DefaultCellStyle = new DataGridViewCellStyle
                    {
                        Format = "N2",
                        Alignment = DataGridViewContentAlignment.MiddleRight
                    }
                },
                new DataGridViewTextBoxColumn
                {
                    Name = "CreatedBy",
                    HeaderText = "أنشئ بواسطة",
                    DataPropertyName = "CreatedBy",
                    Width = 120
                },
                new DataGridViewTextBoxColumn
                {
                    Name = "CreatedDate",
                    HeaderText = "تاريخ الإنشاء",
                    DataPropertyName = "CreatedDate",
                    Width = 120,
                    DefaultCellStyle = new DataGridViewCellStyle { Format = "yyyy-MM-dd HH:mm" }
                }
            });

            // أحداث الجدول
            dgvJournalEntries.SelectionChanged += DgvJournalEntries_SelectionChanged;
            dgvJournalEntries.CellDoubleClick += DgvJournalEntries_CellDoubleClick;
            dgvJournalEntries.CellContentClick += DgvJournalEntries_CellContentClick;
        }

        /// <summary>
        /// إعداد القوائم المنسدلة
        /// </summary>
        private void SetupComboBoxes()
        {
            // إعداد قائمة أنواع القيود
            cmbEntryType.Items.Clear();
            cmbEntryType.Items.Add("جميع الأنواع");
            foreach (JournalEntryType type in Enum.GetValues(typeof(JournalEntryType)))
            {
                cmbEntryType.Items.Add(new { Value = type, Text = GetArabicName(type) });
            }
            cmbEntryType.DisplayMember = "Text";
            cmbEntryType.ValueMember = "Value";
            cmbEntryType.SelectedIndex = 0;

            // إعداد قائمة حالات القيود
            cmbEntryStatus.Items.Clear();
            cmbEntryStatus.Items.Add("جميع الحالات");
            foreach (JournalEntryStatus status in Enum.GetValues(typeof(JournalEntryStatus)))
            {
                cmbEntryStatus.Items.Add(new { Value = status, Text = GetArabicName(status) });
            }
            cmbEntryStatus.DisplayMember = "Text";
            cmbEntryStatus.ValueMember = "Value";
            cmbEntryStatus.SelectedIndex = 0;

            // أحداث القوائم
            cmbEntryType.SelectedIndexChanged += CmbEntryType_SelectedIndexChanged;
            cmbEntryStatus.SelectedIndexChanged += CmbEntryStatus_SelectedIndexChanged;
        }

        /// <summary>
        /// إعداد القائمة المنبثقة
        /// </summary>
        private void SetupContextMenu()
        {
            var contextMenu = new ContextMenuStrip();

            contextMenu.Items.AddRange(new ToolStripItem[]
            {
                new ToolStripMenuItem("إضافة قيد جديد", null, BtnAddEntry_Click),
                new ToolStripMenuItem("تعديل القيد", null, BtnEditEntry_Click),
                new ToolStripMenuItem("عرض تفاصيل القيد", null, BtnViewEntry_Click),
                new ToolStripSeparator(),
                new ToolStripMenuItem("طباعة القيد", null, BtnPrintEntry_Click),
                new ToolStripMenuItem("حذف القيد", null, BtnDeleteEntry_Click),
                new ToolStripSeparator(),
                new ToolStripMenuItem("تحديث البيانات", null, BtnRefresh_Click)
            });

            dgvJournalEntries.ContextMenuStrip = contextMenu;
        }

        /// <summary>
        /// تطبيق إعدادات المظهر
        /// </summary>
        private void ApplyThemeSettings()
        {
            // تطبيق ألوان الثيم على العناصر
            this.BackColor = UIAppearance.BackgroundColor;

            // تطبيق الخط
            var font = new Font("Droid Arabic Kufi", 9F, FontStyle.Regular);
            this.Font = font;

            foreach (Control control in this.Controls)
            {
                ApplyFontToControl(control, font);
            }
        }

        /// <summary>
        /// تطبيق الخط على العنصر وعناصره الفرعية
        /// </summary>
        private void ApplyFontToControl(Control control, Font font)
        {
            control.Font = font;
            foreach (Control childControl in control.Controls)
            {
                ApplyFontToControl(childControl, font);
            }
        }

        // تم حذف طريقة AddControlsToClientArea - العناصر موجودة في ملف Designer

        #endregion

        #region تحميل البيانات

        /// <summary>
        /// تحميل البيانات بشكل غير متزامن
        /// </summary>
        private async void LoadDataAsync()
        {
            try
            {
                _isLoading = true;

                // عرض مؤشر التحميل
                dgvJournalEntries.DataSource = null;
                lblTotalEntries.Text = "جاري التحميل...";

                // تحميل القيود
                _journalEntries = await Task.Run(() => _journalEntryService.GetAllJournalEntries());
                _filteredEntries = new List<JournalEntry>(_journalEntries);

                // عرض البيانات
                RefreshDataGrid();
                UpdateStatistics();

                _isLoading = false;
            }
            catch (Exception ex)
            {
                _isLoading = false;
                RJMessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تحديث جدول البيانات
        /// </summary>
        private void RefreshDataGrid()
        {
            try
            {
                if (_filteredEntries == null) return;

                // إنشاء DataTable للعرض
                var dataTable = new DataTable();
                dataTable.Columns.AddRange(new DataColumn[]
                {
                    new DataColumn("EntryNumber", typeof(string)),
                    new DataColumn("Date", typeof(DateTime)),
                    new DataColumn("Description", typeof(string)),
                    new DataColumn("Type", typeof(string)),
                    new DataColumn("Status", typeof(string)),
                    new DataColumn("TotalDebit", typeof(decimal)),
                    new DataColumn("TotalCredit", typeof(decimal)),
                    new DataColumn("CreatedBy", typeof(string)),
                    new DataColumn("CreatedDate", typeof(DateTime))
                });

                // إضافة البيانات
                foreach (var entry in _filteredEntries)
                {
                    var row = dataTable.NewRow();
                    row["EntryNumber"] = entry.EntryNumber;
                    row["Date"] = entry.Date;
                    row["Description"] = entry.Description;
                    row["Type"] = GetArabicName(entry.Type);
                    row["Status"] = GetArabicName(entry.Status);
                    row["TotalDebit"] = entry.TotalDebit;
                    row["TotalCredit"] = entry.TotalCredit;
                    row["CreatedBy"] = entry.CreatedBy;
                    row["CreatedDate"] = entry.CreatedDate;
                    dataTable.Rows.Add(row);
                }

                dgvJournalEntries.DataSource = dataTable;

                // تلوين الصفوف حسب الحالة
                ColorizeRows();
            }
            catch (Exception ex)
            {
                RJMessageBox.Show($"خطأ في تحديث الجدول: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تلوين الصفوف حسب حالة القيد
        /// </summary>
        private void ColorizeRows()
        {
            try
            {
                for (int i = 0; i < dgvJournalEntries.Rows.Count && i < _filteredEntries.Count; i++)
                {
                    var entry = _filteredEntries[i];
                    var row = dgvJournalEntries.Rows[i];

                    switch (entry.Status)
                    {
                        case JournalEntryStatus.Draft:
                            row.DefaultCellStyle.BackColor = Color.FromArgb(255, 248, 220); // أصفر فاتح
                            break;
                        case JournalEntryStatus.Posted:
                            row.DefaultCellStyle.BackColor = Color.FromArgb(220, 255, 220); // أخضر فاتح
                            break;
                        case JournalEntryStatus.Approved:
                            row.DefaultCellStyle.BackColor = Color.FromArgb(220, 240, 255); // أزرق فاتح
                            break;
                        case JournalEntryStatus.Cancelled:
                            row.DefaultCellStyle.BackColor = Color.FromArgb(255, 220, 220); // أحمر فاتح
                            break;
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في تلوين الصفوف: {ex.Message}");
            }
        }

        /// <summary>
        /// تحديث الإحصائيات
        /// </summary>
        private void UpdateStatistics()
        {
            try
            {
                if (_filteredEntries == null)
                {
                    lblTotalEntries.Text = "عدد القيود: 0";
                    lblTotalDebit.Text = "إجمالي المدين: 0.00";
                    lblTotalCredit.Text = "إجمالي الدائن: 0.00";
                    lblBalance.Text = "الرصيد: متزن";
                    return;
                }

                _totalEntries = _filteredEntries.Count;
                _totalDebit = _filteredEntries.Sum(e => e.TotalDebit);
                _totalCredit = _filteredEntries.Sum(e => e.TotalCredit);

                lblTotalEntries.Text = $"عدد القيود: {_totalEntries:N0}";
                lblTotalDebit.Text = $"إجمالي المدين: {_totalDebit:N2}";
                lblTotalCredit.Text = $"إجمالي الدائن: {_totalCredit:N2}";

                var balance = _totalDebit - _totalCredit;
                if (Math.Abs(balance) < 0.01m)
                {
                    lblBalance.Text = "الرصيد: متزن";
                    lblBalance.ForeColor = Color.FromArgb(40, 167, 69);
                }
                else
                {
                    lblBalance.Text = $"الرصيد: {Math.Abs(balance):N2} {(balance > 0 ? "مدين" : "دائن")}";
                    lblBalance.ForeColor = Color.FromArgb(220, 53, 69);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في تحديث الإحصائيات: {ex.Message}");
            }
        }

        #endregion

        #region أحداث الجدول

        /// <summary>
        /// تغيير التحديد في الجدول
        /// </summary>
        private void DgvJournalEntries_SelectionChanged(object sender, EventArgs e)
        {
            try
            {
                if (_isLoading || dgvJournalEntries.SelectedRows.Count == 0) return;

                var selectedIndex = dgvJournalEntries.SelectedRows[0].Index;
                if (selectedIndex >= 0 && selectedIndex < _filteredEntries.Count)
                {
                    _selectedEntry = _filteredEntries[selectedIndex];

                    // تحديث حالة الأزرار
                    UpdateButtonStates();
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في تغيير التحديد: {ex.Message}");
            }
        }

        /// <summary>
        /// النقر المزدوج على الجدول
        /// </summary>
        private void DgvJournalEntries_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            try
            {
                if (e.RowIndex >= 0)
                {
                    BtnViewEntry_Click(sender, e);
                }
            }
            catch (Exception ex)
            {
                RJMessageBox.Show($"خطأ في فتح القيد: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// النقر على خلية في الجدول
        /// </summary>
        private void DgvJournalEntries_CellContentClick(object sender, DataGridViewCellEventArgs e)
        {
            try
            {
                // التعامل مع عمود الاختيار
                if (e.ColumnIndex == 0 && e.RowIndex >= 0) // عمود Select
                {
                    UpdateButtonStates();
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في النقر على الخلية: {ex.Message}");
            }
        }

        /// <summary>
        /// تحديث حالة الأزرار
        /// </summary>
        private void UpdateButtonStates()
        {
            try
            {
                var selectedCount = GetSelectedEntriesCount();
                var hasSelection = selectedCount > 0;
                var singleSelection = selectedCount == 1;

                btnEditEntry.Enabled = singleSelection;
                btnViewEntry.Enabled = singleSelection;
                btnPrintEntry.Enabled = hasSelection;
                btnDeleteEntry.Enabled = hasSelection;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في تحديث حالة الأزرار: {ex.Message}");
            }
        }

        /// <summary>
        /// الحصول على عدد القيود المحددة
        /// </summary>
        private int GetSelectedEntriesCount()
        {
            try
            {
                int count = 0;
                foreach (DataGridViewRow row in dgvJournalEntries.Rows)
                {
                    if (row.Cells["Select"].Value != null && (bool)row.Cells["Select"].Value)
                    {
                        count++;
                    }
                }
                return count > 0 ? count : dgvJournalEntries.SelectedRows.Count;
            }
            catch
            {
                return dgvJournalEntries.SelectedRows.Count;
            }
        }

        /// <summary>
        /// الحصول على القيود المحددة
        /// </summary>
        private List<JournalEntry> GetSelectedEntries()
        {
            try
            {
                var selectedEntries = new List<JournalEntry>();

                // التحقق من عمود الاختيار أولاً
                for (int i = 0; i < dgvJournalEntries.Rows.Count; i++)
                {
                    var row = dgvJournalEntries.Rows[i];
                    if (row.Cells["Select"].Value != null && (bool)row.Cells["Select"].Value)
                    {
                        if (i < _filteredEntries.Count)
                        {
                            selectedEntries.Add(_filteredEntries[i]);
                        }
                    }
                }

                // إذا لم يتم تحديد أي شيء من عمود الاختيار، استخدم التحديد العادي
                if (selectedEntries.Count == 0)
                {
                    foreach (DataGridViewRow row in dgvJournalEntries.SelectedRows)
                    {
                        if (row.Index < _filteredEntries.Count)
                        {
                            selectedEntries.Add(_filteredEntries[row.Index]);
                        }
                    }
                }

                return selectedEntries;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في الحصول على القيود المحددة: {ex.Message}");
                return new List<JournalEntry>();
            }
        }

        #endregion

        #region أحداث الأزرار

        /// <summary>
        /// إضافة قيد جديد
        /// </summary>
        private void BtnAddEntry_Click(object sender, EventArgs e)
        {
            try
            {
                var addForm = new Frm_AddEditJournalEntry_Enhanced(_journalEntryService, _accountService, _activityService);
                addForm.EntryAdded += (s, entry) => LoadDataAsync();
                addForm.ShowDialog();
            }
            catch (Exception ex)
            {
                RJMessageBox.Show($"خطأ في فتح نموذج الإضافة: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تعديل القيد المحدد
        /// </summary>
        private void BtnEditEntry_Click(object sender, EventArgs e)
        {
            try
            {
                if (_selectedEntry == null)
                {
                    RJMessageBox.Show("يرجى تحديد قيد للتعديل", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                var editForm = new Frm_AddEditJournalEntry_Enhanced(_journalEntryService, _accountService, _activityService, _selectedEntry);
                editForm.EntryUpdated += (s, entry) => LoadDataAsync();
                editForm.ShowDialog();
            }
            catch (Exception ex)
            {
                RJMessageBox.Show($"خطأ في فتح نموذج التعديل: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// عرض تفاصيل القيد
        /// </summary>
        private void BtnViewEntry_Click(object sender, EventArgs e)
        {
            try
            {
                if (_selectedEntry == null)
                {
                    RJMessageBox.Show("يرجى تحديد قيد للعرض", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                var viewForm = new Frm_ViewJournalEntry_Enhanced(_journalEntryService, _accountService, _activityService, _selectedEntry);
                viewForm.EntryUpdated += (s, entry) => LoadDataAsync();
                viewForm.ShowDialog();
            }
            catch (Exception ex)
            {
                RJMessageBox.Show($"خطأ في فتح نموذج العرض: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// طباعة القيود المحددة
        /// </summary>
        private void BtnPrintEntry_Click(object sender, EventArgs e)
        {
            try
            {
                var selectedEntries = GetSelectedEntries();
                if (selectedEntries.Count == 0)
                {
                    RJMessageBox.Show("يرجى تحديد قيد أو أكثر للطباعة", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                var result = RJMessageBox.Show(
                    $"هل تريد طباعة {selectedEntries.Count} قيد محدد؟",
                    "تأكيد الطباعة",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    // تنفيذ الطباعة
                    PrintEntries(selectedEntries);
                }
            }
            catch (Exception ex)
            {
                RJMessageBox.Show($"خطأ في الطباعة: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// حذف القيود المحددة
        /// </summary>
        private void BtnDeleteEntry_Click(object sender, EventArgs e)
        {
            try
            {
                var selectedEntries = GetSelectedEntries();
                if (selectedEntries.Count == 0)
                {
                    RJMessageBox.Show("يرجى تحديد قيد أو أكثر للحذف", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                var result = RJMessageBox.Show(
                    $"هل أنت متأكد من حذف {selectedEntries.Count} قيد؟\n\nهذا الإجراء لا يمكن التراجع عنه!",
                    "تأكيد الحذف",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Warning);

                if (result == DialogResult.Yes)
                {
                    DeleteEntries(selectedEntries);
                }
            }
            catch (Exception ex)
            {
                RJMessageBox.Show($"خطأ في الحذف: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تحديث البيانات
        /// </summary>
        private void BtnRefresh_Click(object sender, EventArgs e)
        {
            try
            {
                LoadDataAsync();
            }
            catch (Exception ex)
            {
                RJMessageBox.Show($"خطأ في تحديث البيانات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// البحث
        /// </summary>
        private void BtnSearch_Click(object sender, EventArgs e)
        {
            try
            {
                ApplyFilters();
            }
            catch (Exception ex)
            {
                RJMessageBox.Show($"خطأ في البحث: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region أحداث الفلاتر

        /// <summary>
        /// تغيير نوع القيد
        /// </summary>
        private void CmbEntryType_SelectedIndexChanged(object sender, EventArgs e)
        {
            try
            {
                if (!_isLoading)
                {
                    ApplyFilters();
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في تغيير نوع القيد: {ex.Message}");
            }
        }

        /// <summary>
        /// تغيير حالة القيد
        /// </summary>
        private void CmbEntryStatus_SelectedIndexChanged(object sender, EventArgs e)
        {
            try
            {
                if (!_isLoading)
                {
                    ApplyFilters();
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في تغيير حالة القيد: {ex.Message}");
            }
        }

        /// <summary>
        /// تطبيق الفلاتر
        /// </summary>
        private void ApplyFilters()
        {
            try
            {
                if (_journalEntries == null) return;

                _filteredEntries = new List<JournalEntry>(_journalEntries);

                // فلتر نوع القيد
                if (cmbEntryType.SelectedIndex > 0 && cmbEntryType.SelectedItem != null)
                {
                    var selectedType = ((dynamic)cmbEntryType.SelectedItem).Value;
                    _filteredEntries = _filteredEntries.Where(e => e.Type == (JournalEntryType)selectedType).ToList();
                }

                // فلتر حالة القيد
                if (cmbEntryStatus.SelectedIndex > 0 && cmbEntryStatus.SelectedItem != null)
                {
                    var selectedStatus = ((dynamic)cmbEntryStatus.SelectedItem).Value;
                    _filteredEntries = _filteredEntries.Where(e => e.Status == (JournalEntryStatus)selectedStatus).ToList();
                }

                // فلتر البحث
                var searchText = txtSearch.Texts?.Trim();
                if (!string.IsNullOrEmpty(searchText))
                {
                    _filteredEntries = _filteredEntries.Where(e =>
                        e.EntryNumber.IndexOf(searchText, StringComparison.OrdinalIgnoreCase) >= 0 ||
                        e.Description.IndexOf(searchText, StringComparison.OrdinalIgnoreCase) >= 0
                    ).ToList();
                }

                RefreshDataGrid();
                UpdateStatistics();
            }
            catch (Exception ex)
            {
                RJMessageBox.Show($"خطأ في تطبيق الفلاتر: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region طرق مساعدة

        /// <summary>
        /// طباعة القيود
        /// </summary>
        private void PrintEntries(List<JournalEntry> entries)
        {
            try
            {
                // تنفيذ منطق الطباعة
                RJMessageBox.Show($"تم إرسال {entries.Count} قيد للطباعة", "طباعة",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في الطباعة: {ex.Message}");
            }
        }

        /// <summary>
        /// حذف القيود
        /// </summary>
        private async void DeleteEntries(List<JournalEntry> entries)
        {
            try
            {
                int deletedCount = 0;
                var errors = new List<string>();

                foreach (var entry in entries)
                {
                    try
                    {
                        await Task.Run(() => _journalEntryService.DeleteJournalEntry(entry.Id));
                        deletedCount++;
                    }
                    catch (Exception ex)
                    {
                        errors.Add($"القيد {entry.EntryNumber}: {ex.Message}");
                    }
                }

                if (deletedCount > 0)
                {
                    RJMessageBox.Show($"تم حذف {deletedCount} قيد بنجاح", "حذف",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    LoadDataAsync();
                }

                if (errors.Count > 0)
                {
                    var errorMessage = "حدثت أخطاء في حذف بعض القيود:\n\n" + string.Join("\n", errors);
                    RJMessageBox.Show(errorMessage, "أخطاء الحذف",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }
            }
            catch (Exception ex)
            {
                RJMessageBox.Show($"خطأ في عملية الحذف: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion
    }
}
