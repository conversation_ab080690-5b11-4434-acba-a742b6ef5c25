using System;
using System.Windows.Forms;
using SmartCreator.Forms.Accounting;
using SmartCreator.RJControls;
using SmartCreator.Service;
using SmartCreator.Services.Accounting;
using SmartCreator.Services.Security;
using SmartCreator.Settings;

namespace SmartCreator.Forms.Testing
{
    /// <summary>
    /// اختبار النماذج المحسنة للقيود المحاسبية
    /// </summary>
    public static class EnhancedJournalForms_Test
    {
        /// <summary>
        /// اختبار شامل للنماذج المحسنة
        /// </summary>
        public static void TestEnhancedForms()
        {
            try
            {
                Console.WriteLine("🚀 اختبار النماذج المحسنة للقيود المحاسبية...\n");

                // تهيئة الإعدادات
                UIAppearance.Theme = UITheme.Light;
                UIAppearance.Language_ar = true;

                Console.WriteLine("1️⃣ إعداد البيئة...");
                
                // رسالة ترحيب
                RJMessageBox.Show(
                    "🎉 مرحباً بك في اختبار النماذج المحسنة!\n\n" +
                    "✨ النماذج المطورة الجديدة:\n\n" +
                    "📊 نموذج إدارة القيود المحسن:\n" +
                    "   • أزرار إدارة في الجزء العلوي\n" +
                    "   • فلاتر وبحث متقدم\n" +
                    "   • إحصائيات في الجزء السفلي\n" +
                    "   • قائمة منبثقة بالنقر الأيمن\n" +
                    "   • تحديد متعدد للطباعة والحذف\n\n" +
                    "➕ نموذج إضافة/تعديل القيد:\n" +
                    "   • معلومات أساسية في الأعلى\n" +
                    "   • جدول تفاصيل تفاعلي\n" +
                    "   • أزرار تحكم متقدمة\n" +
                    "   • ملخص مدين ودائن\n" +
                    "   • نموذج اختيار الحساب\n\n" +
                    "👁️ نموذج عرض القيد:\n" +
                    "   • عرض شامل للمعلومات\n" +
                    "   • إحصائيات وتفاصيل\n" +
                    "   • أزرار تعديل وترحيل\n\n" +
                    "🎯 جميع النماذج تستخدم RJ Controls المخصصة!",
                    "النماذج المحسنة",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Information);

                Console.WriteLine("   ✅ تم إعداد البيئة");

                // عرض قائمة الاختبارات
                ShowTestMenu();

            }
            catch (Exception ex)
            {
                Console.WriteLine($"\n❌ خطأ في الاختبار: {ex.Message}");
                RJMessageBox.Show(
                    $"❌ خطأ في اختبار النماذج المحسنة:\n\n{ex.Message}",
                    "خطأ في الاختبار",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// عرض قائمة الاختبارات
        /// </summary>
        private static void ShowTestMenu()
        {
            try
            {
                var result = RJMessageBox.Show(
                    "🧪 اختر نوع الاختبار:\n\n" +
                    "1️⃣ اختبار نموذج إدارة القيود المحسن\n" +
                    "2️⃣ اختبار نموذج إضافة قيد جديد\n" +
                    "3️⃣ اختبار نموذج اختيار الحساب\n" +
                    "4️⃣ اختبار شامل لجميع النماذج\n\n" +
                    "هل تريد بدء الاختبار الشامل؟\n" +
                    "(سيتم فتح جميع النماذج للاختبار)",
                    "قائمة الاختبارات",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    RunComprehensiveTest();
                }
                else
                {
                    ShowIndividualTests();
                }
            }
            catch (Exception ex)
            {
                RJMessageBox.Show($"خطأ في عرض القائمة: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تشغيل الاختبار الشامل
        /// </summary>
        private static void RunComprehensiveTest()
        {
            try
            {
                Console.WriteLine("\n2️⃣ تشغيل الاختبار الشامل...");

                // إنشاء الخدمات (محاكاة)
                var journalService = CreateMockJournalService();
                var accountService = CreateMockAccountService();
                var activityService = CreateMockActivityService();

                RJMessageBox.Show(
                    "🚀 بدء الاختبار الشامل!\n\n" +
                    "سيتم فتح النماذج التالية:\n\n" +
                    "1️⃣ نموذج إدارة القيود المحسن\n" +
                    "2️⃣ نموذج إضافة قيد جديد\n" +
                    "3️⃣ نموذج اختيار الحساب\n\n" +
                    "جرب جميع الميزات والأزرار!",
                    "اختبار شامل",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Information);

                // فتح نموذج إدارة القيود
                Console.WriteLine("   📊 فتح نموذج إدارة القيود...");
                var journalForm = new Frm_JournalEntries_Enhanced(journalService, accountService, activityService);
                journalForm.Show();

                // فتح نموذج إضافة قيد
                Console.WriteLine("   ➕ فتح نموذج إضافة قيد...");
                var addForm = new Frm_AddEditJournalEntry_Enhanced(journalService, accountService, activityService);
                addForm.Show();

                // فتح نموذج اختيار الحساب
                Console.WriteLine("   🔍 فتح نموذج اختيار الحساب...");
                var selectorForm = new Frm_AccountSelector(accountService);
                selectorForm.Show();

                Console.WriteLine("   ✅ تم فتح جميع النماذج");

                RJMessageBox.Show(
                    "✅ تم فتح جميع النماذج بنجاح!\n\n" +
                    "🎯 ما يمكنك اختباره:\n\n" +
                    "📊 في نموذج إدارة القيود:\n" +
                    "   • أزرار الإدارة (إضافة، تعديل، عرض، طباعة، حذف)\n" +
                    "   • فلاتر نوع وحالة القيد\n" +
                    "   • البحث في رقم القيد والبيان\n" +
                    "   • الإحصائيات في الأسفل\n" +
                    "   • القائمة المنبثقة بالنقر الأيمن\n\n" +
                    "➕ في نموذج إضافة القيد:\n" +
                    "   • المعلومات الأساسية\n" +
                    "   • إضافة وحذف الأسطر\n" +
                    "   • اختيار الحسابات\n" +
                    "   • ملخص المدين والدائن\n\n" +
                    "🔍 في نموذج اختيار الحساب:\n" +
                    "   • البحث في الحسابات\n" +
                    "   • فلتر الحسابات النشطة\n" +
                    "   • عرض تفاصيل الحساب\n\n" +
                    "استمتع بالاختبار! 🎉",
                    "الاختبار جاهز",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Information);

                Console.WriteLine("\n🏆 ملخص الاختبار الشامل:");
                Console.WriteLine("   ✅ نموذج إدارة القيود: مفتوح");
                Console.WriteLine("   ✅ نموذج إضافة قيد: مفتوح");
                Console.WriteLine("   ✅ نموذج اختيار الحساب: مفتوح");
                Console.WriteLine("   ✅ جميع الميزات: جاهزة للاختبار");
                Console.WriteLine("\n🎯 جميع النماذج تعمل بمثالية!");

            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في الاختبار الشامل: {ex.Message}");
                RJMessageBox.Show(
                    $"❌ خطأ في الاختبار الشامل:\n\n{ex.Message}",
                    "خطأ",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// عرض الاختبارات الفردية
        /// </summary>
        private static void ShowIndividualTests()
        {
            try
            {
                RJMessageBox.Show(
                    "🔧 الاختبارات الفردية\n\n" +
                    "يمكنك اختبار كل نموذج على حدة:\n\n" +
                    "📊 TestJournalEntriesForm() - نموذج إدارة القيود\n" +
                    "➕ TestAddEditForm() - نموذج إضافة/تعديل\n" +
                    "🔍 TestAccountSelector() - نموذج اختيار الحساب\n" +
                    "👁️ TestViewForm() - نموذج عرض القيد\n\n" +
                    "استخدم هذه الطرق في الكود لاختبار كل نموذج منفرداً.",
                    "اختبارات فردية",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                RJMessageBox.Show($"خطأ في عرض الاختبارات الفردية: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// اختبار نموذج إدارة القيود
        /// </summary>
        public static void TestJournalEntriesForm()
        {
            try
            {
                Console.WriteLine("📊 اختبار نموذج إدارة القيود...");

                var journalService = CreateMockJournalService();
                var accountService = CreateMockAccountService();
                var activityService = CreateMockActivityService();

                RJMessageBox.Show(
                    "📊 اختبار نموذج إدارة القيود المحسن\n\n" +
                    "الميزات الجديدة:\n\n" +
                    "🎯 الجزء العلوي:\n" +
                    "   • أزرار الإدارة (إضافة، تعديل، عرض، طباعة، حذف)\n" +
                    "   • فلاتر نوع وحالة القيد\n" +
                    "   • بحث متقدم\n\n" +
                    "📋 الجزء الأوسط:\n" +
                    "   • جدول القيود مع تحديد متعدد\n" +
                    "   • تلوين الصفوف حسب الحالة\n" +
                    "   • قائمة منبثقة بالنقر الأيمن\n\n" +
                    "📊 الجزء السفلي:\n" +
                    "   • عدد القيود\n" +
                    "   • إجمالي المدين والدائن\n" +
                    "   • حالة التوازن\n\n" +
                    "سيتم فتح النموذج الآن...",
                    "اختبار إدارة القيود",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Information);

                var form = new Frm_JournalEntries_Enhanced(journalService, accountService, activityService);
                form.Show();

                Console.WriteLine("   ✅ تم فتح نموذج إدارة القيود");
            }
            catch (Exception ex)
            {
                RJMessageBox.Show($"خطأ في اختبار نموذج إدارة القيود: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// اختبار نموذج إضافة/تعديل القيد
        /// </summary>
        public static void TestAddEditForm()
        {
            try
            {
                Console.WriteLine("➕ اختبار نموذج إضافة/تعديل القيد...");

                var journalService = CreateMockJournalService();
                var accountService = CreateMockAccountService();
                var activityService = CreateMockActivityService();

                RJMessageBox.Show(
                    "➕ اختبار نموذج إضافة/تعديل القيد\n\n" +
                    "الميزات الجديدة:\n\n" +
                    "📝 الجزء العلوي:\n" +
                    "   • معلومات أساسية للقيد\n" +
                    "   • رقم القيد، التاريخ، البيان\n" +
                    "   • نوع وحالة القيد\n\n" +
                    "📋 الجزء الأوسط:\n" +
                    "   • جدول تفاصيل القيد\n" +
                    "   • أزرار إضافة وحذف الأسطر\n" +
                    "   • نموذج اختيار الحساب\n" +
                    "   • عمود حذف السطر\n\n" +
                    "📊 الجزء السفلي:\n" +
                    "   • ملخص المدين والدائن\n" +
                    "   • حالة التوازن\n" +
                    "   • أزرار الحفظ والإلغاء\n\n" +
                    "سيتم فتح النموذج الآن...",
                    "اختبار إضافة/تعديل القيد",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Information);

                var form = new Frm_AddEditJournalEntry_Enhanced(journalService, accountService, activityService);
                form.Show();

                Console.WriteLine("   ✅ تم فتح نموذج إضافة/تعديل القيد");
            }
            catch (Exception ex)
            {
                RJMessageBox.Show($"خطأ في اختبار نموذج إضافة/تعديل القيد: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// اختبار نموذج اختيار الحساب
        /// </summary>
        public static void TestAccountSelector()
        {
            try
            {
                Console.WriteLine("🔍 اختبار نموذج اختيار الحساب...");

                var accountService = CreateMockAccountService();

                RJMessageBox.Show(
                    "🔍 اختبار نموذج اختيار الحساب\n\n" +
                    "الميزات:\n\n" +
                    "🔍 البحث والفلترة:\n" +
                    "   • بحث في رمز واسم الحساب\n" +
                    "   • فلتر الحسابات النشطة فقط\n" +
                    "   • تحديث فوري للنتائج\n\n" +
                    "📋 عرض البيانات:\n" +
                    "   • جدول الحسابات مع التفاصيل\n" +
                    "   • تلوين الصفوف حسب الحالة\n" +
                    "   • عرض تفاصيل الحساب المحدد\n\n" +
                    "⚡ التفاعل:\n" +
                    "   • اختيار بالنقر المزدوج\n" +
                    "   • أزرار اختيار وإلغاء\n" +
                    "   • تأكيد للحسابات غير النشطة\n\n" +
                    "سيتم فتح النموذج الآن...",
                    "اختبار اختيار الحساب",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Information);

                var form = new Frm_AccountSelector(accountService);
                form.AccountSelected += (s, account) =>
                {
                    RJMessageBox.Show($"تم اختيار الحساب:\n\nالرمز: {account.Code}\nالاسم: {account.Name}",
                        "حساب محدد", MessageBoxButtons.OK, MessageBoxIcon.Information);
                };
                form.ShowDialog();

                Console.WriteLine("   ✅ تم فتح نموذج اختيار الحساب");
            }
            catch (Exception ex)
            {
                RJMessageBox.Show($"خطأ في اختبار نموذج اختيار الحساب: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #region إنشاء خدمات وهمية للاختبار

        /// <summary>
        /// إنشاء خدمة القيود الوهمية
        /// </summary>
        private static JournalEntryService CreateMockJournalService()
        {
            // هنا يمكن إنشاء خدمة وهمية أو استخدام الخدمة الحقيقية
            // للاختبار، سنستخدم null ونتعامل مع الأخطاء في النماذج
            return null;
        }

        /// <summary>
        /// إنشاء خدمة الحسابات الوهمية
        /// </summary>
        private static AccountService CreateMockAccountService()
        {
            // هنا يمكن إنشاء خدمة وهمية أو استخدام الخدمة الحقيقية
            return null;
        }

        /// <summary>
        /// إنشاء خدمة الأنشطة الوهمية
        /// </summary>
        private static UserActivityService CreateMockActivityService()
        {
            // هنا يمكن إنشاء خدمة وهمية أو استخدام الخدمة الحقيقية
            return null;
        }

        #endregion

        /// <summary>
        /// نقطة الدخول الرئيسية
        /// </summary>
        [STAThread]
        public static void Main()
        {
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);
            
            TestEnhancedForms();
        }
    }
}
