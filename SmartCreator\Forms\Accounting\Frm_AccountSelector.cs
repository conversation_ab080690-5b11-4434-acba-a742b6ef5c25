using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using SmartCreator.Entities.Accounting;
using SmartCreator.RJControls;
using SmartCreator.RJForms;
using FontAwesome.Sharp;
using SmartCreator.Service;
using SmartCreator.Services.Accounting;
using SmartCreator.Settings;

namespace SmartCreator.Forms.Accounting
{
    /// <summary>
    /// نموذج اختيار الحساب
    /// </summary>
    public partial class Frm_AccountSelector : RJChildForm
    {
        #region المتغيرات

        private readonly AccountService _accountService;
        private List<Account> _accounts;
        private List<Account> _filteredAccounts;
        private Account _selectedAccount;

        // الأحداث
        public event EventHandler<Account> AccountSelected;

        #endregion

        #region البناء

        /// <summary>
        /// Constructor افتراضي للـ Designer
        /// </summary>
        public Frm_AccountSelector()
        {
            // إنشاء الخدمة بـ constructor افتراضي للـ Designer
            _accountService = new AccountService();
            _accounts = new List<Account>();
            _filteredAccounts = new List<Account>();

            InitializeComponent();

            // تجنب تحميل البيانات في وضع التصميم
            if (!DesignMode)
            {
                SetupForm();
                LoadAccountsAsync();
            }
        }

        /// <summary>
        /// Constructor مع الخدمة المحقونة
        /// </summary>
        public Frm_AccountSelector(AccountService accountService)
        {
            _accountService = accountService;
            _accounts = new List<Account>();
            _filteredAccounts = new List<Account>();

            InitializeComponent();
            SetupForm();
            LoadAccountsAsync();
        }

        #endregion

        #region الخصائص

        /// <summary>
        /// الحساب المحدد
        /// </summary>
        public Account SelectedAccount => _selectedAccount;

        #endregion

        #region إعداد النموذج

        /// <summary>
        /// إعداد النموذج
        /// </summary>
        private void SetupForm()
        {
            // إعداد خصائص RJChildForm
            this.Text = "اختيار حساب";
            this.Caption = "اختيار حساب";
            this.FormIcon = FontAwesome.Sharp.IconChar.Search;
            this.IsChildForm = true;
            this.Size = new Size(800, 600);
            this.StartPosition = FormStartPosition.CenterScreen;

            // العناصر موجودة بالفعل في ملف Designer - لا حاجة لإضافتها في runtime

            // إعداد جدول البيانات
            SetupDataGridView();

            // تطبيق إعدادات المظهر
            ApplyThemeSettings();
        }

        /// <summary>
        /// إعداد جدول البيانات
        /// </summary>
        private void SetupDataGridView()
        {
            dgvAccounts.AutoGenerateColumns = false;
            dgvAccounts.AllowUserToAddRows = false;
            dgvAccounts.AllowUserToDeleteRows = false;
            dgvAccounts.ReadOnly = true;
            dgvAccounts.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dgvAccounts.MultiSelect = false;

            // إعداد الأعمدة
            dgvAccounts.Columns.Clear();
            dgvAccounts.Columns.AddRange(new DataGridViewColumn[]
            {
                new DataGridViewTextBoxColumn
                {
                    Name = "Code",
                    HeaderText = "رمز الحساب",
                    DataPropertyName = "Code",
                    Width = 120
                },
                new DataGridViewTextBoxColumn
                {
                    Name = "Name",
                    HeaderText = "اسم الحساب",
                    DataPropertyName = "Name",
                    Width = 300
                },
                //new DataGridViewTextBoxColumn
                //{
                //    Name = "Type",
                //    HeaderText = "النوع",
                //    Width = 120
                //},
                //new DataGridViewTextBoxColumn
                //{
                //    Name = "ParentName",
                //    HeaderText = "الحساب الأب",
                //    Width = 200
                //},
                //new DataGridViewCheckBoxColumn
                //{
                //    Name = "IsActive",
                //    HeaderText = "نشط",
                //    DataPropertyName = "IsActive",
                //    Width = 60
                //}
            });

            // أحداث الجدول
            dgvAccounts.SelectionChanged += DgvAccounts_SelectionChanged;
            dgvAccounts.CellDoubleClick += DgvAccounts_CellDoubleClick;
        }

        /// <summary>
        /// تطبيق إعدادات المظهر
        /// </summary>
        private void ApplyThemeSettings()
        {
            //this.BackColor = UIAppearance.BackgroundColor;

            var font = new Font("Droid Arabic Kufi", 9F, FontStyle.Regular);
            //this.Font = font;

            foreach (Control control in this.Controls)
            {
                //ApplyFontToControl(control, font);
            }
        }

        /// <summary>
        /// تطبيق الخط على العنصر وعناصره الفرعية
        /// </summary>
        private void ApplyFontToControl(Control control, Font font)
        {
            //control.Font = font;
            foreach (Control childControl in control.Controls)
            {
                //ApplyFontToControl(childControl, font);
            }
        }

        /// <summary>
        /// إضافة العناصر إلى منطقة العميل في RJChildForm
        /// </summary>
        private void AddControlsToClientArea()
        {
            // الحصول على منطقة العميل من RJChildForm
            //var clientArea = this.Controls.Find("pnlClientArea", true).FirstOrDefault() as Panel;
            //if (clientArea != null)
            //{
            //    // إضافة العناصر الرئيسية إلى منطقة العميل
            //    clientArea.Controls.Add(pnlTop);
            //    clientArea.Controls.Add(pnlMiddle);
            //    clientArea.Controls.Add(pnlBottom);

            //    // تحديث ترتيب العناصر
            //    pnlTop.BringToFront();
            //    pnlBottom.BringToFront();
            //    pnlMiddle.BringToFront();
            //}
        }

        #endregion

        #region تحميل البيانات

        /// <summary>
        /// تحميل الحسابات بشكل غير متزامن
        /// </summary>
        private async void LoadAccountsAsync()
        {
            try
            {
                // عرض مؤشر التحميل
                dgvAccounts.DataSource = null;
                lblStatus.Text = "جاري تحميل الحسابات...";

                // تحميل الحسابات
                _accounts = await Task.Run(() => _accountService.GetAllAccounts());
                _filteredAccounts = new List<Account>(_accounts);

                // عرض البيانات
                RefreshDataGrid();

                lblStatus.Text = $"تم تحميل {_accounts.Count} حساب";
            }
            catch (Exception ex)
            {
                RJMessageBox.Show($"خطأ في تحميل الحسابات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                lblStatus.Text = "خطأ في تحميل البيانات";
            }
        }

        /// <summary>
        /// تحديث جدول البيانات
        /// </summary>
        private void RefreshDataGrid()
        {
            try
            {
                if (_filteredAccounts == null) return;

                // إنشاء DataTable للعرض
                var dataTable = new DataTable();
                dataTable.Columns.AddRange(new DataColumn[]
                {
                    new DataColumn("Code", typeof(string)),
                    new DataColumn("Name", typeof(string)),
                    //new DataColumn("Type", typeof(string)),
                    //new DataColumn("ParentName", typeof(string)),
                    //new DataColumn("IsActive", typeof(bool))
                });

                // إضافة البيانات
                foreach (var account in _filteredAccounts)
                {
                    var row = dataTable.NewRow();
                    row["Code"] = account.Code;
                    row["Name"] = account.Name;
                    //row["Type"] = Entities.Accounting.AccountTypeInfo.GetArabicName(account.Type);
                    //row["ParentName"] = GetParentAccountName(account.ParentId);
                    //row["IsActive"] = account.IsActive;
                    dataTable.Rows.Add(row);
                }

                dgvAccounts.DataSource = dataTable;

                // تلوين الصفوف
                ColorizeRows();
            }
            catch (Exception ex)
            {
                RJMessageBox.Show($"خطأ في تحديث الجدول: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// الحصول على اسم الحساب الأب
        /// </summary>
        private string GetParentAccountName(int? parentId)
        {
            if (!parentId.HasValue) return "";

            var parent = _accounts?.FirstOrDefault(a => a.Id == parentId.Value);
            return parent?.Name ?? "";
        }

        /// <summary>
        /// تلوين الصفوف
        /// </summary>
        private void ColorizeRows()
        {
            try
            {
                for (int i = 0; i < dgvAccounts.Rows.Count && i < _filteredAccounts.Count; i++)
                {
                    var account = _filteredAccounts[i];
                    var row = dgvAccounts.Rows[i];

                    if (!account.IsActive)
                    {
                        row.DefaultCellStyle.BackColor = Color.FromArgb(255, 220, 220); // أحمر فاتح للحسابات غير النشطة
                        row.DefaultCellStyle.ForeColor = Color.Gray;
                    }
                    else if (account.ParentId.HasValue)
                    {
                        row.DefaultCellStyle.BackColor = Color.FromArgb(240, 248, 255); // أزرق فاتح للحسابات الفرعية
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في تلوين الصفوف: {ex.Message}");
            }
        }

        #endregion

        #region أحداث الجدول

        /// <summary>
        /// تغيير التحديد في الجدول
        /// </summary>
        private void DgvAccounts_SelectionChanged(object sender, EventArgs e)
        {
            try
            {
                if (dgvAccounts.SelectedRows.Count > 0)
                {
                    var selectedIndex = dgvAccounts.SelectedRows[0].Index;
                    if (selectedIndex >= 0 && selectedIndex < _filteredAccounts.Count)
                    {
                        _selectedAccount = _filteredAccounts[selectedIndex];
                        btnSelect.Enabled = true;

                        // عرض تفاصيل الحساب
                        ShowAccountDetails(_selectedAccount);
                    }
                }
                else
                {
                    _selectedAccount = null;
                    btnSelect.Enabled = false;
                    ClearAccountDetails();
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في تغيير التحديد: {ex.Message}");
            }
        }

        /// <summary>
        /// النقر المزدوج على الجدول
        /// </summary>
        private void DgvAccounts_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            try
            {
                if (e.RowIndex >= 0 && _selectedAccount != null)
                {
                    SelectAccount();
                }
            }
            catch (Exception ex)
            {
                RJMessageBox.Show($"خطأ في اختيار الحساب: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region أحداث الأزرار

        /// <summary>
        /// اختيار الحساب
        /// </summary>
        private void BtnSelect_Click(object sender, EventArgs e)
        {
            try
            {
                SelectAccount();
            }
            catch (Exception ex)
            {
                RJMessageBox.Show($"خطأ في اختيار الحساب: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// إلغاء الاختيار
        /// </summary>
        private void BtnCancel_Click(object sender, EventArgs e)
        {
            try
            {
                this.DialogResult = DialogResult.Cancel;
                this.Close();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في الإلغاء: {ex.Message}");
            }
        }

        /// <summary>
        /// البحث
        /// </summary>
        private void BtnSearch_Click(object sender, EventArgs e)
        {
            try
            {
                ApplyFilter();
            }
            catch (Exception ex)
            {
                RJMessageBox.Show($"خطأ في البحث: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region طرق مساعدة

        /// <summary>
        /// اختيار الحساب
        /// </summary>
        private void SelectAccount()
        {
            if (_selectedAccount == null)
            {
                RJMessageBox.Show("يرجى تحديد حساب", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            if (!_selectedAccount.IsActive)
            {
                var result = RJMessageBox.Show(
                    "الحساب المحدد غير نشط. هل تريد المتابعة؟",
                    "تأكيد",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question);

                if (result != DialogResult.Yes)
                    return;
            }

            // إثارة الحدث
            AccountSelected?.Invoke(this, _selectedAccount);

            this.DialogResult = DialogResult.OK;
            this.Close();
        }

        /// <summary>
        /// تطبيق الفلتر
        /// </summary>
        private void ApplyFilter()
        {
            try
            {
                if (_accounts == null) return;

                _filteredAccounts = new List<Account>(_accounts);

                // فلتر البحث
                var searchText = txtSearch.Texts?.Trim();
                if (!string.IsNullOrEmpty(searchText))
                {
                    _filteredAccounts = _filteredAccounts.Where(a =>
                        a.Code.IndexOf(searchText, StringComparison.OrdinalIgnoreCase) >= 0 ||
                        a.Name.IndexOf(searchText, StringComparison.OrdinalIgnoreCase) >= 0
                    ).ToList();
                }

                // فلتر الحسابات النشطة فقط
                if (chkActiveOnly.Checked)
                {
                    _filteredAccounts = _filteredAccounts.Where(a => a.IsActive).ToList();
                }

                RefreshDataGrid();
                lblStatus.Text = $"تم العثور على {_filteredAccounts.Count} حساب";
            }
            catch (Exception ex)
            {
                RJMessageBox.Show($"خطأ في تطبيق الفلتر: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// عرض تفاصيل الحساب
        /// </summary>
        private void ShowAccountDetails(Account account)
        {
            //try
            //{
            //    if (account == null) return;

            //    lblAccountCode.Text = $"رمز الحساب: {account.Code}";
            //    lblAccountName.Text = $"اسم الحساب: {account.Name}";
            //    lblAccountType.Text = $"نوع الحساب: {Entities.Accounting.AccountTypeInfo.GetArabicName(account.Type)}";
            //    lblParentAccount.Text = $"الحساب الأب: {GetParentAccountName(account.ParentId)}";
            //    lblAccountStatus.Text = $"الحالة: {(account.IsActive ? "نشط" : "غير نشط")}";
            //}
            //catch (Exception ex)
            //{
            //    Console.WriteLine($"خطأ في عرض تفاصيل الحساب: {ex.Message}");
            //}
        }

        /// <summary>
        /// مسح تفاصيل الحساب
        /// </summary>
        private void ClearAccountDetails()
        {
            lblAccountCode.Text = "رمز الحساب: -";
            lblAccountName.Text = "اسم الحساب: -";
            lblAccountType.Text = "نوع الحساب: -";
            lblParentAccount.Text = "الحساب الأب: -";
            lblAccountStatus.Text = "الحالة: -";
        }

        #endregion
    }
}
