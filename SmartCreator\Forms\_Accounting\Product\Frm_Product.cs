using SmartCreator.Entities.Accounting;
using SmartCreator.RJControls;
using SmartCreator.RJForms;
using SmartCreator.Services.Products;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace SmartCreator.Forms._Accounting.Product
{
    public partial class Frm_Product : RJChildForm
    {
        private readonly ProductService _productService;
        private List<Entities.Accounting.Product> _products;
        private Entities.Accounting.Product _selectedProduct;

        public Frm_Product()
        {
            InitializeComponent();
            _productService = new ProductService();
            _products = new List<Entities.Accounting.Product>();
            InitializeForm();
        }

        private void InitializeForm()
        {
            this.Text = "إدارة المنتجات";
            this.WindowState = FormWindowState.Maximized;
            
            // تهيئة الأحداث
            this.Load += Frm_Product_Load;
            
            // تهيئة أحداث الأزرار
            btnAdd.Click += BtnAdd_Click;
            btnEdit.Click += BtnEdit_Click;
            btnDelete.Click += BtnDelete_Click;
            btnView.Click += BtnView_Click;
            btnPrint.Click += BtnPrint_Click;
            btnRefresh.Click += BtnRefresh_Click;
            btnSearch.Click += BtnSearch_Click;
            btnLowStock.Click += BtnLowStock_Click;
            
            // تهيئة أحداث الجدول
            dgvProducts.SelectionChanged += DgvProducts_SelectionChanged;
            dgvProducts.CellDoubleClick += DgvProducts_CellDoubleClick;
            dgvProducts.KeyDown += DgvProducts_KeyDown;
            
            // تهيئة أحداث البحث
            txtSearch.TextChanged += TxtSearch_TextChanged;
            txtSearch.KeyDown += TxtSearch_KeyDown;
            
            // تهيئة القائمة المنسدلة للبحث
            cmbSearchType.Items.AddRange(new string[] { "الكل", "الاسم", "الكود", "الوصف" });
            cmbSearchType.SelectedIndex = 0;
            
            // تهيئة الجدول
            SetupDataGridView();
        }

        private void SetupDataGridView()
        {
            dgvProducts.AutoGenerateColumns = false;
            dgvProducts.AllowUserToAddRows = false;
            dgvProducts.AllowUserToDeleteRows = false;
            dgvProducts.ReadOnly = true;
            dgvProducts.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dgvProducts.MultiSelect = true;
            
            // إضافة الأعمدة
            dgvProducts.Columns.Clear();
            
            dgvProducts.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Id",
                DataPropertyName = "Id",
                HeaderText = "المعرف",
                Width = 80,
                Visible = false
            });
            
            dgvProducts.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Code",
                DataPropertyName = "Code",
                HeaderText = "الكود",
                Width = 100
            });
            
            dgvProducts.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Name",
                DataPropertyName = "Name",
                HeaderText = "اسم المنتج",
                Width = 200
            });
            
            dgvProducts.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Price",
                DataPropertyName = "Price",
                HeaderText = "السعر",
                Width = 100,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "N2" }
            });
            
            dgvProducts.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Stock",
                DataPropertyName = "Stock",
                HeaderText = "المخزون",
                Width = 100
            });
            
            dgvProducts.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "LowStockThreshold",
                DataPropertyName = "LowStockThreshold",
                HeaderText = "الحد الأدنى",
                Width = 100
            });
            
            dgvProducts.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "StockStatus",
                DataPropertyName = "StockStatus",
                HeaderText = "حالة المخزون",
                Width = 120
            });
            
            dgvProducts.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Description",
                DataPropertyName = "Description",
                HeaderText = "الوصف",
                Width = 200
            });
            
            dgvProducts.Columns.Add(new DataGridViewCheckBoxColumn
            {
                Name = "Str_Active",
                DataPropertyName = "Str_Active",
                HeaderText = "مفعل",
                Width = 80
            });
        }

        private async void Frm_Product_Load(object sender, EventArgs e)
        {
            await LoadProductsAsync();
            UpdateStatistics();
        }

        private async Task LoadProductsAsync()
        {
            try
            {
                ShowLoading(true);
                _products = await _productService.GetAllProductsAsync();
                dgvProducts.DataSource = _products;
                UpdateStatistics();
            }
            catch (Exception ex)
            {
                RJMessageBox.Show($"خطأ في تحميل المنتجات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                ShowLoading(false);
            }
        }

        private void UpdateStatistics()
        {
            if (_products != null)
            {
                lblTotalProducts.Text = $"إجمالي المنتجات: {_products.Count}";
                lblActiveProducts.Text = $"المنتجات المفعلة: {_products.Count(p => p.Str_Active)}";
                lblLowStockProducts.Text = $"منتجات تحتاج إعادة طلب: {_products.Count(p => p.NeedsReorder)}";
                lblTotalValue.Text = $"إجمالي قيمة المخزون: {_products.Sum(p => p.Price * p.Stock):N2}";
            }
        }

        private void ShowLoading(bool show)
        {
            if (show)
            {
                Cursor = Cursors.WaitCursor;
                dgvProducts.Enabled = false;
            }
            else
            {
                Cursor = Cursors.Default;
                dgvProducts.Enabled = true;
            }
        }

        private void DgvProducts_SelectionChanged(object sender, EventArgs e)
        {
            if (dgvProducts.SelectedRows.Count > 0)
            {
                _selectedProduct = dgvProducts.SelectedRows[0].DataBoundItem as Entities.Accounting.Product;
                UpdateButtonStates();
            }
        }

        private void UpdateButtonStates()
        {
            bool hasSelection = _selectedProduct != null;
            bool hasMultipleSelection = dgvProducts.SelectedRows.Count > 1;
            
            btnEdit.Enabled = hasSelection && !hasMultipleSelection;
            btnView.Enabled = hasSelection && !hasMultipleSelection;
            btnDelete.Enabled = hasSelection;
            btnPrint.Enabled = hasSelection;
        }

        private async void BtnAdd_Click(object sender, EventArgs e)
        {
            var addForm = new Frm_ProductAdd();
            if (addForm.ShowDialog() == DialogResult.OK)
            {
                await LoadProductsAsync();
            }
        }

        private async void BtnEdit_Click(object sender, EventArgs e)
        {
            if (_selectedProduct == null) return;
            
            var editForm = new Frm_ProductEdit(_selectedProduct.Id);
            if (editForm.ShowDialog() == DialogResult.OK)
            {
                await LoadProductsAsync();
            }
        }

        private void BtnView_Click(object sender, EventArgs e)
        {
            if (_selectedProduct == null) return;
            
            var viewForm = new Frm_ProductView(_selectedProduct.Id);
            viewForm.ShowDialog();
        }

        private async void BtnDelete_Click(object sender, EventArgs e)
        {
            if (dgvProducts.SelectedRows.Count == 0) return;
            
            var selectedProducts = dgvProducts.SelectedRows.Cast<DataGridViewRow>()
                .Select(row => row.DataBoundItem as Entities.Accounting.Product)
                .Where(p => p != null)
                .ToList();
            
            var message = selectedProducts.Count == 1 
                ? $"هل تريد حذف المنتج '{selectedProducts[0].Name}'؟"
                : $"هل تريد حذف {selectedProducts.Count} منتج؟";
            
            if (RJMessageBox.Show(message, "تأكيد الحذف", 
                MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
            {
                try
                {
                    ShowLoading(true);
                    int deletedCount = 0;
                    
                    foreach (var product in selectedProducts)
                    {
                        if (await _productService.DeleteProductAsync(product.Id))
                        {
                            deletedCount++;
                        }
                    }
                    
                    if (deletedCount > 0)
                    {
                        RJMessageBox.Show($"تم حذف {deletedCount} منتج بنجاح", "نجح",
                            MessageBoxButtons.OK, MessageBoxIcon.Information);
                        await LoadProductsAsync();
                    }
                }
                catch (Exception ex)
                {
                    RJMessageBox.Show($"خطأ في حذف المنتجات: {ex.Message}", "خطأ",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
                finally
                {
                    ShowLoading(false);
                }
            }
        }

        private void BtnPrint_Click(object sender, EventArgs e)
        {
            // TODO: تنفيذ الطباعة
            RJMessageBox.Show("سيتم تنفيذ الطباعة قريباً", "معلومات",
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private async void BtnRefresh_Click(object sender, EventArgs e)
        {
            await LoadProductsAsync();
        }

        private async void BtnSearch_Click(object sender, EventArgs e)
        {
            await PerformSearchAsync();
        }

        private async void BtnLowStock_Click(object sender, EventArgs e)
        {
            try
            {
                ShowLoading(true);
                var lowStockProducts = await _productService.GetLowStockProductsAsync();
                dgvProducts.DataSource = lowStockProducts;
                
                RJMessageBox.Show($"تم العثور على {lowStockProducts.Count} منتج يحتاج إعادة طلب", "معلومات",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                RJMessageBox.Show($"خطأ في البحث عن المنتجات منخفضة المخزون: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                ShowLoading(false);
            }
        }

        private async void TxtSearch_TextChanged(object sender, EventArgs e)
        {
            if (string.IsNullOrWhiteSpace(txtSearch.Text))
            {
                dgvProducts.DataSource = _products;
            }
        }

        private async void TxtSearch_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                await PerformSearchAsync();
            }
        }

        private async Task PerformSearchAsync()
        {
            if (string.IsNullOrWhiteSpace(txtSearch.Text))
            {
                dgvProducts.DataSource = _products;
                return;
            }
            
            try
            {
                ShowLoading(true);
                var searchResults = await _productService.SearchProductsAsync(txtSearch.Text.Trim());
                dgvProducts.DataSource = searchResults;
            }
            catch (Exception ex)
            {
                RJMessageBox.Show($"خطأ في البحث: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                ShowLoading(false);
            }
        }

        private async void DgvProducts_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                BtnView_Click(sender, e);
            }
        }

        private void DgvProducts_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Delete)
            {
                BtnDelete_Click(sender, e);
            }
            else if (e.KeyCode == Keys.Enter)
            {
                BtnView_Click(sender, e);
            }
            else if (e.KeyCode == Keys.F2)
            {
                BtnEdit_Click(sender, e);
            }
        }
    }
}
