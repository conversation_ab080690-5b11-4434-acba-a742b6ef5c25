using SmartCreator.Data;
using SmartCreator.Entities.Accounting;
using SmartCreator.Models;
using SmartCreator.Properties;
using SmartCreator.RJControls;
using SmartCreator.RJForms;
using SmartCreator.Services;
using SmartCreator.Settings;
using SmartCreator.Utils;
using System;
using System.Collections.Generic;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace SmartCreator.Forms.Accounting
{
    /// <summary>
    /// نموذج إدارة المنتجات المحسن
    /// </summary>
    public partial class Frm_Product : RJChildForm
    {
        private readonly ProductService _productService;
        private readonly InventoryService _inventoryService;
        private List<Entities.Accounting.Product> _allProducts;
        private List<Entities.Accounting.Product> _filteredProducts;

        public Frm_Product()
        {
            InitializeComponent();
            _productService = new ProductService();
            _inventoryService = new InventoryService();
            _allProducts = new List<Entities.Accounting.Product>();
            _filteredProducts = new List<Entities.Accounting.Product>();
            
            InitializeForm();
        }

        private void InitializeForm()
        {
            this.Text = "إدارة المنتجات";
            
            // تطبيق الخطوط المخصصة
            if (UIAppearance.Language_ar)
            {
                ApplyArabicFonts();
            }

            // إعداد DataGridView
            SetupDataGridView();
            
            // إعداد أحداث البحث
            txtSearch.onTextChanged += TxtSearch_TextChanged;
            
            // تحميل البيانات
            LoadProductsAsync();
        }

        private void ApplyArabicFonts()
        {
            var titleFont = Program.GetCustomFont(Resources.DroidKufi_Bold, 12, FontStyle.Bold);
            var buttonFont = Program.GetCustomFont(Resources.DroidKufi_Bold, 10, FontStyle.Bold);
            var dgvFont = Program.GetCustomFont(Resources.DroidSansArabic, 9, FontStyle.Regular);

            lblTitle.Font = titleFont;
            btnAdd.Font = btnEdit.Font = btnDelete.Font = btnRefresh.Font = 
            btnInventoryUpdate.Font = btnLowStock.Font = buttonFont;

            dgvProducts.ColumnHeadersDefaultCellStyle.Font = dgvFont;
            dgvProducts.DefaultCellStyle.Font = dgvFont;
        }

        private void SetupDataGridView()
        {
            dgvProducts.AutoGenerateColumns = false;
            dgvProducts.AllowUserToAddRows = false;
            dgvProducts.AllowUserToDeleteRows = false;
            dgvProducts.ReadOnly = true;
            dgvProducts.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dgvProducts.MultiSelect = false;

            // إعداد الأعمدة
            dgvProducts.Columns.Clear();
            
            dgvProducts.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Id",
                DataPropertyName = "Id",
                Visible = false
            });

            dgvProducts.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Code",
                DataPropertyName = "Code",
                HeaderText = "الكود",
                Width = 100
            });

            dgvProducts.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Name",
                DataPropertyName = "Name",
                HeaderText = "اسم المنتج",
                Width = 200
            });

            dgvProducts.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Price",
                DataPropertyName = "Price",
                HeaderText = "السعر",
                Width = 100,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "C2" }
            });

            dgvProducts.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Stock",
                DataPropertyName = "Stock",
                HeaderText = "المخزون",
                Width = 80
            });

            dgvProducts.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "LowStockThreshold",
                DataPropertyName = "LowStockThreshold",
                HeaderText = "الحد الأدنى",
                Width = 80
            });

            dgvProducts.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "StockStatus",
                DataPropertyName = "StockStatus",
                HeaderText = "حالة المخزون",
                Width = 120
            });

            dgvProducts.Columns.Add(new DataGridViewCheckBoxColumn
            {
                Name = "Str_Active",
                DataPropertyName = "Str_Active",
                HeaderText = "مفعل",
                Width = 60
            });

            // تلوين الصفوف حسب حالة المخزون
            dgvProducts.CellFormatting += DgvProducts_CellFormatting;
        }

        private void DgvProducts_CellFormatting(object sender, DataGridViewCellFormattingEventArgs e)
        {
            if (e.RowIndex >= 0 && dgvProducts.Rows[e.RowIndex].DataBoundItem is Entities.Accounting.Product product)
            {
                var row = dgvProducts.Rows[e.RowIndex];
                
                if (product.Stock <= 0)
                {
                    row.DefaultCellStyle.BackColor = Color.FromArgb(255, 235, 235); // أحمر فاتح
                    row.DefaultCellStyle.ForeColor = Color.DarkRed;
                }
                else if (product.NeedsReorder)
                {
                    row.DefaultCellStyle.BackColor = Color.FromArgb(255, 248, 220); // أصفر فاتح
                    row.DefaultCellStyle.ForeColor = Color.DarkOrange;
                }
                else
                {
                    row.DefaultCellStyle.BackColor = Color.White;
                    row.DefaultCellStyle.ForeColor = Color.Black;
                }
            }
        }

        private async void LoadProductsAsync()
        {
            try
            {
                lblStatus.Text = "جاري تحميل المنتجات...";
                btnRefresh.Enabled = false;

                _allProducts = await _productService.GetAllProductsAsync();
                _filteredProducts = _allProducts.ToList();
                
                UpdateDataGridView();
                UpdateStatusLabel();
            }
            catch (Exception ex)
            {
                RJMessageBox.Show($"خطأ في تحميل المنتجات: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                btnRefresh.Enabled = true;
            }
        }

        private void UpdateDataGridView()
        {
            dgvProducts.DataSource = null;
            dgvProducts.DataSource = _filteredProducts;
            
            // إخفاء الأعمدة غير المرغوب فيها
            foreach (DataGridViewColumn column in dgvProducts.Columns)
            {
                if (column.Name == "Description" || column.Name == "Rb" || 
                    column.Name == "Account_IncomeId" || column.Name == "Account_ExpenseId" ||
                    column.Name == "Product_UomId" || column.Name == "Active")
                {
                    column.Visible = false;
                }
            }
        }

        private void UpdateStatusLabel()
        {
            var totalProducts = _allProducts.Count;
            var lowStockCount = _allProducts.Count(p => p.NeedsReorder);
            var outOfStockCount = _allProducts.Count(p => p.Stock <= 0);
            
            lblStatus.Text = $"إجمالي المنتجات: {totalProducts} | مخزون منخفض: {lowStockCount} | نفد المخزون: {outOfStockCount}";
        }

        private void TxtSearch_TextChanged(object sender, EventArgs e)
        {
            FilterProducts();
        }

        private void FilterProducts()
        {
            var searchTerm = txtSearch.Text.Trim().ToLower();
            
            if (string.IsNullOrEmpty(searchTerm))
            {
                _filteredProducts = _allProducts.ToList();
            }
            else
            {
                _filteredProducts = _allProducts.Where(p =>
                    p.Name.ToLower().Contains(searchTerm) ||
                    p.Code.ToLower().Contains(searchTerm) ||
                    (!string.IsNullOrEmpty(p.Description) && p.Description.ToLower().Contains(searchTerm))
                ).ToList();
            }
            
            UpdateDataGridView();
        }

        private void BtnAdd_Click(object sender, EventArgs e)
        {
            var form = new Form_ProductMaintenance();
            if (form.ShowDialog() == DialogResult.OK)
            {
                LoadProductsAsync();
            }
        }

        private void BtnEdit_Click(object sender, EventArgs e)
        {
            if (dgvProducts.SelectedRows.Count == 0)
            {
                RJMessageBox.Show("يرجى اختيار منتج للتعديل", "تنبيه", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var selectedProduct = dgvProducts.SelectedRows[0].DataBoundItem as Entities.Accounting.Product;
            if (selectedProduct != null)
            {
                var form = new Form_ProductMaintenance(selectedProduct);
                if (form.ShowDialog() == DialogResult.OK)
                {
                    LoadProductsAsync();
                }
            }
        }

        private async void BtnDelete_Click(object sender, EventArgs e)
        {
            if (dgvProducts.SelectedRows.Count == 0)
            {
                RJMessageBox.Show("يرجى اختيار منتج للحذف", "تنبيه", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var selectedProduct = dgvProducts.SelectedRows[0].DataBoundItem as Entities.Accounting.Product;
            if (selectedProduct != null)
            {
                var result = RJMessageBox.Show(
                    $"هل أنت متأكد من حذف المنتج '{selectedProduct.Name}'؟\nهذا الإجراء لا يمكن التراجع عنه.",
                    "تأكيد الحذف",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    var success = await _productService.DeleteProductAsync(selectedProduct.Id);
                    if (success)
                    {
                        LoadProductsAsync();
                    }
                }
            }
        }

        private void BtnRefresh_Click(object sender, EventArgs e)
        {
            LoadProductsAsync();
        }

        private void BtnInventoryUpdate_Click(object sender, EventArgs e)
        {
            if (dgvProducts.SelectedRows.Count == 0)
            {
                RJMessageBox.Show("يرجى اختيار منتج لتحديث مخزونه", "تنبيه", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var selectedProduct = dgvProducts.SelectedRows[0].DataBoundItem as Entities.Accounting.Product;
            if (selectedProduct != null)
            {
                var form = new Frm_InventoryUpdate(selectedProduct);
                if (form.ShowDialog() == DialogResult.OK)
                {
                    LoadProductsAsync();
                }
            }
        }

        private async void BtnLowStock_Click(object sender, EventArgs e)
        {
            try
            {
                var lowStockProducts = await _inventoryService.GetLowStockProductsAsync();
                _filteredProducts = lowStockProducts;
                UpdateDataGridView();
                
                if (lowStockProducts.Count == 0)
                {
                    RJMessageBox.Show("لا توجد منتجات تحتاج إعادة طلب", "معلومات", 
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    lblStatus.Text = $"عرض {lowStockProducts.Count} منتج يحتاج إعادة طلب";
                }
            }
            catch (Exception ex)
            {
                RJMessageBox.Show($"خطأ في عرض المنتجات منخفضة المخزون: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void DgvProducts_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                BtnEdit_Click(sender, e);
            }
        }

        private void Frm_Product_Load(object sender, EventArgs e)
        {
            // تم نقل التحميل إلى المنشئ
        }
    }
}
