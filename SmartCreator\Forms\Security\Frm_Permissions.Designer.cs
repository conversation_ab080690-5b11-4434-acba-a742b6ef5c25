namespace SmartCreator.Forms.Security
{
    partial class Frm_Permissions
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle1 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle2 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle3 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle4 = new System.Windows.Forms.DataGridViewCellStyle();
            this.pnlMain = new System.Windows.Forms.Panel();
            this.splitContainer1 = new System.Windows.Forms.SplitContainer();
            this.pnlPermissions = new SmartCreator.RJControls.RJPanel();
            this.dgvPermissions = new SmartCreator.RJControls.RJDataGridView();
            this.pnlPermissionsHeader = new System.Windows.Forms.Panel();
            this.btnRefresh = new SmartCreator.RJControls.RJButton();
            this.btnDeletePermission = new SmartCreator.RJControls.RJButton();
            this.btnEditPermission = new SmartCreator.RJControls.RJButton();
            this.btnAddPermission = new SmartCreator.RJControls.RJButton();
            this.txtSearchPermissions = new SmartCreator.RJControls.RJTextBox();
            this.lblPermissionsTitle = new SmartCreator.RJControls.RJLabel();
            this.pnlCategories = new SmartCreator.RJControls.RJPanel();
            this.pnlCategoriesContent = new System.Windows.Forms.Panel();
            this.lblCategoryFilter = new SmartCreator.RJControls.RJLabel();
            this.cmbCategory = new SmartCreator.RJControls.RJComboBox();
            this.pnlCategoriesButtons = new System.Windows.Forms.Panel();
            this.btnDeleteCategory = new SmartCreator.RJControls.RJButton();
            this.btnEditCategory = new SmartCreator.RJControls.RJButton();
            this.btnAddCategory = new SmartCreator.RJControls.RJButton();
            this.lblCategoriesTitle = new SmartCreator.RJControls.RJLabel();
            this.pnlClientArea.SuspendLayout();
            this.pnlMain.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainer1)).BeginInit();
            this.splitContainer1.Panel1.SuspendLayout();
            this.splitContainer1.Panel2.SuspendLayout();
            this.splitContainer1.SuspendLayout();
            this.pnlPermissions.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dgvPermissions)).BeginInit();
            this.pnlPermissionsHeader.SuspendLayout();
            this.pnlCategories.SuspendLayout();
            this.pnlCategoriesContent.SuspendLayout();
            this.pnlCategoriesButtons.SuspendLayout();
            this.SuspendLayout();
            // 
            // pnlClientArea
            // 
            this.pnlClientArea.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.pnlClientArea.Controls.Add(this.pnlMain);
            this.pnlClientArea.Location = new System.Drawing.Point(5, 45);
            this.pnlClientArea.Size = new System.Drawing.Size(990, 700);
            // 
            // lblCaption
            // 
            this.lblCaption.Size = new System.Drawing.Size(83, 17);
            this.lblCaption.Text = "إدارة الصلاحيات";
            // 
            // pnlMain
            // 
            this.pnlMain.Controls.Add(this.splitContainer1);
            this.pnlMain.Dock = System.Windows.Forms.DockStyle.Fill;
            this.pnlMain.Location = new System.Drawing.Point(0, 0);
            this.pnlMain.Name = "pnlMain";
            this.pnlMain.Padding = new System.Windows.Forms.Padding(10);
            this.pnlMain.Size = new System.Drawing.Size(990, 700);
            this.pnlMain.TabIndex = 0;
            // 
            // splitContainer1
            // 
            this.splitContainer1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainer1.Location = new System.Drawing.Point(10, 10);
            this.splitContainer1.Name = "splitContainer1";
            this.splitContainer1.Orientation = System.Windows.Forms.Orientation.Horizontal;
            // 
            // splitContainer1.Panel1
            // 
            this.splitContainer1.Panel1.Controls.Add(this.pnlPermissions);
            // 
            // splitContainer1.Panel2
            // 
            this.splitContainer1.Panel2.Controls.Add(this.pnlCategories);
            this.splitContainer1.Size = new System.Drawing.Size(970, 680);
            this.splitContainer1.SplitterDistance = 450;
            this.splitContainer1.TabIndex = 0;
            // 
            // pnlPermissions
            // 
            this.pnlPermissions.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.pnlPermissions.BorderRadius = 10;
            this.pnlPermissions.Controls.Add(this.dgvPermissions);
            this.pnlPermissions.Controls.Add(this.pnlPermissionsHeader);
            this.pnlPermissions.Customizable = false;
            this.pnlPermissions.Dock = System.Windows.Forms.DockStyle.Fill;
            this.pnlPermissions.Location = new System.Drawing.Point(0, 0);
            this.pnlPermissions.Name = "pnlPermissions";
            this.pnlPermissions.Size = new System.Drawing.Size(970, 450);
            this.pnlPermissions.TabIndex = 0;
            // 
            // dgvPermissions
            // 
            this.dgvPermissions.AllowUserToAddRows = false;
            this.dgvPermissions.AllowUserToDeleteRows = false;
            this.dgvPermissions.AllowUserToResizeRows = false;
            this.dgvPermissions.AlternatingRowsColor = System.Drawing.Color.Empty;
            this.dgvPermissions.AlternatingRowsColorApply = false;
            this.dgvPermissions.AutoSizeColumnsMode = System.Windows.Forms.DataGridViewAutoSizeColumnsMode.Fill;
            this.dgvPermissions.BackgroundColor = System.Drawing.Color.White;
            this.dgvPermissions.BorderRadius = 13;
            this.dgvPermissions.BorderStyle = System.Windows.Forms.BorderStyle.None;
            this.dgvPermissions.CellBorderStyle = System.Windows.Forms.DataGridViewCellBorderStyle.SingleHorizontal;
            this.dgvPermissions.ColumnHeaderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.dgvPermissions.ColumnHeaderFont = new System.Drawing.Font("Segoe UI", 10F, System.Drawing.FontStyle.Bold);
            this.dgvPermissions.ColumnHeaderHeight = 23;
            this.dgvPermissions.ColumnHeadersBorderStyle = System.Windows.Forms.DataGridViewHeaderBorderStyle.None;
            dataGridViewCellStyle1.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle1.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            dataGridViewCellStyle1.Font = new System.Drawing.Font("Segoe UI", 10F, System.Drawing.FontStyle.Bold);
            dataGridViewCellStyle1.ForeColor = System.Drawing.Color.White;
            dataGridViewCellStyle1.SelectionBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            dataGridViewCellStyle1.WrapMode = System.Windows.Forms.DataGridViewTriState.True;
            this.dgvPermissions.ColumnHeadersDefaultCellStyle = dataGridViewCellStyle1;
            this.dgvPermissions.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.DisableResizing;
            this.dgvPermissions.ColumnHeaderTextColor = System.Drawing.Color.White;
            this.dgvPermissions.ColumnsMode = System.Windows.Forms.DataGridViewAutoSizeColumnsMode.Fill;
            this.dgvPermissions.Customizable = false;
            dataGridViewCellStyle2.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle2.BackColor = System.Drawing.Color.White;
            dataGridViewCellStyle2.Font = new System.Drawing.Font("Segoe UI", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            dataGridViewCellStyle2.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            dataGridViewCellStyle2.SelectionBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(229)))), ((int)(((byte)(226)))), ((int)(((byte)(244)))));
            dataGridViewCellStyle2.SelectionForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            dataGridViewCellStyle2.WrapMode = System.Windows.Forms.DataGridViewTriState.False;
            this.dgvPermissions.DefaultCellStyle = dataGridViewCellStyle2;
            this.dgvPermissions.DgvBackColor = System.Drawing.Color.White;
            this.dgvPermissions.Dock = System.Windows.Forms.DockStyle.Fill;
            this.dgvPermissions.EnableHeadersVisualStyles = false;
            this.dgvPermissions.GridColor = System.Drawing.Color.FromArgb(((int)(((byte)(229)))), ((int)(((byte)(226)))), ((int)(((byte)(244)))));
            this.dgvPermissions.Location = new System.Drawing.Point(0, 120);
            this.dgvPermissions.MultiSelect = false;
            this.dgvPermissions.Name = "dgvPermissions";
            this.dgvPermissions.ReadOnly = true;
            this.dgvPermissions.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.dgvPermissions.RowHeaderColor = System.Drawing.Color.WhiteSmoke;
            this.dgvPermissions.RowHeadersBorderStyle = System.Windows.Forms.DataGridViewHeaderBorderStyle.Single;
            dataGridViewCellStyle3.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle3.BackColor = System.Drawing.Color.WhiteSmoke;
            dataGridViewCellStyle3.Font = new System.Drawing.Font("Segoe UI", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            dataGridViewCellStyle3.ForeColor = System.Drawing.Color.White;
            dataGridViewCellStyle3.SelectionBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(213)))), ((int)(((byte)(199)))), ((int)(((byte)(241)))));
            dataGridViewCellStyle3.SelectionForeColor = System.Drawing.SystemColors.HighlightText;
            dataGridViewCellStyle3.WrapMode = System.Windows.Forms.DataGridViewTriState.False;
            this.dgvPermissions.RowHeadersDefaultCellStyle = dataGridViewCellStyle3;
            this.dgvPermissions.RowHeadersVisible = false;
            this.dgvPermissions.RowHeadersWidth = 30;
            this.dgvPermissions.RowHeadersWidthSizeMode = System.Windows.Forms.DataGridViewRowHeadersWidthSizeMode.DisableResizing;
            this.dgvPermissions.RowHeight = 35;
            this.dgvPermissions.RowsColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            dataGridViewCellStyle4.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle4.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            dataGridViewCellStyle4.Font = new System.Drawing.Font("Segoe UI", 10F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            dataGridViewCellStyle4.ForeColor = System.Drawing.Color.Gray;
            dataGridViewCellStyle4.SelectionBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(213)))), ((int)(((byte)(199)))), ((int)(((byte)(241)))));
            dataGridViewCellStyle4.SelectionForeColor = System.Drawing.Color.Gray;
            this.dgvPermissions.RowsDefaultCellStyle = dataGridViewCellStyle4;
            this.dgvPermissions.RowsTextColor = System.Drawing.Color.Gray;
            this.dgvPermissions.RowTemplate.Height = 35;
            this.dgvPermissions.SelectionBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(213)))), ((int)(((byte)(199)))), ((int)(((byte)(241)))));
            this.dgvPermissions.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect;
            this.dgvPermissions.SelectionTextColor = System.Drawing.Color.Gray;
            this.dgvPermissions.Size = new System.Drawing.Size(970, 330);
            this.dgvPermissions.TabIndex = 1;
            // 
            // pnlPermissionsHeader
            // 
            this.pnlPermissionsHeader.Controls.Add(this.btnRefresh);
            this.pnlPermissionsHeader.Controls.Add(this.btnDeletePermission);
            this.pnlPermissionsHeader.Controls.Add(this.btnEditPermission);
            this.pnlPermissionsHeader.Controls.Add(this.btnAddPermission);
            this.pnlPermissionsHeader.Controls.Add(this.txtSearchPermissions);
            this.pnlPermissionsHeader.Controls.Add(this.lblPermissionsTitle);
            this.pnlPermissionsHeader.Dock = System.Windows.Forms.DockStyle.Top;
            this.pnlPermissionsHeader.Location = new System.Drawing.Point(0, 0);
            this.pnlPermissionsHeader.Name = "pnlPermissionsHeader";
            this.pnlPermissionsHeader.Size = new System.Drawing.Size(970, 120);
            this.pnlPermissionsHeader.TabIndex = 0;
            // 
            // btnRefresh
            // 
            this.btnRefresh.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btnRefresh.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btnRefresh.BorderRadius = 5;
            this.btnRefresh.BorderSize = 0;
            this.btnRefresh.Design = SmartCreator.RJControls.ButtonDesign.Normal;
            this.btnRefresh.FlatAppearance.BorderSize = 0;
            this.btnRefresh.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(115)))), ((int)(((byte)(97)))), ((int)(((byte)(223)))));
            this.btnRefresh.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(108)))), ((int)(((byte)(91)))), ((int)(((byte)(209)))));
            this.btnRefresh.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnRefresh.Font = new System.Drawing.Font("Segoe UI", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnRefresh.ForeColor = System.Drawing.Color.White;
            this.btnRefresh.IconChar = FontAwesome.Sharp.IconChar.None;
            this.btnRefresh.IconColor = System.Drawing.Color.White;
            this.btnRefresh.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btnRefresh.IconSize = 24;
            this.btnRefresh.Location = new System.Drawing.Point(1090, 80);
            this.btnRefresh.Name = "btnRefresh";
            this.btnRefresh.Size = new System.Drawing.Size(80, 30);
            this.btnRefresh.Style = SmartCreator.RJControls.ControlStyle.Solid;
            this.btnRefresh.TabIndex = 5;
            this.btnRefresh.Text = "🔄 تحديث";
            this.btnRefresh.UseVisualStyleBackColor = false;
            // 
            // btnDeletePermission
            // 
            this.btnDeletePermission.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btnDeletePermission.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btnDeletePermission.BorderRadius = 5;
            this.btnDeletePermission.BorderSize = 0;
            this.btnDeletePermission.Design = SmartCreator.RJControls.ButtonDesign.Normal;
            this.btnDeletePermission.FlatAppearance.BorderSize = 0;
            this.btnDeletePermission.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(115)))), ((int)(((byte)(97)))), ((int)(((byte)(223)))));
            this.btnDeletePermission.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(108)))), ((int)(((byte)(91)))), ((int)(((byte)(209)))));
            this.btnDeletePermission.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnDeletePermission.Font = new System.Drawing.Font("Segoe UI", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnDeletePermission.ForeColor = System.Drawing.Color.White;
            this.btnDeletePermission.IconChar = FontAwesome.Sharp.IconChar.None;
            this.btnDeletePermission.IconColor = System.Drawing.Color.White;
            this.btnDeletePermission.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btnDeletePermission.IconSize = 24;
            this.btnDeletePermission.Location = new System.Drawing.Point(1000, 80);
            this.btnDeletePermission.Name = "btnDeletePermission";
            this.btnDeletePermission.Size = new System.Drawing.Size(80, 30);
            this.btnDeletePermission.Style = SmartCreator.RJControls.ControlStyle.Solid;
            this.btnDeletePermission.TabIndex = 4;
            this.btnDeletePermission.Text = "🗑️ حذف";
            this.btnDeletePermission.UseVisualStyleBackColor = false;
            // 
            // btnEditPermission
            // 
            this.btnEditPermission.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnEditPermission.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btnEditPermission.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btnEditPermission.BorderRadius = 5;
            this.btnEditPermission.BorderSize = 0;
            this.btnEditPermission.Design = SmartCreator.RJControls.ButtonDesign.Normal;
            this.btnEditPermission.FlatAppearance.BorderSize = 0;
            this.btnEditPermission.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(115)))), ((int)(((byte)(97)))), ((int)(((byte)(223)))));
            this.btnEditPermission.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(108)))), ((int)(((byte)(91)))), ((int)(((byte)(209)))));
            this.btnEditPermission.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnEditPermission.Font = new System.Drawing.Font("Droid Arabic Kufi", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnEditPermission.ForeColor = System.Drawing.Color.White;
            this.btnEditPermission.IconChar = FontAwesome.Sharp.IconChar.None;
            this.btnEditPermission.IconColor = System.Drawing.Color.White;
            this.btnEditPermission.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btnEditPermission.IconSize = 24;
            this.btnEditPermission.Location = new System.Drawing.Point(842, 78);
            this.btnEditPermission.Name = "btnEditPermission";
            this.btnEditPermission.Size = new System.Drawing.Size(91, 30);
            this.btnEditPermission.Style = SmartCreator.RJControls.ControlStyle.Solid;
            this.btnEditPermission.TabIndex = 3;
            this.btnEditPermission.Text = "✏️ تعديل";
            this.btnEditPermission.UseVisualStyleBackColor = false;
            // 
            // btnAddPermission
            // 
            this.btnAddPermission.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnAddPermission.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btnAddPermission.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btnAddPermission.BorderRadius = 5;
            this.btnAddPermission.BorderSize = 0;
            this.btnAddPermission.Design = SmartCreator.RJControls.ButtonDesign.Normal;
            this.btnAddPermission.FlatAppearance.BorderSize = 0;
            this.btnAddPermission.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(115)))), ((int)(((byte)(97)))), ((int)(((byte)(223)))));
            this.btnAddPermission.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(108)))), ((int)(((byte)(91)))), ((int)(((byte)(209)))));
            this.btnAddPermission.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnAddPermission.Font = new System.Drawing.Font("Droid Arabic Kufi", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnAddPermission.ForeColor = System.Drawing.Color.White;
            this.btnAddPermission.IconChar = FontAwesome.Sharp.IconChar.None;
            this.btnAddPermission.IconColor = System.Drawing.Color.White;
            this.btnAddPermission.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btnAddPermission.IconSize = 24;
            this.btnAddPermission.Location = new System.Drawing.Point(748, 78);
            this.btnAddPermission.Name = "btnAddPermission";
            this.btnAddPermission.Size = new System.Drawing.Size(91, 30);
            this.btnAddPermission.Style = SmartCreator.RJControls.ControlStyle.Solid;
            this.btnAddPermission.TabIndex = 2;
            this.btnAddPermission.Text = "➕ إضافة";
            this.btnAddPermission.UseVisualStyleBackColor = false;
            // 
            // txtSearchPermissions
            // 
            this.txtSearchPermissions._Customizable = false;
            this.txtSearchPermissions.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.txtSearchPermissions.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txtSearchPermissions.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txtSearchPermissions.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txtSearchPermissions.BorderRadius = 5;
            this.txtSearchPermissions.BorderSize = 1;
            this.txtSearchPermissions.Font = new System.Drawing.Font("Segoe UI", 9.5F);
            this.txtSearchPermissions.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txtSearchPermissions.Location = new System.Drawing.Point(13, 80);
            this.txtSearchPermissions.Margin = new System.Windows.Forms.Padding(4);
            this.txtSearchPermissions.MultiLine = false;
            this.txtSearchPermissions.Name = "txtSearchPermissions";
            this.txtSearchPermissions.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txtSearchPermissions.PasswordChar = false;
            this.txtSearchPermissions.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txtSearchPermissions.PlaceHolderText = "البحث في الصلاحيات...";
            this.txtSearchPermissions.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txtSearchPermissions.Size = new System.Drawing.Size(728, 28);
            this.txtSearchPermissions.Style = SmartCreator.RJControls.TextBoxStyle.MatteBorder;
            this.txtSearchPermissions.TabIndex = 1;
            this.txtSearchPermissions.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            // 
            // lblPermissionsTitle
            // 
            this.lblPermissionsTitle.AutoSize = true;
            this.lblPermissionsTitle.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.lblPermissionsTitle.Font = new System.Drawing.Font("Droid Arabic Kufi", 12F, System.Drawing.FontStyle.Bold);
            this.lblPermissionsTitle.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.lblPermissionsTitle.LinkLabel = false;
            this.lblPermissionsTitle.Location = new System.Drawing.Point(419, 10);
            this.lblPermissionsTitle.Name = "lblPermissionsTitle";
            this.lblPermissionsTitle.Size = new System.Drawing.Size(167, 31);
            this.lblPermissionsTitle.Style = SmartCreator.RJControls.LabelStyle.Title;
            this.lblPermissionsTitle.TabIndex = 0;
            this.lblPermissionsTitle.Text = "🔐 إدارة الصلاحيات";
            // 
            // pnlCategories
            // 
            this.pnlCategories.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.pnlCategories.BorderRadius = 10;
            this.pnlCategories.Controls.Add(this.pnlCategoriesContent);
            this.pnlCategories.Controls.Add(this.pnlCategoriesButtons);
            this.pnlCategories.Controls.Add(this.lblCategoriesTitle);
            this.pnlCategories.Customizable = false;
            this.pnlCategories.Dock = System.Windows.Forms.DockStyle.Fill;
            this.pnlCategories.Location = new System.Drawing.Point(0, 0);
            this.pnlCategories.Name = "pnlCategories";
            this.pnlCategories.Size = new System.Drawing.Size(970, 226);
            this.pnlCategories.TabIndex = 0;
            // 
            // pnlCategoriesContent
            // 
            this.pnlCategoriesContent.Controls.Add(this.lblCategoryFilter);
            this.pnlCategoriesContent.Controls.Add(this.cmbCategory);
            this.pnlCategoriesContent.Dock = System.Windows.Forms.DockStyle.Fill;
            this.pnlCategoriesContent.Location = new System.Drawing.Point(0, 42);
            this.pnlCategoriesContent.Name = "pnlCategoriesContent";
            this.pnlCategoriesContent.Size = new System.Drawing.Size(970, 144);
            this.pnlCategoriesContent.TabIndex = 2;
            // 
            // lblCategoryFilter
            // 
            this.lblCategoryFilter.AutoSize = true;
            this.lblCategoryFilter.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.lblCategoryFilter.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.lblCategoryFilter.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.lblCategoryFilter.LinkLabel = false;
            this.lblCategoryFilter.Location = new System.Drawing.Point(10, 20);
            this.lblCategoryFilter.Name = "lblCategoryFilter";
            this.lblCategoryFilter.Size = new System.Drawing.Size(95, 17);
            this.lblCategoryFilter.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.lblCategoryFilter.TabIndex = 0;
            this.lblCategoryFilter.Text = "فلترة حسب الفئة:";
            // 
            // cmbCategory
            // 
            this.cmbCategory.AutoCompleteMode = System.Windows.Forms.AutoCompleteMode.None;
            this.cmbCategory.AutoCompleteSource = System.Windows.Forms.AutoCompleteSource.None;
            this.cmbCategory.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.cmbCategory.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.cmbCategory.BorderRadius = 0;
            this.cmbCategory.BorderSize = 1;
            this.cmbCategory.Customizable = false;
            this.cmbCategory.DataSource = null;
            this.cmbCategory.DropDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.cmbCategory.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cmbCategory.DropDownTextColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.cmbCategory.Font = new System.Drawing.Font("Segoe UI", 9F);
            this.cmbCategory.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.cmbCategory.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.cmbCategory.Location = new System.Drawing.Point(10, 50);
            this.cmbCategory.MinimumSize = new System.Drawing.Size(200, 30);
            this.cmbCategory.Name = "cmbCategory";
            this.cmbCategory.Padding = new System.Windows.Forms.Padding(1);
            this.cmbCategory.SelectedIndex = -1;
            this.cmbCategory.SelectedItem = null;
            this.cmbCategory.SelectedValue = null;
            this.cmbCategory.Size = new System.Drawing.Size(300, 30);
            this.cmbCategory.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.cmbCategory.TabIndex = 1;
            this.cmbCategory.Texts = "";
            // 
            // pnlCategoriesButtons
            // 
            this.pnlCategoriesButtons.Controls.Add(this.btnDeleteCategory);
            this.pnlCategoriesButtons.Controls.Add(this.btnEditCategory);
            this.pnlCategoriesButtons.Controls.Add(this.btnAddCategory);
            this.pnlCategoriesButtons.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.pnlCategoriesButtons.Location = new System.Drawing.Point(0, 186);
            this.pnlCategoriesButtons.Name = "pnlCategoriesButtons";
            this.pnlCategoriesButtons.Size = new System.Drawing.Size(970, 40);
            this.pnlCategoriesButtons.TabIndex = 1;
            // 
            // btnDeleteCategory
            // 
            this.btnDeleteCategory.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btnDeleteCategory.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btnDeleteCategory.BorderRadius = 5;
            this.btnDeleteCategory.BorderSize = 0;
            this.btnDeleteCategory.Design = SmartCreator.RJControls.ButtonDesign.Normal;
            this.btnDeleteCategory.FlatAppearance.BorderSize = 0;
            this.btnDeleteCategory.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(115)))), ((int)(((byte)(97)))), ((int)(((byte)(223)))));
            this.btnDeleteCategory.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(108)))), ((int)(((byte)(91)))), ((int)(((byte)(209)))));
            this.btnDeleteCategory.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnDeleteCategory.Font = new System.Drawing.Font("Droid Arabic Kufi", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnDeleteCategory.ForeColor = System.Drawing.Color.White;
            this.btnDeleteCategory.IconChar = FontAwesome.Sharp.IconChar.None;
            this.btnDeleteCategory.IconColor = System.Drawing.Color.White;
            this.btnDeleteCategory.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btnDeleteCategory.IconSize = 24;
            this.btnDeleteCategory.Location = new System.Drawing.Point(200, 5);
            this.btnDeleteCategory.Name = "btnDeleteCategory";
            this.btnDeleteCategory.Size = new System.Drawing.Size(90, 30);
            this.btnDeleteCategory.Style = SmartCreator.RJControls.ControlStyle.Solid;
            this.btnDeleteCategory.TabIndex = 2;
            this.btnDeleteCategory.Text = "🗑️ حذف فئة";
            this.btnDeleteCategory.UseVisualStyleBackColor = false;
            // 
            // btnEditCategory
            // 
            this.btnEditCategory.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btnEditCategory.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btnEditCategory.BorderRadius = 5;
            this.btnEditCategory.BorderSize = 0;
            this.btnEditCategory.Design = SmartCreator.RJControls.ButtonDesign.Normal;
            this.btnEditCategory.FlatAppearance.BorderSize = 0;
            this.btnEditCategory.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(115)))), ((int)(((byte)(97)))), ((int)(((byte)(223)))));
            this.btnEditCategory.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(108)))), ((int)(((byte)(91)))), ((int)(((byte)(209)))));
            this.btnEditCategory.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnEditCategory.Font = new System.Drawing.Font("Droid Arabic Kufi", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnEditCategory.ForeColor = System.Drawing.Color.White;
            this.btnEditCategory.IconChar = FontAwesome.Sharp.IconChar.None;
            this.btnEditCategory.IconColor = System.Drawing.Color.White;
            this.btnEditCategory.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btnEditCategory.IconSize = 24;
            this.btnEditCategory.Location = new System.Drawing.Point(105, 5);
            this.btnEditCategory.Name = "btnEditCategory";
            this.btnEditCategory.Size = new System.Drawing.Size(90, 30);
            this.btnEditCategory.Style = SmartCreator.RJControls.ControlStyle.Solid;
            this.btnEditCategory.TabIndex = 1;
            this.btnEditCategory.Text = "✏️ تعديل فئة";
            this.btnEditCategory.UseVisualStyleBackColor = false;
            // 
            // btnAddCategory
            // 
            this.btnAddCategory.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btnAddCategory.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btnAddCategory.BorderRadius = 5;
            this.btnAddCategory.BorderSize = 0;
            this.btnAddCategory.Design = SmartCreator.RJControls.ButtonDesign.Normal;
            this.btnAddCategory.FlatAppearance.BorderSize = 0;
            this.btnAddCategory.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(115)))), ((int)(((byte)(97)))), ((int)(((byte)(223)))));
            this.btnAddCategory.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(108)))), ((int)(((byte)(91)))), ((int)(((byte)(209)))));
            this.btnAddCategory.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnAddCategory.Font = new System.Drawing.Font("Droid Arabic Kufi", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnAddCategory.ForeColor = System.Drawing.Color.White;
            this.btnAddCategory.IconChar = FontAwesome.Sharp.IconChar.None;
            this.btnAddCategory.IconColor = System.Drawing.Color.White;
            this.btnAddCategory.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btnAddCategory.IconSize = 24;
            this.btnAddCategory.Location = new System.Drawing.Point(10, 5);
            this.btnAddCategory.Name = "btnAddCategory";
            this.btnAddCategory.Size = new System.Drawing.Size(90, 30);
            this.btnAddCategory.Style = SmartCreator.RJControls.ControlStyle.Solid;
            this.btnAddCategory.TabIndex = 0;
            this.btnAddCategory.Text = "➕ إضافة فئة";
            this.btnAddCategory.UseVisualStyleBackColor = false;
            // 
            // lblCategoriesTitle
            // 
            this.lblCategoriesTitle.AutoSize = true;
            this.lblCategoriesTitle.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.lblCategoriesTitle.Dock = System.Windows.Forms.DockStyle.Top;
            this.lblCategoriesTitle.Font = new System.Drawing.Font("Droid Arabic Kufi", 9F, System.Drawing.FontStyle.Bold);
            this.lblCategoriesTitle.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.lblCategoriesTitle.LinkLabel = false;
            this.lblCategoriesTitle.Location = new System.Drawing.Point(0, 0);
            this.lblCategoriesTitle.Name = "lblCategoriesTitle";
            this.lblCategoriesTitle.Padding = new System.Windows.Forms.Padding(10);
            this.lblCategoriesTitle.Size = new System.Drawing.Size(140, 42);
            this.lblCategoriesTitle.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.lblCategoriesTitle.TabIndex = 0;
            this.lblCategoriesTitle.Text = "📂 فئات الصلاحيات";
            // 
            // Frm_Permissions
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 15F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(111)))), ((int)(((byte)(106)))), ((int)(((byte)(143)))));
            this.BorderSize = 5;
            this.Caption = "إدارة الصلاحيات";
            this.ClientSize = new System.Drawing.Size(1000, 750);
            this.Location = new System.Drawing.Point(0, 0);
            this.Name = "Frm_Permissions";
            this.Padding = new System.Windows.Forms.Padding(5);
            this.Text = "إدارة الصلاحيات";
            this.pnlClientArea.ResumeLayout(false);
            this.pnlMain.ResumeLayout(false);
            this.splitContainer1.Panel1.ResumeLayout(false);
            this.splitContainer1.Panel2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.splitContainer1)).EndInit();
            this.splitContainer1.ResumeLayout(false);
            this.pnlPermissions.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.dgvPermissions)).EndInit();
            this.pnlPermissionsHeader.ResumeLayout(false);
            this.pnlPermissionsHeader.PerformLayout();
            this.pnlCategories.ResumeLayout(false);
            this.pnlCategories.PerformLayout();
            this.pnlCategoriesContent.ResumeLayout(false);
            this.pnlCategoriesContent.PerformLayout();
            this.pnlCategoriesButtons.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.Panel pnlMain;
        private System.Windows.Forms.SplitContainer splitContainer1;
        private SmartCreator.RJControls.RJPanel pnlPermissions;
        private SmartCreator.RJControls.RJDataGridView dgvPermissions;
        private System.Windows.Forms.Panel pnlPermissionsHeader;
        private SmartCreator.RJControls.RJButton btnRefresh;
        private SmartCreator.RJControls.RJButton btnDeletePermission;
        private SmartCreator.RJControls.RJButton btnEditPermission;
        private SmartCreator.RJControls.RJButton btnAddPermission;
        private SmartCreator.RJControls.RJTextBox txtSearchPermissions;
        private SmartCreator.RJControls.RJLabel lblPermissionsTitle;
        private SmartCreator.RJControls.RJPanel pnlCategories;
        private System.Windows.Forms.Panel pnlCategoriesContent;
        private SmartCreator.RJControls.RJLabel lblCategoryFilter;
        private SmartCreator.RJControls.RJComboBox cmbCategory;
        private System.Windows.Forms.Panel pnlCategoriesButtons;
        private SmartCreator.RJControls.RJButton btnDeleteCategory;
        private SmartCreator.RJControls.RJButton btnEditCategory;
        private SmartCreator.RJControls.RJButton btnAddCategory;
        private SmartCreator.RJControls.RJLabel lblCategoriesTitle;
    }
}