using SmartCreator.Entities.Accounting;
using SmartCreator.RJControls;
using SmartCreator.RJForms;
using SmartCreator.Services.Products;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace SmartCreator.Forms._Accounting.Product
{
    public partial class Frm_ProductEdit : RJChildForm
    {
        private readonly ProductService _productService;
        private readonly int _productId;
        private Entities.Accounting.Product _originalProduct;
        private Entities.Accounting.Product _currentProduct;

        public Frm_ProductEdit(int productId)
        {
            InitializeComponent();
            _productService = new ProductService();
            _productId = productId;
            InitializeForm();
        }

        private void InitializeForm()
        {
            this.Text = "تعديل منتج";
            this.Size = new Size(800, 600);
            this.StartPosition = FormStartPosition.CenterParent;
            
            // تهيئة الأحداث
            this.Load += Frm_ProductEdit_Load;
            
            // تهيئة أحداث الأزرار
            btnSave.Click += BtnSave_Click;
            btnCancel.Click += BtnCancel_Click;
            btnReset.Click += BtnReset_Click;
            
            // تهيئة أحداث التحقق
            txtName.Leave += TxtName_Leave;
            txtPrice.Leave += TxtPrice_Leave;
            txtStock.Leave += TxtStock_Leave;
            txtLowStockThreshold.Leave += TxtLowStockThreshold_Leave;
        }

        private async void Frm_ProductEdit_Load(object sender, EventArgs e)
        {
            await LoadProductAsync();
        }

        private async Task LoadProductAsync()
        {
            try
            {
                ShowLoading(true);
                
                _originalProduct = await _productService.GetProductByIdAsync(_productId);
                if (_originalProduct == null)
                {
                    RJMessageBox.Show("المنتج غير موجود", "خطأ",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                    this.DialogResult = DialogResult.Cancel;
                    this.Close();
                    return;
                }

                _currentProduct = CloneProduct(_originalProduct);
                LoadProductToForm(_currentProduct);
            }
            catch (Exception ex)
            {
                RJMessageBox.Show($"خطأ في تحميل بيانات المنتج: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                this.DialogResult = DialogResult.Cancel;
                this.Close();
            }
            finally
            {
                ShowLoading(false);
            }
        }

        private void LoadProductToForm(Entities.Accounting.Product product)
        {
            txtCode.Texts = product.Code ?? "";
            txtName.Texts = product.Name ?? "";
            txtDescription.Texts = product.Description ?? "";
            txtPrice.Texts = product.Price.ToString("F2");
            txtStock.Texts = product.Stock.ToString();
            txtLowStockThreshold.Texts = product.LowStockThreshold.ToString();
            chkActive.Checked = product.Str_Active;
            
            // عرض معلومات إضافية
            lblLastUpdate.Text = $"آخر تحديث: {product.LastStockUpdate?.ToString("yyyy/MM/dd HH:mm") ?? "غير محدد"}";
            lblLastUpdateBy.Text = $"بواسطة: {product.LastUpdateBy ?? "غير محدد"}";
            lblStockStatus.Text = $"حالة المخزون: {product.StockStatus}";
            
            // تعطيل تعديل الكود
            txtCode.Enabled = false;
        }

        private Entities.Accounting.Product CloneProduct(Entities.Accounting.Product original)
        {
            return new Entities.Accounting.Product
            {
                Id = original.Id,
                Code = original.Code,
                Name = original.Name,
                Description = original.Description,
                Price = original.Price,
                Stock = original.Stock,
                LowStockThreshold = original.LowStockThreshold,
                Account_IncomeId = original.Account_IncomeId,
                Account_ExpenseId = original.Account_ExpenseId,
                Product_UomId = original.Product_UomId,
                Active = original.Active,
                Rb = original.Rb,
                LastStockUpdate = original.LastStockUpdate,
                LastUpdateBy = original.LastUpdateBy,
                StockUpdateReason = original.StockUpdateReason
            };
        }

        private void ShowLoading(bool show)
        {
            if (show)
            {
                Cursor = Cursors.WaitCursor;
                pnlForm.Enabled = false;
                pnlButtons.Enabled = false;
            }
            else
            {
                Cursor = Cursors.Default;
                pnlForm.Enabled = true;
                pnlButtons.Enabled = true;
            }
        }

        private async void BtnSave_Click(object sender, EventArgs e)
        {
            if (!ValidateInput())
                return;

            try
            {
                UpdateProductFromForm();
                
                btnSave.Enabled = false;
                btnSave.Text = "جاري الحفظ...";
                
                bool success = await _productService.UpdateProductAsync(_currentProduct, _originalProduct);
                
                if (success)
                {
                    this.DialogResult = DialogResult.OK;
                    this.Close();
                }
            }
            catch (Exception ex)
            {
                RJMessageBox.Show($"خطأ في حفظ التعديلات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                btnSave.Enabled = true;
                btnSave.Text = "حفظ";
            }
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            if (HasChanges())
            {
                var result = RJMessageBox.Show("هل تريد إلغاء التعديلات؟", "تأكيد",
                    MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                
                if (result == DialogResult.No)
                    return;
            }
            
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        private void BtnReset_Click(object sender, EventArgs e)
        {
            if (HasChanges())
            {
                var result = RJMessageBox.Show("هل تريد إعادة تعيين البيانات للقيم الأصلية؟", "تأكيد",
                    MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                
                if (result == DialogResult.Yes)
                {
                    _currentProduct = CloneProduct(_originalProduct);
                    LoadProductToForm(_currentProduct);
                }
            }
        }

        private bool HasChanges()
        {
            if (_originalProduct == null || _currentProduct == null)
                return false;

            return _originalProduct.Name != txtName.Texts.Trim() ||
                   _originalProduct.Description != txtDescription.Texts.Trim() ||
                   _originalProduct.Price != decimal.Parse(txtPrice.Texts) ||
                   _originalProduct.Stock != int.Parse(txtStock.Texts) ||
                   _originalProduct.LowStockThreshold != int.Parse(txtLowStockThreshold.Texts) ||
                   _originalProduct.Str_Active != chkActive.Checked;
        }

        private bool ValidateInput()
        {
            // التحقق من الاسم
            if (string.IsNullOrWhiteSpace(txtName.Texts))
            {
                RJMessageBox.Show("يجب إدخال اسم المنتج", "تحذير",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtName.Focus();
                return false;
            }

            // التحقق من السعر
            if (!decimal.TryParse(txtPrice.Texts, out decimal price) || price < 0)
            {
                RJMessageBox.Show("يجب إدخال سعر صحيح", "تحذير",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtPrice.Focus();
                return false;
            }

            // التحقق من المخزون
            if (!int.TryParse(txtStock.Texts, out int stock) || stock < 0)
            {
                RJMessageBox.Show("يجب إدخال كمية مخزون صحيحة", "تحذير",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtStock.Focus();
                return false;
            }

            // التحقق من الحد الأدنى للمخزون
            if (!int.TryParse(txtLowStockThreshold.Texts, out int threshold) || threshold < 0)
            {
                RJMessageBox.Show("يجب إدخال حد أدنى صحيح للمخزون", "تحذير",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtLowStockThreshold.Focus();
                return false;
            }

            return true;
        }

        private void UpdateProductFromForm()
        {
            _currentProduct.Name = txtName.Texts.Trim();
            _currentProduct.Description = txtDescription.Texts.Trim();
            _currentProduct.Price = decimal.Parse(txtPrice.Texts);
            _currentProduct.Stock = int.Parse(txtStock.Texts);
            _currentProduct.LowStockThreshold = int.Parse(txtLowStockThreshold.Texts);
            _currentProduct.Active = chkActive.Checked ? 1 : 0;
        }

        private void TxtName_Leave(object sender, EventArgs e)
        {
            if (!string.IsNullOrWhiteSpace(txtName.Texts))
            {
                txtName.Texts = txtName.Texts.Trim();
            }
        }

        private void TxtPrice_Leave(object sender, EventArgs e)
        {
            if (decimal.TryParse(txtPrice.Texts, out decimal price))
            {
                txtPrice.Texts = price.ToString("F2");
            }
        }

        private void TxtStock_Leave(object sender, EventArgs e)
        {
            if (int.TryParse(txtStock.Texts, out int stock))
            {
                txtStock.Texts = stock.ToString();
                
                // تحديث حالة المخزون
                if (_currentProduct != null)
                {
                    _currentProduct.Stock = stock;
                    lblStockStatus.Text = $"حالة المخزون: {_currentProduct.StockStatus}";
                    
                    if (_currentProduct.NeedsReorder)
                    {
                        lblStockStatus.ForeColor = Color.Orange;
                    }
                    else
                    {
                        lblStockStatus.ForeColor = Color.LightGreen;
                    }
                }
            }
        }

        private void TxtLowStockThreshold_Leave(object sender, EventArgs e)
        {
            if (int.TryParse(txtLowStockThreshold.Texts, out int threshold))
            {
                txtLowStockThreshold.Texts = threshold.ToString();
                
                // تحديث حالة المخزون
                if (_currentProduct != null)
                {
                    _currentProduct.LowStockThreshold = threshold;
                    lblStockStatus.Text = $"حالة المخزون: {_currentProduct.StockStatus}";
                    
                    if (_currentProduct.NeedsReorder)
                    {
                        lblStockStatus.ForeColor = Color.Orange;
                    }
                    else
                    {
                        lblStockStatus.ForeColor = Color.LightGreen;
                    }
                }
            }
        }
    }
}
