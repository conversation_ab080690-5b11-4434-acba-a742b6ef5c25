using System;
using System.Windows.Forms;

namespace SmartCreator.Forms.Security
{
    /// <summary>
    /// ملف اختبار لـ Frm_UserManagement
    /// </summary>
    public static class TestFrm_UserManagement
    {
        /// <summary>
        /// اختبار إنشاء النموذج
        /// </summary>
        public static void TestFormCreation()
        {
            try
            {
                Console.WriteLine("🧪 اختبار إنشاء Frm_UserManagement...");

                // محاولة إنشاء النموذج
                var form = new Frm_UserManagement();
                Console.WriteLine("   ✅ تم إنشاء النموذج بنجاح");

                // اختبار الخصائص الأساسية
                Console.WriteLine($"   ✅ عنوان النموذج: {form.Text}");
                Console.WriteLine($"   ✅ حجم النموذج: {form.Size}");
                Console.WriteLine($"   ✅ حالة النموذج: {form.WindowState}");

                // اختبار العناصر الأساسية
                if (form.Controls.Count > 0)
                {
                    Console.WriteLine($"   ✅ عدد العناصر الرئيسية: {form.Controls.Count}");
                }

                // إغلاق النموذج
                form.Dispose();
                Console.WriteLine("   ✅ تم إغلاق النموذج بنجاح");

                Console.WriteLine("✅ اختبار إنشاء النموذج نجح!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في اختبار إنشاء النموذج: {ex.Message}");
                Console.WriteLine($"تفاصيل الخطأ: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// اختبار عرض النموذج
        /// </summary>
        public static void TestFormDisplay()
        {
            try
            {
                Console.WriteLine("🖥️ اختبار عرض Frm_UserManagement...");

                // إنشاء النموذج
                var form = new Frm_UserManagement();
                Console.WriteLine("   ✅ تم إنشاء النموذج");

                // عرض النموذج لفترة قصيرة
                form.Show();
                Console.WriteLine("   ✅ تم عرض النموذج");

                // انتظار قصير
                Application.DoEvents();
                System.Threading.Thread.Sleep(1000);

                // إخفاء النموذج
                form.Hide();
                Console.WriteLine("   ✅ تم إخفاء النموذج");

                // إغلاق النموذج
                form.Close();
                form.Dispose();
                Console.WriteLine("   ✅ تم إغلاق النموذج");

                Console.WriteLine("✅ اختبار عرض النموذج نجح!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في اختبار عرض النموذج: {ex.Message}");
                Console.WriteLine($"تفاصيل الخطأ: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// اختبار العناصر الداخلية
        /// </summary>
        public static void TestFormControls()
        {
            try
            {
                Console.WriteLine("🔧 اختبار العناصر الداخلية...");

                var form = new Frm_UserManagement();
                Console.WriteLine("   ✅ تم إنشاء النموذج");

                // البحث عن العناصر الرئيسية
                var foundControls = 0;

                // البحث في جميع العناصر
                foreach (Control control in form.Controls)
                {
                    Console.WriteLine($"   📋 عنصر: {control.Name} - النوع: {control.GetType().Name}");
                    foundControls++;

                    // البحث في العناصر الفرعية
                    SearchChildControls(control, 1);
                }

                Console.WriteLine($"   ✅ تم العثور على {foundControls} عنصر رئيسي");

                form.Dispose();
                Console.WriteLine("✅ اختبار العناصر الداخلية نجح!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في اختبار العناصر الداخلية: {ex.Message}");
                Console.WriteLine($"تفاصيل الخطأ: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// البحث في العناصر الفرعية
        /// </summary>
        private static void SearchChildControls(Control parent, int level)
        {
            if (level > 3) return; // تحديد عمق البحث

            var indent = new string(' ', level * 2);
            foreach (Control child in parent.Controls)
            {
                Console.WriteLine($"   {indent}└─ {child.Name} - {child.GetType().Name}");
                
                if (child.Controls.Count > 0)
                {
                    SearchChildControls(child, level + 1);
                }
            }
        }

        /// <summary>
        /// اختبار الأحداث الأساسية
        /// </summary>
        public static void TestFormEvents()
        {
            try
            {
                Console.WriteLine("⚡ اختبار الأحداث الأساسية...");

                var form = new Frm_UserManagement();
                Console.WriteLine("   ✅ تم إنشاء النموذج");

                // اختبار حدث Load
                bool loadEventFired = false;
                form.Load += (s, e) => {
                    loadEventFired = true;
                    Console.WriteLine("   ✅ تم تشغيل حدث Load");
                };

                // اختبار حدث Shown
                bool shownEventFired = false;
                form.Shown += (s, e) => {
                    shownEventFired = true;
                    Console.WriteLine("   ✅ تم تشغيل حدث Shown");
                };

                // عرض النموذج
                form.Show();
                Application.DoEvents();

                // التحقق من الأحداث
                if (loadEventFired)
                    Console.WriteLine("   ✅ حدث Load تم تشغيله بنجاح");
                else
                    Console.WriteLine("   ⚠️ حدث Load لم يتم تشغيله");

                if (shownEventFired)
                    Console.WriteLine("   ✅ حدث Shown تم تشغيله بنجاح");
                else
                    Console.WriteLine("   ⚠️ حدث Shown لم يتم تشغيله");

                form.Close();
                form.Dispose();
                Console.WriteLine("✅ اختبار الأحداث الأساسية انتهى!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في اختبار الأحداث: {ex.Message}");
                Console.WriteLine($"تفاصيل الخطأ: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// اختبار شامل
        /// </summary>
        public static void RunAllTests()
        {
            try
            {
                Console.WriteLine("🚀 تشغيل جميع اختبارات Frm_UserManagement...\n");

                TestFormCreation();
                Console.WriteLine();

                TestFormControls();
                Console.WriteLine();

                TestFormDisplay();
                Console.WriteLine();

                TestFormEvents();
                Console.WriteLine();

                Console.WriteLine("🏆 انتهت جميع اختبارات Frm_UserManagement!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في تشغيل الاختبارات: {ex.Message}");
                Console.WriteLine($"تفاصيل الخطأ: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// اختبار سريع
        /// </summary>
        public static void QuickTest()
        {
            try
            {
                Console.WriteLine("⚡ اختبار سريع لـ Frm_UserManagement...");

                var form = new Frm_UserManagement();
                Console.WriteLine("✅ تم إنشاء النموذج بنجاح");

                form.Show();
                Application.DoEvents();
                Console.WriteLine("✅ تم عرض النموذج بنجاح");

                System.Threading.Thread.Sleep(500);

                form.Close();
                form.Dispose();
                Console.WriteLine("✅ تم إغلاق النموذج بنجاح");

                Console.WriteLine("✅ الاختبار السريع نجح - النموذج يعمل!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في الاختبار السريع: {ex.Message}");
                Console.WriteLine($"تفاصيل الخطأ: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// اختبار الأخطاء المحتملة
        /// </summary>
        public static void TestPotentialErrors()
        {
            try
            {
                Console.WriteLine("🔍 اختبار الأخطاء المحتملة...");

                // اختبار إنشاء متعدد
                Console.WriteLine("   📋 اختبار إنشاء متعدد للنموذج...");
                for (int i = 0; i < 3; i++)
                {
                    var form = new Frm_UserManagement();
                    Console.WriteLine($"   ✅ النموذج {i + 1} تم إنشاؤه");
                    form.Dispose();
                    Console.WriteLine($"   ✅ النموذج {i + 1} تم إغلاقه");
                }

                // اختبار الذاكرة
                Console.WriteLine("   🧠 اختبار استهلاك الذاكرة...");
                var initialMemory = GC.GetTotalMemory(false);
                
                for (int i = 0; i < 5; i++)
                {
                    var form = new Frm_UserManagement();
                    form.Dispose();
                }
                
                GC.Collect();
                var finalMemory = GC.GetTotalMemory(true);
                Console.WriteLine($"   ✅ الذاكرة المستخدمة: {(finalMemory - initialMemory) / 1024} KB");

                Console.WriteLine("✅ اختبار الأخطاء المحتملة انتهى!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في اختبار الأخطاء المحتملة: {ex.Message}");
                Console.WriteLine($"تفاصيل الخطأ: {ex.StackTrace}");
            }
        }
    }
}
