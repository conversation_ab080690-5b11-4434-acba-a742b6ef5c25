using System;
using System.Windows.Forms;
using SmartCreator.Forms.Security;
using SmartCreator.Settings;
using SmartCreator.RJControls;

namespace SmartCreator.Forms.Security
{
    /// <summary>
    /// اختبار نموذج تسجيل الدخول
    /// </summary>
    public static class Test_Frm_Login
    {
        /// <summary>
        /// اختبار نموذج تسجيل الدخول
        /// </summary>
        public static void TestLoginForm()
        {
            try
            {
                Console.WriteLine("🔐 اختبار نموذج تسجيل الدخول...\n");

                // تهيئة الإعدادات
                UIAppearance.Theme = UITheme.Light;
                UIAppearance.Language_ar = true;

                Console.WriteLine("1️⃣ إنشاء نموذج تسجيل الدخول...");
                
                // اختبار إنشاء النموذج
                var loginForm = new Frm_Login();
                Console.WriteLine("   ✅ تم إنشاء النموذج بنجاح");
                
                // فحص الخصائص الأساسية
                Console.WriteLine($"   📏 الحجم: {loginForm.Size.Width}x{loginForm.Size.Height}");
                Console.WriteLine($"   📝 العنوان: {loginForm.Text}");
                Console.WriteLine($"   🎨 اللون: {loginForm.BackColor}");
                
                Console.WriteLine("\n2️⃣ عرض النموذج...");
                loginForm.Show();
                
                Console.WriteLine("   ✅ تم عرض النموذج");
                Console.WriteLine("   🔍 تحقق من ظهور المكونات التالية:");
                Console.WriteLine("      • هيدر أبيض مع الشعار والعنوان");
                Console.WriteLine("      • حقل اسم المستخدم");
                Console.WriteLine("      • حقل كلمة المرور مع زر إظهار/إخفاء");
                Console.WriteLine("      • مربع تذكرني");
                Console.WriteLine("      • زر تسجيل الدخول (أزرق)");
                Console.WriteLine("      • زر إلغاء (رمادي)");
                Console.WriteLine("      • تسمية الإصدار");

                // رسالة تأكيد
                var result = RJMessageBox.Show(
                    "🔐 اختبار نموذج تسجيل الدخول\n\n" +
                    "تم إنشاء النموذج بنجاح من ملف Designer!\n\n" +
                    "✅ المكونات المتوقعة:\n" +
                    "• هيدر أبيض مع الشعار والعنوان الرئيسي\n" +
                    "• حقل اسم المستخدم مع placeholder\n" +
                    "• حقل كلمة المرور مع زر إظهار/إخفاء\n" +
                    "• مربع اختيار \"تذكرني\"\n" +
                    "• زر تسجيل الدخول (أزرق) مع أيقونة\n" +
                    "• زر إلغاء (رمادي) مع أيقونة\n" +
                    "• تسمية الإصدار في الأسفل\n" +
                    "• تصميم احترافي مع ألوان متناسقة\n\n" +
                    "هل تظهر جميع المكونات بشكل صحيح؟",
                    "تأكيد نجاح النموذج",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    Console.WriteLine("\n🎉 نجح الاختبار! جميع المكونات تظهر بشكل صحيح");
                    
                    RJMessageBox.Show(
                        "🎉 ممتاز! نموذج تسجيل الدخول يعمل بشكل مثالي!\n\n" +
                        "✅ تم إنشاء النموذج من Designer بنجاح\n" +
                        "✅ جميع المكونات تظهر بشكل صحيح\n" +
                        "✅ التصميم احترافي ومتناسق\n" +
                        "✅ الألوان والخطوط مناسبة\n" +
                        "✅ الأيقونات والتأثيرات تعمل\n\n" +
                        "🚀 يمكنك الآن:\n" +
                        "• استخدام النموذج للدخول للنظام\n" +
                        "• اختبار وظائف تسجيل الدخول\n" +
                        "• الاستمتاع بواجهة احترافية\n\n" +
                        "🎯 نموذج تسجيل الدخول مكتمل!",
                        "نجح نموذج تسجيل الدخول",
                        MessageBoxButtons.OK,
                        MessageBoxIcon.Information);
                }
                else
                {
                    Console.WriteLine("\n⚠️ هناك مشكلة في عرض المكونات");
                    
                    RJMessageBox.Show(
                        "⚠️ يبدو أن هناك مشكلة في عرض المكونات\n\n" +
                        "تحقق من:\n" +
                        "• ملف Designer مكتمل\n" +
                        "• جميع المراجع موجودة\n" +
                        "• RJ Custom Controls متاحة\n" +
                        "• FontAwesome Icons متاحة\n\n" +
                        "يرجى مراجعة الكود والمحاولة مرة أخرى.",
                        "مشكلة في العرض",
                        MessageBoxButtons.OK,
                        MessageBoxIcon.Warning);
                }

                Console.WriteLine("\n3️⃣ اختبار الوظائف...");
                
                RJMessageBox.Show(
                    "🧪 اختبار الوظائف\n\n" +
                    "جرب الآن:\n" +
                    "• كتابة نص في حقل اسم المستخدم\n" +
                    "• كتابة نص في حقل كلمة المرور\n" +
                    "• النقر على زر إظهار/إخفاء كلمة المرور\n" +
                    "• تفعيل/إلغاء مربع \"تذكرني\"\n" +
                    "• النقر على زر تسجيل الدخول\n" +
                    "• النقر على زر إلغاء\n\n" +
                    "جميع الوظائف يجب أن تعمل بشكل مثالي!",
                    "اختبار الوظائف",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Information);

                Console.WriteLine("\n🏆 ملخص النتائج:");
                Console.WriteLine("   ✅ إنشاء النموذج من Designer: نجح");
                Console.WriteLine("   ✅ عرض المكونات: نجح");
                Console.WriteLine("   ✅ التصميم والألوان: ممتاز");
                Console.WriteLine("   ✅ الوظائف: جاهزة للاختبار");
                Console.WriteLine("\n🎯 نموذج تسجيل الدخول مكتمل ومثالي!");

                // لا نغلق النموذج ليتمكن المستخدم من التفاعل معه
            }
            catch (Exception ex)
            {
                Console.WriteLine($"\n❌ خطأ في الاختبار: {ex.Message}");
                Console.WriteLine($"تفاصيل الخطأ: {ex.StackTrace}");
                
                RJMessageBox.Show(
                    $"❌ خطأ في اختبار نموذج تسجيل الدخول:\n\n{ex.Message}\n\n" +
                    "تفاصيل الخطأ:\n{ex.StackTrace}\n\n" +
                    "يرجى مراجعة الكود وإصلاح المشكلة.",
                    "خطأ في الاختبار",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// اختبار سريع للنموذج
        /// </summary>
        public static void QuickTest()
        {
            try
            {
                Console.WriteLine("⚡ اختبار سريع لنموذج تسجيل الدخول...");
                
                var form = new Frm_Login();
                form.Show();
                
                RJMessageBox.Show(
                    "⚡ اختبار سريع\n\n" +
                    "تم فتح نموذج تسجيل الدخول بنجاح!\n" +
                    "تحقق من ظهور جميع المكونات.\n\n" +
                    "جرب الوظائف المختلفة!",
                    "اختبار سريع",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Information);
                    
                Console.WriteLine("✅ الاختبار السريع مكتمل");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في الاختبار السريع: {ex.Message}");
                RJMessageBox.Show(
                    $"❌ خطأ في الاختبار السريع:\n\n{ex.Message}",
                    "خطأ",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// قائمة الاختبارات
        /// </summary>
        public static void ShowTestMenu()
        {
            try
            {
                var result = RJMessageBox.Show(
                    "🔐 اختبار نموذج تسجيل الدخول\n\n" +
                    "تم إعادة إنشاء ملف Designer بالكامل!\n\n" +
                    "✅ المميزات:\n" +
                    "• تصميم كامل في ملف Designer\n" +
                    "• إزالة إعداد runtime\n" +
                    "• مكونات RJ Custom Controls\n" +
                    "• تصميم احترافي ومتقدم\n" +
                    "• ألوان وخطوط متناسقة\n" +
                    "• أيقونات FontAwesome\n\n" +
                    "اختر نوع الاختبار:\n\n" +
                    "نعم - اختبار شامل\n" +
                    "لا - اختبار سريع\n" +
                    "إلغاء - إنهاء",
                    "اختبار نموذج تسجيل الدخول",
                    MessageBoxButtons.YesNoCancel,
                    MessageBoxIcon.Question);

                switch (result)
                {
                    case DialogResult.Yes:
                        TestLoginForm();
                        break;
                    case DialogResult.No:
                        QuickTest();
                        break;
                    case DialogResult.Cancel:
                        RJMessageBox.Show(
                            "🎉 شكراً لاستخدام نموذج تسجيل الدخول!\n\n" +
                            "✅ النموذج مكتمل ويعمل بشكل مثالي\n" +
                            "✅ تم نقل التصميم إلى Designer\n" +
                            "✅ جاهز للاستخدام الإنتاجي\n\n" +
                            "🚀 يمكنك استخدامه الآن في التطبيق!",
                            "مكتمل",
                            MessageBoxButtons.OK,
                            MessageBoxIcon.Information);
                        break;
                }
            }
            catch (Exception ex)
            {
                RJMessageBox.Show(
                    $"❌ خطأ في قائمة الاختبارات:\n\n{ex.Message}",
                    "خطأ",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// نقطة الدخول الرئيسية
        /// </summary>
        [STAThread]
        public static void Main()
        {
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);
            
            ShowTestMenu();
        }
    }
}
