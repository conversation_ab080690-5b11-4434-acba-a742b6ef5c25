using SmartCreator.Data;
using SmartCreator.Entities;
using SmartCreator.Forms.Acive_Host;
using SmartCreator.Forms.Security;
using SmartCreator.Models;
using SmartCreator.Properties;
using SmartCreator.RJControls;
using SmartCreator.RJForms;
using SmartCreator.Services;
using SmartCreator.Services.Security;
using SmartCreator.Utils;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace SmartCreator.Forms.Security
{
    /// <summary>
    /// واجهة إدارة المستخدمين والصلاحيات
    /// </summary>
    public partial class Frm_UserManagement : RJChildForm
    {
        private readonly UserManagementService _userService;
        private readonly PermissionService _permissionService;
        private readonly ActivityService _activityService;
        private List<User> _users;
        private List<Permission> _permissions;
        private User _selectedUser;
        private bool _isLoadingPermissions = false;

        public Frm_UserManagement()
        {
            try
            {
                InitializeComponent();

                _userService = new UserManagementService();
                _permissionService = new PermissionService(new Smart_DataAccess(), new UserActivityService(new Smart_DataAccess()));
                _activityService = new ActivityService();
                InitializeForm();
                this.RightToLeft = RightToLeft.No;
                this.RightToLeftLayout = false;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إنشاء النموذج: {ex.Message}\n\nتفاصيل الخطأ: {ex.StackTrace}",
                    "خطأ في الإنشاء", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تهيئة النموذج
        /// </summary>
        private void InitializeForm()
        {
            try
            {
                // إعداد DataGridView للمستخدمين
                SetupUsersDataGridView();

                // إعداد DataGridView للأنشطة
                SetupActivitiesDataGridView();

                // إعداد الأحداث
                SetupEvents();

                // تحميل البيانات
                LoadDataAsync();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تهيئة النموذج: {ex.Message}\n\nتفاصيل الخطأ: {ex.StackTrace}",
                    "خطأ في التهيئة", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// إعداد DataGridView للمستخدمين
        /// </summary>
        private void SetupUsersDataGridView()
        {
            try
            {
                // إعداد التنسيق الأساسي
                ApplyBasicDataGridViewStyling(dgvUsers);

                // إعداد الأعمدة للمستخدمين
                SetupUsersColumns();

                // إضافة تأثيرات التفاعل
                AddDataGridViewInteractionEffects(dgvUsers);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إعداد جدول المستخدمين: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// إعداد DataGridView للأنشطة
        /// </summary>
        private void SetupActivitiesDataGridView()
        {
            try
            {
                // إعداد التنسيق الأساسي
                ApplyBasicDataGridViewStyling(dgvActivities);

                // إعداد الأعمدة للأنشطة
                SetupActivitiesColumns();

                // إضافة تأثيرات التفاعل
                AddDataGridViewInteractionEffects(dgvActivities);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إعداد جدول الأنشطة: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// إعداد الأحداث
        /// </summary>
        private void SetupEvents()
        {
            // أحداث المستخدمين
            btnAddUser.Click += BtnAddUser_Click;
            btnEditUser.Click += BtnEditUser_Click;
            btnDeleteUser.Click += BtnDeleteUser_Click;
            btnRefresh.Click += BtnRefresh_Click;
            dgvUsers.SelectionChanged += DgvUsers_SelectionChanged;
            txtSearchUsers.TextChanged += TxtSearchUsers_TextChanged;

            // أحداث الصلاحيات
            btnSavePermissions.Click += BtnSavePermissions_Click;
            btnCancelPermissions.Click += BtnCancelPermissions_Click;
            treePermissions.AfterCheck += TreePermissions_AfterCheck;

            // أحداث الأنشطة
            btnFilterActivities.Click += BtnFilterActivities_Click;
            btnExportActivities.Click += BtnExportActivities_Click;
            if (btnViewUserActivities != null)
                btnViewUserActivities.Click += BtnViewUserActivities_Click;

            // إعداد التواريخ الافتراضية
            dtpFromDate.Value = DateTime.Now.AddDays(-30);
            dtpToDate.Value = DateTime.Now;
        }

        /// <summary>
        /// تحميل البيانات
        /// </summary>
        private async void LoadDataAsync()
        {

            try
            {
                // تحميل المستخدمين
                await LoadUsersAsync();

                // تحميل الصلاحيات
                await LoadPermissionsAsync();

                // تحميل وحدات الأنشطة
                LoadActivityModules();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تحميل المستخدمين
        /// </summary>
        private async Task LoadUsersAsync()
        {
            try
            {
                _users = await _userService.GetAllUsersAsync().ConfigureAwait(false);

                // التأكد من تحديث UI في الـ UI thread
                if (this.InvokeRequired)
                {
                    this.Invoke(new Action(() =>
                    {
                        dgvUsers.DataSource = _users;
                        UpdateButtonStates();
                    }));
                }
                else
                {
                    dgvUsers.DataSource = _users;
                    UpdateButtonStates();
                }
            }
            catch (Exception ex)
            {
                // التأكد من عرض الرسالة في الـ UI thread
                if (this.InvokeRequired)
                {
                    this.Invoke(new Action(() =>
                    {
                        MessageBox.Show($"خطأ في تحميل المستخدمين: {ex.Message}", "خطأ",
                            MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }));
                }
                else
                {
                    MessageBox.Show($"خطأ في تحميل المستخدمين: {ex.Message}", "خطأ",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        /// <summary>
        /// تحميل الصلاحيات
        /// </summary>
        private async Task LoadPermissionsAsync()
        {
            try
            {
                _permissions = await _userService.GetAllPermissionsAsync();

                // التأكد من تحديث UI في الـ UI thread
                if (this.InvokeRequired)
                {
                    this.Invoke(new Action(() =>
                    {
                        BuildPermissionsTree();
                    }));
                }
                else
                {
                    BuildPermissionsTree();
                }
            }
            catch (Exception ex)
            {
                // التأكد من عرض الرسالة في الـ UI thread
                if (this.InvokeRequired)
                {
                    this.Invoke(new Action(() =>
                    {
                        MessageBox.Show($"خطأ في تحميل الصلاحيات: {ex.Message}", "خطأ",
                            MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }));
                }
                else
                {
                    MessageBox.Show($"خطأ في تحميل الصلاحيات: {ex.Message}", "خطأ",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        /// <summary>
        /// بناء شجرة الصلاحيات
        /// </summary>
        private void BuildPermissionsTree()
        {
            treePermissions.Nodes.Clear();
            treePermissions.CheckBoxes = true;

            // تجميع الصلاحيات حسب الوحدة والفئة
            var groupedPermissions = _permissions
                .GroupBy(p => p.Module)
                .OrderBy(g => g.Key);

            foreach (var moduleGroup in groupedPermissions)
            {
                var moduleNode = new TreeNode(moduleGroup.Key)
                {
                    Tag = "Module"
                };

                var categoryGroups = moduleGroup
                    .GroupBy(p => p.Category)
                    .OrderBy(g => g.Key);

                foreach (var categoryGroup in categoryGroups)
                {
                    var categoryNode = new TreeNode(categoryGroup.Key)
                    {
                        Tag = "Category"
                    };

                    foreach (var permission in categoryGroup.OrderBy(p => p.Name))
                    {
                        var permissionNode = new TreeNode(permission.Name)
                        {
                            Tag = permission
                        };
                        categoryNode.Nodes.Add(permissionNode);
                    }

                    moduleNode.Nodes.Add(categoryNode);
                }

                treePermissions.Nodes.Add(moduleNode);
            }

            treePermissions.ExpandAll();
        }

        /// <summary>
        /// تحميل وحدات الأنشطة
        /// </summary>
        private void LoadActivityModules()
        {
            cmbActivityModule.Items.Clear();
            cmbActivityModule.Items.Add("جميع الوحدات");
            cmbActivityModule.Items.AddRange(new string[]
            {
                ActivityModules.AUTHENTICATION,
                ActivityModules.USER_MANAGEMENT,
                ActivityModules.PERMISSIONS,
                ActivityModules.ACCOUNTING,
                ActivityModules.ACCOUNTS,
                ActivityModules.JOURNAL_ENTRIES,
                ActivityModules.REPORTS,
                ActivityModules.SYSTEM
            });
            cmbActivityModule.SelectedIndex = 0;
        }

        /// <summary>
        /// تحديث حالة الأزرار
        /// </summary>
        private void UpdateButtonStates()
        {
            var hasSelection = dgvUsers.SelectedRows.Count > 0;
            btnEditUser.Enabled = hasSelection;
            btnDeleteUser.Enabled = hasSelection;
        }

        #region أحداث المستخدمين

        /// <summary>
        /// إضافة مستخدم جديد
        /// </summary>
        private async void BtnAddUser_Click(object sender, EventArgs e)
        {
            try
            {
                var addForm = new Frm_AddEditUser(_userService);
                if (addForm.ShowDialog() == DialogResult.OK)
                {
                    await LoadUsersAsync();
                    MessageBox.Show("تم إضافة المستخدم بنجاح", "نجح",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إضافة المستخدم: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تعديل مستخدم
        /// </summary>
        private async void BtnEditUser_Click(object sender, EventArgs e)
        {
            if (_selectedUser == null) return;

            try
            {
                var editForm = new Frm_AddEditUser(_userService, _selectedUser);
                if (editForm.ShowDialog() == DialogResult.OK)
                {
                   await LoadUsersAsync();
                    MessageBox.Show("تم تعديل المستخدم بنجاح", "نجح",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تعديل المستخدم: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// حذف مستخدم
        /// </summary>
        private async void BtnDeleteUser_Click(object sender, EventArgs e)
        {
            if (_selectedUser == null) return;

            var result = MessageBox.Show(
                $"هل أنت متأكد من حذف المستخدم '{_selectedUser.Username}'؟\nسيتم حذف جميع صلاحياته أيضاً.",
                "تأكيد الحذف", MessageBoxButtons.YesNo, MessageBoxIcon.Warning);

            if (result == DialogResult.Yes)
            {
                try
                {
                    await _userService.DeleteUserAsync(_selectedUser.Id, Global_Variable.CurrentUser.Id);
                    await LoadUsersAsync();
                    MessageBox.Show("تم حذف المستخدم بنجاح", "نجح",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في حذف المستخدم: {ex.Message}", "خطأ",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        /// <summary>
        /// تحديث البيانات
        /// </summary>
        private async void BtnRefresh_Click(object sender, EventArgs e)
        {
            LoadDataAsync();
        }

        /// <summary>
        /// تغيير تحديد المستخدم
        /// </summary>
        private async void DgvUsers_SelectionChanged(object sender, EventArgs e)
        {
            if (dgvUsers.SelectedRows.Count > 0)
            {
                var selectedRow = dgvUsers.SelectedRows[0];
                _selectedUser = selectedRow.DataBoundItem as User;

                if (_selectedUser != null)
                {
                    lblSelectedUser.Text = $"المستخدم المحدد: {_selectedUser.DisplayName}";
                    await LoadUserPermissionsAsync();
                    await LoadUserActivitiesAsync();
                }
            }
            else
            {
                _selectedUser = null;
                lblSelectedUser.Text = "لم يتم تحديد مستخدم";
                ClearPermissionsTree();
                dgvActivities.DataSource = null;
            }

            UpdateButtonStates();
        }

        /// <summary>
        /// البحث في المستخدمين
        /// </summary>
        private void TxtSearchUsers_TextChanged(object sender, EventArgs e)
        {
            // تطبيق فلتر البحث
            ApplyDataGridViewSearchFilter(dgvUsers, txtSearchUsers.Text);
        }

        #endregion

        #region أحداث الصلاحيات

        /// <summary>
        /// تحميل صلاحيات المستخدم المحدد
        /// </summary>
        private async Task LoadUserPermissionsAsync()
        {
            if (_selectedUser == null) return;

            try
            {
                _isLoadingPermissions = true;
                var userPermissions = await _permissionService.GetUserPermissionsAsync(_selectedUser.Id);
                var userPermissionIds = userPermissions.Select(p => p.Id).ToHashSet();

                // التأكد من تحديث UI في الـ UI thread
                if (this.InvokeRequired)
                {
                    this.Invoke(new Action(() =>
                    {
                        UpdatePermissionTreeState(treePermissions.Nodes, userPermissionIds);
                        _isLoadingPermissions = false;
                    }));
                }
                else
                {
                    UpdatePermissionTreeState(treePermissions.Nodes, userPermissionIds);
                    _isLoadingPermissions = false;
                }
            }
            catch (Exception ex)
            {
                _isLoadingPermissions = false;

                // التأكد من عرض الرسالة في الـ UI thread
                if (this.InvokeRequired)
                {
                    this.Invoke(new Action(() =>
                    {
                        MessageBox.Show($"خطأ في تحميل صلاحيات المستخدم: {ex.Message}", "خطأ",
                            MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }));
                }
                else
                {
                    MessageBox.Show($"خطأ في تحميل صلاحيات المستخدم: {ex.Message}", "خطأ",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        /// <summary>
        /// تحديث حالة عقد الشجرة
        /// </summary>
        private void UpdatePermissionTreeState(TreeNodeCollection nodes, HashSet<int> userPermissionIds)
        {
            foreach (TreeNode node in nodes)
            {
                if (node.Tag is Permission permission)
                {
                    node.Checked = userPermissionIds.Contains(permission.Id);
                }

                if (node.Nodes.Count > 0)
                {
                    UpdatePermissionTreeState(node.Nodes, userPermissionIds);

                    // تحديث حالة العقدة الأب بناءً على العقد الفرعية
                    if (node.Tag is string)
                    {
                        var checkedChildren = node.Nodes.Cast<TreeNode>().Count(n => n.Checked);
                        var totalChildren = node.Nodes.Count;

                        if (checkedChildren == totalChildren)
                            node.Checked = true;
                        else if (checkedChildren > 0)
                            node.BackColor = Color.LightBlue; // إشارة للتحديد الجزئي
                        else
                            node.Checked = false;
                    }
                }
            }
        }

        /// <summary>
        /// مسح شجرة الصلاحيات
        /// </summary>
        private void ClearPermissionsTree()
        {
            _isLoadingPermissions = true;
            foreach (TreeNode node in treePermissions.Nodes)
            {
                ClearNodeChecked(node);
            }
            _isLoadingPermissions = false;
        }

        /// <summary>
        /// مسح تحديد العقدة وفروعها
        /// </summary>
        private void ClearNodeChecked(TreeNode node)
        {
            node.Checked = false;
            node.BackColor = Color.White;
            foreach (TreeNode childNode in node.Nodes)
            {
                ClearNodeChecked(childNode);
            }
        }

        /// <summary>
        /// فتح نموذج إدارة صلاحيات المستخدم
        /// </summary>
        private async void BtnSavePermissions_Click(object sender, EventArgs e)
        {
            if (_selectedUser == null)
            {
                MessageBox.Show("يرجى تحديد مستخدم أولاً", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            try
            {
                var permissionsForm = new Frm_UserPermissions(_permissionService, _selectedUser);
                if (permissionsForm.ShowDialog() == DialogResult.OK)
                {
                    MessageBox.Show("تم تحديث صلاحيات المستخدم بنجاح", "نجح التحديث",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);

                    // إعادة تحميل صلاحيات المستخدم في الشجرة
                    await LoadUserPermissionsAsync();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح نموذج الصلاحيات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// إلغاء تعديل الصلاحيات
        /// </summary>
        private async void BtnCancelPermissions_Click(object sender, EventArgs e)
        {
            await LoadUserPermissionsAsync();
        }

        /// <summary>
        /// تغيير حالة تحديد الصلاحية
        /// </summary>
        private void TreePermissions_AfterCheck(object sender, TreeViewEventArgs e)
        {
            if (_isLoadingPermissions) return;

            _isLoadingPermissions = true;

            // تحديث العقد الفرعية
            UpdateChildNodes(e.Node, e.Node.Checked);

            // تحديث العقدة الأب
            UpdateParentNode(e.Node);

            _isLoadingPermissions = false;
        }

        /// <summary>
        /// تحديث العقد الفرعية
        /// </summary>
        private void UpdateChildNodes(TreeNode node, bool isChecked)
        {
            foreach (TreeNode childNode in node.Nodes)
            {
                childNode.Checked = isChecked;
                UpdateChildNodes(childNode, isChecked);
            }
        }

        /// <summary>
        /// تحديث العقدة الأب
        /// </summary>
        private void UpdateParentNode(TreeNode node)
        {
            if (node.Parent != null)
            {
                var checkedChildren = node.Parent.Nodes.Cast<TreeNode>().Count(n => n.Checked);
                var totalChildren = node.Parent.Nodes.Count;

                if (checkedChildren == totalChildren)
                {
                    node.Parent.Checked = true;
                    node.Parent.BackColor = Color.White;
                }
                else if (checkedChildren > 0)
                {
                    node.Parent.Checked = false;
                    node.Parent.BackColor = Color.LightBlue;
                }
                else
                {
                    node.Parent.Checked = false;
                    node.Parent.BackColor = Color.White;
                }

                UpdateParentNode(node.Parent);
            }
        }

        /// <summary>
        /// الحصول على الصلاحيات المحددة
        /// </summary>
        private List<int> GetSelectedPermissions()
        {
            var selectedPermissions = new List<int>();
            GetSelectedPermissionsFromNodes(treePermissions.Nodes, selectedPermissions);
            return selectedPermissions;
        }

        /// <summary>
        /// الحصول على الصلاحيات المحددة من العقد
        /// </summary>
        private void GetSelectedPermissionsFromNodes(TreeNodeCollection nodes, List<int> selectedPermissions)
        {
            foreach (TreeNode node in nodes)
            {
                if (node.Checked && node.Tag is Permission permission)
                {
                    selectedPermissions.Add(permission.Id);
                }

                if (node.Nodes.Count > 0)
                {
                    GetSelectedPermissionsFromNodes(node.Nodes, selectedPermissions);
                }
            }
        }

        #endregion

        #region أحداث الأنشطة

        /// <summary>
        /// تحميل أنشطة المستخدم المحدد
        /// </summary>
        private async Task LoadUserActivitiesAsync()
        {
            if (_selectedUser == null) return;

            try
            {
                var activities = await _activityService.GetUserActivitiesAsync(
                    _selectedUser.Id, dtpFromDate.Value, dtpToDate.Value,
                    GetSelectedModule(), 50, 1);

                // التأكد من تحديث UI في الـ UI thread
                if (this.InvokeRequired)
                {
                    this.Invoke(new Action(() =>
                    {
                        dgvActivities.DataSource = activities;
                        ColorizeActivityRows();
                    }));
                }
                else
                {
                    dgvActivities.DataSource = activities;
                    ColorizeActivityRows();
                }
            }
            catch (Exception ex)
            {
                // التأكد من عرض الرسالة في الـ UI thread
                if (this.InvokeRequired)
                {
                    this.Invoke(new Action(() =>
                    {
                        MessageBox.Show($"خطأ في تحميل أنشطة المستخدم: {ex.Message}", "خطأ",
                            MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }));
                }
                else
                {
                    MessageBox.Show($"خطأ في تحميل أنشطة المستخدم: {ex.Message}", "خطأ",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        /// <summary>
        /// تصفية الأنشطة
        /// </summary>
        private async void BtnFilterActivities_Click(object sender, EventArgs e)
        {


            await LoadUserActivitiesAsync();
        }

        /// <summary>
        /// عرض أنشطة المستخدم في نموذج منفصل
        /// </summary>
        private void BtnViewUserActivities_Click(object sender, EventArgs e)
        {
            if (_selectedUser == null)
            {
                MessageBox.Show("يرجى تحديد مستخدم أولاً", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            try
            {
                var activityService = new UserActivityService(new Smart_DataAccess());
                var activitiesForm = new Frm_UserActivities(activityService, _selectedUser);
                activitiesForm.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح نموذج الأنشطة: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تصدير الأنشطة
        /// </summary>
        private async void BtnExportActivities_Click(object sender, EventArgs e)
        {
            if (_selectedUser == null)
            {
                MessageBox.Show("يرجى تحديد مستخدم أولاً", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            try
            {
                var saveDialog = new SaveFileDialog
                {
                    Filter = "Excel Files|*.xlsx|CSV Files|*.csv",
                    FileName = $"أنشطة_{_selectedUser.Username}_{DateTime.Now:yyyyMMdd}.xlsx"
                };

                if (saveDialog.ShowDialog() == DialogResult.OK)
                {
                    var activities = await _activityService.GetUserActivitiesAsync(
                        _selectedUser.Id, dtpFromDate.Value, dtpToDate.Value,
                        GetSelectedModule(), 1000, 1);

                    // تصدير البيانات (يمكن استخدام مكتبة EPPlus أو مشابهة)
                    ExportActivitiesToFile(activities, saveDialog.FileName);

                    MessageBox.Show("تم تصدير الأنشطة بنجاح", "نجح",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تصدير الأنشطة: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// الحصول على الوحدة المحددة
        /// </summary>
        private string GetSelectedModule()
        {
            if (cmbActivityModule.SelectedIndex <= 0)
                return null;

            return cmbActivityModule.SelectedItem.ToString();
        }

        /// <summary>
        /// تلوين صفوف الأنشطة حسب مستوى الخطورة
        /// </summary>
        private void ColorizeActivityRows()
        {
            foreach (DataGridViewRow row in dgvActivities.Rows)
            {
                if (row.DataBoundItem is UserActivity activity)
                {
                    switch (activity.Severity)
                    {
                        case "خطأ":
                        case "Error":
                            row.DefaultCellStyle.BackColor = Color.FromArgb(255, 235, 235);
                            row.DefaultCellStyle.ForeColor = Color.DarkRed;
                            break;
                        case "تحذير":
                        case "Warning":
                            row.DefaultCellStyle.BackColor = Color.FromArgb(255, 248, 220);
                            row.DefaultCellStyle.ForeColor = Color.DarkOrange;
                            break;
                        case "حرج":
                        case "Critical":
                            row.DefaultCellStyle.BackColor = Color.FromArgb(220, 53, 69);
                            row.DefaultCellStyle.ForeColor = Color.White;
                            break;
                        default:
                            row.DefaultCellStyle.BackColor = Color.White;
                            row.DefaultCellStyle.ForeColor = Color.Black;
                            break;
                    }
                }
            }
        }

        /// <summary>
        /// تصدير الأنشطة إلى ملف
        /// </summary>
        private void ExportActivitiesToFile(List<UserActivity> activities, string fileName)
        {
            // تنفيذ بسيط لتصدير CSV
            var lines = new List<string>
            {
                "التاريخ والوقت,الإجراء,الوحدة,الوصف,عنوان IP,المستوى,نجح"
            };

            foreach (var activity in activities)
            {
                var line = $"{activity.Timestamp:yyyy-MM-dd HH:mm:ss}," +
                          $"{activity.Action}," +
                          $"{activity.Module}," +
                          $"\"{activity.Description}\"," +
                          $"{activity.IpAddress}," +
                          $"{activity.Severity}," +
                          $"{(activity.IsSuccessful ? "نعم" : "لا")}";
                lines.Add(line);
            }

            System.IO.File.WriteAllLines(fileName, lines, System.Text.Encoding.UTF8);
        }

        #endregion

        #region وظائف مساعدة

        /// <summary>
        /// التحقق من الصلاحيات
        /// </summary>
        private bool HasPermission(string permissionCode)
        {
            return Global_Variable.CurrentUser?.Permissions?.Contains(permissionCode) == true ||
                   Global_Variable.CurrentUser?.Permissions?.Contains(PermissionCodes.SYSTEM_ADMIN) == true;
        }

        /// <summary>
        /// تطبيق الصلاحيات على الواجهة
        /// </summary>
        private void ApplyPermissions()
        {
            // التحقق من صلاحيات إدارة المستخدمين
            var canManageUsers = HasPermission(PermissionCodes.MANAGE_USERS);

            //btnAddUser.Visible = canManageUsers;
            //btnEditUser.Visible = canManageUsers;
            //btnDeleteUser.Visible = canManageUsers;

            // التحقق من صلاحيات إدارة الصلاحيات
            var canManagePermissions = HasPermission(PermissionCodes.MANAGE_PERMISSIONS);
            tabUserPermissions.Enabled = canManagePermissions;

            // التحقق من صلاحيات عرض سجل الأحداث
            var canViewAuditLogs = HasPermission(PermissionCodes.VIEW_AUDIT_LOGS);
            tabUserActivities.Enabled = canViewAuditLogs;

            if (!canManageUsers && !canManagePermissions && !canViewAuditLogs)
            {
                MessageBox.Show("ليس لديك صلاحية للوصول إلى هذه الواجهة", "غير مصرح",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                //this.Close();
            }
        }

        /// <summary>
        /// عند تحميل النموذج
        /// </summary>
        protected override void OnLoad(EventArgs e)
        {
            base.OnLoad(e);
            ApplyPermissions();
        }

        #endregion

        #region - DataGridView Helper Methods

        /// <summary>
        /// تطبيق التنسيق الأساسي على RJDataGridView
        /// </summary>
        private void ApplyBasicDataGridViewStyling(RJControls.RJDataGridView dgv)
        {
            // الإعدادات الأساسية
            dgv.AllowUserToAddRows = false;
            dgv.AllowUserToDeleteRows = false;
            dgv.ReadOnly = true;
            dgv.MultiSelect = false;
            dgv.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dgv.RightToLeft = RightToLeft.Yes;

            // إعدادات المظهر
            dgv.BackgroundColor = Color.White;
            dgv.BorderStyle = BorderStyle.None;
            dgv.CellBorderStyle = DataGridViewCellBorderStyle.SingleHorizontal;
            dgv.ColumnHeadersBorderStyle = DataGridViewHeaderBorderStyle.None;
            dgv.EnableHeadersVisualStyles = false;
            dgv.GridColor = Color.FromArgb(229, 226, 244);
            dgv.RowHeadersVisible = false;

            // إعدادات الصفوف
            dgv.RowTemplate.Height = 35;
            dgv.AlternatingRowsDefaultCellStyle.BackColor = Color.FromArgb(248, 249, 250);

            // إعدادات رؤوس الأعمدة
            dgv.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(123, 104, 238);
            dgv.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            dgv.ColumnHeadersDefaultCellStyle.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            dgv.ColumnHeadersDefaultCellStyle.SelectionBackColor = Color.FromArgb(123, 104, 238);
            dgv.ColumnHeadersDefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dgv.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            dgv.ColumnHeadersHeight = 40;

            // إعدادات الخلايا
            dgv.DefaultCellStyle.BackColor = Color.White;
            dgv.DefaultCellStyle.ForeColor = Color.FromArgb(64, 64, 64);
            dgv.DefaultCellStyle.Font = new Font("Segoe UI", 9F);
            dgv.DefaultCellStyle.SelectionBackColor = Color.FromArgb(229, 226, 244);
            dgv.DefaultCellStyle.SelectionForeColor = Color.FromArgb(64, 64, 64);
            dgv.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dgv.DefaultCellStyle.Padding = new Padding(5);
        }

        /// <summary>
        /// إعداد أعمدة المستخدمين
        /// </summary>
        private void SetupUsersColumns()
        {
            dgvUsers.AutoGenerateColumns = false;
            dgvUsers.Columns.Clear();

            dgvUsers.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Id",
                HeaderText = "المعرف",
                DataPropertyName = "Id",
                Visible = false
            });

            dgvUsers.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Username",
                HeaderText = "اسم المستخدم",
                DataPropertyName = "Username",
                Width = 120,
                MinimumWidth = 100
            });

            dgvUsers.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "FullName",
                HeaderText = "الاسم الكامل",
                DataPropertyName = "FullName",
                Width = 150,
                MinimumWidth = 120
            });

            dgvUsers.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Email",
                HeaderText = "البريد الإلكتروني",
                DataPropertyName = "Email",
                Width = 180,
                MinimumWidth = 150
            });

            dgvUsers.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Role",
                HeaderText = "الدور",
                DataPropertyName = "Role",
                Width = 100,
                MinimumWidth = 80
            });

            dgvUsers.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "IsActive",
                HeaderText = "الحالة",
                DataPropertyName = "IsActive",
                Width = 80,
                MinimumWidth = 60
            });

            dgvUsers.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "LastLogin",
                HeaderText = "آخر دخول",
                DataPropertyName = "LastLogin",
                Width = 130,
                MinimumWidth = 110,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "dd/MM/yyyy HH:mm" }
            });

            dgvUsers.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "CreatedAt",
                HeaderText = "تاريخ الإنشاء",
                DataPropertyName = "CreatedAt",
                Width = 130,
                MinimumWidth = 110,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "dd/MM/yyyy" }
            });

            // تخصيص عرض الحالة
            dgvUsers.CellFormatting += DgvUsers_CellFormatting;
        }

        /// <summary>
        /// إعداد أعمدة الأنشطة
        /// </summary>
        private void SetupActivitiesColumns()
        {
            dgvActivities.AutoGenerateColumns = false;
            dgvActivities.Columns.Clear();

            dgvActivities.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Id",
                HeaderText = "المعرف",
                DataPropertyName = "Id",
                Visible = false
            });

            dgvActivities.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Timestamp",
                HeaderText = "التوقيت",
                DataPropertyName = "Timestamp",
                Width = 140,
                MinimumWidth = 120,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "dd/MM/yyyy HH:mm:ss" }
            });

            dgvActivities.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Username",
                HeaderText = "المستخدم",
                DataPropertyName = "Username",
                Width = 120,
                MinimumWidth = 100
            });

            dgvActivities.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Module",
                HeaderText = "الوحدة",
                DataPropertyName = "Module",
                Width = 100,
                MinimumWidth = 80
            });

            dgvActivities.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Action",
                HeaderText = "الإجراء",
                DataPropertyName = "Action",
                Width = 150,
                MinimumWidth = 120
            });

            dgvActivities.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Severity",
                HeaderText = "المستوى",
                DataPropertyName = "Severity",
                Width = 80,
                MinimumWidth = 60
            });

            dgvActivities.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Description",
                HeaderText = "الوصف",
                DataPropertyName = "Description",
                Width = 200,
                MinimumWidth = 150,
                AutoSizeMode = DataGridViewAutoSizeColumnMode.Fill
            });

            dgvActivities.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "IpAddress",
                HeaderText = "عنوان IP",
                DataPropertyName = "IpAddress",
                Width = 120,
                MinimumWidth = 100
            });

            // تخصيص عرض مستوى الخطورة
            dgvActivities.CellFormatting += DgvActivities_CellFormatting;
        }

        /// <summary>
        /// إضافة تأثيرات التفاعل للـ DataGridView
        /// </summary>
        private void AddDataGridViewInteractionEffects(RJControls.RJDataGridView dgv)
        {
            // تأثير عند مرور الماوس
            dgv.CellMouseEnter += (sender, e) =>
            {
                if (e.RowIndex >= 0)
                {
                    dgv.Rows[e.RowIndex].DefaultCellStyle.BackColor = Color.FromArgb(240, 245, 249);
                }
            };

            dgv.CellMouseLeave += (sender, e) =>
            {
                if (e.RowIndex >= 0)
                {
                    dgv.Rows[e.RowIndex].DefaultCellStyle.BackColor =
                        e.RowIndex % 2 == 0 ? Color.White : Color.FromArgb(248, 249, 250);
                }
            };
        }

        /// <summary>
        /// تطبيق فلتر البحث على DataGridView
        /// </summary>
        private void ApplyDataGridViewSearchFilter(RJControls.RJDataGridView dgv, string searchText)
        {
            if (string.IsNullOrWhiteSpace(searchText))
            {
                // إظهار جميع الصفوف
                foreach (DataGridViewRow row in dgv.Rows)
                {
                    row.Visible = true;
                }
                return;
            }

            // إخفاء الصفوف التي لا تحتوي على نص البحث
            foreach (DataGridViewRow row in dgv.Rows)
            {
                bool found = false;
                foreach (DataGridViewCell cell in row.Cells)
                {
                    if (cell.Value != null &&
                        cell.Value.ToString().IndexOf(searchText, StringComparison.OrdinalIgnoreCase) >= 0)
                    {
                        found = true;
                        break;
                    }
                }
                row.Visible = found;
            }
        }

        /// <summary>
        /// تنسيق خلايا المستخدمين
        /// </summary>
        private void DgvUsers_CellFormatting(object sender, DataGridViewCellFormattingEventArgs e)
        {
            if (e.ColumnIndex == dgvUsers.Columns["IsActive"].Index && e.Value != null)
            {
                bool isActive = Convert.ToBoolean(e.Value);
                e.Value = isActive ? "نشط" : "غير نشط";
                e.CellStyle.ForeColor = isActive ? Color.FromArgb(40, 167, 69) : Color.FromArgb(220, 53, 69);
                e.FormattingApplied = true;
            }
        }

        /// <summary>
        /// تنسيق خلايا الأنشطة
        /// </summary>
        private void DgvActivities_CellFormatting(object sender, DataGridViewCellFormattingEventArgs e)
        {
            if (e.ColumnIndex == dgvActivities.Columns["Severity"].Index && e.Value != null)
            {
                string severity = e.Value.ToString();
                switch (severity.ToLower())
                {
                    case "info":
                        e.Value = "معلومات";
                        e.CellStyle.ForeColor = Color.FromArgb(23, 162, 184);
                        break;
                    case "warning":
                        e.Value = "تحذير";
                        e.CellStyle.ForeColor = Color.FromArgb(255, 193, 7);
                        break;
                    case "error":
                        e.Value = "خطأ";
                        e.CellStyle.ForeColor = Color.FromArgb(220, 53, 69);
                        break;
                    case "success":
                        e.Value = "نجح";
                        e.CellStyle.ForeColor = Color.FromArgb(40, 167, 69);
                        break;
                    default:
                        e.Value = severity;
                        break;
                }
                e.FormattingApplied = true;
            }
        }

        #endregion

        private void splitContainer1_Panel2_Paint(object sender, PaintEventArgs e)
        {




        }



        //===================================================================


        bool FirstLoad = true;
        public Frm_Users form_User;
        //public Form_HostUser form_HostUser;
        //public Form_AciveUser form_HostUser;
        //public Form_IpBinding form_IpBinding;
        //public Form_WalledGarden form_WalledGarden;
        bool First_form_User = true;
        //bool First_form_HostUser = true;
        //bool First_form_IpBinding = true;
        //bool First_form_WalledGarden = true;

        private void Frm_UserManagment_Load(object sender, EventArgs e)
        {
            timer1.Start();
        }
        private void timer1_Tick(object sender, EventArgs e)
        {
            timer1.Stop();
            btn_Users_Click(sender, e);
            FirstLoad = false;
        }
        private void Set_Font()
        {
            Font fnt = Program.GetCustomFont(Resources.DroidKufi_Bold, 8 * utils.ScaleFactor, FontStyle.Bold);
            btn_Users.Font = fnt;
            btn_Host.Font = fnt;
            btn_IpBinding.Font = fnt;
            btn_WalledGarden.Font = fnt;

        }
        private void pnlClientArea_Resize(object sender, EventArgs e)
        {
            panel_Tab_Container.Refresh();
        }
        private void Btn_Active(RJButton bnt)
        {
            bnt.Style = ControlStyle.Solid;
            bnt.BorderSize = 1;
            bnt.Invalidate();
            bnt.Refresh();
            bnt.FlatAppearance.MouseOverBackColor = ColorEditor.Darken(bnt.BackColor, 12);
            bnt.FlatAppearance.MouseDownBackColor = ColorEditor.Darken(bnt.BackColor, 6);
            bnt.BorderSize = 1;

        }
        private void Btn_DeActive()
        {
            foreach (Control contrl in tableLayoutPanel_Top_Btn.Controls)
            {
                if (contrl.GetType() == typeof(RJControls.RJButton))
                {
                    RJButton bnt = (RJButton)contrl;
                    bnt.Style = ControlStyle.Glass;
                    bnt.Invalidate();
                    bnt.Refresh();
                    bnt.FlatAppearance.MouseOverBackColor = ColorEditor.Darken(bnt.BackColor, 12);
                    bnt.FlatAppearance.MouseDownBackColor = ColorEditor.Darken(bnt.BackColor, 6);
                }

            }
        }

        private void btn_Users_Click(object sender, EventArgs e)
        {
            Btn_DeActive();
            Btn_DeActive();
            Btn_Active(btn_Users);

            if (First_form_User)
            {
                First_form_User = false;
                form_User = new Frm_Users();
                form_User.TopLevel = false;
                form_User.IsChildForm = true;
                form_User.Dock = DockStyle.Fill;

                this.panel_Tab_Container.Controls.Add(form_User);
                this.panel_Tab_Container.Tag = First_form_User;
                form_User.Show(); //show on desktop panel  
                form_User.BringToFront();
                form_User.Focus();

                //formAllCardsUserManager.LoadDataGridviewData();
            }
            else
            {
                form_User.BringToFront();
                form_User.Show();
                form_User.Focus();
            }
        }

        private void btnAddUser_Click_1(object sender, EventArgs e)
        {

        }

        private void btnSavePermissions_Click_1(object sender, EventArgs e)
        {

        }
    }
}
