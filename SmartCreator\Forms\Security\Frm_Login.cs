using System;
using System.Drawing;
using System.Windows.Forms;
using System.Threading.Tasks;
using SmartCreator.RJForms;
using SmartCreator.RJControls;
using SmartCreator.Settings;
using SmartCreator.Utils;
//using SmartCreator.Services;
using SmartCreator.Services.Security;
using SmartCreator.Entities;
using SmartCreator.Models;
using SmartCreator.Data;
using SmartCreator.Service;
using SmartCreator.Services;
using FontAwesome.Sharp;
using System.Security.Cryptography;
using System.Text;
using System.Collections.Generic;

namespace SmartCreator.Forms.Security
{
    /// <summary>
    /// نموذج تسجيل الدخول المتقدم باستخدام RJ Custom Controls
    /// </summary>
    public partial class Frm_Login : RJBaseForm
    {
        #region Fields

        private UserManagementService _userService;
        private UserActivityService _activityService;
        private bool _isLoading = false;
        private int _loginAttempts = 0;
        private const int MAX_LOGIN_ATTEMPTS = 3;

        #endregion

        #region Constructor

        /// <summary>
        /// منشئ نموذج تسجيل الدخول
        /// </summary>
        public Frm_Login()
        {
            InitializeComponent();
            InitializeServices();
            SetupForm();
            SetupEvents();
            ApplyTheme();
        }

        #endregion

        #region Initialization

        /// <summary>
        /// تهيئة الخدمات
        /// </summary>
        private void InitializeServices()
        {
            try
            {
                _userService = new UserManagementService();
                _activityService = new UserActivityService(new Smart_DataAccess());
            }
            catch (Exception ex)
            {
                RJMessageBox.Show($"خطأ في تهيئة الخدمات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// إعداد النموذج
        /// </summary>
        private void SetupForm()
        {
            // إعدادات النموذج الأساسية
            this.Text = "تسجيل الدخول - Smart Creator";
            this.Size = new Size(450, 600);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.None;
            this.BackColor = UIAppearance.Theme == UITheme.Dark ?
                ColorTranslator.FromHtml("#2D3748") : Color.White;

            // تطبيق الألوان والخطوط
            this.Font = new Font("Segoe UI", 10F, FontStyle.Regular);

            // إعداد الشفافية والتأثيرات
            this.Opacity = 0.95;
            this.BorderSize = 2;
            this.BorderColor = RJColors.Lisianthus;

            // منع تغيير الحجم
            this.Resizable = false;
            this.DisplayMaximizeButton = false;

            // إعداد الشعار
            SetupLogo();
        }

        /// <summary>
        /// إعداد شعار بسيط
        /// </summary>
        private void SetupLogo()
        {
            try
            {
                // إنشاء شعار بسيط بدلاً من الصورة المفقودة
                var bitmap = new Bitmap(64, 64);
                using (var g = Graphics.FromImage(bitmap))
                {
                    // خلفية دائرية
                    g.SmoothingMode = System.Drawing.Drawing2D.SmoothingMode.AntiAlias;
                    g.FillEllipse(new SolidBrush(Color.FromArgb(123, 104, 238)), 0, 0, 64, 64);

                    // نص الشعار
                    using (var font = new Font("Arial", 16, FontStyle.Bold))
                    {
                        var text = "SC";
                        var textSize = g.MeasureString(text, font);
                        var x = (64 - textSize.Width) / 2;
                        var y = (64 - textSize.Height) / 2;

                        g.DrawString(text, font, Brushes.White, x, y);
                    }
                }

                picLogo.Image = bitmap;
                Console.WriteLine("✅ تم إنشاء شعار بسيط للنموذج");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"تحذير: لم يتم إنشاء الشعار - {ex.Message}");
                // إخفاء PictureBox إذا فشل إنشاء الشعار
                picLogo.Visible = false;
            }
        }

        /// <summary>
        /// ربط الأحداث
        /// </summary>
        private void SetupEvents()
        {
            // أحداث النموذج
            this.Load += Frm_Login_Load;
            this.KeyDown += Frm_Login_KeyDown;

            // أحداث الأزرار
            btnLogin.Click += BtnLogin_Click;
            btnCancel.Click += BtnCancel_Click;
            btnShowPassword.Click += BtnShowPassword_Click;

            // أحداث النصوص
            txtUsername.KeyPress += TxtUsername_KeyPress;
            txtPassword.KeyPress += TxtPassword_KeyPress;
            txtUsername.TextChanged += TxtUsername_TextChanged;
            txtPassword.TextChanged += TxtPassword_TextChanged;

            // أحداث التحقق
            chkRememberMe.CheckedChanged += ChkRememberMe_CheckedChanged;

            // أحداث الماوس للتأثيرات البصرية
            // أحداث الماوس للتأثيرات البصرية - سيتم تطبيقها في Designer
            // btnLogin.MouseEnter += BtnLogin_MouseEnter;
            // btnLogin.MouseLeave += BtnLogin_MouseLeave;
        }

        /// <summary>
        /// تطبيق السمة
        /// </summary>
        private void ApplyTheme()
        {
            if (UIAppearance.Theme == UITheme.Dark)
            {
                ApplyDarkTheme();
            }
            else
            {
                ApplyLightTheme();
            }
        }

        /// <summary>
        /// تطبيق السمة المظلمة
        /// </summary>
        private void ApplyDarkTheme()
        {
            this.BackColor = ColorTranslator.FromHtml("#2D3748");
            pnlMain.BackColor = ColorTranslator.FromHtml("#1A202C");
            pnlHeader.BackColor = ColorTranslator.FromHtml("#2D3748");

            lblTitle.ForeColor = Color.White;
            lblSubtitle.ForeColor = ColorTranslator.FromHtml("#A0AEC0");
            lblUsername.ForeColor = Color.White;
            lblPassword.ForeColor = Color.White;

            txtUsername.BackColor = ColorTranslator.FromHtml("#4A5568");
            txtUsername.ForeColor = Color.White;
            txtPassword.BackColor = ColorTranslator.FromHtml("#4A5568");
            txtPassword.ForeColor = Color.White;
        }

        /// <summary>
        /// تطبيق السمة الفاتحة
        /// </summary>
        private void ApplyLightTheme()
        {
            this.BackColor = Color.White;
            pnlMain.BackColor = ColorTranslator.FromHtml("#F7FAFC");
            pnlHeader.BackColor = Color.White;

            lblTitle.ForeColor = ColorTranslator.FromHtml("#2D3748");
            lblSubtitle.ForeColor = ColorTranslator.FromHtml("#718096");
            lblUsername.ForeColor = ColorTranslator.FromHtml("#4A5568");
            lblPassword.ForeColor = ColorTranslator.FromHtml("#4A5568");

            txtUsername.BackColor = Color.White;
            txtUsername.ForeColor = ColorTranslator.FromHtml("#2D3748");
            txtPassword.BackColor = Color.White;
            txtPassword.ForeColor = ColorTranslator.FromHtml("#2D3748");
        }

        #endregion

        #region Events

        /// <summary>
        /// حدث تحميل النموذج
        /// </summary>
        private async void Frm_Login_Load(object sender, EventArgs e)
        {
            try
            {
                // تطبيق تأثير الظهور التدريجي
                await AnimateFormAppearance();

                // تحميل بيانات المستخدم المحفوظة
                LoadSavedCredentials();

                // تركيز على حقل اسم المستخدم
                txtUsername.Focus();

                // عرض رسالة ترحيب
                ShowWelcomeMessage();
            }
            catch (Exception ex)
            {
                RJMessageBox.Show($"خطأ في تحميل النموذج: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// حدث الضغط على مفاتيح النموذج
        /// </summary>
        private void Frm_Login_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                PerformLogin();
            }
            else if (e.KeyCode == Keys.Escape)
            {
                this.Close();
            }
        }

        /// <summary>
        /// حدث النقر على زر تسجيل الدخول
        /// </summary>
        private async void BtnLogin_Click(object sender, EventArgs e)
        {
            await PerformLoginAsync();
        }

        /// <summary>
        /// حدث النقر على زر الإلغاء
        /// </summary>
        private void BtnCancel_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        /// <summary>
        /// حدث النقر على زر إظهار/إخفاء كلمة المرور
        /// </summary>
        private void BtnShowPassword_Click(object sender, EventArgs e)
        {
            TogglePasswordVisibility();
        }

        /// <summary>
        /// حدث الضغط على مفاتيح في حقل اسم المستخدم
        /// </summary>
        private void TxtUsername_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (e.KeyChar == (char)Keys.Enter)
            {
                txtPassword.Focus();
                e.Handled = true;
            }
        }

        /// <summary>
        /// حدث الضغط على مفاتيح في حقل كلمة المرور
        /// </summary>
        private void TxtPassword_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (e.KeyChar == (char)Keys.Enter)
            {
                PerformLogin();
                e.Handled = true;
            }
        }

        /// <summary>
        /// حدث تغيير نص اسم المستخدم
        /// </summary>
        private void TxtUsername_TextChanged(object sender, EventArgs e)
        {
            ValidateInput();
        }

        /// <summary>
        /// حدث تغيير نص كلمة المرور
        /// </summary>
        private void TxtPassword_TextChanged(object sender, EventArgs e)
        {
            ValidateInput();
        }

        /// <summary>
        /// حدث تغيير حالة تذكرني
        /// </summary>
        private void ChkRememberMe_CheckedChanged(object sender, EventArgs e)
        {
            // يمكن إضافة منطق إضافي هنا
        }

        /// <summary>
        /// حدث دخول الماوس على زر تسجيل الدخول
        /// </summary>
        private void BtnLogin_MouseEnter(object sender, EventArgs e)
        {
            // سيتم تطبيق التأثيرات في Designer
        }

        /// <summary>
        /// حدث خروج الماوس من زر تسجيل الدخول
        /// </summary>
        private void BtnLogin_MouseLeave(object sender, EventArgs e)
        {
            // سيتم تطبيق التأثيرات في Designer
        }

        #endregion

        #region Methods

        /// <summary>
        /// تنفيذ عملية تسجيل الدخول
        /// </summary>
        private async void PerformLogin()
        {
            await PerformLoginAsync();
        }

        /// <summary>
        /// تنفيذ عملية تسجيل الدخول بشكل غير متزامن
        /// </summary>
        private async Task PerformLoginAsync()
        {
            if (_isLoading) return;

            try
            {
                _isLoading = true;
                ShowLoadingState(true);

                // التحقق من صحة البيانات
                if (!ValidateLoginData())
                {
                    ShowLoadingState(false);
                    _isLoading = false;
                    return;
                }

                string username = txtUsername.Text.Trim();
                string password = txtPassword.Text;

                // عرض معلومات تسجيل الدخول للمطور
                //ShowLoginAttemptInfo(username, password);

                // محاولة تسجيل الدخول من قاعدة البيانات
                User authenticatedUser = await AttemptLogin(username, password);

                if (authenticatedUser != null)
                {
                    // نجح تسجيل الدخول
                    await HandleSuccessfulLogin(authenticatedUser);
                }
                else
                {
                    // فشل تسجيل الدخول
                    await HandleFailedLogin(username);
                }
            }
            catch (Exception ex)
            {
                RJMessageBox.Show($"خطأ في تسجيل الدخول: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                ShowLoadingState(false);
                _isLoading = false;
            }
        }

        /// <summary>
        /// التحقق من صحة بيانات تسجيل الدخول
        /// </summary>
        private bool ValidateLoginData()
        {
            if (string.IsNullOrWhiteSpace(txtUsername.Text))
            {
                RJMessageBox.Show("يرجى إدخال اسم المستخدم", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtUsername.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(txtPassword.Text))
            {
                RJMessageBox.Show("يرجى إدخال كلمة المرور", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtPassword.Focus();
                return false;
            }

            return true;
        }

        /// <summary>
        /// عرض معلومات محاولة تسجيل الدخول
        /// </summary>
        private void ShowLoginAttemptInfo(string username, string password)
        {
            try
            {
                // عرض معلومات تسجيل الدخول في وضع التطوير
                string info = $"🔐 محاولة تسجيل الدخول:\n" +
                             $"👤 اسم المستخدم: {username}\n" +
                             $"🔑 كلمة المرور: {new string('*', password.Length)} ({password.Length} أحرف)\n" +
                             $"⏰ الوقت: {DateTime.Now:yyyy-MM-dd HH:mm:ss}\n" +
                             $"🔢 المحاولة رقم: {_loginAttempts + 1}";

                Console.WriteLine(info);

                // عرض في نافذة منبثقة للتطوير
                RJMessageBox.Show(
                    $"🔐 معلومات تسجيل الدخول\n\n" +
                    $"اسم المستخدم: {username}\n" +
                    $"كلمة المرور: {password}\n" +
                    $"طول كلمة المرور: {password.Length} حرف\n" +
                    $"الوقت: {DateTime.Now:HH:mm:ss}\n\n" +
                    "سيتم الآن التحقق من صحة البيانات...",
                    "معلومات تسجيل الدخول",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في عرض معلومات تسجيل الدخول: {ex.Message}");
            }
        }

        /// <summary>
        /// محاولة تسجيل الدخول مع التحقق من قاعدة البيانات
        /// </summary>
        private async Task<User> AttemptLogin(string username, string password)
        {
            try
            {
                //Console.WriteLine($"🔍 بدء التحقق من المستخدم: {username}");

                // التحقق من وجود الجداول المطلوبة
                var tablesExist = await CheckRequiredTablesExistAsync();

                if (!tablesExist)
                {
                    //Console.WriteLine("❌ الجداول المطلوبة غير موجودة");

                    var result = RJMessageBox.Show(
                        "🗄️ قاعدة البيانات غير مكتملة\n\n" +
                        "الجداول المطلوبة لنظام الأمان غير موجودة.\n\n" +
                        "هل تريد إنشاء الجداول الآن؟\n\n" +
                        "سيتم إنشاء:\n" +
                        "• جدول المستخدمين\n" +
                        "• جدول الأنشطة\n" +
                        "• جدول الصلاحيات\n" +
                        "• مستخدمين افتراضيين للاختبار",
                        "إنشاء قاعدة البيانات",
                        MessageBoxButtons.YesNo,
                        MessageBoxIcon.Question);

                    if (result == DialogResult.Yes)
                    {
                        //Console.WriteLine("🔧 إنشاء الجداول المطلوبة...");
                        await CreateRequiredTablesAsync();

                        RJMessageBox.Show(
                            "✅ تم إنشاء قاعدة البيانات بنجاح!\n\n" +
                            "المستخدمون المُنشأون:\n" +
                            "• admin / admin123 (مدير النظام)\n" +
                            "• test / test123 (مستخدم تجريبي)\n\n" +
                            "يمكنك الآن تسجيل الدخول!",
                            "نجح إنشاء قاعدة البيانات",
                            MessageBoxButtons.OK,
                            MessageBoxIcon.Information);
                    }
                    else
                    {
                        return null;
                    }
                }

                // استخدام UserService للمصادقة من قاعدة البيانات
                var dataAccess = new Smart_DataAccess();
                var permissionService = new PermissionService(dataAccess, _activityService);
                var userService = new Services.Security.UserService(dataAccess, _activityService, permissionService);
                var authenticatedUser = await userService.AuthenticateAsync(username, password);

                if (authenticatedUser != null)
                {
                    Console.WriteLine($"✅ نجح التحقق من المستخدم: {authenticatedUser.Username}");
                    Console.WriteLine($"📧 البريد الإلكتروني: {authenticatedUser.Email}");
                    Console.WriteLine($"👤 الاسم الكامل: {authenticatedUser.DisplayName}");
                    Console.WriteLine($"🏢 القسم: {authenticatedUser.Department}");
                    Console.WriteLine($"💼 المنصب: {authenticatedUser.Position}");

                    return authenticatedUser;
                }
                else
                {
                    Console.WriteLine($"❌ فشل التحقق من المستخدم: {username}");
                    return null;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في محاولة تسجيل الدخول: {ex.Message}");

                // تسجيل محاولة دخول فاشلة بسبب خطأ تقني
                try
                {
                    await _activityService?.LogLoginAttemptAsync(null, username, false,
                        $"خطأ تقني: {ex.Message}");
                }
                catch (Exception logEx)
                {
                    Console.WriteLine($"⚠️ خطأ في تسجيل النشاط: {logEx.Message}");
                }

                return null;
            }
        }

        /// <summary>
        /// معالجة تسجيل الدخول الناجح
        /// </summary>
        private async Task HandleSuccessfulLogin(User user)
        {
            try
            {
                Console.WriteLine($"✅ نجح تسجيل الدخول للمستخدم: {user.Username}");
                Console.WriteLine($"📋 معلومات المستخدم:");
                Console.WriteLine($"   🆔 المعرف: {user.Id}");
                Console.WriteLine($"   👤 الاسم: {user.DisplayName}");
                Console.WriteLine($"   📧 البريد: {user.Email}");
                Console.WriteLine($"   🏢 القسم: {user.Department ?? "غير محدد"}");
                Console.WriteLine($"   💼 المنصب: {user.Position ?? "غير محدد"}");
                Console.WriteLine($"   📱 الهاتف: {user.Phone ?? "غير محدد"}");
                Console.WriteLine($"   🔐 الصلاحيات: {user.Permissions?.Count ?? 0} صلاحية");

                // تسجيل النشاط - تم بالفعل في UserService.AuthenticateAsync
                Console.WriteLine($"📝 تم تسجيل نشاط تسجيل الدخول في قاعدة البيانات");

                // حفظ بيانات المستخدم الحالي
                Global_Variable.CurrentUser = user;

                // حفظ بيانات المستخدم إذا كان مطلوباً
                if (chkRememberMe.Checked)
                {
                    SaveCredentials();
                }

                // عرض رسالة نجاح
                ShowSuccessMessage();

                // عرض معلومات المستخدم الكاملة من قاعدة البيانات
                var welcomeMessage = $"🎉 مرحباً بك {user.DisplayName}!\n\n" +
                    $"✅ تم تسجيل الدخول بنجاح من قاعدة البيانات\n\n" +
                    $"📋 معلومات حسابك:\n" +
                    $"👤 اسم المستخدم: {user.Username}\n" +
                    $"📧 البريد الإلكتروني: {user.Email}\n" +
                    $"🏢 القسم: {user.Department ?? "غير محدد"}\n" +
                    $"💼 المنصب: {user.Position ?? "غير محدد"}\n" +
                    $"📱 الهاتف: {user.Phone ?? "غير محدد"}\n" +
                    $"🔐 عدد الصلاحيات: {user.Permissions?.Count ?? 0}\n" +
                    $"⏰ وقت الدخول: {DateTime.Now:yyyy-MM-dd HH:mm:ss}\n" +
                    $"🌐 آخر دخول: {user.LastLoginDate?.ToString("yyyy-MM-dd HH:mm") ?? "أول مرة"}\n\n" +
                    "سيتم الآن فتح التطبيق الرئيسي...";

                RJMessageBox.Show(welcomeMessage, "نجح تسجيل الدخول",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);

                // تأخير قصير للتأثير البصري
                await Task.Delay(500);

                // إغلاق النموذج بنتيجة إيجابية
                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في معالجة تسجيل الدخول: {ex.Message}");
                RJMessageBox.Show($"خطأ في معالجة تسجيل الدخول: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// معالجة تسجيل الدخول الفاشل
        /// </summary>
        private async Task HandleFailedLogin(string username)
        {
            try
            {
                _loginAttempts++;

                Console.WriteLine($"❌ فشل تسجيل الدخول للمستخدم: {username}");
                Console.WriteLine($"🔢 المحاولة رقم: {_loginAttempts} من {MAX_LOGIN_ATTEMPTS}");

                // تسجيل محاولة الدخول الفاشلة - تم بالفعل في UserService.AuthenticateAsync
                Console.WriteLine($"📝 تم تسجيل محاولة الدخول الفاشلة في قاعدة البيانات");

                if (_loginAttempts >= MAX_LOGIN_ATTEMPTS)
                {
                    Console.WriteLine($"🚨 تم تجاوز الحد الأقصى للمحاولات!");

                    // تسجيل تجاوز الحد الأقصى
                    try
                    {
                        await _activityService?.LogActivityAsync(0, "تجاوز الحد الأقصى للمحاولات",
                            "Authentication", $"تم تجاوز الحد الأقصى لمحاولات تسجيل الدخول للمستخدم: {username}",
                            severity: "Critical", isSuccessful: false);
                    }
                    catch (Exception logEx)
                    {
                        Console.WriteLine($"⚠️ خطأ في تسجيل تجاوز الحد الأقصى: {logEx.Message}");
                    }

                    RJMessageBox.Show($"🚨 تحذير أمني!\n\n" +
                        $"تم تجاوز الحد الأقصى لمحاولات تسجيل الدخول ({MAX_LOGIN_ATTEMPTS})\n\n" +
                        "سيتم إغلاق التطبيق لأسباب أمنية.\n" +
                        "يرجى التواصل مع مدير النظام إذا كنت تواجه مشاكل في تسجيل الدخول.",
                        "تحذير أمني", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    Application.Exit();
                }
                else
                {
                    int remainingAttempts = MAX_LOGIN_ATTEMPTS - _loginAttempts;
                    RJMessageBox.Show($"اسم المستخدم أو كلمة المرور غير صحيحة\nالمحاولات المتبقية: {remainingAttempts}",
                        "خطأ في تسجيل الدخول", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }

                // مسح كلمة المرور
                txtPassword.Clear();
                txtPassword.Focus();
            }
            catch (Exception ex)
            {
                RJMessageBox.Show($"خطأ في معالجة فشل تسجيل الدخول: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تبديل رؤية كلمة المرور
        /// </summary>
        private void TogglePasswordVisibility()
        {
            try
            {
                // تبديل حالة إظهار كلمة المرور
                txtPassword.PasswordChar = !txtPassword.PasswordChar;

                // تحديث الأيقونة
                btnShowPassword.IconChar = txtPassword.PasswordChar ? IconChar.Eye : IconChar.EyeSlash;

                // تحديث لون الأيقونة
                btnShowPassword.IconColor = txtPassword.PasswordChar ?
                    Color.FromArgb(160, 174, 192) : RJColors.PrimaryColor;

                // تحديث tooltip
                toolTip1.SetToolTip(btnShowPassword, txtPassword.PasswordChar ?
                    "إظهار كلمة المرور" : "إخفاء كلمة المرور");

                // عرض معلومات للمطور
                Console.WriteLine($"🔍 تم {(txtPassword.PasswordChar ? "إخفاء" : "إظهار")} كلمة المرور");

                // إعادة التركيز على حقل كلمة المرور
                txtPassword.Focus();

                // وضع المؤشر في نهاية النص
                SetTextBoxCursorToEnd(txtPassword);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في تبديل رؤية كلمة المرور: {ex.Message}");
                RJMessageBox.Show($"خطأ في تبديل رؤية كلمة المرور: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// التحقق من صحة الإدخال
        /// </summary>
        private void ValidateInput()
        {
            bool isValid = !string.IsNullOrWhiteSpace(txtUsername.Text) &&
                          !string.IsNullOrWhiteSpace(txtPassword.Text);

            btnLogin.Enabled = isValid;
            btnLogin.BackColor = isValid ? RJColors.Lisianthus : RJColors.Deactive;
        }

        /// <summary>
        /// عرض حالة التحميل
        /// </summary>
        private void ShowLoadingState(bool loading)
        {
            btnLogin.Enabled = !loading;
            btnLogin.Text = loading ? "جاري تسجيل الدخول..." : "تسجيل الدخول";

            if (loading)
            {
                btnLogin.IconChar = IconChar.Spinner;
                // يمكن إضافة تأثير دوران للأيقونة
            }
            else
            {
                btnLogin.IconChar = IconChar.SignInAlt;
            }
        }

        /// <summary>
        /// تحميل بيانات المستخدم المحفوظة
        /// </summary>
        private void LoadSavedCredentials()
        {
            try
            {
                // محاولة تحميل البيانات المحفوظة
                string savedUsername = Properties.Settings.Default.SavedUsername ?? "";
                bool rememberMe = Properties.Settings.Default.RememberUsername;

                if (rememberMe && !string.IsNullOrEmpty(savedUsername))
                {
                    txtUsername.Text = savedUsername;
                    chkRememberMe.Checked = true;

                    // التركيز على حقل كلمة المرور إذا كان اسم المستخدم محفوظ
                    txtPassword.Focus();

                    Console.WriteLine($"📥 تم تحميل اسم المستخدم المحفوظ: {savedUsername}");

                    // عرض رسالة ترحيب
                    lblSubtitle.Text = $"مرحباً بعودتك {savedUsername}!";
                }
                else
                {
                    // التركيز على حقل اسم المستخدم
                    txtUsername.Focus();
                    Console.WriteLine("📥 لا توجد بيانات محفوظة");
                }

                // إضافة أسماء مستخدمين للاختبار السريع
                AddTestUsernames();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في تحميل البيانات المحفوظة: {ex.Message}");
                txtUsername.Focus();
            }
        }

        /// <summary>
        /// إضافة أسماء مستخدمين للاختبار السريع
        /// </summary>
        private void AddTestUsernames()
        {
            try
            {
                // إضافة tooltip مع أسماء المستخدمين المتاحة
                string testUsers = "أسماء المستخدمين المتاحة للاختبار:\n" +
                                  "• admin (كلمة المرور: 123456)\n" +
                                  "• user (كلمة المرور: password)\n" +
                                  "• test (كلمة المرور: test)\n" +
                                  "• مدير (كلمة المرور: 123)\n" +
                                  "• مستخدم (كلمة المرور: 123)";

                toolTip1.SetToolTip(txtUsername, testUsers);
                toolTip1.SetToolTip(lblUsername, testUsers);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في إضافة أسماء المستخدمين للاختبار: {ex.Message}");
            }
        }

        /// <summary>
        /// حفظ بيانات المستخدم
        /// </summary>
        private void SaveCredentials()
        {
            try
            {
                if (chkRememberMe.Checked)
                {
                    // حفظ اسم المستخدم
                    Properties.Settings.Default.SavedUsername = txtUsername.Text.Trim();
                    Properties.Settings.Default.RememberUsername = true;

                    Console.WriteLine($"💾 تم حفظ اسم المستخدم: {txtUsername.Text.Trim()}");
                }
                else
                {
                    // مسح البيانات المحفوظة
                    Properties.Settings.Default.SavedUsername = "";
                    Properties.Settings.Default.RememberUsername = false;

                    Console.WriteLine("💾 تم مسح البيانات المحفوظة");
                }

                // حفظ الإعدادات
                Properties.Settings.Default.Save();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في حفظ البيانات: {ex.Message}");
            }
        }

        /// <summary>
        /// تأثير الظهور التدريجي للنموذج
        /// </summary>
        private async Task AnimateFormAppearance()
        {
            this.Opacity = 0;
            this.Show();

            for (double opacity = 0; opacity <= 0.95; opacity += 0.05)
            {
                this.Opacity = opacity;
                await Task.Delay(20);
            }
        }

        /// <summary>
        /// عرض رسالة الترحيب
        /// </summary>
        private void ShowWelcomeMessage()
        {
            lblSubtitle.Text = $"مرحباً بك في Smart Creator - {DateTime.Now:yyyy/MM/dd}";
        }

        /// <summary>
        /// عرض رسالة النجاح
        /// </summary>
        private void ShowSuccessMessage()
        {
            // يمكن عرض رسالة نجاح أو تأثير بصري
            lblSubtitle.Text = "تم تسجيل الدخول بنجاح!";
            lblSubtitle.ForeColor = Color.Green;
        }

        /// <summary>
        /// تنظيف الموارد المخصصة
        /// </summary>
        private void CleanupCustomResources()
        {
            try
            {
                // تنظيف الخدمات إذا كانت تدعم IDisposable
                 //_userService?.Dispose();
                 //_activityService?.Dispose();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تنظيف الموارد: {ex.Message}");
            }
        }

        /// <summary>
        /// وضع المؤشر في نهاية النص لـ RJTextBox
        /// </summary>
        private void SetTextBoxCursorToEnd(RJTextBox rjTextBox)
        {
            try
            {
                if (rjTextBox?.Text != null)
                {
                    // محاولة الوصول للـ TextBox الداخلي في RJTextBox
                    var textBoxField = rjTextBox.GetType().GetField("textBox1",
                        System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

                    if (textBoxField != null)
                    {
                        var innerTextBox = textBoxField.GetValue(rjTextBox) as TextBox;
                        if (innerTextBox != null)
                        {
                            innerTextBox.SelectionStart = innerTextBox.Text.Length;
                            innerTextBox.SelectionLength = 0;
                            Console.WriteLine($"🎯 تم وضع المؤشر في نهاية النص (الموضع: {innerTextBox.SelectionStart})");
                            return;
                        }
                    }

                    // محاولة أخرى بأسماء مختلفة للحقل
                    var possibleNames = new[] { "textBox", "txtBox", "innerTextBox", "baseTextBox" };

                    foreach (var fieldName in possibleNames)
                    {
                        var field = rjTextBox.GetType().GetField(fieldName,
                            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

                        if (field != null)
                        {
                            var innerTextBox = field.GetValue(rjTextBox) as TextBox;
                            if (innerTextBox != null)
                            {
                                innerTextBox.SelectionStart = innerTextBox.Text.Length;
                                innerTextBox.SelectionLength = 0;
                                Console.WriteLine($"🎯 تم وضع المؤشر باستخدام الحقل: {fieldName}");
                                return;
                            }
                        }
                    }

                    Console.WriteLine("⚠️ لم يتم العثور على TextBox الداخلي في RJTextBox");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"تحذير: لم يتم تحديد موضع المؤشر - {ex.Message}");
            }
        }

        /// <summary>
        /// التحقق من وجود الجداول المطلوبة
        /// </summary>
        private async Task<bool> CheckRequiredTablesExistAsync()
        {
            try
            {
                var dataAccess = new Smart_DataAccess();
                var tables = new[] { "Users", "UserActivities", "Permissions", "UserPermissions" };

                foreach (var table in tables)
                {
                    var sql = "SELECT name FROM sqlite_master WHERE type='table' AND name=@TableName";
                    var result = await dataAccess.ExecuteScalarAsync<string>(sql, new { TableName = table });

                    if (string.IsNullOrEmpty(result))
                    {
                        Console.WriteLine($"❌ جدول {table} غير موجود");
                        return false;
                    }
                }

                Console.WriteLine("✅ جميع الجداول موجودة");
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في فحص الجداول: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// إنشاء الجداول المطلوبة
        /// </summary>
        private async Task CreateRequiredTablesAsync()
        {
            try
            {
                var dataAccess = new Smart_DataAccess();

                Console.WriteLine("🗄️ بدء إنشاء جداول الأمان...");

                // إنشاء جدول المستخدمين
                await CreateUsersTableAsync(dataAccess);
                Console.WriteLine("   ✅ تم إنشاء جدول Users");

                // إنشاء جدول الأنشطة
                await CreateUserActivitiesTableAsync(dataAccess);
                Console.WriteLine("   ✅ تم إنشاء جدول UserActivities");

                // إنشاء جدول الصلاحيات
                await CreatePermissionsTableAsync(dataAccess);
                Console.WriteLine("   ✅ تم إنشاء جدول Permissions");

                // إنشاء جدول صلاحيات المستخدمين
                await CreateUserPermissionsTableAsync(dataAccess);
                Console.WriteLine("   ✅ تم إنشاء جدول UserPermissions");

                // إدراج البيانات الأساسية
                await InsertDefaultDataAsync(dataAccess);
                Console.WriteLine("   ✅ تم إدراج البيانات الأساسية");

                Console.WriteLine("🎉 تم إنشاء جداول الأمان بنجاح!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في إنشاء جداول الأمان: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// إنشاء جدول المستخدمين
        /// </summary>
        private async Task CreateUsersTableAsync(Smart_DataAccess dataAccess)
        {
            //var sql = @"
            //    CREATE TABLE IF NOT EXISTS Users (
            //        Id INTEGER PRIMARY KEY AUTOINCREMENT,
            //        Username NVARCHAR(50) NOT NULL UNIQUE,
            //        Email NVARCHAR(150) NOT NULL UNIQUE,
            //        PasswordHash NVARCHAR(255) NOT NULL,
            //        Salt NVARCHAR(255) ,
            //        FirstName NVARCHAR(100) NOT NULL,
            //        LastName NVARCHAR(100) ,
            //        FullName NVARCHAR(200) ,

            //        Phone NVARCHAR(20),
            //        Department NVARCHAR(100),
            //        Position NVARCHAR(100),
            //        IsActive BOOLEAN NOT NULL DEFAULT 1,
            //        IsLocked BOOLEAN NOT NULL DEFAULT 0,
            //        FailedLoginAttempts INTEGER NOT NULL DEFAULT 0,
            //        LastLoginDate DATETIME,
            //        PasswordExpiryDate DATETIME,
            //        Notes NVARCHAR(200) ,
            //        CreatedDate DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
            //        CreatedBy INTEGER,
            //        ModifiedDate DATETIME,
            //        ModifiedBy INTEGER
            //    )";
            var sql = $@"CREATE TABLE Users (
	                                    Id	INTEGER,
	                                    Username	NVARCHAR(50) NOT NULL,
	                                    Email	NVARCHAR(150),
	                                    PasswordHash	NVARCHAR(255),
	                                    Salt	NVARCHAR(255),
	                                    FirstName	NVARCHAR(100),
	                                    LastName	NVARCHAR(100),
	                                    FullName	NVARCHAR(200),
	                                    Phone	NVARCHAR(20),
	                                    Department	NVARCHAR(100),
	                                    Position	NVARCHAR(100),
	                                    IsActive	BOOLEAN NOT NULL DEFAULT 1,
	                                    IsLocked	BOOLEAN NOT NULL DEFAULT 0,
	                                    FailedLoginAttempts	INTEGER NOT NULL DEFAULT 0,
	                                    LastLoginDate	DATETIME,
	                                    LastPasswordChangeDate	DATETIME,
	                                    PasswordExpiryDate	DATETIME,
	                                    Notes	NVARCHAR(200),
	                                    CreatedDate	DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
	                                    CreatedBy	INTEGER,
	                                    ModifiedDate	DATETIME,
	                                    ModifiedBy	INTEGER,
	                                    Rb	TEXT,
	                                    UNIQUE(Email,Rb),
	                                    PRIMARY KEY(Id AUTOINCREMENT),
	                                    UNIQUE(Phone,Rb),
	                                    UNIQUE(Username,Rb)
                                    )";

            await dataAccess.ExecuteAsync(sql);
        }

        /// <summary>
        /// إنشاء جدول أنشطة المستخدمين
        /// </summary>
        private async Task CreateUserActivitiesTableAsync(Smart_DataAccess dataAccess)
        {
            var sql = @"
                CREATE TABLE IF NOT EXISTS UserActivities (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    UserId INTEGER NOT NULL,
                    Username NVARCHAR(50),
                    Action NVARCHAR(100) NOT NULL,
                    Module NVARCHAR(50) NOT NULL,
                    SubModule NVARCHAR(50),
                    Description NTEXT,
                    EntityType NVARCHAR(50),
                    EntityId INTEGER,
                    OldValues NTEXT,
                    NewValues NTEXT,
                    IpAddress NVARCHAR(45),
                    UserAgent NTEXT,
                    Timestamp DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    ActivityType NVARCHAR(50) NOT NULL,
                    Severity NVARCHAR(20) NOT NULL DEFAULT 'Info',
                    IsSuccessful BOOLEAN NOT NULL DEFAULT 1,
                    Rb	TEXT,
                    ErrorMessage NTEXT
                )";

            await dataAccess.ExecuteAsync(sql);
        }

        /// <summary>
        /// إنشاء جدول الصلاحيات
        /// </summary>
        private async Task CreatePermissionsTableAsync(Smart_DataAccess dataAccess)
        {
            var sql = @"
                CREATE TABLE IF NOT EXISTS Permissions (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    Name NVARCHAR(100) NOT NULL,
                    Code NVARCHAR(50) NOT NULL UNIQUE,
                    Description NTEXT,
                    Module NVARCHAR(50) NOT NULL,
                    Category NVARCHAR(50) NOT NULL,
                    IsActive BOOLEAN NOT NULL DEFAULT 1,
                    CreatedDate DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    CreatedBy INTEGER,
                    ModifiedDate DATETIME,
                    Rb	TEXT,
                    ModifiedBy INTEGER
                )";

            await dataAccess.ExecuteAsync(sql);
        }

        /// <summary>
        /// إنشاء جدول صلاحيات المستخدمين
        /// </summary>
        private async Task CreateUserPermissionsTableAsync(Smart_DataAccess dataAccess)
        {
            var sql = @"
                CREATE TABLE IF NOT EXISTS UserPermissions (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    UserId INTEGER NOT NULL,
                    PermissionId INTEGER NOT NULL,
                    IsGranted BOOLEAN NOT NULL DEFAULT 1,
                    GrantedDate DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    GrantedBy INTEGER NOT NULL,
                    ExpiryDate DATETIME,
                    IsActive BOOLEAN NOT NULL DEFAULT 1,
                    Notes	NVARCHAR(200),
                    Rb	TEXT,
                    UNIQUE(UserId, PermissionId)
                )";

            await dataAccess.ExecuteAsync(sql);
        }

        /// <summary>
        /// إدراج البيانات الأساسية
        /// </summary>
        private async Task InsertDefaultDataAsync(Smart_DataAccess dataAccess)
        {
            // إدراج الصلاحيات الأساسية
            await InsertDefaultPermissionsAsync(dataAccess);

            // إنشاء مستخدم مدير افتراضي
            await CreateDefaultAdminUserAsync(dataAccess);

            // إنشاء مستخدم تجريبي
            await CreateDefaultTestUserAsync(dataAccess);
        }

        /// <summary>
        /// إدراج الصلاحيات الافتراضية
        /// </summary>
        private async Task InsertDefaultPermissionsAsync(Smart_DataAccess dataAccess)
        {
            var permissions = new[]
            {
                // صلاحيات النظام الأساسية
                new { Name = "مدير النظام", Code = "SYSTEM_ADMIN", Description = "صلاحيات كاملة للنظام", Module = "CORE", Category = "SYSTEM" },
                new { Name = "عرض لوحة التحكم", Code = "VIEW_DASHBOARD", Description = "عرض لوحة التحكم الرئيسية", Module = "CORE", Category = "SYSTEM" },
                new { Name = "إعدادات النظام", Code = "SYSTEM_SETTINGS", Description = "تعديل إعدادات النظام", Module = "CORE", Category = "SYSTEM" },
                new { Name = "النسخ الاحتياطي", Code = "BACKUP_RESTORE", Description = "إنشاء واستعادة النسخ الاحتياطية", Module = "CORE", Category = "SYSTEM" },

                // صلاحيات إدارة المستخدمين
                new { Name = "إدارة المستخدمين", Code = "MANAGE_USERS", Description = "إضافة وتعديل وحذف المستخدمين", Module = "SECURITY", Category = "USERS" },
                new { Name = "عرض المستخدمين", Code = "VIEW_USERS", Description = "عرض قائمة المستخدمين", Module = "SECURITY", Category = "USERS" },
                new { Name = "إضافة مستخدم", Code = "ADD_USER", Description = "إضافة مستخدم جديد", Module = "SECURITY", Category = "USERS" },
                new { Name = "تعديل مستخدم", Code = "EDIT_USER", Description = "تعديل بيانات المستخدم", Module = "SECURITY", Category = "USERS" },
                new { Name = "حذف مستخدم", Code = "DELETE_USER", Description = "حذف مستخدم", Module = "SECURITY", Category = "USERS" },
                new { Name = "تفعيل/إلغاء تفعيل مستخدم", Code = "ACTIVATE_USER", Description = "تفعيل أو إلغاء تفعيل حساب المستخدم", Module = "SECURITY", Category = "USERS" },
                new { Name = "إعادة تعيين كلمة المرور", Code = "RESET_PASSWORD", Description = "إعادة تعيين كلمة مرور المستخدم", Module = "SECURITY", Category = "USERS" },

                // صلاحيات إدارة الصلاحيات
                new { Name = "إدارة الصلاحيات", Code = "MANAGE_PERMISSIONS", Description = "إدارة صلاحيات المستخدمين", Module = "SECURITY", Category = "PERMISSIONS" },
                new { Name = "عرض الصلاحيات", Code = "VIEW_PERMISSIONS", Description = "عرض قائمة الصلاحيات", Module = "SECURITY", Category = "PERMISSIONS" },
                new { Name = "منح الصلاحيات", Code = "GRANT_PERMISSIONS", Description = "منح صلاحيات للمستخدمين", Module = "SECURITY", Category = "PERMISSIONS" },
                new { Name = "سحب الصلاحيات", Code = "REVOKE_PERMISSIONS", Description = "سحب صلاحيات من المستخدمين", Module = "SECURITY", Category = "PERMISSIONS" },

                // صلاحيات مراقبة الأنشطة
                new { Name = "عرض الأنشطة", Code = "VIEW_ACTIVITIES", Description = "عرض سجل أنشطة المستخدمين", Module = "SECURITY", Category = "AUDIT" },
                new { Name = "إدارة الأنشطة", Code = "MANAGE_ACTIVITIES", Description = "إدارة سجل الأنشطة", Module = "SECURITY", Category = "AUDIT" },
                new { Name = "تصدير الأنشطة", Code = "EXPORT_ACTIVITIES", Description = "تصدير سجل الأنشطة", Module = "SECURITY", Category = "AUDIT" },
                new { Name = "حذف الأنشطة", Code = "DELETE_ACTIVITIES", Description = "حذف سجلات الأنشطة القديمة", Module = "SECURITY", Category = "AUDIT" },

                // صلاحيات إدارة البيانات
                new { Name = "عرض البيانات", Code = "VIEW_DATA", Description = "عرض البيانات العامة", Module = "DATA", Category = "GENERAL" },
                new { Name = "إضافة البيانات", Code = "ADD_DATA", Description = "إضافة بيانات جديدة", Module = "DATA", Category = "GENERAL" },
                new { Name = "تعديل البيانات", Code = "EDIT_DATA", Description = "تعديل البيانات الموجودة", Module = "DATA", Category = "GENERAL" },
                new { Name = "حذف البيانات", Code = "DELETE_DATA", Description = "حذف البيانات", Module = "DATA", Category = "GENERAL" },
                new { Name = "تصدير البيانات", Code = "EXPORT_DATA", Description = "تصدير البيانات", Module = "DATA", Category = "GENERAL" },
                new { Name = "استيراد البيانات", Code = "IMPORT_DATA", Description = "استيراد البيانات", Module = "DATA", Category = "GENERAL" },

                // صلاحيات التقارير
                new { Name = "عرض التقارير", Code = "VIEW_REPORTS", Description = "عرض التقارير", Module = "REPORTS", Category = "GENERAL" },
                new { Name = "إنشاء التقارير", Code = "CREATE_REPORTS", Description = "إنشاء تقارير جديدة", Module = "REPORTS", Category = "GENERAL" },
                new { Name = "تعديل التقارير", Code = "EDIT_REPORTS", Description = "تعديل التقارير الموجودة", Module = "REPORTS", Category = "GENERAL" },
                new { Name = "حذف التقارير", Code = "DELETE_REPORTS", Description = "حذف التقارير", Module = "REPORTS", Category = "GENERAL" },
                new { Name = "طباعة التقارير", Code = "PRINT_REPORTS", Description = "طباعة التقارير", Module = "REPORTS", Category = "GENERAL" },

                // صلاحيات إدارة الملفات
                new { Name = "عرض الملفات", Code = "VIEW_FILES", Description = "عرض الملفات", Module = "FILES", Category = "GENERAL" },
                new { Name = "رفع الملفات", Code = "UPLOAD_FILES", Description = "رفع ملفات جديدة", Module = "FILES", Category = "GENERAL" },
                new { Name = "تحميل الملفات", Code = "DOWNLOAD_FILES", Description = "تحميل الملفات", Module = "FILES", Category = "GENERAL" },
                new { Name = "حذف الملفات", Code = "DELETE_FILES", Description = "حذف الملفات", Module = "FILES", Category = "GENERAL" }
            };

            Console.WriteLine($"   📋 إدراج {permissions.Length} صلاحية افتراضية...");

            var sql = @"
                INSERT OR IGNORE INTO Permissions (Name, Code, Description, Module, Category, CreatedDate)
                VALUES (@Name, @Code, @Description, @Module, @Category, CURRENT_TIMESTAMP)";

            int insertedCount = 0;
            foreach (var permission in permissions)
            {
                var rowsAffected = await dataAccess.ExecuteAsync(sql, permission);
                if (rowsAffected > 0)
                {
                    insertedCount++;
                    Console.WriteLine($"     ✅ تم إدراج صلاحية: {permission.Name} ({permission.Code})");
                }
                else
                {
                    Console.WriteLine($"     ℹ️ صلاحية موجودة: {permission.Name} ({permission.Code})");
                }
            }

            Console.WriteLine($"   📊 تم إدراج {insertedCount} صلاحية جديدة من أصل {permissions.Length}");
        }

        /// <summary>
        /// إنشاء مستخدم مدير افتراضي
        /// </summary>
        private async Task CreateDefaultAdminUserAsync(Smart_DataAccess dataAccess)
        {
            // التحقق من وجود المستخدم
            var existsQuery = "SELECT COUNT(*) FROM Users WHERE Username = @Username";
            var exists = await dataAccess.ExecuteScalarAsync<int>(existsQuery, new { Username = "admin" }) > 0;

            if (!exists)
            {
                // إنشاء كلمة مرور مشفرة بسيطة للاختبار
                var salt = "admin_salt_123";
                var passwordHash = Convert.ToBase64String(System.Text.Encoding.UTF8.GetBytes("1" + salt));

                var sql = @"
                    INSERT INTO Users (Username, Email, PasswordHash, Salt, FirstName, LastName, FullName,
                                     Department, Position, IsActive, CreatedDate)
                    VALUES (@Username, @Email, @PasswordHash, @Salt, @FirstName, @LastName, @FullName,
                            @Department, @Position, @IsActive, CURRENT_TIMESTAMP)";

                var adminUser = new
                {
                    Username = "admin",
                    Email = "<EMAIL>",
                    PasswordHash = passwordHash,
                    Salt = salt,
                    FirstName = "مدير",
                    LastName = "النظام",
                    FullName = "مدير النظام",
                    Department = "تقنية المعلومات",
                    Position = "مدير النظام",
                    IsActive = true
                };

                await dataAccess.ExecuteAsync(sql, adminUser);
                Console.WriteLine("   ✅ تم إنشاء مستخدم admin (كلمة المرور: 1)");

                // الحصول على ID المستخدم المُنشأ
                var getUserIdQuery = "SELECT Id FROM Users WHERE Username = @Username";
                var adminUserId = await dataAccess.ExecuteScalarAsync<int>(getUserIdQuery, new { Username = "admin" });

                // منح جميع الصلاحيات للمستخدم المدير
                await GrantAllPermissionsToAdminAsync(dataAccess, adminUserId);
                Console.WriteLine("   ✅ تم منح جميع الصلاحيات لمستخدم admin");
            }
            else
            {
                Console.WriteLine("   ℹ️ مستخدم admin موجود بالفعل");

                // التحقق من وجود الصلاحيات وإضافتها إذا كانت مفقودة
                var getUserIdQuery = "SELECT Id FROM Users WHERE Username = @Username";
                var adminUserId = await dataAccess.ExecuteScalarAsync<int>(getUserIdQuery, new { Username = "admin" });

                var hasPermissionsQuery = "SELECT COUNT(*) FROM UserPermissions WHERE UserId = @UserId";
                var hasPermissions = await dataAccess.ExecuteScalarAsync<int>(hasPermissionsQuery, new { UserId = adminUserId }) > 0;

                if (!hasPermissions)
                {
                    await GrantAllPermissionsToAdminAsync(dataAccess, adminUserId);
                    Console.WriteLine("   ✅ تم منح الصلاحيات المفقودة لمستخدم admin");
                }
                else
                {
                    Console.WriteLine("   ℹ️ مستخدم admin لديه صلاحيات بالفعل");
                }
            }
        }

        /// <summary>
        /// منح جميع الصلاحيات للمستخدم المدير
        /// </summary>
        private async Task GrantAllPermissionsToAdminAsync(Smart_DataAccess dataAccess, int adminUserId)
        {
            try
            {
                Console.WriteLine("   🔐 بدء منح الصلاحيات الكاملة للمدير...");

                // الحصول على جميع الصلاحيات المتاحة
                var getAllPermissionsQuery = "SELECT Id, Code, Name FROM Permissions WHERE IsActive = 1";
                var permissions = await dataAccess.GetDataAsync<dynamic>(getAllPermissionsQuery);

                int grantedCount = 0;
                int skippedCount = 0;

                foreach (var permission in permissions)
                {
                    // التحقق من وجود الصلاحية للمستخدم
                    var hasPermissionQuery = @"
                        SELECT COUNT(*) FROM UserPermissions
                        WHERE UserId = @UserId AND PermissionId = @PermissionId";

                    var hasPermission = await dataAccess.ExecuteScalarAsync<int>(hasPermissionQuery,
                        new { UserId = adminUserId, PermissionId = permission.Id }) > 0;

                    if (!hasPermission)
                    {
                        // منح الصلاحية
                        var grantPermissionSql = @"
                            INSERT INTO UserPermissions (UserId, PermissionId, IsGranted, GrantedDate, GrantedBy, IsActive)
                            VALUES (@UserId, @PermissionId, @IsGranted, @GrantedDate, @GrantedBy, @IsActive)";

                        await dataAccess.ExecuteAsync(grantPermissionSql, new
                        {
                            UserId = adminUserId,
                            PermissionId = permission.Id,
                            IsGranted = true,
                            GrantedDate = DateTime.Now,
                            GrantedBy = adminUserId, // المدير يمنح لنفسه
                            IsActive = true
                        });

                        grantedCount++;
                        Console.WriteLine($"     ✅ تم منح صلاحية: {permission.Name} ({permission.Code})");
                    }
                    else
                    {
                        skippedCount++;
                        Console.WriteLine($"     ℹ️ صلاحية موجودة: {permission.Name} ({permission.Code})");
                    }
                }

                Console.WriteLine($"   📊 ملخص منح الصلاحيات:");
                Console.WriteLine($"     ✅ صلاحيات جديدة: {grantedCount}");
                Console.WriteLine($"     ℹ️ صلاحيات موجودة: {skippedCount}");
                Console.WriteLine($"     📈 إجمالي الصلاحيات: {grantedCount + skippedCount}");

                // التحقق النهائي من الصلاحيات
                var totalPermissionsQuery = @"
                    SELECT COUNT(*) FROM UserPermissions
                    WHERE UserId = @UserId AND IsGranted = 1 AND IsActive = 1";

                var totalPermissions = await dataAccess.ExecuteScalarAsync<int>(totalPermissionsQuery,
                    new { UserId = adminUserId });

                Console.WriteLine($"   🎯 إجمالي صلاحيات المدير النشطة: {totalPermissions}");

                // منح صلاحية مدير النظام الخاصة (إذا كانت موجودة)
                await GrantSystemAdminPermissionAsync(dataAccess, adminUserId);

            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ❌ خطأ في منح الصلاحيات: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// منح صلاحية مدير النظام الخاصة
        /// </summary>
        private async Task GrantSystemAdminPermissionAsync(Smart_DataAccess dataAccess, int adminUserId)
        {
            try
            {
                // البحث عن صلاحية مدير النظام
                var systemAdminPermissionQuery = "SELECT Id FROM Permissions WHERE Code = @Code";
                var systemAdminPermissionId = await dataAccess.ExecuteScalarAsync<int?>(systemAdminPermissionQuery,
                    new { Code = "SYSTEM_ADMIN" });

                if (systemAdminPermissionId.HasValue)
                {
                    // التحقق من وجود الصلاحية
                    var hasSystemAdminQuery = @"
                        SELECT COUNT(*) FROM UserPermissions
                        WHERE UserId = @UserId AND PermissionId = @PermissionId AND IsGranted = 1 AND IsActive = 1";

                    var hasSystemAdmin = await dataAccess.ExecuteScalarAsync<int>(hasSystemAdminQuery,
                        new { UserId = adminUserId, PermissionId = systemAdminPermissionId.Value }) > 0;

                    if (!hasSystemAdmin)
                    {
                        // منح صلاحية مدير النظام
                        var grantSystemAdminSql = @"
                            INSERT OR REPLACE INTO UserPermissions (UserId, PermissionId, IsGranted, GrantedDate, GrantedBy, IsActive)
                            VALUES (@UserId, @PermissionId, @IsGranted, @GrantedDate, @GrantedBy, @IsActive)";

                        await dataAccess.ExecuteAsync(grantSystemAdminSql, new
                        {
                            UserId = adminUserId,
                            PermissionId = systemAdminPermissionId.Value,
                            IsGranted = true,
                            GrantedDate = DateTime.Now,
                            GrantedBy = adminUserId,
                            IsActive = true
                        });

                        Console.WriteLine("   🔑 تم منح صلاحية مدير النظام الخاصة (SYSTEM_ADMIN)");
                    }
                    else
                    {
                        Console.WriteLine("   ℹ️ صلاحية مدير النظام موجودة بالفعل");
                    }
                }
                else
                {
                    Console.WriteLine("   ⚠️ صلاحية مدير النظام (SYSTEM_ADMIN) غير موجودة في الجدول");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ❌ خطأ في منح صلاحية مدير النظام: {ex.Message}");
            }
        }

        /// <summary>
        /// إنشاء مستخدم تجريبي
        /// </summary>
        private async Task CreateDefaultTestUserAsync(Smart_DataAccess dataAccess)
        {
            // التحقق من وجود المستخدم
            var existsQuery = "SELECT COUNT(*) FROM Users WHERE Username = @Username";
            var exists = await dataAccess.ExecuteScalarAsync<int>(existsQuery, new { Username = "test" }) > 0;

            if (!exists)
            {
                // إنشاء كلمة مرور مشفرة بسيطة للاختبار
                var salt = "test_salt_456";
                var passwordHash = Convert.ToBase64String(System.Text.Encoding.UTF8.GetBytes("test123" + salt));

                var sql = @"
                    INSERT INTO Users (Username, Email, PasswordHash, Salt, FirstName, LastName, FullName,
                                     Department, Position, IsActive, CreatedDate)
                    VALUES (@Username, @Email, @PasswordHash, @Salt, @FirstName, @LastName, @FullName,
                            @Department, @Position, @IsActive, CURRENT_TIMESTAMP)";

                var testUser = new
                {
                    Username = "test",
                    Email = "<EMAIL>",
                    PasswordHash = passwordHash,
                    Salt = salt,
                    FirstName = "مستخدم",
                    LastName = "تجريبي",
                    FullName = "مستخدم تجريبي",
                    Department = "الاختبار",
                    Position = "مختبر",
                    IsActive = true
                };

                await dataAccess.ExecuteAsync(sql, testUser);
                Console.WriteLine("   ✅ تم إنشاء مستخدم test (كلمة المرور: test123)");
            }
        }

        #endregion
    }
}
