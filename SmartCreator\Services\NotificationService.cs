using SmartCreator.Data;
using SmartCreator.Entities;
using SmartCreator.Models;
using SmartCreator.ViewModels;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace SmartCreator.Services
{
    /// <summary>
    /// خدمة إدارة الإشعارات
    /// </summary>
    public class NotificationService
    {
        private readonly Smart_DataAccess _dataAccess;

        public NotificationService(Smart_DataAccess dataAccess)
        {
            _dataAccess = dataAccess;
        }

        public NotificationService()
        {
            _dataAccess = new Smart_DataAccess();
        }

        /// <summary>
        /// إنشاء إشعار جديد
        /// </summary>
        public async Task<bool> CreateNotificationAsync(string title, string message, string type = NotificationTypes.INFO,
            string priority = NotificationPriorities.NORMAL, int? userId = null, string username = null,
            string module = null, int? entityId = null, string entityType = null, string actionUrl = null,
            DateTime? expiryDate = null, string additionalData = null)
        {
            try
            {
                var notification = new Notification
                {
                    Title = title,
                    Message = message,
                    Type = type,
                    Priority = priority,
                    UserId = userId,
                    Username = username,
                    Module = module,
                    EntityId = entityId,
                    EntityType = entityType,
                    ActionUrl = actionUrl,
                    ExpiryDate = expiryDate,
                    AdditionalData = additionalData,
                    CreatedDate = DateTime.Now,
                    IsRead = false,
                    IsActive = true,
                    Rb = Global_Variable.Mk_resources?.RB_code ?? ""
                };

                var query = @"
                    INSERT INTO Notifications
                    (Title, Message, Type, Priority, UserId, Username, Module, EntityId, EntityType,
                     ActionUrl, ExpiryDate, AdditionalData, CreatedDate, IsRead, IsActive, Rb)
                    VALUES
                    (@Title, @Message, @Type, @Priority, @UserId, @Username, @Module, @EntityId, @EntityType,
                     @ActionUrl, @ExpiryDate, @AdditionalData, @CreatedDate, @IsRead, @IsActive, @Rb)";

                var result = await Task.Run(() => _dataAccess.Execute(query, notification));
                return result > 0;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إنشاء الإشعار: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// الحصول على جميع الإشعارات
        /// </summary>
        public async Task<List<Notification>> GetAllNotificationsAsync(int? userId = null, bool includeRead = true)
        {
            try
            {
                var whereClause = "WHERE IsActive = 1";

                if (userId.HasValue)
                    whereClause += $" AND (UserId = {userId} OR UserId IS NULL)";

                if (!includeRead)
                    whereClause += " AND IsRead = 0";

                var query = $@"
                    SELECT * FROM Notifications
                    {whereClause}
                    ORDER BY CreatedDate DESC";

                return await Task.Run(() => _dataAccess.GetListAnyDB<Notification>(query));
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في جلب الإشعارات: {ex.Message}");
                return new List<Notification>();
            }
        }

        /// <summary>
        /// الحصول على الإشعارات غير المقروءة
        /// </summary>
        public async Task<List<Notification>> GetUnreadNotificationsAsync(int? userId = null)
        {
            return await GetAllNotificationsAsync(userId, false);
        }

        /// <summary>
        /// عدد الإشعارات غير المقروءة
        /// </summary>
        public async Task<int> GetUnreadCountAsync(int? userId = null)
        {
            try
            {
                var whereClause = "WHERE IsActive = 1 AND IsRead = 0";

                if (userId.HasValue)
                    whereClause += $" AND (UserId = {userId} OR UserId IS NULL)";

                var query = $"SELECT COUNT(*) FROM Notifications {whereClause}";

                return await Task.Run(() => (int)_dataAccess.Get_int_FromDB(query));
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في عد الإشعارات غير المقروءة: {ex.Message}");
                return 0;
            }
        }

        /// <summary>
        /// تحديد إشعار كمقروء
        /// </summary>
        public async Task<bool> MarkAsReadAsync(int notificationId)
        {
            try
            {
                var query = @"
                    UPDATE Notifications
                    SET IsRead = 1, ReadDate = @ReadDate
                    WHERE Id = @Id";

                var parameters = new { Id = notificationId, ReadDate = DateTime.Now };
                var result = await Task.Run(() => _dataAccess.Execute(query, parameters));
                return result > 0;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديد الإشعار كمقروء: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// تحديد جميع الإشعارات كمقروءة
        /// </summary>
        public async Task<bool> MarkAllAsReadAsync(int? userId = null)
        {
            try
            {
                var whereClause = "WHERE IsRead = 0";

                if (userId.HasValue)
                    whereClause += $" AND (UserId = {userId} OR UserId IS NULL)";

                var query = $@"
                    UPDATE Notifications
                    SET IsRead = 1, ReadDate = @ReadDate
                    {whereClause}";

                var parameters = new { ReadDate = DateTime.Now };
                var result = await Task.Run(() => _dataAccess.Execute(query, parameters));
                return result > 0;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديد جميع الإشعارات كمقروءة: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// حذف إشعار
        /// </summary>
        public async Task<bool> DeleteNotificationAsync(int notificationId)
        {
            try
            {
                var query = "UPDATE Notifications SET IsActive = 0 WHERE Id = @Id";
                var result = await Task.Run(() => _dataAccess.Execute(query, new { Id = notificationId }));
                return result > 0;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في حذف الإشعار: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// حذف الإشعارات القديمة
        /// </summary>
        public async Task<bool> CleanupOldNotificationsAsync(int daysToKeep = 30)
        {
            try
            {
                var cutoffDate = DateTime.Now.AddDays(-daysToKeep);
                var query = "UPDATE Notifications SET IsActive = 0 WHERE CreatedDate < @CutoffDate";
                var result = await Task.Run(() => _dataAccess.Execute(query, new { CutoffDate = cutoffDate }));
                return result > 0;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تنظيف الإشعارات القديمة: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// البحث في الإشعارات
        /// </summary>
        public async Task<List<Notification>> SearchNotificationsAsync(string searchTerm, int? userId = null)
        {
            try
            {
                var whereClause = "WHERE IsActive = 1 AND (Title LIKE @SearchTerm OR Message LIKE @SearchTerm)";

                if (userId.HasValue)
                    whereClause += $" AND (UserId = {userId} OR UserId IS NULL)";

                var query = $@"
                    SELECT * FROM Notifications
                    {whereClause}
                    ORDER BY CreatedDate DESC";

                // نحتاج لتعديل الاستعلام لأن Smart_DataAccess لا يدعم parameters في GetListAnyDB
                var searchQuery = query.Replace("@SearchTerm", $"'%{searchTerm}%'");
                return await Task.Run(() => _dataAccess.GetListAnyDB<Notification>(searchQuery));
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في البحث في الإشعارات: {ex.Message}");
                return new List<Notification>();
            }
        }

        /// <summary>
        /// إنشاء إشعارات النظام التلقائية
        /// </summary>
        public async Task CreateSystemNotificationsAsync()
        {
            try
            {
                // إشعار ترحيب
                await CreateNotificationAsync(
                    "مرحباً بك في Smart Creator",
                    "تم تسجيل دخولك بنجاح إلى النظام. نتمنى لك تجربة ممتعة!",
                    NotificationTypes.SUCCESS,
                    NotificationPriorities.NORMAL,
                    module: "System"
                );

                // إشعار حول النسخة
                if (Properties.Settings.Default.isActive == false)
                {
                    await CreateNotificationAsync(
                        "نسخة تجريبية",
                        $"أنت تستخدم النسخة التجريبية. متبقي {Properties.Settings.Default.rb_active_exp} يوم",
                        NotificationTypes.WARNING,
                        NotificationPriorities.MEDIUM,
                        module: "License"
                    );
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إنشاء إشعارات النظام: {ex.Message}");
            }
        }
    }
}
