using SmartCreator.Entities;
using SmartCreator.RJControls;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;

namespace SmartCreator.Forms.Security
{
    /// <summary>
    /// ملف اختبار لـ RJDataGridView في نظام الصلاحيات
    /// </summary>
    public static class TestRJDataGridView
    {
        /// <summary>
        /// اختبار شامل لـ RJDataGridView في نظام الصلاحيات
        /// </summary>
        public static void RunAllTests()
        {
            try
            {
                Console.WriteLine("🧪 بدء اختبار RJDataGridView في نظام الصلاحيات...\n");

                // 1. اختبار إعداد DataGridView للمستخدمين
                TestUsersDataGridView();
                Console.WriteLine();

                // 2. اختبار إعداد DataGridView للأنشطة
                TestActivitiesDataGridView();
                Console.WriteLine();

                // 3. اختبار SecurityDataGridHelper
                TestSecurityDataGridHelper();
                Console.WriteLine();

                // 4. اختبار التفاعل والتأثيرات
                TestInteractionEffects();
                Console.WriteLine();

                // 5. اختبار البحث والفلترة
                TestSearchAndFiltering();
                Console.WriteLine();

                // 6. اختبار التصدير
                TestExportFunctionality();
                Console.WriteLine();

                Console.WriteLine("🎉 انتهت جميع اختبارات RJDataGridView بنجاح!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في اختبار RJDataGridView: {ex.Message}");
                Console.WriteLine($"تفاصيل الخطأ: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// اختبار إعداد DataGridView للمستخدمين
        /// </summary>
        private static void TestUsersDataGridView()
        {
            Console.WriteLine("👥 اختبار إعداد DataGridView للمستخدمين:");

            try
            {
                var dgv = new RJDataGridView();

                // إعداد DataGridView باستخدام SecurityDataGridHelper
                SecurityDataGridHelper.SetupUsersDataGridView(dgv);

                // التحقق من الأعمدة
                Console.WriteLine($"   ✅ عدد الأعمدة: {dgv.Columns.Count}");

                var expectedColumns = new[] { "Id", "Username", "FullName", "Email", "Role", "IsActive", "LastLogin", "CreatedAt" };
                foreach (var columnName in expectedColumns)
                {
                    var column = dgv.Columns[columnName];
                    if (column != null)
                    {
                        Console.WriteLine($"   ✅ العمود '{column.HeaderText}' موجود");
                    }
                    else
                    {
                        Console.WriteLine($"   ❌ العمود '{columnName}' مفقود");
                    }
                }

                // التحقق من التنسيق
                Console.WriteLine($"   ✅ لون رأس الأعمدة: {dgv.ColumnHeadersDefaultCellStyle.BackColor}");
                Console.WriteLine($"   ✅ خط رأس الأعمدة: {dgv.ColumnHeadersDefaultCellStyle.Font.Name}");
                Console.WriteLine($"   ✅ اتجاه النص: {dgv.RightToLeft}");
                Console.WriteLine($"   ✅ ارتفاع الصف: {dgv.RowTemplate.Height}");

                // إضافة بيانات تجريبية
                var testUsers = GenerateTestUsers();
                dgv.DataSource = testUsers;

                Console.WriteLine($"   ✅ تم إضافة {testUsers.Count} مستخدم تجريبي");
                Console.WriteLine("✅ اختبار إعداد DataGridView للمستخدمين نجح!");

                dgv.Dispose();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في اختبار DataGridView للمستخدمين: {ex.Message}");
            }
        }

        /// <summary>
        /// اختبار إعداد DataGridView للأنشطة
        /// </summary>
        private static void TestActivitiesDataGridView()
        {
            Console.WriteLine("📊 اختبار إعداد DataGridView للأنشطة:");

            try
            {
                var dgv = new RJDataGridView();

                // إعداد DataGridView باستخدام SecurityDataGridHelper
                SecurityDataGridHelper.SetupActivitiesDataGridView(dgv);

                // التحقق من الأعمدة
                Console.WriteLine($"   ✅ عدد الأعمدة: {dgv.Columns.Count}");

                var expectedColumns = new[] { "Id", "Timestamp", "Username", "Module", "Action", "Severity", "Description", "IpAddress" };
                foreach (var columnName in expectedColumns)
                {
                    var column = dgv.Columns[columnName];
                    if (column != null)
                    {
                        Console.WriteLine($"   ✅ العمود '{column.HeaderText}' موجود");
                    }
                    else
                    {
                        Console.WriteLine($"   ❌ العمود '{columnName}' مفقود");
                    }
                }

                // إضافة بيانات تجريبية
                var testActivities = GenerateTestActivities();
                dgv.DataSource = testActivities;

                Console.WriteLine($"   ✅ تم إضافة {testActivities.Count} نشاط تجريبي");
                Console.WriteLine("✅ اختبار إعداد DataGridView للأنشطة نجح!");

                dgv.Dispose();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في اختبار DataGridView للأنشطة: {ex.Message}");
            }
        }

        /// <summary>
        /// اختبار SecurityDataGridHelper
        /// </summary>
        private static void TestSecurityDataGridHelper()
        {
            Console.WriteLine("🛡️ اختبار SecurityDataGridHelper:");

            try
            {
                var dgv = new RJDataGridView();

                // اختبار التنسيق الأساسي
                SecurityDataGridHelper.ApplyBasicStyling(dgv);
                Console.WriteLine("   ✅ تم تطبيق التنسيق الأساسي");

                // اختبار إضافة التأثيرات
                SecurityDataGridHelper.AddInteractionEffects(dgv);
                Console.WriteLine("   ✅ تم إضافة تأثيرات التفاعل");

                // اختبار الألوان
                Console.WriteLine($"   ✅ لون الرأس: {SecurityDataGridHelper.HeaderBackColor}");
                Console.WriteLine($"   ✅ لون الخلايا: {SecurityDataGridHelper.CellBackColor}");
                Console.WriteLine($"   ✅ لون التحديد: {SecurityDataGridHelper.SelectionBackColor}");
                Console.WriteLine($"   ✅ لون الشبكة: {SecurityDataGridHelper.GridColor}");

                // اختبار الخطوط
                Console.WriteLine($"   ✅ خط الرأس: {SecurityDataGridHelper.HeaderFont.Name} - {SecurityDataGridHelper.HeaderFont.Size}pt");
                Console.WriteLine($"   ✅ خط الخلايا: {SecurityDataGridHelper.CellFont.Name} - {SecurityDataGridHelper.CellFont.Size}pt");

                Console.WriteLine("✅ اختبار SecurityDataGridHelper نجح!");

                dgv.Dispose();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في اختبار SecurityDataGridHelper: {ex.Message}");
            }
        }

        /// <summary>
        /// اختبار التفاعل والتأثيرات
        /// </summary>
        private static void TestInteractionEffects()
        {
            Console.WriteLine("🎨 اختبار التفاعل والتأثيرات:");

            try
            {
                var dgv = new RJDataGridView();
                SecurityDataGridHelper.SetupUsersDataGridView(dgv);
                SecurityDataGridHelper.AddInteractionEffects(dgv);

                // إضافة بيانات تجريبية
                var testUsers = GenerateTestUsers();
                dgv.DataSource = testUsers;

                // محاكاة أحداث التفاعل
                Console.WriteLine("   ✅ تم إعداد أحداث مرور الماوس");
                Console.WriteLine("   ✅ تم إعداد أحداث التحديد");
                Console.WriteLine("   ✅ تم إعداد تأثيرات الألوان");

                // اختبار تغيير الألوان عند التحديد
                if (dgv.Rows.Count > 0)
                {
                    dgv.Rows[0].Selected = true;
                    Console.WriteLine("   ✅ تم اختبار تحديد الصف");
                }

                Console.WriteLine("✅ اختبار التفاعل والتأثيرات نجح!");

                dgv.Dispose();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في اختبار التفاعل والتأثيرات: {ex.Message}");
            }
        }

        /// <summary>
        /// اختبار البحث والفلترة
        /// </summary>
        private static void TestSearchAndFiltering()
        {
            Console.WriteLine("🔍 اختبار البحث والفلترة:");

            try
            {
                var dgv = new RJDataGridView();
                SecurityDataGridHelper.SetupUsersDataGridView(dgv);

                // إضافة بيانات تجريبية
                var testUsers = GenerateTestUsers();
                dgv.DataSource = testUsers;

                var initialRowCount = dgv.Rows.Count;
                Console.WriteLine($"   ✅ عدد الصفوف الأولي: {initialRowCount}");

                // اختبار البحث
                SecurityDataGridHelper.ApplySearchFilter(dgv, "admin");
                var filteredCount = dgv.Rows.Cast<DataGridViewRow>().Count(r => r.Visible);
                Console.WriteLine($"   ✅ عدد الصفوف بعد البحث عن 'admin': {filteredCount}");

                // إعادة تعيين الفلتر
                SecurityDataGridHelper.ApplySearchFilter(dgv, "");
                var resetCount = dgv.Rows.Cast<DataGridViewRow>().Count(r => r.Visible);
                Console.WriteLine($"   ✅ عدد الصفوف بعد إعادة التعيين: {resetCount}");

                Console.WriteLine("✅ اختبار البحث والفلترة نجح!");

                dgv.Dispose();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في اختبار البحث والفلترة: {ex.Message}");
            }
        }

        /// <summary>
        /// اختبار التصدير
        /// </summary>
        private static void TestExportFunctionality()
        {
            Console.WriteLine("📤 اختبار التصدير:");

            try
            {
                var dgv = new RJDataGridView();
                SecurityDataGridHelper.SetupUsersDataGridView(dgv);

                // إضافة بيانات تجريبية
                var testUsers = GenerateTestUsers();
                dgv.DataSource = testUsers;

                // اختبار التصدير إلى CSV
                var tempFile = System.IO.Path.GetTempFileName() + ".csv";
                SecurityDataGridHelper.ExportToCSV(dgv, tempFile);

                if (System.IO.File.Exists(tempFile))
                {
                    var fileSize = new System.IO.FileInfo(tempFile).Length;
                    Console.WriteLine($"   ✅ تم إنشاء ملف CSV بحجم {fileSize} بايت");

                    // قراءة أول سطرين للتحقق
                    var lines = System.IO.File.ReadAllLines(tempFile);
                    if (lines.Length > 0)
                    {
                        Console.WriteLine($"   ✅ رأس الأعمدة: {lines[0]}");
                        if (lines.Length > 1)
                        {
                            Console.WriteLine($"   ✅ أول صف بيانات: {lines[1]}");
                        }
                    }

                    // حذف الملف المؤقت
                    System.IO.File.Delete(tempFile);
                    Console.WriteLine("   ✅ تم حذف الملف المؤقت");
                }
                else
                {
                    Console.WriteLine("   ❌ فشل في إنشاء ملف CSV");
                }

                Console.WriteLine("✅ اختبار التصدير نجح!");

                dgv.Dispose();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في اختبار التصدير: {ex.Message}");
            }
        }

        /// <summary>
        /// إنشاء مستخدمين تجريبيين
        /// </summary>
        private static List<User> GenerateTestUsers()
        {
            return new List<User>
            {
                new User
                {
                    Id = 1,
                    Username = "admin",
                    FullName = "المدير العام",
                    Email = "<EMAIL>",
                    IsActive = true,
                    LastLoginDate = DateTime.Now.AddHours(-2),
                    CreatedDate = DateTime.Now.AddDays(-30)
                },
                new User
                {
                    Id = 2,
                    Username = "user1",
                    FullName = "أحمد محمد",
                    Email = "<EMAIL>",
                    IsActive = true,
                    LastLoginDate = DateTime.Now.AddDays(-1),
                    CreatedDate = DateTime.Now.AddDays(-15)
                },
                new User
                {
                    Id = 3,
                    Username = "user2",
                    FullName = "فاطمة علي",
                    Email = "<EMAIL>",
                    IsActive = false,
                    LastLoginDate = DateTime.Now.AddDays(-7),
                    CreatedDate = DateTime.Now.AddDays(-10)
                }
            };
        }

        /// <summary>
        /// إنشاء أنشطة تجريبية
        /// </summary>
        private static List<UserActivity> GenerateTestActivities()
        {
            return new List<UserActivity>
            {
                new UserActivity
                {
                    Id = 1,
                    Timestamp = DateTime.Now.AddMinutes(-30),
                    Username = "admin",
                    Module = "إدارة المستخدمين",
                    Action = "إضافة مستخدم",
                    Severity = "معلومات",
                    Description = "تم إضافة مستخدم جديد: user3",
                    IpAddress = "*************"
                },
                new UserActivity
                {
                    Id = 2,
                    Timestamp = DateTime.Now.AddHours(-1),
                    Username = "user1",
                    Module = "المحاسبة",
                    Action = "إنشاء قيد",
                    Severity = "معلومات",
                    Description = "تم إنشاء قيد محاسبي جديد",
                    IpAddress = "*************"
                },
                new UserActivity
                {
                    Id = 3,
                    Timestamp = DateTime.Now.AddHours(-2),
                    Username = "user2",
                    Module = "النظام",
                    Action = "محاولة دخول فاشلة",
                    Severity = "تحذير",
                    Description = "محاولة دخول بكلمة مرور خاطئة",
                    IpAddress = "*************"
                }
            };
        }

        /// <summary>
        /// اختبار سريع
        /// </summary>
        public static void QuickTest()
        {
            try
            {
                Console.WriteLine("⚡ اختبار سريع لـ RJDataGridView...");

                var dgv = new RJDataGridView();
                SecurityDataGridHelper.SetupUsersDataGridView(dgv);

                var testUsers = GenerateTestUsers();
                dgv.DataSource = testUsers;

                Console.WriteLine($"✅ تم إعداد DataGridView مع {dgv.Columns.Count} أعمدة و {dgv.Rows.Count} صفوف");
                Console.WriteLine("✅ الاختبار السريع نجح!");

                dgv.Dispose();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في الاختبار السريع: {ex.Message}");
            }
        }
    }
}
