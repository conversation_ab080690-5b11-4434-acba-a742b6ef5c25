using System;

namespace SmartCreator.Models.Security
{
    /// <summary>
    /// ثوابت أنواع الأنشطة
    /// </summary>
    public static class ActivityTypes
    {
        public const string LOGIN = "تسجيل دخول";
        public const string LOGOUT = "تسجيل خروج";
        public const string CREATE = "إنشاء";
        public const string UPDATE = "تعديل";
        public const string DELETE = "حذف";
        public const string VIEW = "عرض";
        public const string EXPORT = "تصدير";
        public const string IMPORT = "استيراد";
        public const string BACKUP = "نسخ احتياطي";
        public const string RESTORE = "استعادة";
    }

    /// <summary>
    /// ثوابت إجراءات الأنشطة (alias لـ ActivityTypes)
    /// </summary>
    public static class ActivityActions
    {
        public const string USER_LOGIN = ActivityTypes.LOGIN;
        public const string USER_LOGOUT = ActivityTypes.LOGOUT;
        public const string USER_CREATE = ActivityTypes.CREATE;
        public const string USER_UPDATE = ActivityTypes.UPDATE;
        public const string USER_DELETE = ActivityTypes.DELETE;
        public const string USER_VIEW = ActivityTypes.VIEW;
        public const string DATA_EXPORT = ActivityTypes.EXPORT;
        public const string DATA_IMPORT = ActivityTypes.IMPORT;
        public const string SYSTEM_BACKUP = ActivityTypes.BACKUP;
        public const string SYSTEM_RESTORE = ActivityTypes.RESTORE;
    }

    /// <summary>
    /// ثوابت وحدات النظام
    /// </summary>
    public static class ActivityModules
    {
        public const string AUTHENTICATION = "المصادقة";
        public const string USER_MANAGEMENT = "إدارة المستخدمين";
        public const string PERMISSIONS = "الصلاحيات";
        public const string ACCOUNTING = "المحاسبة";
        public const string ACCOUNTS = "الحسابات";
        public const string JOURNAL_ENTRIES = "القيود";
        public const string REPORTS = "التقارير";
        public const string SYSTEM = "النظام";
        public const string HOTSPOT = "الهوت سبوت";
        public const string BROADBAND = "البرود باند";
        public const string DEVICES = "الأجهزة";
        public const string CARDS = "البطاقات";
        public const string SELLING_POINTS = "نقاط البيع";
    }

    /// <summary>
    /// ثوابت مستويات الخطورة
    /// </summary>
    public static class SeverityLevels
    {
        public const string INFO = "معلومات";
        public const string WARNING = "تحذير";
        public const string ERROR = "خطأ";
        public const string CRITICAL = "حرج";
        public const string DEBUG = "تصحيح";
    }

    /// <summary>
    /// ثوابت حالات النشاط
    /// </summary>
    public static class ActivityStatus
    {
        public const string SUCCESS = "نجح";
        public const string FAILED = "فشل";
        public const string PENDING = "معلق";
        public const string CANCELLED = "ملغي";
    }

    /// <summary>
    /// ثوابت أنواع الأحداث
    /// </summary>
    public static class EventTypes
    {
        public const string SECURITY = "أمني";
        public const string BUSINESS = "تجاري";
        public const string TECHNICAL = "تقني";
        public const string ADMINISTRATIVE = "إداري";
    }

    /// <summary>
    /// ثوابت مصادر الأنشطة
    /// </summary>
    public static class ActivitySources
    {
        public const string WEB_APPLICATION = "تطبيق ويب";
        public const string DESKTOP_APPLICATION = "تطبيق سطح المكتب";
        public const string MOBILE_APPLICATION = "تطبيق موبايل";
        public const string API = "واجهة برمجية";
        public const string SYSTEM = "النظام";
        public const string SCHEDULED_TASK = "مهمة مجدولة";
    }

    /// <summary>
    /// ثوابت أولويات الأنشطة
    /// </summary>
    public static class ActivityPriorities
    {
        public const string LOW = "منخفض";
        public const string NORMAL = "عادي";
        public const string HIGH = "عالي";
        public const string URGENT = "عاجل";
    }

    /// <summary>
    /// ثوابت فترات الاحتفاظ بالأنشطة
    /// </summary>
    public static class RetentionPeriods
    {
        public const int DAYS_30 = 30;
        public const int DAYS_90 = 90;
        public const int DAYS_180 = 180;
        public const int DAYS_365 = 365;
        public const int DAYS_UNLIMITED = -1;
    }

    /// <summary>
    /// ثوابت تصنيفات البيانات
    /// </summary>
    public static class DataClassifications
    {
        public const string PUBLIC = "عام";
        public const string INTERNAL = "داخلي";
        public const string CONFIDENTIAL = "سري";
        public const string RESTRICTED = "مقيد";
    }

    /// <summary>
    /// ثوابت أنواع التنبيهات
    /// </summary>
    public static class AlertTypes
    {
        public const string EMAIL = "بريد إلكتروني";
        public const string SMS = "رسالة نصية";
        public const string PUSH_NOTIFICATION = "إشعار فوري";
        public const string SYSTEM_NOTIFICATION = "إشعار النظام";
    }

    /// <summary>
    /// ثوابت حالات التنبيهات
    /// </summary>
    public static class AlertStatuses
    {
        public const string PENDING = "معلق";
        public const string SENT = "مرسل";
        public const string DELIVERED = "تم التسليم";
        public const string FAILED = "فشل";
        public const string ACKNOWLEDGED = "تم الإقرار";
    }

    /// <summary>
    /// ثوابت أنواع المراجعة
    /// </summary>
    public static class AuditTypes
    {
        public const string SECURITY_AUDIT = "مراجعة أمنية";
        public const string COMPLIANCE_AUDIT = "مراجعة امتثال";
        public const string PERFORMANCE_AUDIT = "مراجعة أداء";
        public const string FINANCIAL_AUDIT = "مراجعة مالية";
    }

    /// <summary>
    /// ثوابت نتائج المراجعة
    /// </summary>
    public static class AuditResults
    {
        public const string PASSED = "نجح";
        public const string FAILED = "فشل";
        public const string WARNING = "تحذير";
        public const string NEEDS_REVIEW = "يحتاج مراجعة";
    }
}
