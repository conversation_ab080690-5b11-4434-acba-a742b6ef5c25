using SmartCreator.Data;
using SmartCreator.Entities;
using SmartCreator.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;

namespace SmartCreator.Services.Security
{
    /// <summary>
    /// خدمة إدارة المستخدمين
    /// </summary>
    public class UserService
    {
        private readonly Smart_DataAccess _dataAccess;
        private readonly UserActivityService _activityService;
        private readonly PermissionService _permissionService;

        public UserService(Smart_DataAccess dataAccess, UserActivityService activityService, PermissionService permissionService)
        {
            _dataAccess = dataAccess;
            _activityService = activityService;
            _permissionService = permissionService;
        }

        /// <summary>
        /// المصادقة على المستخدم
        /// </summary>
        public async Task<User> AuthenticateAsync(string username, string password)
        {
            try
            {
                var query = "SELECT * FROM Users WHERE Username = @Username AND IsActive = 1";
                var users = await _dataAccess.GetDataAsync<User>(query, new { Username = username });
                var user = users.FirstOrDefault();

                if (user == null)
                {
                    await _activityService.LogLoginAttemptAsync(null, username, false, "المستخدم غير موجود");
                    return null;
                }

                if (user.IsLocked)
                {
                    await _activityService.LogLoginAttemptAsync(user.Id, username, false, "الحساب مقفل");
                    return null;
                }      
                    
                if (! VerifyPassword(password, user.PasswordHash, user.Salt))
                {  
                    user.RecordFailedLogin();
                    await UpdateUserLastLoginAsync(user, user.Id);
                    //await UpdateUserAsync(user, user.Id);
                    await _activityService.LogLoginAttemptAsync(user.Id, username, false, "كلمة مرور خاطئة");
                    return null;
                }

                // تسجيل دخول ناجح
                user.RecordSuccessfulLogin();
                await UpdateUserLastLoginAsync(user, user.Id);

                //await UpdateUserAsync(user, user.Id);  
                await _activityService.LogLoginAttemptAsync(user.Id, username, true);

                // تحميل صلاحيات المستخدم
                var permissions = await _permissionService.GetUserPermissionsAsync(user.Id);
                user.Permissions = permissions.Select(p => p.Code).ToList();

                Global_Variable.CurrentUser = user;
                return user;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في المصادقة: {ex.Message}");
            }
        }

        /// <summary>
        /// الحصول على جميع المستخدمين
        /// </summary>
        public async Task<List<User>> GetAllUsersAsync()
        {
            try
            {
                var query = "SELECT * FROM Users ORDER BY FullName";
                return await _dataAccess.GetDataAsync<User>(query);
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في جلب المستخدمين: {ex.Message}");
            }
        }

        /// <summary>
        /// الحصول على مستخدم بالمعرف
        /// </summary>
        public async Task<User> GetUserByIdAsync(int id)
        {
            try
            {
                var query = "SELECT * FROM Users WHERE Id = @Id";
                var users = await _dataAccess.GetDataAsync<User>(query, new { Id = id });
                return users.FirstOrDefault();
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في جلب المستخدم: {ex.Message}");
            }
        }

        /// <summary>
        /// إنشاء مستخدم جديد
        /// </summary>
        public async Task<User> CreateUserAsync(User user, string password, int createdBy)
        {
            try
            {
                // التحقق من عدم تكرار اسم المستخدم
                var existingUser = await GetUserByUsernameAsync(user.Username);
                if (existingUser != null)
                {
                    throw new Exception("اسم المستخدم موجود مسبقاً");
                }

                // تشفير كلمة المرور
                var salt = GenerateSalt();
                var hashedPassword = HashPassword(password, salt);

                user.PasswordHash = hashedPassword;
                user.Salt = salt;
                user.CreatedDate = DateTime.Now;
                user.CreatedBy = createdBy;
                user.UpdateFullName();

                var query = @"
                    INSERT INTO Users
                    (Username, Email, PasswordHash, Salt, FirstName, LastName, FullName, Phone,
                     Department, Position, IsActive, CreatedDate, CreatedBy)
                    VALUES
                    (@Username, @Email, @PasswordHash, @Salt, @FirstName, @LastName, @FullName, @Phone,
                     @Department, @Position, @IsActive, @CreatedDate, @CreatedBy);
                    SELECT CAST(SCOPE_IDENTITY() as int)";

                var userId = await _dataAccess.ExecuteScalarAsync<int>(query, user);
                user.Id = userId;

                // تسجيل النشاط
                await _activityService.LogActivityAsync(createdBy, ActivityActions.USER_CREATED,
                    ActivityModules.USER_MANAGEMENT, $"إنشاء مستخدم جديد: {user.Username}", "User", userId);

                return user;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في إنشاء المستخدم: {ex.Message}");
            }
        }

        /// <summary>
        /// تحديث مستخدم
        /// </summary>
        public async Task<bool> UpdateUserAsync(User user, int updatedBy)
        {
            try
            {
                user.UpdatedDate = DateTime.Now;
                user.ModifiedDate = DateTime.Now;
                user.ModifiedBy = updatedBy;
                user.UpdateFullName();

                var query = @"
                    UPDATE Users SET
                        Email = @Email, FirstName = @FirstName, LastName = @LastName, FullName = @FullName,
                        Phone = @Phone, Department = @Department, Position = @Position, IsActive = @IsActive,
                        IsLocked = @IsLocked, FailedLoginAttempts = @FailedLoginAttempts,
                        LastLoginDate = @LastLoginDate, ModifiedDate = @ModifiedDate, ModifiedBy = @ModifiedBy,
                        Notes = @Notes
                    WHERE Id = @Id";

                var result = await _dataAccess.ExecuteAsync(query, user);

                // تسجيل النشاط
                await _activityService.LogActivityAsync(updatedBy, ActivityActions.USER_UPDATED,
                    ActivityModules.USER_MANAGEMENT, $"تحديث المستخدم: {user.Username}", "User", user.Id);

                return result > 0;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تحديث المستخدم: {ex.Message}");
            }
        }
        /// <summary>
        /// تحديث مستخدم
        /// </summary>
        public async Task<bool> UpdateUserLastLoginAsync(User user, int updatedBy)
        {
            try
            {
                user.LastLoginDate = DateTime.Now;
                //user.ModifiedBy = updatedBy;
                //user.UpdateFullName();

                var query = @"
                    UPDATE Users SET LastLoginDate = @LastLoginDate WHERE Id = @Id";

                var result = await _dataAccess.ExecuteAsync(query, user);

                //// تسجيل النشاط
                //await _activityService.LogActivityAsync(updatedBy, ActivityActions.USER_LOGIN,
                //    ActivityModules.USER_MANAGEMENT, $"تسجيل دخول المستخدم :  {user.Username}", "User", user.Id);

                return result > 0;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تحديث المستخدم: {ex.Message}");
            }
        }

        /// <summary>
        /// حذف مستخدم
        /// </summary>
        public async Task<bool> DeleteUserAsync(int userId, int deletedBy)
        {
            try
            {
                var user = await GetUserByIdAsync(userId);
                if (user == null)
                {
                    throw new Exception("المستخدم غير موجود");
                }

                // حذف صلاحيات المستخدم أولاً
                await _dataAccess.ExecuteAsync("DELETE FROM UserPermissions WHERE UserId = @UserId", new { UserId = userId });

                // حذف المستخدم
                var query = "DELETE FROM Users WHERE Id = @Id";
                var result = await _dataAccess.ExecuteAsync(query, new { Id = userId });

                // تسجيل النشاط
                await _activityService.LogActivityAsync(deletedBy, ActivityActions.USER_DELETED,
                    ActivityModules.USER_MANAGEMENT, $"حذف المستخدم: {user.Username}", "User", userId);

                return result > 0;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في حذف المستخدم: {ex.Message}");
            }
        }

        /// <summary>
        /// تغيير كلمة مرور المستخدم
        /// </summary>
        public async Task<bool> ChangePasswordAsync(int userId, string newPassword, int changedBy)
        {
            try
            {
                var salt = GenerateSalt();
                var hashedPassword = HashPassword(newPassword, salt);

                var query = @"
                    UPDATE Users SET
                        PasswordHash = @PasswordHash, Salt = @Salt,
                        LastPasswordChangeDate = @LastPasswordChangeDate,
                        MustChangePassword = 0
                    WHERE Id = @Id";

                var parameters = new
                {
                    Id = userId,
                    PasswordHash = hashedPassword,
                    Salt = salt,
                    LastPasswordChangeDate = DateTime.Now
                };

                var result = await _dataAccess.ExecuteAsync(query, parameters);

                // تسجيل النشاط
                await _activityService.LogActivityAsync(changedBy, ActivityActions.PASSWORD_CHANGED,
                    ActivityModules.USER_MANAGEMENT, $"تغيير كلمة مرور المستخدم", "User", userId);

                return result > 0;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تغيير كلمة المرور: {ex.Message}");
            }
        }

        /// <summary>
        /// قفل/إلغاء قفل حساب مستخدم
        /// </summary>
        public async Task<bool> ToggleUserLockAsync(int userId, bool isLocked, int changedBy)
        {
            try
            {
                var query = @"
                    UPDATE Users SET
                        IsLocked = @IsLocked,
                        FailedLoginAttempts = CASE WHEN @IsLocked = 0 THEN 0 ELSE FailedLoginAttempts END
                    WHERE Id = @Id";

                var parameters = new { Id = userId, IsLocked = isLocked };
                var result = await _dataAccess.ExecuteAsync(query, parameters);

                // تسجيل النشاط
                var action = isLocked ? ActivityActions.ACCOUNT_LOCKED : ActivityActions.ACCOUNT_UNLOCKED;
                var description = isLocked ? "قفل حساب المستخدم" : "إلغاء قفل حساب المستخدم";
                await _activityService.LogActivityAsync(changedBy, action,
                    ActivityModules.USER_MANAGEMENT, description, "User", userId);

                return result > 0;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تغيير حالة قفل المستخدم: {ex.Message}");
            }
        }

        /// <summary>
        /// الحصول على مستخدم باسم المستخدم
        /// </summary>
        public async Task<User> GetUserByUsernameAsync(string username)
        {
            try
            {
                var query = "SELECT * FROM Users WHERE Username = @Username";
                var users = await _dataAccess.GetDataAsync<User>(query, new { Username = username });
                return users.FirstOrDefault();
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في جلب المستخدم: {ex.Message}");
            }
        }

        /// <summary>
        /// توليد ملح عشوائي
        /// </summary>
        private string GenerateSalt()
        {
            var saltBytes = new byte[32];
            using (var rng = RandomNumberGenerator.Create())
            {
                rng.GetBytes(saltBytes);
            }
            return Convert.ToBase64String(saltBytes);
        }

        /// <summary>
        /// تشفير كلمة المرور
        /// </summary>
        private string HashPassword(string password, string salt)
        {
            
            var passwordHash = Convert.ToBase64String(System.Text.Encoding.UTF8.GetBytes(password + salt));
            return passwordHash;

            using (var sha256 = SHA256.Create())
            {
                var saltedPassword = password + salt;
                var hashedBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(saltedPassword));
                return Convert.ToBase64String(hashedBytes);
            }
        }

        /// <summary>
        /// التحقق من كلمة المرور
        /// </summary>
        private bool VerifyPassword(string password, string hashedPassword, string salt)
        {
            var computedHash = HashPassword(password, salt);
            return computedHash == hashedPassword;
        }
    }
}
