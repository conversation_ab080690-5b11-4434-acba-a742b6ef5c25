using SmartCreator.Entities.Accounting;
using SmartCreator.Properties;
using SmartCreator.RJControls;
using SmartCreator.RJForms;
using SmartCreator.Services;
using SmartCreator.Settings;
using System;
using System.Drawing;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace SmartCreator.Forms.Accounting
{
    /// <summary>
    /// نموذج تحديث المخزون
    /// </summary>
    public partial class Frm_InventoryUpdate : RJChildForm
    {
        private readonly InventoryService _inventoryService;
        private readonly Entities.Accounting.Product _product;

        public Frm_InventoryUpdate(Entities.Accounting.Product product)
        {
            InitializeComponent();
            _inventoryService = new InventoryService();
            _product = product ?? throw new ArgumentNullException(nameof(product));
            
            InitializeForm();
        }

        private void InitializeForm()
        {
            this.Text = "تحديث المخزون";
            this.Size = new Size(500, 400);
            this.StartPosition = FormStartPosition.CenterParent;
            
            // تطبيق الخطوط المخصصة
            if (UIAppearance.Language_ar)
            {
                ApplyArabicFonts();
            }

            // تحميل بيانات المنتج
            LoadProductData();
            
            // إعداد الأحداث
            SetupEvents();
        }

        private void ApplyArabicFonts()
        {
            var titleFont = Program.GetCustomFont(Resources.DroidKufi_Bold, 12, FontStyle.Bold);
            var labelFont = Program.GetCustomFont(Resources.DroidSansArabic, 10, FontStyle.Regular);
            var buttonFont = Program.GetCustomFont(Resources.DroidKufi_Bold, 10, FontStyle.Bold);

            lblTitle.Font = titleFont;
            lblProductName.Font = lblCurrentStock.Font = lblQuantity.Font = 
            lblReason.Font = lblUpdateType.Font = labelFont;
            btnSave.Font = btnCancel.Font = buttonFont;
        }

        private void LoadProductData()
        {
            lblProductName.Text = $"المنتج: {_product.Name} (الكود: {_product.Code})";
            lblCurrentStock.Text = $"المخزون الحالي: {_product.Stock}";
            
            // تلوين حالة المخزون
            if (_product.Stock <= 0)
            {
                lblCurrentStock.ForeColor = Color.Red;
            }
            else if (_product.NeedsReorder)
            {
                lblCurrentStock.ForeColor = Color.Orange;
            }
            else
            {
                lblCurrentStock.ForeColor = Color.Green;
            }

            // إعداد نوع التحديث
            rbIncrease.Checked = true;
            txtQuantity.Text = "0";
            txtReason.Text = "";
        }

        private void SetupEvents()
        {
            rbIncrease.CheckedChanged += UpdateType_CheckedChanged;
            rbDecrease.CheckedChanged += UpdateType_CheckedChanged;
            rbSet.CheckedChanged += UpdateType_CheckedChanged;
            txtQuantity.onTextChanged += TxtQuantity_TextChanged;
        }

        private void UpdateType_CheckedChanged(object sender, EventArgs e)
        {
            UpdatePreview();
        }

        private void TxtQuantity_TextChanged(object sender, EventArgs e)
        {
            UpdatePreview();
        }

        private void UpdatePreview()
        {
            if (int.TryParse(txtQuantity.Text, out int quantity))
            {
                int newStock = _product.Stock;
                
                if (rbIncrease.Checked)
                {
                    newStock = _product.Stock + quantity;
                    lblPreview.Text = $"المخزون بعد التحديث: {newStock} (+{quantity})";
                    lblPreview.ForeColor = Color.Green;
                }
                else if (rbDecrease.Checked)
                {
                    newStock = _product.Stock - quantity;
                    lblPreview.Text = $"المخزون بعد التحديث: {newStock} (-{quantity})";
                    lblPreview.ForeColor = newStock < 0 ? Color.Red : Color.Orange;
                }
                else if (rbSet.Checked)
                {
                    newStock = quantity;
                    var difference = newStock - _product.Stock;
                    lblPreview.Text = $"المخزون بعد التحديث: {newStock} ({(difference >= 0 ? "+" : "")}{difference})";
                    lblPreview.ForeColor = newStock < 0 ? Color.Red : Color.Blue;
                }

                // تحذير إذا كان المخزون سيصبح سالب
                if (newStock < 0)
                {
                    lblWarning.Text = "تحذير: المخزون سيصبح أقل من الصفر!";
                    lblWarning.Visible = true;
                    btnSave.Enabled = false;
                }
                else if (newStock <= _product.LowStockThreshold)
                {
                    lblWarning.Text = "تنبيه: المخزون سيصبح أقل من الحد الأدنى!";
                    lblWarning.Visible = true;
                    btnSave.Enabled = true;
                }
                else
                {
                    lblWarning.Visible = false;
                    btnSave.Enabled = true;
                }
            }
            else
            {
                lblPreview.Text = "أدخل كمية صحيحة";
                lblPreview.ForeColor = Color.Gray;
                lblWarning.Visible = false;
                btnSave.Enabled = false;
            }
        }

        private async void BtnSave_Click(object sender, EventArgs e)
        {
            if (!ValidateInput())
                return;

            try
            {
                btnSave.Enabled = false;
                btnSave.Text = "جاري الحفظ...";

                var quantity = int.Parse(txtQuantity.Text);
                var reason = txtReason.Text.Trim();
                
                if (string.IsNullOrEmpty(reason))
                {
                    reason = "تحديث مخزون يدوي";
                }

                bool success = false;

                if (rbIncrease.Checked)
                {
                    success = await _inventoryService.UpdateStockAsync(_product.Id, quantity, reason, true);
                }
                else if (rbDecrease.Checked)
                {
                    success = await _inventoryService.UpdateStockAsync(_product.Id, quantity, reason, false);
                }
                else if (rbSet.Checked)
                {
                    success = await _inventoryService.SetStockAsync(_product.Id, quantity, reason);
                }

                if (success)
                {
                    this.DialogResult = DialogResult.OK;
                    this.Close();
                }
            }
            catch (Exception ex)
            {
                RJMessageBox.Show($"خطأ في تحديث المخزون: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                btnSave.Enabled = true;
                btnSave.Text = "حفظ";
            }
        }

        private bool ValidateInput()
        {
            if (!int.TryParse(txtQuantity.Text, out int quantity))
            {
                RJMessageBox.Show("يرجى إدخال كمية صحيحة", "خطأ في البيانات", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtQuantity.Focus();
                return false;
            }

            if (quantity < 0)
            {
                RJMessageBox.Show("الكمية يجب أن تكون أكبر من أو تساوي الصفر", "خطأ في البيانات", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtQuantity.Focus();
                return false;
            }

            if (rbDecrease.Checked && quantity > _product.Stock)
            {
                RJMessageBox.Show("لا يمكن تقليل المخزون بكمية أكبر من المخزون الحالي", "خطأ في البيانات", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtQuantity.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(txtReason.Text))
            {
                var result = RJMessageBox.Show("لم تقم بإدخال سبب التحديث. هل تريد المتابعة؟", "تأكيد", 
                    MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                
                if (result == DialogResult.No)
                {
                    txtReason.Focus();
                    return false;
                }
            }

            return true;
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        private void Frm_InventoryUpdate_Load(object sender, EventArgs e)
        {
            txtQuantity.Focus();
        }
    }
}
