﻿using SmartCreator.Data;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SmartCreator.Entities.Accounting;
using Dapper;

namespace SmartCreator.Services.Products
{
    public class ProductService
    {
        private readonly DatabaseHelper _dbHelper;

        public ProductService()
        {
            _dbHelper = new DatabaseHelper();
        }

        public ProductService(DatabaseHelper dbHelper)
        {
            _dbHelper = dbHelper;
        }

        public async Task<List <Entities.Accounting.Product>> GetAllProductsAsync()
        {
            using var connection = _dbHelper.GetConnection();
            var products = await connection.QueryAsync<Product>("SELECT * FROM Products ORDER BY Name");
            return products.ToList();
        }

        public async Task<Product?> GetProductByIdAsync(int id)
        {
            using var connection = _dbHelper.GetConnection();
            return await connection.QueryFirstOrDefaultAsync<Product>("SELECT * FROM Products WHERE Id = @Id", new { Id = id });
        }

        public async Task<int> AddProductAsync(Product product)
        {
            using var connection = _dbHelper.GetConnection();
            product.CreatedDate = DateTime.Now;

            var sql = @"INSERT INTO Products (Name, Price, Stock, Description, CreatedDate)
                       VALUES (@Name, @Price, @Stock, @Description, @CreatedDate);
                       SELECT last_insert_rowid();";

            return await connection.QuerySingleAsync<int>(sql, product);
        }

        public async Task<bool> UpdateProductAsync(Product product)
        {
            using var connection = _dbHelper.GetConnection();

            var sql = @"UPDATE Products
                       SET Name = @Name, Price = @Price, Stock = @Stock, Description = @Description
                       WHERE Id = @Id";

            var rowsAffected = await connection.ExecuteAsync(sql, product);
            return rowsAffected > 0;
        }

        public async Task<bool> DeleteProductAsync(int id)
        {
            using var connection = _dbHelper.GetConnection();
            var rowsAffected = await connection.ExecuteAsync("DELETE FROM Products WHERE Id = @Id", new { Id = id });
            return rowsAffected > 0;
        }

        public async Task<bool> UpdateStockAsync(int productId, int newStock)
        {
            using var connection = _dbHelper.GetConnection();
            var rowsAffected = await connection.ExecuteAsync(
                "UPDATE Products SET Stock = @Stock WHERE Id = @Id",
                new { Stock = newStock, Id = productId });
            return rowsAffected > 0;
        }

        public async Task<bool> ReduceStockAsync(int productId, int quantity)
        {
            using var connection = _dbHelper.GetConnection();
            var rowsAffected = await connection.ExecuteAsync(
                "UPDATE Products SET Stock = Stock - @Quantity WHERE Id = @Id AND Stock >= @Quantity",
                new { Quantity = quantity, Id = productId });
            return rowsAffected > 0;
        }

        public async Task<List<Product>> SearchProductsAsync(string searchTerm)
        {
            using var connection = _dbHelper.GetConnection();
            var products = await connection.QueryAsync<Product>(
                "SELECT * FROM Products WHERE Name LIKE @SearchTerm OR Description LIKE @SearchTerm ORDER BY Name",
                new { SearchTerm = $"%{searchTerm}%" });
            return products.ToList();
        }

        public async Task<List<Product>> GetLowStockProductsAsync(int threshold = 10)
        {
            using var connection = _dbHelper.GetConnection();
            var products = await connection.QueryAsync<Product>(
                "SELECT * FROM Products WHERE Stock <= @Threshold ORDER BY Stock",
                new { Threshold = threshold });
            return products.ToList();
        }
    }

}
