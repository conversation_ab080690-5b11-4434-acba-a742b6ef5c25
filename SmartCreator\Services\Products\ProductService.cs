﻿using SmartCreator.Data;
using SmartCreator.Entities.Accounting;
using SmartCreator.Models;
using SmartCreator.RJControls;
using SmartCreator.Services.Security;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Dapper;

namespace SmartCreator.Services.Products
{
    /// <summary>
    /// خدمة إدارة المنتجات
    /// </summary>
    public class ProductService
    {
        private readonly Smart_DataAccess _dataAccess;
        private readonly UserActivityService _activityService;
        private readonly NotificationService _notificationService;

        public ProductService()
        {
            _dataAccess = new Smart_DataAccess();
            _activityService = new UserActivityService(_dataAccess);
            _notificationService = new NotificationService(_dataAccess);
        }

        /// <summary>
        /// الحصول على جميع المنتجات
        /// </summary>
        public async Task<List<Product>> GetAllProductsAsync()
        {
            try
            {
                var query = $"SELECT * FROM Product WHERE Rb='{Global_Variable.Mk_resources.RB_SN}' ORDER BY Name";
                var products = _dataAccess.Load<Product>(query);

                await _activityService.LogActivityAsync(
                    Global_Variable.CurrentUser?.Id ?? 1,
                    "عرض المنتجات",
                    "إدارة المنتجات",
                    $"تم عرض {products.Count} منتج",
                    "Product",
                    null,
                    null,
                    null,
                    "Info"
                );

                return products;
            }
            catch (Exception ex)
            {
                await _activityService.LogActivityAsync(
                    Global_Variable.CurrentUser?.Id ?? 1,
                    "خطأ في عرض المنتجات",
                    "إدارة المنتجات",
                    $"خطأ: {ex.Message}",
                    "Product",
                    null,
                    null,
                    null,
                    "Error",
                    false,
                    ex.Message
                );
                throw;
            }
        }

        /// <summary>
        /// الحصول على منتج بالمعرف
        /// </summary>
        public async Task<Product> GetProductByIdAsync(int id)
        {
            try
            {
                var product = _dataAccess.GetSingleById<Product>(id);

                if (product != null)
                {
                    await _activityService.LogActivityAsync(
                        Global_Variable.CurrentUser?.Id ?? 1,
                        "عرض منتج",
                        "إدارة المنتجات",
                        $"تم عرض المنتج: {product.Name}",
                        "Product",
                        id
                    );
                }

                return product;
            }
            catch (Exception ex)
            {
                await _activityService.LogActivityAsync(
                    Global_Variable.CurrentUser?.Id ?? 1,
                    "خطأ في عرض المنتج",
                    "إدارة المنتجات",
                    $"خطأ في عرض المنتج ID: {id} - {ex.Message}",
                    "Product",
                    id,
                    null,
                    null,
                    "Error",
                    false,
                    ex.Message
                );
                throw;
            }
        }

        /// <summary>
        /// إضافة منتج جديد
        /// </summary>
        public async Task<bool> AddProductAsync(Product product)
        {
            try
            {
                // التحقق من عدم تكرار الكود
                if (!string.IsNullOrEmpty(product.Code))
                {
                    var existingProduct = _dataAccess.LoadSingle<Product>(
                        $"SELECT * FROM Product WHERE Code='{product.Code}' AND Rb='{Global_Variable.Mk_resources.RB_SN}'"
                    );

                    if (existingProduct != null)
                    {
                        RJMessageBox.Show("كود المنتج موجود مسبقاً", "تحذير",
                            System.Windows.Forms.MessageBoxButtons.OK,
                            System.Windows.Forms.MessageBoxIcon.Warning);
                        return false;
                    }
                }

                // تعيين القيم الافتراضية
                product.Rb = Global_Variable.Mk_resources.RB_SN;
                product.LastStockUpdate = DateTime.Now;
                product.LastUpdateBy = Global_Variable.CurrentUser?.Username ?? "System";
                product.StockUpdateReason = "إنشاء منتج جديد";

                var fields = new List<string>
                {
                    "Code", "Name", "Price", "Stock", "LowStockThreshold", "Description",
                    "Account_IncomeId", "Account_ExpenseId", "Product_UomId", "Active",
                    "Rb", "LastStockUpdate", "LastUpdateBy", "StockUpdateReason"
                };

                var result = _dataAccess.InsertTable(fields, product, "Product");

                if (result > 0)
                {
                    await _activityService.LogActivityAsync(
                        Global_Variable.CurrentUser?.Id ?? 1,
                        "إضافة منتج",
                        "إدارة المنتجات",
                        $"تم إضافة المنتج: {product.Name} - الكود: {product.Code}",
                        "Product",
                        result,
                        null,
                        product
                    );

                    // التحقق من حاجة المنتج لإعادة الطلب
                    await CheckLowStockNotificationAsync(product);

                    RJMessageBox.Show("تم إضافة المنتج بنجاح", "نجح",
                        System.Windows.Forms.MessageBoxButtons.OK,
                        System.Windows.Forms.MessageBoxIcon.Information);
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                await _activityService.LogActivityAsync(
                    Global_Variable.CurrentUser?.Id ?? 1,
                    "خطأ في إضافة منتج",
                    "إدارة المنتجات",
                    $"خطأ في إضافة المنتج: {product.Name} - {ex.Message}",
                    "Product",
                    null,
                    null,
                    product,
                    "Error",
                    false,
                    ex.Message
                );

                RJMessageBox.Show($"خطأ في إضافة المنتج: {ex.Message}", "خطأ",
                    System.Windows.Forms.MessageBoxButtons.OK,
                    System.Windows.Forms.MessageBoxIcon.Error);
                return false;
            }
        }

        /// <summary>
        /// تحديث منتج
        /// </summary>
        public async Task<bool> UpdateProductAsync(Product product, Product oldProduct = null)
        {
            try
            {
                product.LastStockUpdate = DateTime.Now;
                product.LastUpdateBy = Global_Variable.CurrentUser?.Username ?? "System";
                product.StockUpdateReason = "تحديث بيانات المنتج";

                var fields = new List<string>
                {
                    "Name", "Price", "Stock", "LowStockThreshold", "Description",
                    "Account_IncomeId", "Account_ExpenseId", "Product_UomId", "Active",
                    "LastStockUpdate", "LastUpdateBy", "StockUpdateReason"
                };

                var result = _dataAccess.UpdateTable(fields, product, "Product", product.Id);

                if (result > 0)
                {
                    await _activityService.LogActivityAsync(
                        Global_Variable.CurrentUser?.Id ?? 1,
                        "تحديث منتج",
                        "إدارة المنتجات",
                        $"تم تحديث المنتج: {product.Name}",
                        "Product",
                        product.Id,
                        oldProduct,
                        product
                    );

                    // التحقق من حاجة المنتج لإعادة الطلب
                    await CheckLowStockNotificationAsync(product);

                    RJMessageBox.Show("تم تحديث المنتج بنجاح", "نجح",
                        System.Windows.Forms.MessageBoxButtons.OK,
                        System.Windows.Forms.MessageBoxIcon.Information);
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                await _activityService.LogActivityAsync(
                    Global_Variable.CurrentUser?.Id ?? 1,
                    "خطأ في تحديث منتج",
                    "إدارة المنتجات",
                    $"خطأ في تحديث المنتج: {product.Name} - {ex.Message}",
                    "Product",
                    product.Id,
                    oldProduct,
                    product,
                    "Error",
                    false,
                    ex.Message
                );

                RJMessageBox.Show($"خطأ في تحديث المنتج: {ex.Message}", "خطأ",
                    System.Windows.Forms.MessageBoxButtons.OK,
                    System.Windows.Forms.MessageBoxIcon.Error);
                return false;
            }
        }

        /// <summary>
        /// حذف منتج
        /// </summary>
        public async Task<bool> DeleteProductAsync(int productId)
        {
            try
            {
                var product = await GetProductByIdAsync(productId);
                if (product == null)
                {
                    RJMessageBox.Show("المنتج غير موجود", "خطأ",
                        System.Windows.Forms.MessageBoxButtons.OK,
                        System.Windows.Forms.MessageBoxIcon.Error);
                    return false;
                }

                var result = _dataAccess.DeleteById<Product>(productId.ToString(), "Product");

                if (result > 0)
                {
                    await _activityService.LogActivityAsync(
                        Global_Variable.CurrentUser?.Id ?? 1,
                        "حذف منتج",
                        "إدارة المنتجات",
                        $"تم حذف المنتج: {product.Name} - الكود: {product.Code}",
                        "Product",
                        productId,
                        product,
                        null
                    );

                    RJMessageBox.Show("تم حذف المنتج بنجاح", "نجح",
                        System.Windows.Forms.MessageBoxButtons.OK,
                        System.Windows.Forms.MessageBoxIcon.Information);
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                await _activityService.LogActivityAsync(
                    Global_Variable.CurrentUser?.Id ?? 1,
                    "خطأ في حذف منتج",
                    "إدارة المنتجات",
                    $"خطأ في حذف المنتج ID: {productId} - {ex.Message}",
                    "Product",
                    productId,
                    null,
                    null,
                    "Error",
                    false,
                    ex.Message
                );

                RJMessageBox.Show($"خطأ في حذف المنتج: {ex.Message}", "خطأ",
                    System.Windows.Forms.MessageBoxButtons.OK,
                    System.Windows.Forms.MessageBoxIcon.Error);
                return false;
            }
        }

        /// <summary>
        /// البحث في المنتجات
        /// </summary>
        public async Task<List<Product>> SearchProductsAsync(string searchTerm)
        {
            try
            {
                var query = $@"
                    SELECT * FROM Product
                    WHERE Rb='{Global_Variable.Mk_resources.RB_SN}'
                    AND (Name LIKE '%{searchTerm}%' OR Code LIKE '%{searchTerm}%' OR Description LIKE '%{searchTerm}%')
                    ORDER BY Name";

                var products = _dataAccess.Load<Product>(query);

                await _activityService.LogActivityAsync(
                    Global_Variable.CurrentUser?.Id ?? 1,
                    "البحث في المنتجات",
                    "إدارة المنتجات",
                    $"تم البحث عن: {searchTerm} - النتائج: {products.Count}",
                    "Product"
                );

                return products;
            }
            catch (Exception ex)
            {
                await _activityService.LogActivityAsync(
                    Global_Variable.CurrentUser?.Id ?? 1,
                    "خطأ في البحث",
                    "إدارة المنتجات",
                    $"خطأ في البحث عن: {searchTerm} - {ex.Message}",
                    "Product",
                    null,
                    null,
                    null,
                    "Error",
                    false,
                    ex.Message
                );
                throw;
            }
        }

        /// <summary>
        /// الحصول على المنتجات منخفضة المخزون
        /// </summary>
        public async Task<List<Product>> GetLowStockProductsAsync()
        {
            try
            {
                var query = $@"
                    SELECT * FROM Product
                    WHERE Rb='{Global_Variable.Mk_resources.RB_SN}'
                    AND Stock <= LowStockThreshold
                    AND Active = 1
                    ORDER BY Stock ASC, Name";

                var products = _dataAccess.Load<Product>(query);

                await _activityService.LogActivityAsync(
                    Global_Variable.CurrentUser?.Id ?? 1,
                    "عرض المنتجات منخفضة المخزون",
                    "إدارة المنتجات",
                    $"تم عرض {products.Count} منتج يحتاج إعادة طلب",
                    "Product"
                );

                return products;
            }
            catch (Exception ex)
            {
                await _activityService.LogActivityAsync(
                    Global_Variable.CurrentUser?.Id ?? 1,
                    "خطأ في عرض المنتجات منخفضة المخزون",
                    "إدارة المنتجات",
                    $"خطأ: {ex.Message}",
                    "Product",
                    null,
                    null,
                    null,
                    "Error",
                    false,
                    ex.Message
                );
                throw;
            }
        }

        /// <summary>
        /// التحقق من إشعارات المخزون المنخفض
        /// </summary>
        private async Task CheckLowStockNotificationAsync(Product product)
        {
            try
            {
                if (product.NeedsReorder)
                {
                    var title = "تنبيه مخزون منخفض";
                    var message = $"المنتج '{product.Name}' (الكود: {product.Code}) وصل إلى الحد الأدنى للمخزون. المخزون الحالي: {product.Stock}، الحد الأدنى: {product.LowStockThreshold}";

                    await _notificationService.CreateNotificationAsync(
                        title,
                        message,
                        "Warning",
                        "High",
                        Global_Variable.CurrentUser?.Id,
                        Global_Variable.CurrentUser?.Username,
                        "إدارة المنتجات",
                        product.Id,
                        "Product",
                        $"/Products/Details/{product.Id}",
                        DateTime.Now.AddDays(30) // تنتهي صلاحية الإشعار بعد 30 يوم
                    );
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إنشاء إشعار المخزون: {ex.Message}");
            }
        }
    }
}
