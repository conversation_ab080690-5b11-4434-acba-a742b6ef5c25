using SmartCreator.Helpers;
using SmartCreator.Entities;
using SmartCreator.Models;
using SmartCreator.Services.Security;
using SmartCreator.Service;
using SmartCreator.Data;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace SmartCreator
{
    /// <summary>
    /// ملف اختبار نهائي للتأكد من أن جميع الإصلاحات تعمل
    /// </summary>
    public class TestFinalFixes
    {
        /// <summary>
        /// اختبار شامل لجميع الإصلاحات النهائية
        /// </summary>
        public static async Task TestAllFinalFixesAsync()
        {
            try
            {
                Console.WriteLine("🔧 اختبار جميع الإصلاحات النهائية...\n");

                // 1. اختبار ActivityService مع Contains fix
                await TestActivityServiceContainsFix();
                Console.WriteLine();

                // 2. اختبار SecurityHelper مع parameter fix
                await TestSecurityHelperParameterFix();
                Console.WriteLine();

                // 3. اختبار UserService مع int? to string fix
                await TestUserServiceTypeFix();
                Console.WriteLine();

                // 4. اختبار التكامل الكامل
                await TestFullIntegration();
                Console.WriteLine();

                Console.WriteLine("🎉 جميع الإصلاحات النهائية تعمل بنجاح!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في الاختبار النهائي: {ex.Message}");
                Console.WriteLine($"تفاصيل الخطأ: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// اختبار إصلاح Contains في ActivityService
        /// </summary>
        private static async Task TestActivityServiceContainsFix()
        {
            Console.WriteLine("📊 اختبار إصلاح Contains في ActivityService:");

            try
            {
                var activityService = new ActivityService();

                // إضافة بعض الأنشطة للاختبار
                await activityService.LogActivityAsync("admin", "تسجيل دخول", "النظام", "دخول ناجح");
                await activityService.LogActivityAsync("user1", "إضافة مستخدم", "إدارة المستخدمين", "إضافة مستخدم جديد");
                await activityService.LogActivityAsync("admin", "عرض تقرير", "التقارير", "عرض تقرير المبيعات");

                Console.WriteLine("   ✅ تم إضافة أنشطة تجريبية");

                // اختبار GetUserActivitiesAsync مع فلترة الوحدة (يستخدم IndexOf بدلاً من Contains)
                var userActivities = await activityService.
                    (
                    1, DateTime.Now.AddDays(-1), DateTime.Now.AddDays(1), "النظام");

                Console.WriteLine($"   ✅ GetUserActivitiesAsync مع فلترة الوحدة: {userActivities.Count} نشاط");

                // اختبار مع وحدة جزئية
                var partialActivities = await activityService.GetUserActivitiesAsync(
                    1, DateTime.Now.AddDays(-1), DateTime.Now.AddDays(1), "إدارة");

                Console.WriteLine($"   ✅ فلترة جزئية للوحدة: {partialActivities.Count} نشاط");

                Console.WriteLine("✅ اختبار إصلاح Contains نجح!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في اختبار Contains: {ex.Message}");
            }
        }

        /// <summary>
        /// اختبار إصلاح معاملات SecurityHelper
        /// </summary>
        private static async Task TestSecurityHelperParameterFix()
        {
            Console.WriteLine("🛡️ اختبار إصلاح معاملات SecurityHelper:");

            try
            {
                // اختبار مع مستخدم null
                Global_Variable.CurrentUser = null;
                await SecurityHelper.LogActivityAsync("نشاط النظام", "النظام", "اختبار مع null user");
                Console.WriteLine("   ✅ LogActivityAsync يعمل مع null user");

                // اختبار مع مستخدم صحيح
                Global_Variable.CurrentUser = new User
                {
                    Id = 1,
                    Username = "test_admin",
                    FullName = "مدير اختبار",
                    Email = "<EMAIL>",
                    IsActive = true,
                    Permissions = new List<string> { PermissionCodes.SYSTEM_ADMIN }
                };

                await SecurityHelper.LogActivityAsync("تسجيل دخول", "النظام", "دخول ناجح للنظام");
                Console.WriteLine("   ✅ LogActivityAsync يعمل مع مستخدم صحيح");

                // اختبار مع معاملات مختلفة
                await SecurityHelper.LogActivityAsync("إنشاء ملف", "النظام", "تم إنشاء ملف جديد", "Info", true);
                Console.WriteLine("   ✅ LogActivityAsync مع جميع المعاملات");

                await SecurityHelper.LogActivityAsync("خطأ في النظام", "النظام", "خطأ غير متوقع", "Error", false);
                Console.WriteLine("   ✅ LogActivityAsync مع خطأ");

                Console.WriteLine("✅ اختبار إصلاح معاملات SecurityHelper نجح!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في اختبار معاملات SecurityHelper: {ex.Message}");
            }
        }

        /// <summary>
        /// اختبار إصلاح تحويل الأنواع في UserService
        /// </summary>
        private static async Task TestUserServiceTypeFix()
        {
            Console.WriteLine("👤 اختبار إصلاح تحويل الأنواع في UserService:");

            try
            {
                // إنشاء UserService (لن ننشئ مستخدم فعلي لتجنب التأثير على قاعدة البيانات)
                var dbHelper = new DatabaseHelper();
                var auditService = new AuditService(dbHelper);
                var userService = new UserService(dbHelper, auditService);

                Console.WriteLine("   ✅ تم إنشاء UserService بنجاح");

                // اختبار إنشاء نموذج مستخدم (بدون حفظ في قاعدة البيانات)
                var testUser = new User
                {
                    Username = "test_user_" + DateTime.Now.Ticks,
                    Email = "<EMAIL>",
                    FirstName = "مستخدم",
                    LastName = "تجريبي",
                    Phone = "123456789",
                    Department = "تقنية المعلومات",
                    Position = "مطور",
                    IsActive = true
                };

                // تعيين CreatedBy (هذا ما كان يسبب المشكلة)
                int? createdBy = 1;
                testUser.CreatedBy = createdBy?.ToString(); // الإصلاح المطبق

                Console.WriteLine($"   ✅ تم تعيين CreatedBy: {testUser.CreatedBy}");

                // اختبار تحديث المستخدم
                int? modifiedBy = 2;
                testUser.ModifiedBy = modifiedBy?.ToString();

                Console.WriteLine($"   ✅ تم تعيين ModifiedBy: {testUser.ModifiedBy}");

                Console.WriteLine("✅ اختبار إصلاح تحويل الأنواع نجح!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في اختبار تحويل الأنواع: {ex.Message}");
            }
        }

        /// <summary>
        /// اختبار التكامل الكامل
        /// </summary>
        private static async Task TestFullIntegration()
        {
            Console.WriteLine("🔗 اختبار التكامل الكامل:");

            try
            {
                // إعداد مستخدم للاختبار
                Global_Variable.CurrentUser = new User
                {
                    Id = 1,
                    Username = "integration_admin",
                    FullName = "مدير التكامل",
                    Email = "<EMAIL>",
                    IsActive = true,
                    Permissions = new List<string>
                    {
                        PermissionCodes.SYSTEM_ADMIN,
                        PermissionCodes.MANAGE_USERS,
                        PermissionCodes.VIEW_AUDIT_LOGS
                    }
                };

                Console.WriteLine($"   ✅ تم إعداد المستخدم: {Global_Variable.CurrentUser.Username}");

                // اختبار SecurityHelper
                var hasPermission = SecurityHelper.HasPermission(PermissionCodes.SYSTEM_ADMIN);
                Console.WriteLine($"   ✅ SecurityHelper.HasPermission: {hasPermission}");

                await SecurityHelper.LogActivityAsync("اختبار التكامل", "النظام", "اختبار التكامل الكامل");
                Console.WriteLine("   ✅ SecurityHelper.LogActivityAsync");

                // اختبار ActivityService
                var activityService = new ActivityService();
                var (activities, totalCount) = await activityService.GetActivitiesAsync(1, 10);
                Console.WriteLine($"   ✅ ActivityService.GetActivitiesAsync: {activities.Count}/{totalCount}");

                var userActivities = await activityService.GetUserActivitiesAsync(
                    1, DateTime.Now.AddDays(-1), DateTime.Now.AddDays(1));
                Console.WriteLine($"   ✅ ActivityService.GetUserActivitiesAsync: {userActivities.Count}");

                // اختبار كلمة المرور والتشفير
                var strength = SecurityHelper.CheckPasswordStrength("MyStr0ng@Password!");
                var description = SecurityHelper.GetPasswordStrengthDescription(strength);
                Console.WriteLine($"   ✅ PasswordStrength: {description}");

                var encrypted = SecurityHelper.EncryptText("نص سري للاختبار");
                var decrypted = SecurityHelper.DecryptText(encrypted);
                var isCorrect = "نص سري للاختبار" == decrypted;
                Console.WriteLine($"   ✅ Encryption/Decryption: {isCorrect}");

                // اختبار تسجيل الخروج
                await SecurityHelper.LogoutUserAsync();
                var isLoggedOut = Global_Variable.CurrentUser == null;
                Console.WriteLine($"   ✅ Logout: {isLoggedOut}");

                Console.WriteLine("✅ اختبار التكامل الكامل نجح!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في اختبار التكامل: {ex.Message}");
            }
        }

        /// <summary>
        /// اختبار سريع للتأكد من عدم وجود أخطاء
        /// </summary>
        public static async Task QuickValidationTestAsync()
        {
            try
            {
                Console.WriteLine("⚡ اختبار سريع للتحقق من الإصلاحات...");

                // اختبار أساسي لكل مكون
                Global_Variable.CurrentUser = new User { Id = 1, Username = "quick_test" };

                // SecurityHelper
                await SecurityHelper.LogActivityAsync("اختبار سريع", "النظام", "تحقق من الإصلاحات");
                var hasPermission = SecurityHelper.HasPermission(PermissionCodes.SYSTEM_ADMIN);

                // ActivityService
                var activityService = new ActivityService();
                var activities = await activityService.GetUserActivitiesAsync(1, DateTime.Now.AddDays(-1), DateTime.Now);

                // UserService type conversion
                var testUser = new User();
                int? createdBy = 1;
                testUser.CreatedBy = createdBy?.ToString();

                Console.WriteLine("✅ الاختبار السريع نجح - جميع الإصلاحات تعمل!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في الاختبار السريع: {ex.Message}");
            }
        }

        /// <summary>
        /// تشغيل جميع الاختبارات النهائية
        /// </summary>
        public static async Task RunCompleteTestSuiteAsync()
        {
            Console.WriteLine("🎯 تشغيل مجموعة الاختبارات النهائية الكاملة...\n");

            await TestAllFinalFixesAsync();
            Console.WriteLine();

            await QuickValidationTestAsync();
            Console.WriteLine();

            Console.WriteLine("🏆 انتهت جميع الاختبارات النهائية بنجاح!");
            Console.WriteLine("🎉 النظام الأمني جاهز للاستخدام بالكامل!");
        }
    }
}
