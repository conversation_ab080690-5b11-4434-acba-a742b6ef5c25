using System;

namespace SmartCreator.Forms.Notifications
{
    partial class Frm_Notifications
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle1 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle2 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle3 = new System.Windows.Forms.DataGridViewCellStyle();
            this.pnlMain = new SmartCreator.RJControls.RJPanel();
            this.dgvNotifications = new SmartCreator.RJControls.RJDataGridView();
            this.pnlTop = new SmartCreator.RJControls.RJPanel();
            this.txtSearch = new SmartCreator.RJControls.RJTextBox();
            this.lblSearch = new SmartCreator.RJControls.RJLabel();
            this.btnShowUnreadOnly = new SmartCreator.RJControls.RJButton();
            this.btnMarkAllRead = new SmartCreator.RJControls.RJButton();
            this.btnDeleteSelected = new SmartCreator.RJControls.RJButton();
            this.btnRefresh = new SmartCreator.RJControls.RJButton();
            this.pnlBottom = new SmartCreator.RJControls.RJPanel();
            this.lblStatus = new SmartCreator.RJControls.RJLabel();
            this.lblUnreadCount = new SmartCreator.RJControls.RJLabel();
            this.pnlClientArea.SuspendLayout();
            this.pnlMain.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dgvNotifications)).BeginInit();
            this.pnlTop.SuspendLayout();
            this.pnlBottom.SuspendLayout();
            this.SuspendLayout();
            // 
            // pnlClientArea
            // 
            this.pnlClientArea.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.pnlClientArea.Controls.Add(this.pnlMain);
            this.pnlClientArea.Location = new System.Drawing.Point(5, 45);
            this.pnlClientArea.Size = new System.Drawing.Size(990, 550);
            // 
            // lblCaption
            // 
            this.lblCaption.Size = new System.Drawing.Size(85, 17);
            this.lblCaption.Text = "نظام الإشعارات";
            // 
            // pnlMain
            // 
            this.pnlMain.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.pnlMain.BorderRadius = 10;
            this.pnlMain.Controls.Add(this.dgvNotifications);
            this.pnlMain.Controls.Add(this.pnlTop);
            this.pnlMain.Controls.Add(this.pnlBottom);
            this.pnlMain.Customizable = false;
            this.pnlMain.Dock = System.Windows.Forms.DockStyle.Fill;
            this.pnlMain.ForeColor = System.Drawing.Color.Black;
            this.pnlMain.GradientAngle = 90F;
            this.pnlMain.GradientBottomColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(240)))), ((int)(((byte)(240)))));
            this.pnlMain.GradientTopColor = System.Drawing.Color.White;
            this.pnlMain.Location = new System.Drawing.Point(0, 0);
            this.pnlMain.Name = "pnlMain";
            this.pnlMain.Size = new System.Drawing.Size(990, 550);
            this.pnlMain.TabIndex = 0;
            // 
            // dgvNotifications
            // 
            this.dgvNotifications.AllowUserToAddRows = false;
            this.dgvNotifications.AllowUserToDeleteRows = false;
            this.dgvNotifications.AllowUserToResizeRows = false;
            this.dgvNotifications.AlternatingRowsColor = System.Drawing.Color.Empty;
            this.dgvNotifications.AlternatingRowsColorApply = false;
            this.dgvNotifications.AutoSizeColumnsMode = System.Windows.Forms.DataGridViewAutoSizeColumnsMode.Fill;
            this.dgvNotifications.BackgroundColor = System.Drawing.Color.White;
            this.dgvNotifications.BorderRadius = 13;
            this.dgvNotifications.BorderStyle = System.Windows.Forms.BorderStyle.None;
            this.dgvNotifications.CellBorderStyle = System.Windows.Forms.DataGridViewCellBorderStyle.SingleHorizontal;
            this.dgvNotifications.ColumnHeaderColor = System.Drawing.Color.MediumPurple;
            this.dgvNotifications.ColumnHeaderFont = new System.Drawing.Font("Segoe UI", 8.25F);
            this.dgvNotifications.ColumnHeaderHeight = 40;
            this.dgvNotifications.ColumnHeadersBorderStyle = System.Windows.Forms.DataGridViewHeaderBorderStyle.None;
            dataGridViewCellStyle1.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle1.BackColor = System.Drawing.Color.MediumPurple;
            dataGridViewCellStyle1.Font = new System.Drawing.Font("Segoe UI", 8.25F);
            dataGridViewCellStyle1.ForeColor = System.Drawing.Color.White;
            dataGridViewCellStyle1.WrapMode = System.Windows.Forms.DataGridViewTriState.True;
            this.dgvNotifications.ColumnHeadersDefaultCellStyle = dataGridViewCellStyle1;
            this.dgvNotifications.ColumnHeadersHeight = 40;
            this.dgvNotifications.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.DisableResizing;
            this.dgvNotifications.ColumnHeaderTextColor = System.Drawing.Color.White;
            this.dgvNotifications.ColumnsMode = System.Windows.Forms.DataGridViewAutoSizeColumnsMode.Fill;
            this.dgvNotifications.Customizable = false;
            this.dgvNotifications.DgvBackColor = System.Drawing.Color.White;
            this.dgvNotifications.Dock = System.Windows.Forms.DockStyle.Fill;
            this.dgvNotifications.EnableHeadersVisualStyles = false;
            this.dgvNotifications.GridColor = System.Drawing.Color.FromArgb(((int)(((byte)(230)))), ((int)(((byte)(230)))), ((int)(((byte)(230)))));
            this.dgvNotifications.Location = new System.Drawing.Point(0, 60);
            this.dgvNotifications.MultiSelect = false;
            this.dgvNotifications.Name = "dgvNotifications";
            this.dgvNotifications.ReadOnly = true;
            this.dgvNotifications.RightToLeft = System.Windows.Forms.RightToLeft.No;
            this.dgvNotifications.RowHeaderColor = System.Drawing.Color.WhiteSmoke;
            this.dgvNotifications.RowHeadersBorderStyle = System.Windows.Forms.DataGridViewHeaderBorderStyle.Single;
            dataGridViewCellStyle2.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle2.BackColor = System.Drawing.Color.WhiteSmoke;
            dataGridViewCellStyle2.Font = new System.Drawing.Font("Segoe UI", 8.25F);
            dataGridViewCellStyle2.ForeColor = System.Drawing.Color.White;
            dataGridViewCellStyle2.SelectionBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(213)))), ((int)(((byte)(199)))), ((int)(((byte)(241)))));
            dataGridViewCellStyle2.SelectionForeColor = System.Drawing.SystemColors.HighlightText;
            dataGridViewCellStyle2.WrapMode = System.Windows.Forms.DataGridViewTriState.False;
            this.dgvNotifications.RowHeadersDefaultCellStyle = dataGridViewCellStyle2;
            this.dgvNotifications.RowHeadersVisible = false;
            this.dgvNotifications.RowHeadersWidth = 30;
            this.dgvNotifications.RowHeadersWidthSizeMode = System.Windows.Forms.DataGridViewRowHeadersWidthSizeMode.DisableResizing;
            this.dgvNotifications.RowHeight = 60;
            this.dgvNotifications.RowsColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            dataGridViewCellStyle3.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle3.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            dataGridViewCellStyle3.Font = new System.Drawing.Font("Segoe UI", 10F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            dataGridViewCellStyle3.ForeColor = System.Drawing.Color.Gray;
            dataGridViewCellStyle3.SelectionBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(232)))), ((int)(((byte)(225)))), ((int)(((byte)(247)))));
            dataGridViewCellStyle3.SelectionForeColor = System.Drawing.Color.Gray;
            this.dgvNotifications.RowsDefaultCellStyle = dataGridViewCellStyle3;
            this.dgvNotifications.RowsTextColor = System.Drawing.Color.Gray;
            this.dgvNotifications.RowTemplate.Height = 60;
            this.dgvNotifications.SelectionBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(213)))), ((int)(((byte)(199)))), ((int)(((byte)(241)))));
            this.dgvNotifications.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect;
            this.dgvNotifications.SelectionTextColor = System.Drawing.Color.Gray;
            this.dgvNotifications.Size = new System.Drawing.Size(990, 450);
            this.dgvNotifications.TabIndex = 0;
            // 
            // pnlTop
            // 
            this.pnlTop.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.pnlTop.BorderRadius = 5;
            this.pnlTop.Controls.Add(this.txtSearch);
            this.pnlTop.Controls.Add(this.lblSearch);
            this.pnlTop.Controls.Add(this.btnShowUnreadOnly);
            this.pnlTop.Controls.Add(this.btnMarkAllRead);
            this.pnlTop.Controls.Add(this.btnDeleteSelected);
            this.pnlTop.Controls.Add(this.btnRefresh);
            this.pnlTop.Customizable = false;
            this.pnlTop.Dock = System.Windows.Forms.DockStyle.Top;
            this.pnlTop.ForeColor = System.Drawing.Color.Black;
            this.pnlTop.GradientAngle = 90F;
            this.pnlTop.GradientBottomColor = System.Drawing.Color.FromArgb(((int)(((byte)(248)))), ((int)(((byte)(249)))), ((int)(((byte)(250)))));
            this.pnlTop.GradientTopColor = System.Drawing.Color.White;
            this.pnlTop.Location = new System.Drawing.Point(0, 0);
            this.pnlTop.Name = "pnlTop";
            this.pnlTop.Size = new System.Drawing.Size(990, 60);
            this.pnlTop.TabIndex = 1;
            // 
            // txtSearch
            // 
            this.txtSearch._Customizable = false;
            this.txtSearch.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txtSearch.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txtSearch.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txtSearch.BorderRadius = 5;
            this.txtSearch.BorderSize = 2;
            this.txtSearch.Font = new System.Drawing.Font("Segoe UI", 9F);
            this.txtSearch.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txtSearch.Location = new System.Drawing.Point(10, 15);
            this.txtSearch.Margin = new System.Windows.Forms.Padding(4);
            this.txtSearch.MultiLine = false;
            this.txtSearch.Name = "txtSearch";
            this.txtSearch.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txtSearch.PasswordChar = false;
            this.txtSearch.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txtSearch.PlaceHolderText = "البحث في الإشعارات...";
            this.txtSearch.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txtSearch.Size = new System.Drawing.Size(200, 26);
            this.txtSearch.Style = SmartCreator.RJControls.TextBoxStyle.MatteBorder;
            this.txtSearch.TabIndex = 0;
            this.txtSearch.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            this.txtSearch.Texts = "";
            this.txtSearch.UnderlinedStyle = false;
            // 
            // lblSearch
            // 
            this.lblSearch.AutoSize = true;
            this.lblSearch.BackColor = System.Drawing.Color.Transparent;
            this.lblSearch.Font = new System.Drawing.Font("Segoe UI", 9F);
            this.lblSearch.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(100)))), ((int)(((byte)(100)))), ((int)(((byte)(100)))));
            this.lblSearch.LinkLabel = false;
            this.lblSearch.Location = new System.Drawing.Point(220, 22);
            this.lblSearch.Name = "lblSearch";
            this.lblSearch.Size = new System.Drawing.Size(44, 15);
            this.lblSearch.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.lblSearch.TabIndex = 1;
            this.lblSearch.Text = "🔍 بحث";
            // 
            // btnShowUnreadOnly
            // 
            this.btnShowUnreadOnly.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(155)))), ((int)(((byte)(89)))), ((int)(((byte)(182)))));
            this.btnShowUnreadOnly.BackgroundColor = System.Drawing.Color.FromArgb(((int)(((byte)(155)))), ((int)(((byte)(89)))), ((int)(((byte)(182)))));
            this.btnShowUnreadOnly.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btnShowUnreadOnly.BorderRadius = 5;
            this.btnShowUnreadOnly.BorderSize = 0;
            this.btnShowUnreadOnly.Design = SmartCreator.RJControls.ButtonDesign.Normal;
            this.btnShowUnreadOnly.FlatAppearance.BorderSize = 0;
            this.btnShowUnreadOnly.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(145)))), ((int)(((byte)(83)))), ((int)(((byte)(171)))));
            this.btnShowUnreadOnly.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(136)))), ((int)(((byte)(78)))), ((int)(((byte)(160)))));
            this.btnShowUnreadOnly.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnShowUnreadOnly.Font = new System.Drawing.Font("Segoe UI", 8F, System.Drawing.FontStyle.Bold);
            this.btnShowUnreadOnly.ForeColor = System.Drawing.Color.White;
            this.btnShowUnreadOnly.IconChar = FontAwesome.Sharp.IconChar.None;
            this.btnShowUnreadOnly.IconColor = System.Drawing.Color.White;
            this.btnShowUnreadOnly.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btnShowUnreadOnly.IconSize = 24;
            this.btnShowUnreadOnly.Location = new System.Drawing.Point(630, 15);
            this.btnShowUnreadOnly.Name = "btnShowUnreadOnly";
            this.btnShowUnreadOnly.Size = new System.Drawing.Size(120, 30);
            this.btnShowUnreadOnly.Style = SmartCreator.RJControls.ControlStyle.Solid;
            this.btnShowUnreadOnly.TabIndex = 5;
            this.btnShowUnreadOnly.Text = "👁️ غير مقروء فقط";
            this.btnShowUnreadOnly.TextColor = System.Drawing.Color.White;
            this.btnShowUnreadOnly.UseVisualStyleBackColor = false;
            // 
            // btnMarkAllRead
            // 
            this.btnMarkAllRead.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(52)))), ((int)(((byte)(152)))), ((int)(((byte)(219)))));
            this.btnMarkAllRead.BackgroundColor = System.Drawing.Color.FromArgb(((int)(((byte)(52)))), ((int)(((byte)(152)))), ((int)(((byte)(219)))));
            this.btnMarkAllRead.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btnMarkAllRead.BorderRadius = 5;
            this.btnMarkAllRead.BorderSize = 0;
            this.btnMarkAllRead.Design = SmartCreator.RJControls.ButtonDesign.Normal;
            this.btnMarkAllRead.FlatAppearance.BorderSize = 0;
            this.btnMarkAllRead.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(142)))), ((int)(((byte)(205)))));
            this.btnMarkAllRead.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(45)))), ((int)(((byte)(133)))), ((int)(((byte)(192)))));
            this.btnMarkAllRead.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnMarkAllRead.Font = new System.Drawing.Font("Segoe UI", 8F, System.Drawing.FontStyle.Bold);
            this.btnMarkAllRead.ForeColor = System.Drawing.Color.White;
            this.btnMarkAllRead.IconChar = FontAwesome.Sharp.IconChar.None;
            this.btnMarkAllRead.IconColor = System.Drawing.Color.White;
            this.btnMarkAllRead.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btnMarkAllRead.IconSize = 24;
            this.btnMarkAllRead.Location = new System.Drawing.Point(390, 15);
            this.btnMarkAllRead.Name = "btnMarkAllRead";
            this.btnMarkAllRead.Size = new System.Drawing.Size(120, 30);
            this.btnMarkAllRead.Style = SmartCreator.RJControls.ControlStyle.Solid;
            this.btnMarkAllRead.TabIndex = 3;
            this.btnMarkAllRead.Text = "✓ تحديد الكل كمقروء";
            this.btnMarkAllRead.TextColor = System.Drawing.Color.White;
            this.btnMarkAllRead.UseVisualStyleBackColor = false;
            // 
            // btnDeleteSelected
            // 
            this.btnDeleteSelected.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(231)))), ((int)(((byte)(76)))), ((int)(((byte)(60)))));
            this.btnDeleteSelected.BackgroundColor = System.Drawing.Color.FromArgb(((int)(((byte)(231)))), ((int)(((byte)(76)))), ((int)(((byte)(60)))));
            this.btnDeleteSelected.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btnDeleteSelected.BorderRadius = 5;
            this.btnDeleteSelected.BorderSize = 0;
            this.btnDeleteSelected.Design = SmartCreator.RJControls.ButtonDesign.Normal;
            this.btnDeleteSelected.FlatAppearance.BorderSize = 0;
            this.btnDeleteSelected.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(217)))), ((int)(((byte)(71)))), ((int)(((byte)(56)))));
            this.btnDeleteSelected.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(203)))), ((int)(((byte)(66)))), ((int)(((byte)(52)))));
            this.btnDeleteSelected.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnDeleteSelected.Font = new System.Drawing.Font("Segoe UI", 8F, System.Drawing.FontStyle.Bold);
            this.btnDeleteSelected.ForeColor = System.Drawing.Color.White;
            this.btnDeleteSelected.IconChar = FontAwesome.Sharp.IconChar.None;
            this.btnDeleteSelected.IconColor = System.Drawing.Color.White;
            this.btnDeleteSelected.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btnDeleteSelected.IconSize = 24;
            this.btnDeleteSelected.Location = new System.Drawing.Point(520, 15);
            this.btnDeleteSelected.Name = "btnDeleteSelected";
            this.btnDeleteSelected.Size = new System.Drawing.Size(100, 30);
            this.btnDeleteSelected.Style = SmartCreator.RJControls.ControlStyle.Solid;
            this.btnDeleteSelected.TabIndex = 4;
            this.btnDeleteSelected.Text = "🗑️ حذف المحدد";
            this.btnDeleteSelected.TextColor = System.Drawing.Color.White;
            this.btnDeleteSelected.UseVisualStyleBackColor = false;
            // 
            // btnRefresh
            // 
            this.btnRefresh.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(46)))), ((int)(((byte)(204)))), ((int)(((byte)(113)))));
            this.btnRefresh.BackgroundColor = System.Drawing.Color.FromArgb(((int)(((byte)(46)))), ((int)(((byte)(204)))), ((int)(((byte)(113)))));
            this.btnRefresh.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btnRefresh.BorderRadius = 5;
            this.btnRefresh.BorderSize = 0;
            this.btnRefresh.Design = SmartCreator.RJControls.ButtonDesign.Normal;
            this.btnRefresh.FlatAppearance.BorderSize = 0;
            this.btnRefresh.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(43)))), ((int)(((byte)(191)))), ((int)(((byte)(106)))));
            this.btnRefresh.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(40)))), ((int)(((byte)(179)))), ((int)(((byte)(99)))));
            this.btnRefresh.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnRefresh.Font = new System.Drawing.Font("Segoe UI", 9F, System.Drawing.FontStyle.Bold);
            this.btnRefresh.ForeColor = System.Drawing.Color.White;
            this.btnRefresh.IconChar = FontAwesome.Sharp.IconChar.None;
            this.btnRefresh.IconColor = System.Drawing.Color.White;
            this.btnRefresh.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btnRefresh.IconSize = 24;
            this.btnRefresh.Location = new System.Drawing.Point(300, 15);
            this.btnRefresh.Name = "btnRefresh";
            this.btnRefresh.Size = new System.Drawing.Size(80, 30);
            this.btnRefresh.Style = SmartCreator.RJControls.ControlStyle.Solid;
            this.btnRefresh.TabIndex = 2;
            this.btnRefresh.Text = "🔄 تحديث";
            this.btnRefresh.TextColor = System.Drawing.Color.White;
            this.btnRefresh.UseVisualStyleBackColor = false;
            // 
            // pnlBottom
            // 
            this.pnlBottom.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.pnlBottom.BorderRadius = 5;
            this.pnlBottom.Controls.Add(this.lblStatus);
            this.pnlBottom.Controls.Add(this.lblUnreadCount);
            this.pnlBottom.Customizable = false;
            this.pnlBottom.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.pnlBottom.ForeColor = System.Drawing.Color.Black;
            this.pnlBottom.GradientAngle = 90F;
            this.pnlBottom.GradientBottomColor = System.Drawing.Color.FromArgb(((int)(((byte)(248)))), ((int)(((byte)(249)))), ((int)(((byte)(250)))));
            this.pnlBottom.GradientTopColor = System.Drawing.Color.White;
            this.pnlBottom.Location = new System.Drawing.Point(0, 510);
            this.pnlBottom.Name = "pnlBottom";
            this.pnlBottom.Size = new System.Drawing.Size(990, 40);
            this.pnlBottom.TabIndex = 2;
            // 
            // lblStatus
            // 
            this.lblStatus.AutoSize = true;
            this.lblStatus.BackColor = System.Drawing.Color.Transparent;
            this.lblStatus.Font = new System.Drawing.Font("Segoe UI", 9F);
            this.lblStatus.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(100)))), ((int)(((byte)(100)))), ((int)(((byte)(100)))));
            this.lblStatus.LinkLabel = false;
            this.lblStatus.Location = new System.Drawing.Point(10, 12);
            this.lblStatus.Name = "lblStatus";
            this.lblStatus.Size = new System.Drawing.Size(79, 15);
            this.lblStatus.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.lblStatus.TabIndex = 0;
            this.lblStatus.Text = "جاري التحميل...";
            // 
            // lblUnreadCount
            // 
            this.lblUnreadCount.AutoSize = true;
            this.lblUnreadCount.BackColor = System.Drawing.Color.Transparent;
            this.lblUnreadCount.Font = new System.Drawing.Font("Segoe UI", 9F, System.Drawing.FontStyle.Bold);
            this.lblUnreadCount.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(231)))), ((int)(((byte)(76)))), ((int)(((byte)(60)))));
            this.lblUnreadCount.LinkLabel = false;
            this.lblUnreadCount.Location = new System.Drawing.Point(750, 12);
            this.lblUnreadCount.Name = "lblUnreadCount";
            this.lblUnreadCount.Size = new System.Drawing.Size(67, 15);
            this.lblUnreadCount.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.lblUnreadCount.TabIndex = 1;
            this.lblUnreadCount.Text = "غير مقروء: 0";
            // 
            // Frm_Notifications
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.BorderSize = 5;
            this.Caption = "نظام الإشعارات";
            this.ClientSize = new System.Drawing.Size(1000, 600);
            this.Font = new System.Drawing.Font("Segoe UI", 8.25F);
            this.Location = new System.Drawing.Point(0, 0);
            this.Name = "Frm_Notifications";
            this.Padding = new System.Windows.Forms.Padding(5);
            this.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "نظام الإشعارات";
            this.pnlClientArea.ResumeLayout(false);
            this.pnlMain.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.dgvNotifications)).EndInit();
            this.pnlTop.ResumeLayout(false);
            this.pnlTop.PerformLayout();
            this.pnlBottom.ResumeLayout(false);
            this.pnlBottom.PerformLayout();
            this.ResumeLayout(false);

        }

        #endregion

        private SmartCreator.RJControls.RJPanel pnlMain;
        private SmartCreator.RJControls.RJDataGridView dgvNotifications;
        private SmartCreator.RJControls.RJPanel pnlTop;
        private SmartCreator.RJControls.RJTextBox txtSearch;
        private SmartCreator.RJControls.RJLabel lblSearch;
        private SmartCreator.RJControls.RJButton btnShowUnreadOnly;
        private SmartCreator.RJControls.RJButton btnMarkAllRead;
        private SmartCreator.RJControls.RJButton btnDeleteSelected;
        private SmartCreator.RJControls.RJButton btnRefresh;
        private SmartCreator.RJControls.RJPanel pnlBottom;
        private SmartCreator.RJControls.RJLabel lblStatus;
        private SmartCreator.RJControls.RJLabel lblUnreadCount;
    }
}
