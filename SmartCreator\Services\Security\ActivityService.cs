using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using SmartCreator.Entities;

namespace SmartCreator.Services.Security
{
    /// <summary>
    /// خدمة إدارة أنشطة المستخدمين
    /// </summary>
    public class ActivityService
    {
        private readonly List<UserActivity> _activities;

        public ActivityService()
        {
            _activities = new List<UserActivity>();
            InitializeSampleData();
        }

        /// <summary>
        /// تسجيل نشاط جديد
        /// </summary>
        public async Task LogActivityAsync(string username, string action, string module,
            string description, bool isSuccessful = true, string severity = "Info", string ipAddress = "")
        {
            var activity = new UserActivity
            {
                // Id سيتم تعيينه تلقائياً من قاعدة البيانات
                UserId = 1, // مؤقت - يجب الحصول على UserId الحقيقي
                Username = username,
                Action = action,
                Module = module,
                Description = description,
                IsSuccessful = isSuccessful,
                Severity = severity,
                IpAddress = ipAddress,
                ActivityType = action,
                Timestamp = DateTime.Now
            };

            _activities.Add(activity);
            await Task.CompletedTask;
        }

        /// <summary>
        /// الحصول على الأنشطة مع الفلترة والترقيم
        /// </summary>
        public async Task<(List<UserActivity> Activities, int TotalCount)> GetActivitiesAsync(
            int page = 1, int pageSize = 100, string username = null, string module = null,
            string severity = null, DateTime? fromDate = null, DateTime? toDate = null, string searchText = null)
        {
            await Task.Delay(10); // محاكاة عملية غير متزامنة

            var query = _activities.AsQueryable();

            // تطبيق الفلاتر
            if (!string.IsNullOrEmpty(username))
                query = query.Where(a => a.Username.Contains(username));

            if (!string.IsNullOrEmpty(module))
                query = query.Where(a => a.Module == module);

            if (!string.IsNullOrEmpty(severity))
                query = query.Where(a => a.Severity == severity);

            if (fromDate.HasValue)
                query = query.Where(a => a.Timestamp >= fromDate.Value);

            if (toDate.HasValue)
                query = query.Where(a => a.Timestamp <= toDate.Value.AddDays(1));

            if (!string.IsNullOrEmpty(searchText))
                query = query.Where(a => a.Description.Contains(searchText) ||
                                        a.Action.Contains(searchText));

            // ترتيب حسب التاريخ (الأحدث أولاً)
            query = query.OrderByDescending(a => a.Timestamp);

            var totalCount = query.Count();
            var activities = query.Skip((page - 1) * pageSize).Take(pageSize).ToList();

            return (activities, totalCount);
        }

        /// <summary>
        /// الحصول على إحصائيات الأنشطة
        /// </summary>
        public async Task<Dictionary<string, int>> GetActivityStatisticsAsync(
            DateTime? fromDate = null, DateTime? toDate = null)
        {
            await Task.Delay(10);

            var query = _activities.AsQueryable();

            if (fromDate.HasValue)
                query = query.Where(a => a.Timestamp >= fromDate.Value);

            if (toDate.HasValue)
                query = query.Where(a => a.Timestamp <= toDate.Value.AddDays(1));

            var stats = new Dictionary<string, int>
            {
                ["Total"] = query.Count(),
                ["Successful"] = query.Count(a => a.IsSuccessful),
                ["Failed"] = query.Count(a => !a.IsSuccessful),
                ["Login"] = query.Count(a => a.Action.Contains("تسجيل دخول")),
                ["Error"] = query.Count(a => a.Severity == "Error"),
                ["Warning"] = query.Count(a => a.Severity == "Warning"),
                ["Info"] = query.Count(a => a.Severity == "Info")
            };

            return stats;
        }

        /// <summary>
        /// الحصول على قائمة الوحدات المتاحة
        /// </summary>
        public List<string> GetAvailableModules()
        {
            return _activities.Select(a => a.Module).Distinct().OrderBy(m => m).ToList();
        }

        /// <summary>
        /// الحصول على قائمة مستويات الخطورة
        /// </summary>
        public List<string> GetSeverityLevels()
        {
            return new List<string> { "Info", "Warning", "Error", "Critical" };
        }

        /// <summary>
        /// حذف الأنشطة القديمة
        /// </summary>
        public async Task CleanupOldActivitiesAsync(int daysToKeep = 90)
        {
            var cutoffDate = DateTime.Now.AddDays(-daysToKeep);
            var toRemove = _activities.Where(a => a.Timestamp < cutoffDate).ToList();

            foreach (var activity in toRemove)
            {
                _activities.Remove(activity);
            }

            await Task.CompletedTask;
        }

        /// <summary>
        /// تصدير الأنشطة
        /// </summary>
        public async Task<List<UserActivity>> ExportActivitiesAsync(
            DateTime? fromDate = null, DateTime? toDate = null, string username = null, string module = null)
        {
            var (activities, _) = await GetActivitiesAsync(1, int.MaxValue, username, module,
                null, fromDate, toDate);
            return activities;
        }

        /// <summary>
        /// الحصول على جميع الأنشطة (للتوافق مع الكود الموجود)
        /// </summary>
        public async Task<List<UserActivity>> GetAllActivitiesAsync(
            DateTime fromDate, DateTime toDate, string module = null, string severity = null,
            int pageSize = 100, int page = 1)
        {
            var (activities, _) = await GetActivitiesAsync(page, pageSize, null, module,
                severity, fromDate, toDate);
            return activities;
        }

        /// <summary>
        /// الحصول على أنشطة مستخدم معين
        /// </summary>
        public async Task<List<UserActivity>> GetUserActivitiesAsync(int userId, DateTime fromDate, DateTime toDate,
            string module = null, int pageSize = 100, int pageNumber = 1)
        {
            await Task.CompletedTask; // محاكاة عملية غير متزامنة

            var filteredActivities = _activities.Where(a => a.UserId == userId).ToList();

            // تطبيق فلترة التاريخ
            filteredActivities = filteredActivities.Where(a =>
                a.Timestamp >= fromDate && a.Timestamp <= toDate).ToList();

            // تطبيق فلترة الوحدة
            if (!string.IsNullOrEmpty(module))
            {
                filteredActivities = filteredActivities.Where(a =>
                    a.Module.IndexOf(module, StringComparison.OrdinalIgnoreCase) >= 0).ToList();
            }

            // ترتيب حسب التاريخ (الأحدث أولاً)
            filteredActivities = filteredActivities.OrderByDescending(a => a.Timestamp).ToList();

            // تطبيق الترقيم
            var skip = (pageNumber - 1) * pageSize;
            return filteredActivities.Skip(skip).Take(pageSize).ToList();
        }

        /// <summary>
        /// تهيئة بيانات تجريبية
        /// </summary>
        private void InitializeSampleData()
        {
            var sampleActivities = new List<UserActivity>
            {
                new UserActivity
                {
                    Id = 1,
                    UserId = 1,
                    Username = "admin",
                    Action = "تسجيل دخول",
                    Module = "النظام",
                    Description = "تسجيل دخول ناجح للنظام",
                    IsSuccessful = true,
                    Severity = "Info",
                    IpAddress = "*************",
                    ActivityType = "تسجيل دخول",
                    Timestamp = DateTime.Now.AddHours(-1)
                },
                new UserActivity
                {
                    Id = 2,
                    UserId = 2,
                    Username = "user1",
                    Action = "إضافة مستخدم",
                    Module = "إدارة المستخدمين",
                    Description = "تم إضافة مستخدم جديد: user2",
                    IsSuccessful = true,
                    Severity = "Info",
                    IpAddress = "*************",
                    ActivityType = "إنشاء",
                    Timestamp = DateTime.Now.AddHours(-2)
                },
                new UserActivity
                {
                    Id = 3,
                    UserId = 2,
                    Username = "user1",
                    Action = "محاولة تسجيل دخول فاشلة",
                    Module = "النظام",
                    Description = "كلمة مرور خاطئة",
                    IsSuccessful = false,
                    Severity = "Warning",
                    IpAddress = "*************",
                    ActivityType = "فشل تسجيل دخول",
                    Timestamp = DateTime.Now.AddHours(-3)
                },
                new UserActivity
                {
                    Id = 4,
                    UserId = 1,
                    Username = "admin",
                    Action = "تعديل صلاحيات",
                    Module = "الصلاحيات",
                    Description = "تم تعديل صلاحيات المستخدم user1",
                    IsSuccessful = true,
                    Severity = "Info",
                    IpAddress = "*************",
                    ActivityType = "تعديل",
                    Timestamp = DateTime.Now.AddHours(-4)
                },
                new UserActivity
                {
                    Id = 5,
                    UserId = 0, // نظام
                    Username = "system",
                    Action = "خطأ في النظام",
                    Module = "النظام",
                    Description = "خطأ في الاتصال بقاعدة البيانات",
                    IsSuccessful = false,
                    Severity = "Error",
                    IpAddress = "localhost",
                    ActivityType = "خطأ",
                    Timestamp = DateTime.Now.AddHours(-5)
                }
            };

            _activities.AddRange(sampleActivities);
        }
    }
}
