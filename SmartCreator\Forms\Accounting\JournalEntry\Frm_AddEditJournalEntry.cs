using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using SmartCreator.Entities.Accounting;
using SmartCreator.Services.Accounting;
using SmartCreator.RJControls;
using SmartCreator.Settings;
using SmartCreator.Service;
using SmartCreator.RJForms;
using SmartCreator.Utils;

namespace SmartCreator.Forms.Accounting
{
    /// <summary>
    /// نموذج إضافة/تعديل القيد المحاسبي
    /// </summary>
    public partial class Frm_AddEditJournalEntry : RJChildForm
    {
        #region المتغيرات

        private readonly JournalEntryService _journalEntryService;
        private readonly AccountService _accountService;
        private JournalEntry _journalEntry;
        private List<Account> _accounts;
        private List<JournalEntryDetail> _details;
        private bool _isEditMode;
        private bool _isLoading = false;

        #endregion

        #region البناء

        /// <summary>
        /// إضافة قيد جديد
        /// </summary>
        public Frm_AddEditJournalEntry(JournalEntryService journalEntryService, AccountService accountService)
        {
            _journalEntryService = journalEntryService;
            _accountService = accountService;
            _isEditMode = false;
            _journalEntry = new JournalEntry
            {
                Date = DateTime.Today,
                Type = JournalEntryType.Manual,
                Status = JournalEntryStatus.Draft,
                Details = new List<JournalEntryDetail>()
            };
            _details = new List<JournalEntryDetail>();
            _accounts = new List<Account>();

            InitializeComponent();
            SetupForm();
            LoadDataAsync();


            utils utils = new utils();
            utils.Control_textSize1(this);

        }

        /// <summary>
        /// تعديل قيد موجود
        /// </summary>
        public Frm_AddEditJournalEntry(JournalEntryService journalEntryService, AccountService accountService, JournalEntry journalEntry)
        {
            _journalEntryService = journalEntryService;
            _accountService = accountService;
            _isEditMode = true;
            _journalEntry = journalEntry;
            _details = new List<JournalEntryDetail>();
            _accounts = new List<Account>();

            InitializeComponent();
            SetupForm();
            LoadDataAsync();
        }

        #endregion

        #region إعداد النموذج

        /// <summary>
        /// إعداد النموذج
        /// </summary>
        private void SetupForm()
        {
            this.Text = _isEditMode ? "تعديل قيد محاسبي" : "إضافة قيد محاسبي";
            lblTitle.Text = _isEditMode ? "تعديل قيد محاسبي" : "إضافة قيد محاسبي";

            // إعداد أعمدة جدول التفاصيل
            SetupDetailsGrid();

            // إعداد قوائم الاختيار
            SetupComboBoxes();

            // تعبئة البيانات في حالة التعديل
            if (_isEditMode)
            {
                PopulateFormData();
            }

            // إضافة صف فارغ للبداية
            AddEmptyDetailRow();
        }

        /// <summary>
        /// إعداد أعمدة جدول التفاصيل
        /// </summary>
        private void SetupDetailsGrid()
        {
            dgvDetails.AutoGenerateColumns = false;
            dgvDetails.AllowUserToAddRows = true;
            dgvDetails.AllowUserToDeleteRows = true;
            dgvDetails.Columns.Clear();

            // عمود الحساب
            var accountColumn = new DataGridViewComboBoxColumn
            {
                Name = "AccountId",
                HeaderText = "الحساب",
                DataPropertyName = "AccountId",
                Width = 200,
                DisplayMember = "DisplayText",
                ValueMember = "Id"
            };
            dgvDetails.Columns.Add(accountColumn);

            // عمود البيان
            dgvDetails.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Description",
                HeaderText = "البيان",
                DataPropertyName = "Description",
                Width = 250
            });

            // عمود المدين
            dgvDetails.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "DebitAmount",
                HeaderText = "مدين",
                DataPropertyName = "DebitAmount",
                Width = 120,
                DefaultCellStyle = new DataGridViewCellStyle
                {
                    Format = "N2",
                    Alignment = DataGridViewContentAlignment.MiddleRight
                }
            });

            // عمود الدائن
            dgvDetails.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "CreditAmount",
                HeaderText = "دائن",
                DataPropertyName = "CreditAmount",
                Width = 120,
                DefaultCellStyle = new DataGridViewCellStyle
                {
                    Format = "N2",
                    Alignment = DataGridViewContentAlignment.MiddleRight
                }
            });

            // عمود الحذف
            var deleteColumn = new DataGridViewButtonColumn
            {
                Name = "Delete",
                HeaderText = "حذف",
                Text = "🗑️",
                UseColumnTextForButtonValue = true,
                Width = 60
            };
            dgvDetails.Columns.Add(deleteColumn);
        }

        /// <summary>
        /// إعداد قوائم الاختيار
        /// </summary>
        private void SetupComboBoxes()
        {
            // إعداد قائمة أنواع القيود
            cmbEntryType.Items.Clear();
            foreach (JournalEntryType type in Enum.GetValues(typeof(JournalEntryType)))
            {
                cmbEntryType.Items.Add(new { Value = type, Text = JournalEntryTypeInfo.GetArabicName(type) });
            }
            cmbEntryType.DisplayMember = "Text";
            cmbEntryType.ValueMember = "Value";
            cmbEntryType.SelectedIndex = 0;
        }

        /// <summary>
        /// تعبئة بيانات النموذج في حالة التعديل
        /// </summary>
        private void PopulateFormData()
        {
            if (_journalEntry == null) return;

            txtEntryNumber.Text = _journalEntry.EntryNumber;
            dtpDate.Value = _journalEntry.Date;
            txtDescription.Text = _journalEntry.Description;
            txtReference.Text = _journalEntry.Reference;

            // تحديد نوع القيد
            for (int i = 0; i < cmbEntryType.Items.Count; i++)
            {
                var item = (dynamic)cmbEntryType.Items[i];
                if (item.Value.Equals(_journalEntry.Type))
                {
                    cmbEntryType.SelectedIndex = i;
                    break;
                }
            }

            // تعطيل رقم القيد في حالة التعديل
            txtEntryNumber.Enabled = false;
        }

        /// <summary>
        /// إضافة صف فارغ للتفاصيل
        /// </summary>
        private void AddEmptyDetailRow()
        {
            var newDetail = new JournalEntryDetail
            {
                JournalEntryId = _journalEntry.Id,
                LineNumber = _details.Count + 1,
                DebitAmount = 0,
                CreditAmount = 0,
                Description = ""
            };
            _details.Add(newDetail);
            RefreshDetailsGrid();
        }

        /// <summary>
        /// تحديث جدول التفاصيل
        /// </summary>
        private void RefreshDetailsGrid()
        {
            dgvDetails.DataSource = null;
            dgvDetails.DataSource = _details;

            // تحديث قائمة الحسابات في العمود
            var accountColumn = dgvDetails.Columns["AccountId"] as DataGridViewComboBoxColumn;
            if (accountColumn != null)
            {
                accountColumn.DataSource = _accounts.Select(a => new
                {
                    Id = a.Id,
                    DisplayText = $"{a.Code} - {a.Name}"
                }).ToList();
            }

            CalculateTotals();
        }

        #endregion

        #region تحميل البيانات

        /// <summary>
        /// تحميل البيانات
        /// </summary>
        private async void LoadDataAsync()
        {
            if (_isLoading) return;

            try
            {
                _isLoading = true;
                ShowLoadingIndicator(true);

                // تحميل الحسابات
                _accounts = await _accountService.GetActiveAccountsAsync();

                // تحميل تفاصيل القيد في حالة التعديل
                if (_isEditMode && _journalEntry.Id > 0)
                {
                    var details = await _journalEntryService.GetJournalEntryDetailsAsync(_journalEntry.Id);
                    _details = details.ToList();
                }

                RefreshDetailsGrid();
            }
            catch (Exception ex)
            {
                RJMessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                _isLoading = false;
                ShowLoadingIndicator(false);
            }
        }

        /// <summary>
        /// عرض مؤشر التحميل
        /// </summary>
        private void ShowLoadingIndicator(bool show)
        {
            if (this.InvokeRequired)
            {
                this.Invoke(new Action<bool>(ShowLoadingIndicator), show);
                return;
            }

            lblStatus.Text = show ? "جاري التحميل..." : "جاهز";
            lblStatus.ForeColor = show ? Color.Orange : Color.Green;
        }

        #endregion

        #region العمليات

        /// <summary>
        /// حساب الإجماليات
        /// </summary>
        private void CalculateTotals()
        {
            var totalDebit = _details.Sum(d => d.DebitAmount);
            var totalCredit = _details.Sum(d => d.CreditAmount);
            var difference = totalDebit - totalCredit;

            lblTotalDebit.Text = $"إجمالي المدين: {totalDebit:N2}";
            lblTotalCredit.Text = $"إجمالي الدائن: {totalCredit:N2}";
            lblDifference.Text = $"الفرق: {difference:N2}";

            // تلوين الفرق
            if (difference == 0)
            {
                lblDifference.ForeColor = Color.Green;
                lblBalance.Text = "القيد متوازن ✓";
                lblBalance.ForeColor = Color.Green;
            }
            else
            {
                lblDifference.ForeColor = Color.Red;
                lblBalance.Text = "القيد غير متوازن ✗";
                lblBalance.ForeColor = Color.Red;
            }

            // تفعيل/تعطيل زر الحفظ
            btnSave.Enabled = difference == 0 && _details.Any(d => d.DebitAmount > 0 || d.CreditAmount > 0);
        }

        /// <summary>
        /// التحقق من صحة البيانات
        /// </summary>
        private bool ValidateData()
        {
            if (string.IsNullOrWhiteSpace(txtDescription.Text))
            {
                RJMessageBox.Show("يرجى إدخال وصف القيد", "تحقق من البيانات",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtDescription.Focus();
                return false;
            }

            if (!_details.Any(d => d.DebitAmount > 0 || d.CreditAmount > 0))
            {
                RJMessageBox.Show("يرجى إضافة تفاصيل للقيد", "تحقق من البيانات",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }

            var totalDebit = _details.Sum(d => d.DebitAmount);
            var totalCredit = _details.Sum(d => d.CreditAmount);

            if (totalDebit != totalCredit)
            {
                RJMessageBox.Show("القيد غير متوازن - يجب أن يكون إجمالي المدين مساوياً لإجمالي الدائن",
                    "تحقق من البيانات", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }

            return true;
        }

        /// <summary>
        /// حفظ القيد
        /// </summary>
        private async Task<bool> SaveJournalEntryAsync()
        {
            try
            {
                if (!ValidateData()) return false;

                // تحديث بيانات القيد
                _journalEntry.Date = dtpDate.Value;
                _journalEntry.Description = txtDescription.Text.Trim();
                _journalEntry.Reference = txtReference.Text.Trim();
                _journalEntry.Type = ((dynamic)cmbEntryType.SelectedItem).Value;
                _journalEntry.Details = _details.Where(d => d.DebitAmount > 0 || d.CreditAmount > 0).ToList();

                // إعادة ترقيم التفاصيل
                for (int i = 0; i < _journalEntry.Details.Count; i++)
                {
                    _journalEntry.Details[i].LineNumber = i + 1;
                }

                _journalEntry.RecalculateTotals();

                if (_isEditMode)
                {
                    await _journalEntryService.UpdateJournalEntryAsync(_journalEntry);
                }
                else
                {
                    var newId = await _journalEntryService.AddJournalEntryAsync(_journalEntry);
                    _journalEntry.Id = newId;
                }

                return true;
            }
            catch (Exception ex)
            {
                RJMessageBox.Show($"خطأ في حفظ القيد: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                return false;
            }
        }

        #endregion

        #region أحداث النموذج

        /// <summary>
        /// تغيير قيمة خلية في جدول التفاصيل
        /// </summary>
        private void DgvDetails_CellValueChanged(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex < 0 || _isLoading) return;

            try
            {
                var row = dgvDetails.Rows[e.RowIndex];
                var detail = row.DataBoundItem as JournalEntryDetail;

                if (detail != null)
                {
                    // التأكد من أن المبالغ صحيحة
                    if (detail.DebitAmount < 0) detail.DebitAmount = 0;
                    if (detail.CreditAmount < 0) detail.CreditAmount = 0;

                    // لا يمكن أن يكون الحساب مدين ودائن في نفس الوقت
                    if (e.ColumnIndex == dgvDetails.Columns["DebitAmount"].Index && detail.DebitAmount > 0)
                    {
                        detail.CreditAmount = 0;
                    }
                    else if (e.ColumnIndex == dgvDetails.Columns["CreditAmount"].Index && detail.CreditAmount > 0)
                    {
                        detail.DebitAmount = 0;
                    }

                    RefreshDetailsGrid();
                }
            }
            catch (Exception ex)
            {
                RJMessageBox.Show($"خطأ في تحديث التفاصيل: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// النقر على خلية في جدول التفاصيل
        /// </summary>
        private void DgvDetails_CellClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex < 0) return;

            try
            {
                // التحقق من النقر على عمود الحذف
                if (e.ColumnIndex == dgvDetails.Columns["Delete"].Index)
                {
                    var result = RJMessageBox.Show("هل أنت متأكد من حذف هذا الصف؟", "تأكيد الحذف",
                        MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                    if (result == DialogResult.Yes)
                    {
                        if (e.RowIndex < _details.Count)
                        {
                            _details.RemoveAt(e.RowIndex);
                            RefreshDetailsGrid();
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                RJMessageBox.Show($"خطأ في حذف الصف: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// إضافة صف جديد
        /// </summary>
        private void BtnAddRow_Click(object sender, EventArgs e)
        {
            AddEmptyDetailRow();
        }

        /// <summary>
        /// حفظ القيد
        /// </summary>
        private async void BtnSave_Click(object sender, EventArgs e)
        {
            try
            {
                btnSave.Enabled = false;
                lblStatus.Text = "جاري الحفظ...";
                lblStatus.ForeColor = Color.Orange;

                if (await SaveJournalEntryAsync())
                {
                    this.DialogResult = DialogResult.OK;
                    this.Close();
                }
            }
            catch (Exception ex)
            {
                RJMessageBox.Show($"خطأ في حفظ القيد: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                btnSave.Enabled = true;
                lblStatus.Text = "جاهز";
                lblStatus.ForeColor = Color.Green;
            }
        }

        /// <summary>
        /// إلغاء العملية
        /// </summary>
        private void BtnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        #endregion

        private void Frm_AddEditJournalEntry_SizeChanged(object sender, EventArgs e)
        {
            this.Refresh();
            pnlMain.Refresh();
            dgvDetails.Refresh();
            pnlDetails.Refresh();
            pnlHeader.Refresh();
            pnlTotals.Refresh();
            pnlStatus.Refresh();
        }
    }
}
