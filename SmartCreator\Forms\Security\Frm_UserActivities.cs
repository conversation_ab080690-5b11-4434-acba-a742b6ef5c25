using SmartCreator.Models;
using SmartCreator.Entities;
using SmartCreator.Services;
using SmartCreator.Services.Security;
using SmartCreator.RJControls;
using SmartCreator.Helpers;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace SmartCreator.Forms.Security
{
    /// <summary>
    /// نموذج عرض أنشطة المستخدم المحسن
    /// </summary>
    public partial class Frm_UserActivities : Form
    {
        private readonly UserActivityService _activityService;
        private readonly SmartCreator.Entities.User _user;
        private List<SmartCreator.Entities.UserActivity> _activities;

        public Frm_UserActivities(UserActivityService activityService, SmartCreator.Entities.User user)
        {
            _activityService = activityService;
            _user = user;
            InitializeComponent();
            SetupForm();
            LoadDataAsync();
        }



        /// <summary>
        /// إعداد النموذج
        /// </summary>
        private void SetupForm()
        {
            // تحديث معلومات المستخدم
            if (_user != null)
            {
                lblUserInfo.Text = $"المستخدم: {_user.DisplayName} ({_user.Username})";
            }

            // تعيين التواريخ الافتراضية
            dtpFromDate.Value = DateTime.Now.AddDays(-30);
            dtpToDate.Value = DateTime.Now;

            // إعداد أعمدة الجدول
            SetupDataGridViewColumns();

            // إعداد قوائم الفلاتر
            SetupFilterComboBoxes();
        }

        /// <summary>
        /// إعداد أعمدة الجدول
        /// </summary>
        private void SetupDataGridViewColumns()
        {
            dgvActivities.Columns.Clear();

            dgvActivities.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Timestamp",
                HeaderText = "التاريخ والوقت",
                DataPropertyName = "Timestamp",
                DefaultCellStyle = { Format = "yyyy-MM-dd HH:mm:ss" },
                Width = 150
            });

            dgvActivities.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Action",
                HeaderText = "الإجراء",
                DataPropertyName = "Action",
                Width = 120
            });

            dgvActivities.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Module",
                HeaderText = "الوحدة",
                DataPropertyName = "Module",
                Width = 100
            });

            dgvActivities.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Description",
                HeaderText = "الوصف",
                DataPropertyName = "Description",
                AutoSizeMode = DataGridViewAutoSizeColumnMode.Fill
            });

            dgvActivities.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Severity",
                HeaderText = "الخطورة",
                DataPropertyName = "Severity",
                Width = 80
            });

            dgvActivities.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "IpAddress",
                HeaderText = "عنوان IP",
                DataPropertyName = "IpAddress",
                Width = 120
            });

            // تلوين الصفوف حسب الخطورة
            dgvActivities.CellFormatting += DgvActivities_CellFormatting;
        }

        /// <summary>
        /// تنسيق الخلايا حسب الخطورة
        /// </summary>
        private void DgvActivities_CellFormatting(object sender, DataGridViewCellFormattingEventArgs e)
        {
            if (e.RowIndex >= 0 && dgvActivities.Rows[e.RowIndex].DataBoundItem is SmartCreator.Entities.UserActivity activity)
            {
                var row = dgvActivities.Rows[e.RowIndex];

                switch (activity.Severity?.ToLower())
                {
                    case "error":
                        row.DefaultCellStyle.BackColor = Color.FromArgb(139, 69, 19);
                        break;
                    case "warning":
                        row.DefaultCellStyle.BackColor = Color.FromArgb(184, 134, 11);
                        break;
                    case "info":
                        row.DefaultCellStyle.BackColor = Color.FromArgb(74, 79, 99);
                        break;
                    default:
                        row.DefaultCellStyle.BackColor = Color.FromArgb(74, 79, 99);
                        break;
                }
            }
        }

        /// <summary>
        /// إعداد قوائم الفلاتر
        /// </summary>
        private void SetupFilterComboBoxes()
        {
            // قائمة الوحدات
            cmbModule.Items.Clear();
            cmbModule.Items.Add("جميع الوحدات");
            cmbModule.Items.AddRange(new string[]
            {
                ActivityModules.AUTHENTICATION,
                ActivityModules.USER_MANAGEMENT,
                ActivityModules.PERMISSIONS,
                ActivityModules.ACCOUNTING,
                ActivityModules.ACCOUNTS,
                ActivityModules.JOURNAL_ENTRIES,
                ActivityModules.REPORTS,
                ActivityModules.SYSTEM
            });
            cmbModule.SelectedIndex = 0;

            // قائمة مستويات الخطورة
            cmbSeverity.Items.Clear();
            cmbSeverity.Items.Add("جميع المستويات");
            cmbSeverity.Items.AddRange(new string[]
            {
                SeverityLevels.INFO,
                SeverityLevels.WARNING,
                SeverityLevels.ERROR,
                SeverityLevels.CRITICAL
            });
            cmbSeverity.SelectedIndex = 0;
        }

        /// <summary>
        /// تحميل البيانات
        /// </summary>
        private async void LoadDataAsync()
        {
            try
            {
                await LoadActivitiesAsync();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تحميل الأنشطة
        /// </summary>
        private async Task LoadActivitiesAsync()
        {
            try
            {
                var fromDate = dtpFromDate.Value.Date;
                var toDate = dtpToDate.Value.Date.AddDays(1).AddSeconds(-1);
                var module = cmbModule.SelectedIndex == 0 ? null : cmbModule.SelectedItem.ToString();
                var severity = cmbSeverity.SelectedIndex == 0 ? null : cmbSeverity.SelectedItem.ToString();

                _activities = await _activityService.GetUserActivitiesAsync(
                    _user.Id, fromDate, toDate, module);

                // تطبيق فلتر الخطورة إذا وجد
                var filteredActivities = _activities;
                if (!string.IsNullOrWhiteSpace(severity))
                {
                    filteredActivities = filteredActivities.Where(a =>
                        a.Severity.Equals(severity, StringComparison.OrdinalIgnoreCase)).ToList();
                }

                // تطبيق فلتر البحث إذا وجد
                if (!string.IsNullOrWhiteSpace(txtSearch.Text))
                {
                    var searchText = txtSearch.Text.ToLower();
                    filteredActivities = filteredActivities.Where(a =>
                        a.Action.ToLower().Contains(searchText) ||
                        a.Description?.ToLower().Contains(searchText) == true ||
                        a.Module.ToLower().Contains(searchText)).ToList();
                }

                dgvActivities.DataSource = filteredActivities.OrderByDescending(a => a.Timestamp).ToList();
                lblTotalActivities.Text = $"إجمالي الأنشطة: {filteredActivities.Count}";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الأنشطة: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تحديث البيانات
        /// </summary>
        private async void BtnRefresh_Click(object sender, EventArgs e)
        {
            await LoadActivitiesAsync();
        }

        /// <summary>
        /// تطبيق الفلتر
        /// </summary>
        private async void BtnFilter_Click(object sender, EventArgs e)
        {
            await LoadActivitiesAsync();
        }

        /// <summary>
        /// مسح الفلاتر
        /// </summary>
        private async void BtnClear_Click(object sender, EventArgs e)
        {
            dtpFromDate.Value = DateTime.Now.AddDays(-30);
            dtpToDate.Value = DateTime.Now;
            cmbModule.SelectedIndex = 0;
            cmbSeverity.SelectedIndex = 0;
            txtSearch.Text = "";
            await LoadActivitiesAsync();
        }

        /// <summary>
        /// البحث في الأنشطة
        /// </summary>
        private async void TxtSearch_TextChanged(object sender, EventArgs e)
        {
            // تأخير البحث لتجنب الاستدعاءات المتكررة
            await Task.Delay(500);
            if (txtSearch.Text == ((TextBox)sender).Text) // التأكد من عدم تغيير النص
            {
                await LoadActivitiesAsync();
            }
        }

        /// <summary>
        /// تصدير البيانات
        /// </summary>
        private void BtnExport_Click(object sender, EventArgs e)
        {
            try
            {
                if (_activities == null || _activities.Count == 0)
                {
                    MessageBox.Show("لا توجد بيانات للتصدير", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                var saveDialog = new SaveFileDialog
                {
                    Filter = "CSV Files (*.csv)|*.csv|Excel Files (*.xlsx)|*.xlsx",
                    FileName = $"أنشطة_{_user.Username}_{DateTime.Now:yyyyMMdd}.csv"
                };

                if (saveDialog.ShowDialog() == DialogResult.OK)
                {
                    ExportToCSV(saveDialog.FileName);
                    MessageBox.Show("تم تصدير البيانات بنجاح", "نجح التصدير",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تصدير البيانات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تصدير البيانات إلى CSV
        /// </summary>
        private void ExportToCSV(string fileName)
        {
            using (var writer = new System.IO.StreamWriter(fileName, false, System.Text.Encoding.UTF8))
            {
                // كتابة العناوين
                writer.WriteLine("التاريخ والوقت,الإجراء,الوحدة,الوصف,الخطورة,عنوان IP");

                // كتابة البيانات
                foreach (var activity in _activities.OrderByDescending(a => a.Timestamp))
                {
                    writer.WriteLine($"{activity.Timestamp:yyyy-MM-dd HH:mm:ss}," +
                                   $"{activity.Action}," +
                                   $"{activity.Module}," +
                                   $"\"{activity.Description}\"," +
                                   $"{activity.Severity}," +
                                   $"{activity.IpAddress}");
                }
            }
        }
    }
}
