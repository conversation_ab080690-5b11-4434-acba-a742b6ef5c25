using SmartCreator.RJForms;
using SmartCreator.RJControls;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace SmartCreator.Forms.Security
{
    /// <summary>
    /// واجهة إدارة الصلاحيات
    /// </summary>
    public partial class Frm_Permissions : RJChildForm
    {
        private List<SimplePermission> _permissions;
        private List<PermissionCategory> _categories;
        private SimplePermission _selectedPermission;

        public Frm_Permissions()
        {
            try
            {
                InitializeComponent();
                InitializeForm();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إنشاء نموذج إدارة الصلاحيات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تهيئة النموذج
        /// </summary>
        private void InitializeForm()
        {
            try
            {
                // إعداد خصائص النموذج
                this.Text = "إدارة الصلاحيات";
                this.FormIcon = FontAwesome.Sharp.IconChar.Shield;
                this.Caption = "إدارة الصلاحيات";

                // إعداد DataGridView للصلاحيات
                SetupPermissionsDataGridView();

                // إعداد الأحداث
                SetupEvents();

                // تحميل البيانات
                LoadDataAsync();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تهيئة نموذج إدارة الصلاحيات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// إعداد DataGridView للصلاحيات
        /// </summary>
        private void SetupPermissionsDataGridView()
        {
            try
            {
                // إعداد التنسيق الأساسي
                ApplyBasicDataGridViewStyling(dgvPermissions);

                // إعداد الأعمدة
                SetupPermissionsColumns();

                // إضافة تأثيرات التفاعل
                AddDataGridViewInteractionEffects(dgvPermissions);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إعداد جدول الصلاحيات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// إعداد الأحداث
        /// </summary>
        private void SetupEvents()
        {
            try
            {
                // أحداث الصلاحيات
                btnAddPermission.Click += BtnAddPermission_Click;
                btnEditPermission.Click += BtnEditPermission_Click;
                btnDeletePermission.Click += BtnDeletePermission_Click;
                btnRefresh.Click += BtnRefresh_Click;
                dgvPermissions.SelectionChanged += DgvPermissions_SelectionChanged;
                txtSearchPermissions.TextChanged += TxtSearchPermissions_TextChanged;

                // أحداث الفئات
                btnAddCategory.Click += BtnAddCategory_Click;
                btnEditCategory.Click += BtnEditCategory_Click;
                btnDeleteCategory.Click += BtnDeleteCategory_Click;
                cmbCategory.OnSelectedIndexChanged += CmbCategory_SelectedIndexChanged;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إعداد الأحداث: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تحميل البيانات
        /// </summary>
        private async void LoadDataAsync()
        {
            try
            {
                // تحميل الفئات
                await LoadCategoriesAsync();

                // تحميل الصلاحيات
                await LoadPermissionsAsync();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تحميل الفئات
        /// </summary>
        private async Task LoadCategoriesAsync()
        {
            try
            {
                // محاكاة تحميل الفئات
                _categories = new List<PermissionCategory>
                {
                    new PermissionCategory { Id = 1, Name = "إدارة المستخدمين", Description = "صلاحيات إدارة المستخدمين" },
                    new PermissionCategory { Id = 2, Name = "إدارة النظام", Description = "صلاحيات إدارة النظام" },
                    new PermissionCategory { Id = 3, Name = "التقارير", Description = "صلاحيات التقارير" },
                    new PermissionCategory { Id = 4, Name = "المحاسبة", Description = "صلاحيات المحاسبة" }
                };

                // تحديث ComboBox
                cmbCategory.Items.Clear();
                cmbCategory.Items.Add("جميع الفئات");
                foreach (var category in _categories)
                {
                    cmbCategory.Items.Add(category.Name);
                }
                cmbCategory.SelectedIndex = 0;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الفئات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تحميل الصلاحيات
        /// </summary>
        private async Task LoadPermissionsAsync()
        {
            try
            {
                // محاكاة تحميل الصلاحيات
                _permissions = new List<SimplePermission>
                {
                    new SimplePermission { Id = 1, Code = "USER_CREATE", Name = "إضافة مستخدم", Description = "إضافة مستخدم جديد", Module = "إدارة المستخدمين", Category = "إدارة المستخدمين", IsActive = true },
                    new SimplePermission { Id = 2, Code = "USER_EDIT", Name = "تعديل مستخدم", Description = "تعديل بيانات المستخدم", Module = "إدارة المستخدمين", Category = "إدارة المستخدمين", IsActive = true },
                    new SimplePermission { Id = 3, Code = "USER_DELETE", Name = "حذف مستخدم", Description = "حذف مستخدم من النظام", Module = "إدارة المستخدمين", Category = "إدارة المستخدمين", IsActive = true },
                    new SimplePermission { Id = 4, Code = "USER_VIEW", Name = "عرض المستخدمين", Description = "عرض قائمة المستخدمين", Module = "إدارة المستخدمين", Category = "إدارة المستخدمين", IsActive = true },
                    new SimplePermission { Id = 5, Code = "PERMISSION_MANAGE", Name = "إدارة الصلاحيات", Description = "إدارة صلاحيات المستخدمين", Module = "إدارة النظام", Category = "إدارة النظام", IsActive = true },
                    new SimplePermission { Id = 6, Code = "AUDIT_VIEW", Name = "عرض سجل الأحداث", Description = "عرض سجل أحداث النظام", Module = "إدارة النظام", Category = "إدارة النظام", IsActive = true },
                    new SimplePermission { Id = 7, Code = "REPORT_VIEW", Name = "عرض التقارير", Description = "عرض التقارير", Module = "التقارير", Category = "التقارير", IsActive = true },
                    new SimplePermission { Id = 8, Code = "REPORT_EXPORT", Name = "تصدير التقارير", Description = "تصدير التقارير", Module = "التقارير", Category = "التقارير", IsActive = true },
                    new SimplePermission { Id = 9, Code = "ACCOUNT_VIEW", Name = "عرض الحسابات", Description = "عرض الحسابات المالية", Module = "المحاسبة", Category = "المحاسبة", IsActive = true },
                    new SimplePermission { Id = 10, Code = "ACCOUNT_EDIT", Name = "تعديل الحسابات", Description = "تعديل الحسابات المالية", Module = "المحاسبة", Category = "المحاسبة", IsActive = true }
                };

                dgvPermissions.DataSource = _permissions;
                UpdateButtonStates();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الصلاحيات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تحديث حالة الأزرار
        /// </summary>
        private void UpdateButtonStates()
        {
            var hasSelection = dgvPermissions.SelectedRows.Count > 0;
            btnEditPermission.Enabled = hasSelection;
            btnDeletePermission.Enabled = hasSelection;
        }

        #region أحداث الصلاحيات

        private void BtnAddPermission_Click(object sender, EventArgs e)
        {
            try
            {
                MessageBox.Show("سيتم إضافة نموذج إضافة صلاحية جديدة", "معلومات",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إضافة الصلاحية: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnEditPermission_Click(object sender, EventArgs e)
        {
            if (_selectedPermission == null) return;

            try
            {
                MessageBox.Show($"سيتم تعديل الصلاحية: {_selectedPermission.Name}", "معلومات",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تعديل الصلاحية: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnDeletePermission_Click(object sender, EventArgs e)
        {
            if (_selectedPermission == null) return;

            var result = MessageBox.Show(
                $"هل أنت متأكد من حذف الصلاحية '{_selectedPermission.Name}'؟",
                "تأكيد الحذف", MessageBoxButtons.YesNo, MessageBoxIcon.Warning);

            if (result == DialogResult.Yes)
            {
                try
                {
                    MessageBox.Show("تم حذف الصلاحية بنجاح", "نجح",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    LoadDataAsync();
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في حذف الصلاحية: {ex.Message}", "خطأ",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        private void BtnRefresh_Click(object sender, EventArgs e)
        {
            LoadDataAsync();
        }

        private void DgvPermissions_SelectionChanged(object sender, EventArgs e)
        {
            if (dgvPermissions.SelectedRows.Count > 0)
            {
                var selectedRow = dgvPermissions.SelectedRows[0];
                _selectedPermission = selectedRow.DataBoundItem as SimplePermission;
            }
            else
            {
                _selectedPermission = null;
            }

            UpdateButtonStates();
        }

        private void TxtSearchPermissions_TextChanged(object sender, EventArgs e)
        {
            ApplyDataGridViewSearchFilter(dgvPermissions, txtSearchPermissions.Text);
        }

        #endregion

        #region أحداث الفئات

        private void BtnAddCategory_Click(object sender, EventArgs e)
        {
            try
            {
                MessageBox.Show("سيتم إضافة نموذج إضافة فئة جديدة", "معلومات",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إضافة الفئة: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnEditCategory_Click(object sender, EventArgs e)
        {
            try
            {
                MessageBox.Show("سيتم إضافة نموذج تعديل الفئة", "معلومات",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تعديل الفئة: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnDeleteCategory_Click(object sender, EventArgs e)
        {
            try
            {
                MessageBox.Show("سيتم إضافة نموذج حذف الفئة", "معلومات",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حذف الفئة: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void CmbCategory_SelectedIndexChanged(object sender, EventArgs e)
        {
            try
            {
                // تطبيق فلتر الفئة
                if (cmbCategory.SelectedIndex == 0) // جميع الفئات
                {
                    dgvPermissions.DataSource = _permissions;
                }
                else
                {
                    var selectedCategory = cmbCategory.SelectedItem.ToString();
                    var filteredPermissions = _permissions.Where(p => p.Category == selectedCategory).ToList();
                    dgvPermissions.DataSource = filteredPermissions;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تطبيق فلتر الفئة: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region طرق مساعدة

        /// <summary>
        /// تطبيق التنسيق الأساسي على RJDataGridView
        /// </summary>
        private void ApplyBasicDataGridViewStyling(RJControls.RJDataGridView dgv)
        {
            // نفس التنسيق المستخدم في Frm_UserManagement
            dgv.AllowUserToAddRows = false;
            dgv.AllowUserToDeleteRows = false;
            dgv.ReadOnly = true;
            dgv.MultiSelect = false;
            dgv.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dgv.RightToLeft = RightToLeft.Yes;

            dgv.BackgroundColor = Color.White;
            dgv.BorderStyle = BorderStyle.None;
            dgv.CellBorderStyle = DataGridViewCellBorderStyle.SingleHorizontal;
            dgv.ColumnHeadersBorderStyle = DataGridViewHeaderBorderStyle.None;
            dgv.EnableHeadersVisualStyles = false;
            dgv.GridColor = Color.FromArgb(229, 226, 244);
            dgv.RowHeadersVisible = false;

            dgv.RowTemplate.Height = 35;
            dgv.AlternatingRowsDefaultCellStyle.BackColor = Color.FromArgb(248, 249, 250);

            dgv.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(123, 104, 238);
            dgv.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            dgv.ColumnHeadersDefaultCellStyle.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            dgv.ColumnHeadersDefaultCellStyle.SelectionBackColor = Color.FromArgb(123, 104, 238);
            dgv.ColumnHeadersDefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dgv.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            dgv.ColumnHeadersHeight = 40;

            dgv.DefaultCellStyle.BackColor = Color.White;
            dgv.DefaultCellStyle.ForeColor = Color.FromArgb(64, 64, 64);
            dgv.DefaultCellStyle.Font = new Font("Segoe UI", 9F);
            dgv.DefaultCellStyle.SelectionBackColor = Color.FromArgb(229, 226, 244);
            dgv.DefaultCellStyle.SelectionForeColor = Color.FromArgb(64, 64, 64);
            dgv.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dgv.DefaultCellStyle.Padding = new Padding(5);
        }

        /// <summary>
        /// إعداد أعمدة الصلاحيات
        /// </summary>
        private void SetupPermissionsColumns()
        {
            dgvPermissions.AutoGenerateColumns = false;
            dgvPermissions.Columns.Clear();

            dgvPermissions.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Id",
                HeaderText = "المعرف",
                DataPropertyName = "Id",
                Visible = false
            });

            dgvPermissions.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Code",
                HeaderText = "الكود",
                DataPropertyName = "Code",
                Width = 120,
                MinimumWidth = 100
            });

            dgvPermissions.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Name",
                HeaderText = "اسم الصلاحية",
                DataPropertyName = "Name",
                Width = 150,
                MinimumWidth = 120
            });

            dgvPermissions.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Description",
                HeaderText = "الوصف",
                DataPropertyName = "Description",
                Width = 200,
                MinimumWidth = 150,
                AutoSizeMode = DataGridViewAutoSizeColumnMode.Fill
            });

            dgvPermissions.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Module",
                HeaderText = "الوحدة",
                DataPropertyName = "Module",
                Width = 120,
                MinimumWidth = 100
            });

            dgvPermissions.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Category",
                HeaderText = "الفئة",
                DataPropertyName = "Category",
                Width = 120,
                MinimumWidth = 100
            });

            dgvPermissions.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "IsActive",
                HeaderText = "الحالة",
                DataPropertyName = "IsActive",
                Width = 80,
                MinimumWidth = 60
            });

            dgvPermissions.CellFormatting += DgvPermissions_CellFormatting;
        }

        /// <summary>
        /// إضافة تأثيرات التفاعل للـ DataGridView
        /// </summary>
        private void AddDataGridViewInteractionEffects(RJControls.RJDataGridView dgv)
        {
            dgv.CellMouseEnter += (sender, e) =>
            {
                if (e.RowIndex >= 0)
                {
                    dgv.Rows[e.RowIndex].DefaultCellStyle.BackColor = Color.FromArgb(240, 245, 249);
                }
            };

            dgv.CellMouseLeave += (sender, e) =>
            {
                if (e.RowIndex >= 0)
                {
                    dgv.Rows[e.RowIndex].DefaultCellStyle.BackColor =
                        e.RowIndex % 2 == 0 ? Color.White : Color.FromArgb(248, 249, 250);
                }
            };
        }

        /// <summary>
        /// تطبيق فلتر البحث على DataGridView
        /// </summary>
        private void ApplyDataGridViewSearchFilter(RJControls.RJDataGridView dgv, string searchText)
        {
            if (string.IsNullOrWhiteSpace(searchText))
            {
                foreach (DataGridViewRow row in dgv.Rows)
                {
                    row.Visible = true;
                }
                return;
            }

            foreach (DataGridViewRow row in dgv.Rows)
            {
                bool found = false;
                foreach (DataGridViewCell cell in row.Cells)
                {
                    if (cell.Value != null &&
                        cell.Value.ToString().IndexOf(searchText, StringComparison.OrdinalIgnoreCase) >= 0)
                    {
                        found = true;
                        break;
                    }
                }
                row.Visible = found;
            }
        }

        /// <summary>
        /// تنسيق خلايا الصلاحيات
        /// </summary>
        private void DgvPermissions_CellFormatting(object sender, DataGridViewCellFormattingEventArgs e)
        {
            if (e.ColumnIndex == dgvPermissions.Columns["IsActive"].Index && e.Value != null)
            {
                bool isActive = Convert.ToBoolean(e.Value);
                e.Value = isActive ? "نشط" : "غير نشط";
                e.CellStyle.ForeColor = isActive ? Color.FromArgb(40, 167, 69) : Color.FromArgb(220, 53, 69);
                e.FormattingApplied = true;
            }
        }

        #endregion
    }

    /// <summary>
    /// فئة الصلاحيات البسيطة
    /// </summary>
    public class SimplePermission
    {
        public int Id { get; set; }
        public string Code { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public string Module { get; set; }
        public string Category { get; set; }
        public bool IsActive { get; set; }
    }

    /// <summary>
    /// فئة الصلاحيات
    /// </summary>
    public class PermissionCategory
    {
        public int Id { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
    }
}
