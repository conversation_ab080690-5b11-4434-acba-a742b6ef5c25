﻿// using DevExpress.Utils.MVVM.Services;
// using DevExpress.XtraLayout;
using SmartCreator.Data;
using SmartCreator.Entities;
using SmartCreator.Forms;
using SmartCreator.Forms.Accounting;
using SmartCreator.Forms.Accounting.AccountMove;
using SmartCreator.Forms.Accounting.Accounts;
using SmartCreator.Forms.Accounting.Partner;
using SmartCreator.Forms.Acive_Host;
//using ServiceStack.OrmLite;
using SmartCreator.Forms.BatchCards;
using SmartCreator.Forms.Brodband;
using SmartCreator.Forms.Devices;
using SmartCreator.Forms.Hotspot;
using SmartCreator.Forms.PageHtml;
using SmartCreator.Forms.SellingPoints;
using SmartCreator.Forms.Security;
using SmartCreator.Forms.Testing;
using SmartCreator.Forms.UserManager;
using SmartCreator.Forms.UserManager.Maintenance;
using SmartCreator.Models;
using SmartCreator.Properties;
using SmartCreator.RJControls;
using SmartCreator.RJForms;
using SmartCreator.Service;
using SmartCreator.Services;
using SmartCreator.Services.Accounting;
using SmartCreator.Services.Security;
using SmartCreator.Settings;
using SmartCreator.TestAndDemo;
using SmartCreator.Utils;
using SmartCreator.ViewModels;
using System;
//using SmartCreator.TestAndDemo;
using System.Diagnostics;
using System.Drawing;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace SmartCreator
{
    public partial class MainForm : RJForms.RJMainForm
    {
        #region -> Fields

        private User userConnected;
        private string linkUrl = "";
        private int xPos = 0;
        //public static UC_StatusBar_Info UC_StatusBar = null;
        private Sql_DataAccess Local_DA = null;
        private Smart_DataAccess Smart_DA = null;



        //============================================================================
        private readonly User _currentUser;
        private readonly string _sessionId;
        private readonly DatabaseHelper _dbHelper;
        //private readonly UserService _userService;
        //private readonly RoleService _roleService;
        //private readonly PermissionService _permissionService;
        private readonly AuditService _auditService;
        //private readonly SessionService _sessionService;
        //private readonly AuthorizationService _authorizationService;
        //private readonly DashboardService _dashboardService;
        private readonly NotificationService _notificationService;
        //private readonly NotificationRuleEngine _notificationRuleEngine;
        //private readonly NotificationScheduler _notificationScheduler;
        //private readonly NotificationDatabaseService _notificationDatabaseService;

        //============================================================================


        #endregion

        #region -> Journal Entries Integration

        /// <summary>
        /// إضافة أزرار القيود المحاسبية المحسنة إلى النموذج الرئيسي
        /// </summary>
        private void AddJournalEntriesButtons()
        {
            try
            {
                // إنشاء لوحة أزرار القيود المحاسبية
                var journalPanel = new Panel
                {
                    Name = "pnlJournalEntries",
                    Size = new Size(200, 450),
                    Location = new Point(10, 100),
                    BackColor = Color.FromArgb(42, 46, 50),
                    BorderStyle = BorderStyle.FixedSingle
                };

                // إضافة عنوان للوحة
                var titleLabel = new RJLabel
                {
                    Text = "القيود المحاسبية المحسنة",
                    Size = new Size(190, 30),
                    Location = new Point(5, 5),
                    ForeColor = Color.White,
                    BackColor = Color.FromArgb(23, 162, 184),
                    TextAlign = ContentAlignment.MiddleCenter,
                    Font = new Font("Droid Arabic Kufi", 10F, FontStyle.Bold)
                };
                journalPanel.Controls.Add(titleLabel);

                int currentY = 40;
                int buttonHeight = 35;
                int spacing = 5;

                // زر إدارة القيود
                var btnManageEntries = CreateJournalButton(
                    "إدارة القيود المحاسبية",
                    new Point(10, currentY),
                    Color.FromArgb(23, 162, 184)
                );
                btnManageEntries.Click += BtnManageEntries_Click;
                journalPanel.Controls.Add(btnManageEntries);
                currentY += buttonHeight + spacing;

                // زر إضافة قيد
                var btnAddEntry = CreateJournalButton(
                    "إضافة قيد جديد",
                    new Point(10, currentY),
                    Color.FromArgb(40, 167, 69)
                );
                btnAddEntry.Click += BtnAddEntry_Click;
                journalPanel.Controls.Add(btnAddEntry);
                currentY += buttonHeight + spacing;

                // زر اختيار حساب
                var btnSelectAccount = CreateJournalButton(
                    "اختيار حساب",
                    new Point(10, currentY),
                    Color.FromArgb(255, 193, 7)
                );
                btnSelectAccount.Click += BtnSelectAccount_Click;
                journalPanel.Controls.Add(btnSelectAccount);
                currentY += buttonHeight + spacing;

                // زر الاختبار النهائي
                var btnFinalTest = CreateJournalButton(
                    "🏆 الاختبار النهائي",
                    new Point(10, currentY),
                    Color.FromArgb(46, 204, 113)
                );
                btnFinalTest.Click += (s, e) => Forms.Testing.CS2001_Fixed_Final_Test.RunFinalCompilationTest();
                journalPanel.Controls.Add(btnFinalTest);
                currentY += buttonHeight + spacing;

                // زر الاختبار الدائم
                var btnPermanentTest = CreateJournalButton(
                    "🎯 حل CS2001 الدائم",
                    new Point(10, currentY),
                    Color.FromArgb(155, 89, 182)
                );
                btnPermanentTest.Click += (s, e) => Forms.Testing.CS2001_Permanently_Fixed_Test.RunPermanentFixTest();
                journalPanel.Controls.Add(btnPermanentTest);
                currentY += buttonHeight + spacing;

                // زر دليل RJControls Designer
                var btnDesignerGuide = CreateJournalButton(
                    "📖 دليل RJControls Designer",
                    new Point(10, currentY),
                    Color.FromArgb(230, 126, 34)
                );
                btnDesignerGuide.Click += (s, e) => Forms.Testing.RJControls_Designer_Guide.ShowDesignerGuide();
                journalPanel.Controls.Add(btnDesignerGuide);
                currentY += buttonHeight + spacing;

                // زر ملخص ترحيل الكنترولز
                var btnMigrationSummary = CreateJournalButton(
                    "📋 ملخص ترحيل الكنترولز",
                    new Point(10, currentY),
                    Color.FromArgb(142, 68, 173)
                );
                //btnMigrationSummary.Click += (s, e) => Forms.Testing.Designer_Controls_Migration_Summary.ShowMigrationSummary();
                journalPanel.Controls.Add(btnMigrationSummary);
                currentY += buttonHeight + spacing;

                // زر اختبار النماذج مع Designer
                var btnDesignerTest = CreateJournalButton(
                    "🧪 اختبار النماذج مع Designer",
                    new Point(10, currentY),
                    Color.FromArgb(39, 174, 96)
                );
                //btnDesignerTest.Click += (s, e) => Forms.Testing.Designer_Forms_Test.TestFormsWithDesigner();
                journalPanel.Controls.Add(btnDesignerTest);

                // إضافة اللوحة إلى النموذج الرئيسي
                this.Controls.Add(journalPanel);
                journalPanel.BringToFront();

                Console.WriteLine("✅ تم إضافة أزرار القيود المحاسبية المحسنة بنجاح");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في إضافة أزرار القيود المحاسبية: {ex.Message}");
            }
        }

        /// <summary>
        /// إنشاء زر للقيود المحاسبية
        /// </summary>
        private RJButton CreateJournalButton(string text, Point location, Color backColor)
        {
            return new RJButton
            {
                Text = text,
                Size = new Size(180, 30),
                Location = location,
                BackColor = backColor,
                ForeColor = Color.White,
                Font = new Font("Droid Arabic Kufi", 9F, FontStyle.Bold),
                BorderRadius = 8,
                BorderSize = 0,
                FlatStyle = FlatStyle.Flat,
                UseVisualStyleBackColor = false
            };
        }

        /// <summary>
        /// حدث النقر على زر إدارة القيود
        /// </summary>
        private void BtnManageEntries_Click(object sender, EventArgs e)
        {
            try
            {
                var journalService = new SmartCreator.Services.Accounting.JournalEntryService();
                var accountService = new SmartCreator.Service.AccountService();
                var activityService = new SmartCreator.Services.Security.UserActivityService();

                var form = new Frm_JournalEntries_Enhanced(journalService, accountService, activityService);
                form.Show();
            }
            catch (Exception ex)
            {
                RJMessageBox.Show($"خطأ في فتح نموذج إدارة القيود: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// حدث النقر على زر إضافة قيد
        /// </summary>
        private void BtnAddEntry_Click(object sender, EventArgs e)
        {
            try
            {
                var journalService = new SmartCreator.Services.Accounting.JournalEntryService();
                var accountService = new SmartCreator.Service.AccountService();
                var activityService = new SmartCreator.Services.Security.UserActivityService();

                var form = new Frm_AddEditJournalEntry_Enhanced(journalService, accountService, activityService);
                form.Show();
            }
            catch (Exception ex)
            {
                RJMessageBox.Show($"خطأ في فتح نموذج إضافة القيد: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// حدث النقر على زر اختيار حساب
        /// </summary>
        private void BtnSelectAccount_Click(object sender, EventArgs e)
        {
            try
            {
                var accountService = new SmartCreator.Service.AccountService();
                var form = new Frm_AccountSelector(accountService);
                form.ShowDialog();
            }
            catch (Exception ex)
            {
                RJMessageBox.Show($"خطأ في فتح نموذج اختيار الحساب: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region -> Database Initialization

        /// <summary>
        /// تهيئة قاعدة البيانات وإنشاء الجداول المطلوبة
        /// </summary>
        private async void InitializeDatabaseAsync()
        {
            try
            {
                var dbSetup = new DatabaseSetupService();

                // فحص وجود الجداول
                var tablesExist = await dbSetup.CheckTablesExistAsync();

                if (!tablesExist)
                {
                    Console.WriteLine("🗄️ إنشاء جداول قاعدة البيانات...");
                    await dbSetup.CreateSecurityTablesAsync();
                    Console.WriteLine("✅ تم إنشاء جداول قاعدة البيانات بنجاح");
                }
                else
                {
                    Console.WriteLine("✅ جداول قاعدة البيانات موجودة");
                }

                // عرض معلومات قاعدة البيانات
                await dbSetup.ShowDatabaseInfoAsync();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في تهيئة قاعدة البيانات: {ex.Message}");
                RJMessageBox.Show(
                    $"خطأ في تهيئة قاعدة البيانات:\n{ex.Message}",
                    "خطأ في قاعدة البيانات",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error
                );
            }
        }

        #endregion

        #region -> Constructor

        public MainForm()
        {
            //this.Size = new Size(1220, 680);
            //this.MinimumSize = new Size(975, 680);

            //this.SuspendLayout();
            //this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);


            set_Dpi_global();

            InitializeComponent();
            InitializeItems();
            this.Icon = Properties.Resources.Smart_Creator;
            //lnk_merque.Font = Program.GetCustomFont(Resources.DroidSansArabic, 9, FontStyle.Regular, GraphicsUnit.Point, ((byte)(0)));


            lblUserName.Text = "No login";
            Local_DA = new Sql_DataAccess();
            Smart_DA = new Smart_DataAccess();

            // تهيئة خدمة الإشعارات
            _notificationService = new NotificationService();

            // إعداد قاعدة البيانات
            InitializeDatabaseAsync();

            // تحديث عداد الإشعارات
            UpdateNotificationBadgeAsync();

            //this.ResumeLayout();
        }

        public MainForm(User user)
        {
            //this.SuspendLayout();
            //this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);


            //this.MinimumSize = new Size(975, 680);

            //this.TopLevel = true;
            InitializeComponent();
            InitializeItems();
            Local_DA = new Sql_DataAccess();
            Smart_DA = new Smart_DataAccess();

            //userConnected = user;
            //lblUserName.Text = user.FirstName + " " + user.LastName;
            //pbProfilePicture.Image = user.ProfilePicture;

        }

        private void InitializeItems()
        {
            //this.AutoScaleDimensions = new System.Drawing.SizeF(96F, 96F);
            //this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Dpi;
            //this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);

            //this.Size = new Size(1220, 680);
            //this.MinimumSize = new Size(975, 680);
            //pnlSideMenu.Size= new Size(220, pnlSideMenu.Height);

            //biFormOptions.DropdownMenu = this.dmFormOptions;//Set dropdown menu of form options


            //utils.Control_textSize1(pnlSideMenu);
            //utils.Control_textSize1(pnlSideMenuHeader);
            //utils.Control_textSize1(pnlTitleBar);

            if (Settings.UIAppearance.Style == Settings.UIStyle.Supernova)
                pbSideMenuLogo.Image = Properties.Resources.RJTitleBarLogoColor;
            //AR_Applye_Change();

            try
            {
                Global_Variable.Uc_StatusBar = new UC_StatusBar_Info();
                this.pnlDesktopFooter.Controls.Add(Global_Variable.Uc_StatusBar);
                Global_Variable.Uc_StatusBar.Dock = DockStyle.Fill;
            }
            catch (Exception ex) { MessageBox.Show(ex.ToString() + "  throw 002"); }
            AR_Applye_Change();

            utils utils = new utils();
            utils.Control_textSize1(this);
            //biSideMenuButton.IconSize = (int)(biSideMenuButton.IconSize + (biSideMenuButton.IconSize - (biSideMenuButton.IconSize * 96f / Global_Variable.Graphics_dpi)));
            //biFormIcon.IconSize = (int)(biFormIcon.IconSize + (biFormIcon.IconSize - (biFormIcon.IconSize * 96f / Global_Variable.Graphics_dpi)));
            //biFormOptions.IconSize = (int)(biFormOptions.IconSize + (biFormOptions.IconSize - (biFormOptions.IconSize * 96f / Global_Variable.Graphics_dpi)));
            //biNotifications.IconSize = (int)(biNotifications.IconSize + (biNotifications.IconSize - (biNotifications.IconSize * 96f / Global_Variable.Graphics_dpi)));
            //biUserOptions.IconSize = (int)(biUserOptions.IconSize + (biUserOptions.IconSize - (biUserOptions.IconSize * 96f / Global_Variable.Graphics_dpi)));
            //biLanguage.IconSize = (int)(biUserOptions.IconSize + (biUserOptions.IconSize - (biUserOptions.IconSize * 96f / Global_Variable.Graphics_dpi)));

            //btnMainPage.IconSize = (int)(btnMainPage.IconSize + (btnMainPage.IconSize - (btnMainPage.IconSize * 96f / Global_Variable.Graphics_dpi)));
            //btnUserManager.IconSize = btnMainPage.IconSize;
            //btnHotspot.IconSize = btnMainPage.IconSize;
            //btnBrodband.IconSize = btnMainPage.IconSize;
            //btnAciveHost.IconSize = btnMainPage.IconSize;
            //btnDevice.IconSize = btnMainPage.IconSize;
            //btnReport.IconSize = btnMainPage.IconSize;
            //btn_Pages_Editor.IconSize = btnMainPage.IconSize;



            //this.ResumeLayout();
            //this.SuspendLayout();
            //this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            //this.ResumeLayout();

            this.Resizable = true;

            // إضافة أزرار القيود المحاسبية المحسنة
            AddJournalEntriesButtons();
        }
        private void AR_Applye_Change()
        {
            //return;
            //this.Size = new System.Drawing.Size(1180, 700);
            //this.OpenChildForm(() => new FormDashboard(), btnMainPage);

            int y = 26;
            int biSideMenuButton_y = 15;
            if (Global_Variable.Graphics_dpi != 96f)
            {
                y = (int)(y + (y - (y * 96f / Global_Variable.Graphics_dpi)));
                biSideMenuButton_y = (int)(biSideMenuButton_y + (biSideMenuButton_y - (biSideMenuButton_y * 96f / Global_Variable.Graphics_dpi)));

            }
            if (UIAppearance.Language_ar)
            {
                pnlDesktop.RightToLeft = RightToLeft.Yes;

                biUserOptions.Location = new Point(0, y);
                //biLanguage.Location = new Point(biUserOptions.Location.X + biLanguage.Width + 2, 26);
                biNotifications.Location = new Point(biUserOptions.Location.X + biNotifications.Width + 5, y);
                biFormOptions.Location = new Point(biNotifications.Location.X + biFormOptions.Width + 5, y);

                biUserOptions.Anchor = (AnchorStyles.Top | AnchorStyles.Left);
                biLanguage.Anchor = (AnchorStyles.Top | AnchorStyles.Left);
                biNotifications.Anchor = (AnchorStyles.Top | AnchorStyles.Left);
                biFormOptions.Anchor = (AnchorStyles.Top | AnchorStyles.Left);


                Font frm_font = Program.GetCustomFont(Resources.DroidKufi_Bold, 9.75f , FontStyle.Bold);
                //Font frm_font = Program.GetCustomFont(Resources.DroidKufi_Bold, 9.75f * utils.ScaleFactor_sideMenu, FontStyle.Bold);
                ////Font frm_font = Program.GetCustomFont(Resources.Cairo_Medium, 10, FontStyle.Regular, GraphicsUnit.Point, ((byte)(0)));
                //foreach (Control control in this.pnlSideMenu.Controls)
                //{
                //    if (control.GetType() == typeof(RJControls.RJMenuButton))
                //    {
                //        //control.Padding = new Padding(0, 0, 11, 0);


                //        //control.Font = frm_font;
                //    }
                //}
                //btnMainPage.Font = frm_font;
                //btnUserManager.Font = frm_font;
                //btnHotspot.Font = frm_font;
                //btnBrodband.Font = frm_font;
                //btnAciveHost.Font = frm_font;
                //btnDevice.Font = frm_font;
                //btnReport2.Font = frm_font;
                //btnReport.Font = frm_font;
                //btn_Pages_Editor.Font = frm_font;

                ////Font menu_font = Program.GetCustomFont(Resources.Cairo_Medium, 9, FontStyle.Regular);
                //Font menu_font = Program.GetCustomFont(Resources.DroidSansArabic, 9 * utils.ScaleFactor_sideMenu, FontStyle.Regular);
                Font menu_font = Program.GetCustomFont(Resources.DroidSansArabic, 9, FontStyle.Regular);
                dmFormOptions.Font = menu_font;
                dmHotSpot.Font = menu_font;
                dmLanguage.Font = menu_font;
                dmUserManager.Font = menu_font;
                dmPPP.Font = menu_font;
                dmCardsTemplate.Font = menu_font;
                dmAccount2.Font = menu_font;
                dmUserOptions.Font = menu_font;


                ////Font lblCaption_font = Program.GetCustomFont(Resources.Cairo_Medium, 10, FontStyle.Regular);
                ////lblCaption.Font = lblCaption_font;
                ////biFormIcon.Location = new Point(pnlTitleBar.Width - biFormIcon.Width - 5, 15);

                lbl_SmartCreator.Font = Program.GetCustomFont(Resources.DroidKufi_Bold, 11, FontStyle.Bold);
                //lbl_SmartCreator.Font = Program.GetCustomFont(Resources.DroidKufi_Bold, 11 * utils.ScaleFactor_sideMenu, FontStyle.Bold);




                lblCaption_MeasureString();
                biSideMenuButton.Location = new Point(pnlSideMenuHeader.Location.X + biSideMenuButton.Width + 5, biSideMenuButton_y);
                lblCaption_MeasureString();

                //biSideMenuButton.Location = new Point(pnlSideMenuHeader.Location.X + biSideMenuButton.Width + 5, 15);
                //biSideMenuButton.Location = new Point(6, 17);
                //dmUserManager.Show(btnUserManager, DropdownMenuPosition.TopRight);
            }
            else
            {
                lbl_SmartCreator.Text = "Smart-Creator";
                pnlSideMenu.RightToLeft = RightToLeft.No;
                biSideMenuButton.Location = new Point(pnlSideMenuHeader.Width - (biSideMenuButton.Width + 10), biSideMenuButton_y);
                pbSideMenuLogo.Location = new Point(10, 10);
            }

            //lnk_merque.Font = Program.GetCustomFont(Resources.DroidSansArabic, 9 * utils.ScaleFactor, FontStyle.Regular);
            lnk_merque.Font = Program.GetCustomFont(Resources.DroidSansArabic, 9 , FontStyle.Regular);
            lbl_SmartCreator.ForeColor = biSideMenuButton.ForeColor;
            lbl_headerTitle.Style = LabelStyle.BarCaption;

            lbl_day_expir.ForeColor = biSideMenuButton.ForeColor;
            lbl_day_expir.Location = new Point(lbl_day_expir.Location.X, lbl_headerTitle.Location.Y);

            //lbl_day_expir.Style = LabelStyle.BarCaption;
            //lbl_headerTitle.Font = lblCaption.Font;
            lbl_headerTitle.Font = Program.GetCustomFont(Resources.DroidSansArabic, 9f , FontStyle.Regular);
            //lbl_headerTitle.Font = Program.GetCustomFont(Resources.DroidSansArabic, 9f * utils.ScaleFactor, FontStyle.Regular);
            lbl_day_expir.Font = lbl_headerTitle.Font;




        }

        private void AR_SidePanal()
        {
            Font frm_font = Program.GetCustomFont(Resources.DroidKufi_Bold, 9f, FontStyle.Bold);
            //Font frm_font = CustomFonts.Get_Custom_Font("Cairo_Medium", 10,false,GraphicsUnit.Point,0);
            btnMainPage.Font = frm_font;
            btnUserManager.Font = frm_font;
            btnHotspot.Font = frm_font;
            btnBrodband.Font = frm_font;
            btnAciveHost.Font = frm_font;
            btnDevice.Font = frm_font;
            btn_CardsDesigen.Font = frm_font;
            btnReport.Font = frm_font;
            btn_Pages_Editor.Font = frm_font;
            btnSecurity.Font = frm_font;
        }
        #endregion


        #region -> Events Methods Definition

        //How to use the OpenChildForm<childForm>(...) method

        /// You can use the Func<TResult> delegate with anonymous methods or lambda expression,
        /// for example, we can call this method as follows:
        /// With anonymous method:
        ///     <see cref="OpenChildForm( delegate () { return new MyForm('MyParameter'); });"/>
        /// With lambda expression
        ///     <see cref="OpenChildForm( () => new MyForm('id', 'username'));"/>


        #region - Open Child Form
        //(User Options Dropdown Menu)
        /// Using [<see cref="OpenChildForm<childForm>(Func<childForm> _delegate) where childForm : RJChildForm"/>] Method

        private void miMyProfile_Click(object sender, EventArgs e)
        {
            this.OpenChildForm(() => new FormUserProfile(userConnected));
            //()=> : Generic delegate call
        }
        private void miSettings_Click(object sender, EventArgs e)
        {
            this.OpenChildForm(() => new RJForms.RJSettingsForm());
        }

        #endregion

        #region - Open Child Form from a Menu Button
        //(Side Menu)
        /// Using [<see cref="OpenChildForm<childForm>(Func<childForm> _delegate, object senderMenuButton) where childForm : RJChildForm"/>] Method


        private void btnUserManager_Click(object sender, EventArgs e)
        {
            if (UIAppearance.Language_ar)
            //if (pnlSideMenu.Width > 200)
                    dmUserManager.Show(btnUserManager, DropdownMenuPosition.BottomRight, pnlSideMenu.Width);



            //lblCaption_MeasureString();
            //this.OpenChildForm(() => new FormUserControls(), sender);
            ////()=> : Generic delegate call
            ////sender: btnUserControls (MenuButton)
        }
        private void btnDashboard_Click(object sender, EventArgs e)
        {
            if (UIAppearance.Language_ar)
                //if (pnlSideMenu.Width > 200)
                dmAccount2.Show(btnBrodband, DropdownMenuPosition.BottomRight, pnlSideMenu.Width);


            //this.OpenChildForm(() => new FormDashboard(), sender);
        }
        private void btnProducts_Click(object sender, EventArgs e)
        {
            //this.OpenChildForm(() => new FormProducts(), sender);
        }
        private void btnCustomers_Click(object sender, EventArgs e)
        {
            //this.OpenChildForm(() => new FormCustomer(), sender);
        }
        #endregion

        #region - Open Child Form from a Dropdown Menu Item associated with a Menu Button
        //(Side Menu)
        /// Using [<see cref="OpenChildForm<childForm>(Func<childForm> _delegate, object senderMenuItem, RJMenuButton ownerMenuButton) where childForm : RJChildForm"/>] Method

        private void miCommonControls_Click(object sender, EventArgs e)
        {
            //this.OpenChildForm(() => new FormCommonControls(), sender, btnUserManager);
            //()=> : Generic delegate call
            //sender: commonToolStripMenuItem (dmCustomControls Menu Item)
            //btnCustomControls: MenuButton
        }
        private void miComponentsControls_Click(object sender, EventArgs e)
        {
            this.OpenChildForm(() => new FormReportUserManager(), sender, btnUserManager);

        }
        private void miMenuControls_Click(object sender, EventArgs e)
        {
            //this.OpenChildForm(() => new FormMenuControls(), sender, btnUserManager);
        }
        private void miContainerControls_Click(object sender, EventArgs e)
        {
            this.OpenChildForm(() => new FormProfileUserManager(), sender, btnUserManager);
            //lblCaption_MeasureString();

        }
        private void miDataControls_Click(object sender, EventArgs e)
        {

        }
        private void miSpecialControls_Click(object sender, EventArgs e)
        {

        }
        private void miSalesList_Click(object sender, EventArgs e)
        {
            //this.OpenChildForm(() => new FormSalesOrder(), sender, btnAciveHost);
        }
        private void baseFormToolStripMenuItem_Click(object sender, EventArgs e)
        {

            this.OpenChildForm(() => new FormAddHotspotCards(), sender, btnHotspot);
        }
        private void mainFormToolStripMenuItem_Click(object sender, EventArgs e)
        {
            //this.OpenChildForm(() => new FormRJMainFormDoc(), sender, btnHotspot);
        }
        private void childFormToolStripMenuItem_Click(object sender, EventArgs e)
        {
            //this.OpenChildForm(() => new FormRJChildFormDoc(), sender, btnHotspot);

        }
        #endregion

        #region - User Options
        //(User Options Dropdown Menu)

        private void miExit_Click(object sender, EventArgs e)
        {
            this.CloseWindow();
        }
        private void miLogout_Click(object sender, EventArgs e)
        {
            this.Close();
        }
        private void miHelp_Click(object sender, EventArgs e)
        {
            //Process.Start(@"Files\Documentation.pdf");
        }
        private void miTermsCond_Click(object sender, EventArgs e)
        {
            //Process.Start(@"Files\License.pdf");
        }

        #endregion

        #region - Security Menu Events
        //(Security Menu)

        private void btnSecurity_Click(object sender, EventArgs e)
        {
            if (UIAppearance.Language_ar)
                dmSecurity.Show(btnSecurity, DropdownMenuPosition.BottomRight, pnlSideMenu.Width);
        }

        private void miUserManagement_Click(object sender, EventArgs e)
        {
            this.OpenChildForm(() => new Frm_UserManagement(), sender, btnSecurity);
        }

        private void miPermissions_Click(object sender, EventArgs e)
        {
            this.OpenChildForm(() => new Frm_Permissions(), sender, btnSecurity);
        }

        private void miAuditLog_Click(object sender, EventArgs e)
        {
            this.OpenChildForm(() => new Frm_AuditLog(), sender, btnSecurity);
        }

        private void miSecuritySettings_Click(object sender, EventArgs e)
        {
            this.OpenChildForm(() => new Frm_SecuritySettings(), sender, btnSecurity);
        }

        #endregion

        #endregion

        private void GetDataInfo()
        {

        }
        private void btnYoutube_Click(object sender, EventArgs e)
        {

            //string rb = Global_Variable.Mk_resources.RB_code;
            //Smart_DA.Load<SellingPoint>(x => x.Rb == rb);
            //return;
            Fast_Load_From_Mikrotik f = new Fast_Load_From_Mikrotik();
            //f.Remove_Indexs();
            //f.Create_Indexs();
            //return;
            //var addnewUmUser = studentList[0];
            string sql = rjTextBox1.Text;
            //Sql_DataAccess sql_DataAccess = new Sql_DataAccess();
            RJMessageBox.Show(Local_DA.RunSqlScript(sql) + "");
            return;
            Process.Start(@"https://www.youtube.com/smartcreator");
        }
        private void btnGithub_Click(object sender, EventArgs e)
        {
            string name = "Smart_No_Pass.db";
            string Smartfile = utils.Get_Database_Directory() + "\\"+name;
            string cs = $"Data Source={Smartfile};";

            //var rb = Smart_DataAccess.Get_default_Connections_Db();
            var rb = new Connections_Db()
            {
                Connection_string = cs,
                FileName = Smartfile,
                Mk_code = Global_Variable.Mk_resources.RB_SN,
                Mk_sn = Global_Variable.Mk_resources.RB_SN,
                Name = name,
                Soft_id = Global_Variable.Mk_resources.RB_Soft_id,
                LocalDB_path = utils.Get_Database_Directory()

            };
            clss_CreateNewDatabase ff = new clss_CreateNewDatabase();
            ff.Create_Smart_db(rb);
        }

        [Obsolete]
        private void MainForm_Load(object sender, EventArgs e)
        {
            //this.MinimumSize = new Size(975, 680);
            //this.ShowInTaskbar = true;

            timer1.Start();
            Show_Top_Active();
            //this.Size = new Size(1180, 800);

            //this.OpenChildForm(() => new FormDashboard(), btnMainPage);

            //lbl_headerTitle.Style = LabelStyle.BarCaption;
            //lbl_SmartCreator.Style = LabelStyle.BarCaption;

            //if (UIAppearance.Theme==UITheme.Light)
            //{
            //
            //    lbl_SmartCreator.ForeColor = Color.White;
            //}

            //timer_merque.Start();
            //if (Global_Variable.Server_Port == 8728)
            //    lbl_headerTitle.Text = $"{Global_Variable.Server_Username}@{Global_Variable.Server_IP} ({Global_Variable.Mk_resources.identity} - {Global_Variable.Mk_Login_data.Note})";
            ////Global_Variable.Server_Username + "@" + Global_Variable.Server_IP + "  ( " + Global_Variable.Mk_resources.identity + " )";
            //else
            //    lbl_headerTitle.Text = lbl_headerTitle.Text = $"{Global_Variable.Server_Username}@{Global_Variable.Server_IP}:{Global_Variable.Server_Port} ({Global_Variable.Mk_resources.identity} - {Global_Variable.Mk_Login_data.Note})";

            //lbl_headerTitle.Text = Global_Variable.Server_Username + "@" + Global_Variable.Server_IP + ":" + Global_Variable.Server_Port + " ( " + Global_Variable.Mk_resources.identity + " ) ";




        }

        private void btnCustomControls_Click(object sender, EventArgs e)
        {

        }
         private void lblCaption_MeasureString()
        {
            if (UIAppearance.Language_ar)
            {
                //=============================
                //set font, size & style
                //Font f = new Font("Microsoft Sans Serif", 14, FontStyle.Regular);
                //Font lblCaption_font = CustomFonts.Get_Custom_Font("", 10, false);

                //Font lblCaption_font = Program.GetCustomFont(Resources.DroidSansArabic, 10 * utils.ScaleFactor, FontStyle.Regular);
                //lblCaption.Font = lblCaption_font;

                lblCaption.Font = lblCaption.Font;
                Bitmap b = new Bitmap(2200, 2200);
                Graphics g = Graphics.FromImage(b);

                SizeF sizeOfString = new SizeF();
                sizeOfString = g.MeasureString(lblCaption.Text, lblCaption.Font);

                biFormIcon.Location = new Point(pnlTitleBar.Width - biFormIcon.Width - 5, 16);
                //lblCaption.Location = new Point((int)(biFormIcon.Location.X - sizeOfString.Width - 10), biFormIcon.Location.Y);
                //lblCaption.Location = new Point((int)(biFormIcon.Location.X - sizeOfString.Width - 10), 15);
                lblCaption.RightToLeft = RightToLeft.Yes;
            }

        }
        private void lbl_day_expir_MeasureString()
        {
            if (UIAppearance.Language_ar)
            {
                //=============================

                //Font lbl_day_expir_font = Program.GetCustomFont(Resources.DroidSansArabic, 10, FontStyle.Regular);
                //Font lbl_day_expir_font = lbl_day_expir.Font;

                lbl_day_expir.Font = lbl_day_expir.Font;
                Bitmap b = new Bitmap(2200, 2200);
                Graphics g = Graphics.FromImage(b);

                SizeF sizeOfString = new SizeF();
                sizeOfString = g.MeasureString(lbl_day_expir.Text, lbl_day_expir.Font);

                biFormIcon.Location = new Point(pnlTitleBar.Width - biFormIcon.Width - 5, 16);
                //lblCaption.Location = new Point((int)(biFormIcon.Location.X - sizeOfString.Width - 10), biFormIcon.Location.Y);
                //lblCaption.Location = new Point((int)(biFormIcon.Location.X - sizeOfString.Width - 10), 15);
                lblCaption.RightToLeft = RightToLeft.Yes;
            }

        }
        private void lbl_headerTitle_MeasureString()
        {
            //if (UIAppearance.Language_ar)
            {

                //Font lblCaption_font = Program.GetCustomFont(Resources.Cairo_Medium, 10, FontStyle.Regular);
                //Font lblCaption_font = lbl_headerTitle.Font;

                Bitmap b = new Bitmap(2200, 2200);
                Graphics g = Graphics.FromImage(b);

                SizeF sizeOfString = new SizeF();
                //sizeOfString = g.MeasureString(lbl_headerTitle.Text, lblCaption_font);
                sizeOfString = g.MeasureString(lbl_headerTitle.Text, lbl_headerTitle.Font);

                //biFormIcon.Location = new Point(pnlTitleBar.Width - biFormIcon.Width - 5, 15);
                lbl_headerTitle.Location = new Point((int)((pnlTitleBar.Width / 2) - (sizeOfString.Width / 2)+20),lblCaption.Location.Y);
                //lbl_headerTitle.Location = new Point((int)((pnlTitleBar.Width / 2) - (sizeOfString.Width / 2)), pnlTitleBar.Height / 2);
                //lblCompany.Location = new Point(lblCompany.Location.X, pnlHeader.Height / 2);
                //lblTitle.Location = new Point(lblTitle.Location.X, pnlHeader.Height / 2);
                //lblCaption.RightToLeft = RightToLeft.Yes;
            }

        }


        private void biUserOptions_Click(object sender, EventArgs e)
        {
            dmUserOptions.Show(Cursor.Position.X, Cursor.Position.Y);
            //if (UIAppearance.Language_ar)
            //    dmUserOptions.Show(biUserOptions, DropdownMenuPosition.BottomRight);
        }

        private void biFormOptions_Click(object sender, EventArgs e)
        {
            dmFormOptions.Show(Cursor.Position.X, Cursor.Position.Y);

            //if (UIAppearance.Language_ar)
            //    dmFormOptions.Show(biFormOptions, DropdownMenuPosition.LeftBottom);

            //if (UIAppearance.Language_ar)
            //    dmFormOptions.Show(biFormOptions, DropdownMenuPosition.TopRight);
        }

        private void biLanguage_Click(object sender, EventArgs e)
        {
            //Form_UsersInfo form_UsersInfo = new Form_UsersInfo();
            //form_UsersInfo.ShowDialog();


            //if (UIAppearance.Language_ar)
            //    dmLanguage.Show(biLanguage, DropdownMenuPosition.LeftBottom);

        }

        /// <summary>
        /// حدث النقر على أيقونة الإشعارات
        /// </summary>
        private void biNotifications_Click(object sender, EventArgs e)
        {
            try
            {
                this.OpenChildForm(() => new Forms.Notifications.Frm_Notifications());

                // تحديث عداد الإشعارات بعد فتح النموذج
                UpdateNotificationBadgeAsync();
            }
            catch (Exception ex)
            {
                RJMessageBox.Show($"خطأ في فتح نموذج الإشعارات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تحديث عداد الإشعارات غير المقروءة
        /// </summary>
        private async void UpdateNotificationBadgeAsync()
        {
            try
            {
                if (_notificationService != null)
                {
                    var unreadCount = await _notificationService.GetUnreadCountAsync();

                    // تحديث لون الأيقونة حسب وجود إشعارات غير مقروءة
                    if (unreadCount > 0)
                    {
                        biNotifications.IconColor = Color.Red;
                        // يمكن إضافة تأثير وميض هنا
                        biNotifications.IconChar = FontAwesome.Sharp.IconChar.BellSlash;
                    }
                    else
                    {
                        biNotifications.IconColor = Color.WhiteSmoke;
                        biNotifications.IconChar = FontAwesome.Sharp.IconChar.Bell;
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديث عداد الإشعارات: {ex.Message}");
            }
        }

        /// <summary>
        /// إنشاء إشعارات تجريبية
        /// </summary>
        public async Task CreateSampleNotificationsAsync()
        {
            try
            {
                if (_notificationService != null)
                {
                    // إشعار ترحيب
                    await _notificationService.CreateNotificationAsync(
                        "مرحباً بك في Smart Creator",
                        "تم تسجيل دخولك بنجاح إلى النظام. نتمنى لك تجربة ممتعة!",
                        NotificationTypes.SUCCESS,
                        NotificationPriorities.NORMAL,
                        module: "System"
                    );

                    // إشعار تحديث
                    await _notificationService.CreateNotificationAsync(
                        "تحديث متوفر",
                        "يتوفر تحديث جديد للنظام. يرجى التحديث للحصول على أحدث الميزات.",
                        NotificationTypes.INFO,
                        NotificationPriorities.MEDIUM,
                        module: "Update"
                    );

                    // إشعار أمان
                    await _notificationService.CreateNotificationAsync(
                        "تنبيه أمني",
                        "تم اكتشاف محاولة دخول غير مصرح بها. يرجى مراجعة إعدادات الأمان.",
                        NotificationTypes.WARNING,
                        NotificationPriorities.HIGH,
                        module: "Security"
                    );

                    // تحديث العداد
                    UpdateNotificationBadgeAsync();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إنشاء الإشعارات التجريبية: {ex.Message}");
            }
        }

        private void btnMain_Click(object sender, EventArgs e)
        {
            this.OpenChildForm(() => new FormDashboard(), sender);
            //lblCaption_MeasureString();
        }

        private void btnHotsopt_Click(object sender, EventArgs e)
        {
            if (UIAppearance.Language_ar)
                //if (pnlSideMenu.Width > 200)
                    dmHotSpot.Show(btnHotspot, DropdownMenuPosition.BottomRight, pnlSideMenu.Width);


            //if (UIAppearance.Language_ar)
            //    dmHotSpot.Show(btnBrodband, DropdownMenuPosition.TopRight);
            lblCaption_MeasureString();

        }

        private void miAddUserManagerCards_Click(object sender, EventArgs e)
        {

            this.OpenChildForm(() => new FormAddUsersManager(), sender, btnUserManager);
            //lblCaption_MeasureString();

            //()=> : Generic delegate call
            //sender: commonToolStripMenuItem (dmCustomControls Menu Item)
            //btnCustomControls: MenuButton
        }

        private void miCardsProccess_Click(object sender, EventArgs e)
        {
            this.OpenChildForm(() => new Form_Cards_UserManager(), sender, btnUserManager);
            //lblCaption_MeasureString();

        }

        private void timer_merque_Tick(object sender, EventArgs e)
        {
            if (pnl_merqu_right.Width == xPos)
            {
                //repeat marquee
                lnk_merque.Location = new System.Drawing.Point(xPos, 4);
                xPos = 0 - lnk_merque.Width;
            }
            else
            {
                lnk_merque.Location = new System.Drawing.Point(xPos, 4);
                xPos++;
            }
        }

        private void lnk_merque_Click(object sender, EventArgs e)
        {
            try
            {
                Process.Start(linkUrl);
            }
            catch { }
        }

        private void lnk_merque_MouseDown(object sender, MouseEventArgs e)
        {
            timer_merque.Stop();
        }

        private void lnk_merque_MouseHover(object sender, EventArgs e)
        {
            timer_merque.Stop();
        }

        private void lnk_merque_MouseLeave(object sender, EventArgs e)
        {
            timer_merque.Start();
        }

        private void pnlDesktop_SizeChanged(object sender, EventArgs e)
        {
            this.pnlDesktopFooter.Refresh();
        }

        private void btnCardsDesgin_Click(object sender, EventArgs e)
        {
            if (UIAppearance.Language_ar)
                //if (pnlSideMenu.Width > 200)
                    dmCardsTemplate.Show(btn_CardsDesigen, DropdownMenuPosition.BottomRight,pnlSideMenu.Width);
            //else
            //dmCardsTemplate.Show(btnDevice, DropdownMenuPosition.LeftBottom);

            //this.OpenChildForm(() => new FormDashboard(), sender);
            //lblCaption_MeasureString();
        }

        private void miCardsTemplate_Click(object sender, EventArgs e)
        {
            this.OpenChildForm(() => new Form_CardsDesigen_Graghics(), sender, btn_CardsDesigen);
            //lblCaption_MeasureString();
        }

        private void miCardsTable_Click(object sender, EventArgs e)
        {
            this.OpenChildForm(() => new Form_TemplateTable1(), sender, btn_CardsDesigen);
        }

        private void btnDevice_Click(object sender, EventArgs e)
        {
            //this.OpenChildForm(() => new Form_Device_Management(), sender, btnDevice);
            //this.OpenChildForm(() => new Form_Device_Management(), sender, btnDevice);
            this.OpenChildForm(() => new Form_Device_Management(), sender);

        }
        private void AllCardsFromRouterToolStripMenuItem_Click(object sender, EventArgs e)
        {
            //this.OpenChildForm(() => new FormCardsProcessUsersManager(), sender, btnUserManager);
            //this.OpenChildForm(() => new FormAllCardsUserManager(), sender, btnUserManager);
            this.OpenChildForm(() => new FormAllCardsUserManager("From_Server"), sender, btnUserManager);

        }

        private void CardsByPrintNuberToolStripMenuItem_Click(object sender, EventArgs e)
        {
            this.OpenChildForm(() => new FormAllBatchsCards(), sender, btnUserManager);
        }

        private void pnlSideMenu_SizeChanged(object sender, EventArgs e)
        {
            try
            {
                if (activeChildForm != null)
                {
                    //AR_SidePanal();

                    //pnlMarker.Location = new Point(pnlSideMenu.Width - pnlMarker.Width, pnlSideMenu.Location.X);
                    //pnlMarker.Location = new Point(pnlSideMenu.Width - pnlMarker.Width, pnlSideMenu.Location.X-pnlSideMenu.Height/2);
                    pnlMarker.Location = new Point(pnlSideMenu.Width - pnlMarker.Width, activeChildForm.MarkerPosition);
                    pnlMarker.Refresh();
                    pnlSideMenu.Refresh();
                    AR_Applye_Change();
                }
            }
            catch { }
            //AR_Applye_Change();

        }

        private void UserManagertoolStripMenu_Click(object sender, EventArgs e)
        {
            this.OpenChildForm(() => new FormReportUserManager(), sender, btn_CardsDesigen);

        }

        private void miHotspotProfile_Click(object sender, EventArgs e)
        {
            this.OpenChildForm(() => new FormProfileHotspotLocal(), sender, btnHotspot);

        }

        private void dropCardsProccessHotspot_Click(object sender, EventArgs e)
        {
            this.OpenChildForm(() => new Form_Cards_Hotspot(), sender, btnHotspot);

        }

        private void Import_UM_ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            this.OpenChildForm(() => new Form_import_batch_cards_UM(), sender, btnUserManager);

        }

        private void Archive_UserManager_ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            this.OpenChildForm(() => new FormAllBatchsCards_Archive(), sender, btnUserManager);
            //this.OpenChildForm(() => new Form_Archive_Cards(), sender, btnUserManager);

        }

        private void Hotspor_ReportToolStripMenuItem_Click(object sender, EventArgs e)
        {
            //this.OpenChildForm(() => new FormReportHotspot(), sender, btnHotspot);
            this.OpenChildForm(() => new FormReportHotspot1(), sender, btnHotspot);

        }

        private void btn_Pages_Editor_Click(object sender, EventArgs e)
        {
            //this.OpenChildForm(() => new FormReportHotspot(), sender, btnHotspot);
            this.OpenChildForm(() => new Form_Check_Library_Html(), sender);
            //this.OpenChildForm(() => new Form_Page_Editor(), sender);
            //this.OpenChildForm(() => new Form_Html_Mangament(), sender);


        }

        private void btnSetting_Click(object sender, EventArgs e)
        {
            this.OpenChildForm(() => new Forms.Testing.Frm_ComprehensiveTest(), sender);
            //this.OpenChildForm(() => new Form_Backup(), sender);
        }

        private void CardsFromRB_LDBToolStripMenuItem_Click(object sender, EventArgs e)
        {
            this.OpenChildForm(() => new FormAllCardsUserManager("From_RB_Archive"), sender, btnUserManager);

        }

        private void عرضالكروتالمنتهيةToolStripMenuItem_Click(object sender, EventArgs e)
        {
            this.OpenChildForm(() => new FormAllCardsUserManager("From_Finsh_Cards"), sender);
        }

        private void ادارةالصلاحياتToolStripMenuItem_Click(object sender, EventArgs e)
        {
            this.OpenChildForm(() => new Form_Smart_Validatiy_Hotspot(), sender, btnHotspot);

        }

        private void Maintain_Backup_StripMenuItem_Click(object sender, EventArgs e)
        {
            this.OpenChildForm(() => new Form_Maintenances_UserManager(), sender, btnUserManager);

        }

        private void AdvanceTools_MenuItem_Click(object sender, EventArgs e)
        {

        }

        private void biSideMenuButton_Click(object sender, EventArgs e)
        {

            //AR_Applye_Change();
        }

        private void pnlTitleBar_SizeChanged(object sender, EventArgs e)
        {
            lbl_headerTitle_MeasureString();
        }

        [Obsolete]
        private void timer1_Tick(object sender, EventArgs e)
        {
            timer1.Stop();
            //AR_Applye_Change();


            //this.TopLevel = false;
            //this.OpenChildForm(() => new FormDashboard(), btnMainPage);

            //Thread.Sleep(3000);
            if (Global_Variable.Server_Port == 8728)
                lbl_headerTitle.Text = $"{Global_Variable.Server_Username}@{Global_Variable.Server_IP} ({Global_Variable.Mk_resources.identity} - {Global_Variable.Mk_Login_data.Note})";
            //Global_Variable.Server_Username + "@" + Global_Variable.Server_IP + "  ( " + Global_Variable.Mk_resources.identity + " )";
            else
                lbl_headerTitle.Text = lbl_headerTitle.Text = $"{Global_Variable.Server_Username}@{Global_Variable.Server_IP}:{Global_Variable.Server_Port} ({Global_Variable.Mk_resources.identity} - {Global_Variable.Mk_Login_data.Note})";

            lbl_headerTitle_MeasureString();
            try
            {
                Mk_DataAccess GetData = new Mk_DataAccess();
                //if (Global_Variable.RunOffline == false)
                //{
                ThreadStart therGetData = new ThreadStart(() => GetData.FirstLoadDataFromMK());
                Thread startGetData = new Thread(therGetData);
                startGetData.Name = "Get Information And Data";
                startGetData.Start();

                //return;
                //}
                //else
                //{

                //    Global_Variable.Source_profile = Smart_DA.GetListAnyDB<UmProfile>($"select * from UmProfile where DeleteFromServer=0 and Rb='{Global_Variable.Mk_resources.RB_code}';");
                //    Global_Variable.Source_limtition = Smart_DA.GetListAnyDB<UmLimitation>($"select * from UmLimitation where DeleteFromServer=0 and Rb='{Global_Variable.Mk_resources.RB_code}';");
                //    Global_Variable.Source_profile_limtition = Smart_DA.GetListAnyDB<UmProfile_Limtition>($"select * from UmProfile_Limtition where DeleteFromServer=0 and Rb='{Global_Variable.Mk_resources.RB_code}';");


                //    UmProfile umProfile = new UmProfile();
                //    Global_Variable.UM_Profile = umProfile.Get_UMProfile();


                //    Hotspot_Source_Profile hotspot_Source_Profile = new Hotspot_Source_Profile();
                //    Global_Variable.Source_HS_Profile = hotspot_Source_Profile.Ge_Source_ProfileHotspot_FromDB();

                //    if (Global_Variable.Mk_resources.version > 6)
                //    {
                //        UserManager_Customer userManager_Customer = new UserManager_Customer();
                //        Global_Variable.UM_Customer = userManager_Customer.Ge_Customer_FromDB();
                //    }

                //    Global_Variable.Update_Um_StatusBar(false, true, 0, "", "تم العرض من قاعدة البيانات المحلية");
                //    //lbl_SmartCreator.ForeColor = btnMainPage.ForeColor;
                //    lbl_SmartCreator.ForeColor = biSideMenuButton.ForeColor;

                //}
            }
            catch { }
            //lbl_SmartCreator.ForeColor = btnMainPage.ForeColor;
            lbl_SmartCreator.ForeColor = biSideMenuButton.ForeColor;


            //Thread.Sleep(30);
            FormDashboard dash=new FormDashboard();
            this.OpenChildForm(() => new FormDashboard(true), btnMainPage);
            timer_merque.Start();


            //ThreadStart therGetSession = new ThreadStart(() => dash.Create_Session());
            //Thread startSession = new Thread(therGetSession);
            //startSession.Start();

            //FormDashboard.Create_Session();

            this.ShowInTaskbar = true;

        }

        private void miAddPPPCards_Click(object sender, EventArgs e)
        {
            this.OpenChildForm(() => new FormAddBrodbandCards(), sender, btnBrodband);
        }

        private void btnAciveHost_Click(object sender, EventArgs e)
        {
            //this.OpenChildForm(() => new Form_Acive_Host_Bar(), sender, btnUserManager);
            this.OpenChildForm(() => new Form_Acive_Host_Bar(), sender);

        }

        private void Show_Top_Active()
        {
            try
            {
                string header_active = "";
                if (Global_Variable.App_Info.lbl_licence_type_header_active == true)
                {
                    header_active = "نسخة كاملة";
                    lbl_licence_type_header.Text = header_active;
                    lbl_licence_type_header.ForeColor = Color.Yellow;
                    lbl_day_expir.Text = header_active;
                    label3.Visible = false;
                    try
                    {
                        double ex = Properties.Settings.Default.rb_active_exp;
                        if (Global_Variable.Response_api.Notify_finsh == true)
                            if (ex <= Global_Variable.Response_api.Notify_finsh_days && Properties.Settings.Default.isActive)
                            {
                                lbl_day_expir.Text = "  اقتراب انتهاء النسخة الكاملة(" + ex + ") يوم";
                                if (ex<=10)
                                    lbl_day_expir.Text = "  اقتراب انتهاء النسخة الكاملة(" + ex + ") أيام";
                            }
                    }
                    catch { }
                }
                else
                {
                    lbl_licence_type_header.Text = "";
                    header_active = "نسخة تجريبية";
                    lbl_day_expir.Text = $"{header_active} ";

                    if (Properties.Settings.Default.rb_active_exp <= 0)
                    //if (Global_Variable.App_Info.App_lce.rb_exp <= 0)
                    {
                        lbl_day_expir.Text = "";
                    }
                    else
                    {
                        lbl_day_expir.Text = "نسخة تجريبية متبقي (" + Properties.Settings.Default.rb_active_exp + ") يوم";
                        if (Properties.Settings.Default.rb_active_exp <= 10)
                        lbl_day_expir.Text = "نسخة تجريبية متبقي (" + Properties.Settings.Default.rb_active_exp + ") أيام";
                    }
                }
            }
            catch { }

        }

        private void lbl_day_expir_Click(object sender, EventArgs e)
        {
            if (Properties.Settings.Default.isActive == false)
            {
                try
                {

                    Form_UsersInfo form_UsersInfo = new Form_UsersInfo();
                    form_UsersInfo.Show();

                }
                catch { }
            }

            else
            {
                Form_AciveInfo form_UsersInfo = new Form_AciveInfo();
                form_UsersInfo.ShowDialog();
            }

            //Form_UsersInfo f = new Form_UsersInfo();
            //f.ShowDialog();
        }

        private void Add_SP_ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            this.OpenChildForm(() => new Form_SellingPoint(), sender, btnReport);
        }

        private void Report_SP_ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            this.OpenChildForm(() => new Form_Sales_SellingPoint(), sender, btnReport);

        }

        private void btnReport_Click(object sender, EventArgs e)
        {
            if (UIAppearance.Language_ar)
                //if (pnlSideMenu.Width > 200)
                    dmAccount2.Show(btnReport, DropdownMenuPosition.BottomRight, pnlSideMenu.Width);

        }

        private void miLicence_Click(object sender, EventArgs e)
        {
            if (Properties.Settings.Default.isActive == false)
            {
                try
                {

                    Form_UsersInfo form_UsersInfo = new Form_UsersInfo();
                    form_UsersInfo.Show();

                }
                catch { }
            }

            else
            {
                Form_AciveInfo form_UsersInfo = new Form_AciveInfo();
                form_UsersInfo.ShowDialog();
            }

        }

        private void lbl_day_expir_MouseHover(object sender, EventArgs e)
        {
            lbl_day_expir.ForeColor = biSideMenuButton.ForeColor;
        }

        private void lbl_day_expir_MouseLeave(object sender, EventArgs e)
        {
            lbl_day_expir.ForeColor = biSideMenuButton.ForeColor;
        }

        private async void الدليلالمحاسبيToolStripMenuItem_Click(object sender, EventArgs e)
        {
            //this.OpenChildForm(() => new Frm_Account_Manual(), sender, btnReport);
            //this.OpenChildForm(() => new Form_Account(), sender, btnReport);

            try
            {
                await LogUserActivityAsync("OpenSimpleChart", "User opened  chart of accounts");

                this.OpenChildForm(() => new Frm_Account_Manual(), sender, btnReport);

                //var accountService = new AccountService(_dbHelper);
                //var simpleChartForm = new Frm_Account_Manual();
                //simpleChartForm.ShowDialog(this);
            }
            catch (Exception ex)
            {
                MessageBox.Show("خطأ في فتح الشجرة المحاسبية ", ex.Message);
                await LogUserActivityAsync("OpenSimpleChartError", $"Error opening simplified chart: {ex.Message}");
            }

        }

        private void الحساباتToolStripMenuItem_Click(object sender, EventArgs e)
        {
            this.OpenChildForm(() => new Form_Party(), sender, btnReport);

        }

        private void الاصنافToolStripMenuItem_Click(object sender, EventArgs e)
        {
            this.OpenChildForm(() => new Form_Product(), sender, btnReport);

        }

        private void فواتيرالمبيعاتToolStripMenuItem_Click(object sender, EventArgs e)
        {


        }

        private void سنداتالصرفToolStripMenuItem_Click(object sender, EventArgs e)
        {
            //this.OpenChildForm(() => new Form_Out_IN_Receipt(), sender, btnReport);


        }

        private void فواتيرالمبيعاتToolStripMenuItem1_Click(object sender, EventArgs e)
        {
            this.OpenChildForm(() => new Form_Invoice(), sender, btnReport);
        }

        private void مردودالمبيعاتToolStripMenuItem1_Click(object sender, EventArgs e)
        {

        }

        private void سندصرفToolStripMenuItem_Click(object sender, EventArgs e)
        {
            this.OpenChildForm(() => new Form_Out_IN_Receipt("Out"), sender, btnReport);
        }

        private void الصناديقToolStripMenuItem_Click(object sender, EventArgs e)
        {
            this.OpenChildForm(() => new Form_CashPayment(), sender, btnReport);
        }

        private void بياناتالشركةالشبكةToolStripMenuItem_Click(object sender, EventArgs e)
        {
            this.OpenChildForm(() => new Form_Company(), sender, btnReport);

        }

        private void بنودالايراداتToolStripMenuItem_Click(object sender, EventArgs e)
        {
            try
            {
                Form_Expense_Income formcards = (Form_Expense_Income)RJMainForm.listChildForms.Find(x => x.Name == "Form_Expense_Income");
                if (formcards != null)
                    CloseActiveChildForm();
            }
            catch { }
            this.OpenChildForm(() => new Form_Expense_Income("Income"), sender, btnReport);

        }

        private void بنودالمصروفاتToolStripMenuItem_Click(object sender, EventArgs e)
        {
            try
            {
                Form_Expense_Income formcards = (Form_Expense_Income)RJMainForm.listChildForms.Find(x => x.Name == "Form_Expense_Income");
                if (formcards != null)
                    CloseActiveChildForm();
            }
            catch { }

            this.OpenChildForm(() => new Form_Expense_Income("Expense"), sender, btnReport);

        }

        private void CloseActiveChildForm()
        {//Close Active child Form

            if (activeChildForm != null)
            {
                listChildForms.Remove(activeChildForm);//Remove current child form from list forms.
                pnlDesktop.Controls.Remove(activeChildForm);//Remove current child form from desktop panel .
                activeChildForm.Close();//Close current child form.
            }
        }

        private void سندقبضToolStripMenuItem_Click(object sender, EventArgs e)
        {
            this.OpenChildForm(() => new Form_Out_IN_Receipt("In"), sender, btnReport);

        }



        private async Task LogUserActivityAsync(string action, string details)
        {
            //return;
            try
            {
                await _auditService.LogAsync(
                    action: action,
                    entityType: "UserInterface",
                    entityId: null,
                    entityName: "MainForm",
                    oldValues: null,
                    newValues: new { Details = details, SessionId = _sessionId },
                    userId: Global_Variable.CurrentUser.Id,
                    module: "UI",
                    severity: AuditSeverity.Information,
                    success: true,
                    errorMessage: null);
            }
            catch (Exception ex)
            {
                // Log error but don't show to user
                Console.WriteLine($"Error logging user activity: {ex.Message}");
            }
        }

        private void rjButton1_Click(object sender, EventArgs e)
        {
            //DELETE FROM My_Sequence;
            string sql = rjTextBox1.Text;
            //Smart_DA sql_DataAccess = new Sql_DataAccess();
            RJMessageBox.Show(Smart_DA.RunSqlScript(sql) + "");
            return;

        }

        /// <summary>
        /// حدث النقر على زر الاختبار الشامل
        /// </summary>
        private void btnComprehensiveTest_Click(object sender, EventArgs e)
        {
            try
            {
                // فتح نموذج الاختبار الشامل
                this.OpenChildForm(() => new Forms.Testing.Frm_ComprehensiveTest(), sender);
            }
            catch (Exception ex)
            {
                RJMessageBox.Show(
                    $"خطأ في فتح نموذج الاختبار الشامل:\n\n{ex.Message}",
                    "خطأ",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// حدث النقر على زر الاختبار البسيط
        /// </summary>
        private void btnSimpleTest_Click(object sender, EventArgs e)
        {
            try
            {
                this.OpenChildForm(() => new SmartCreator.Forms.Testing.Frm_ComprehensiveTest(), sender);
                // فتح نموذج الاختبار البسيط كنافذة منفصلة
                //var form = new Frm_SimpleTest();
                //form.Show();
            }
            catch (Exception ex)
            {
                RJMessageBox.Show(
                    $"خطأ في فتح نموذج الاختبار البسيط:\n\n{ex.Message}",
                    "خطأ",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// حدث النقر على زر اختبار الإشعارات
        /// </summary>
        private void btnNotificationTest_Click(object sender, EventArgs e)
        {
            try
            {
                this.OpenChildForm(() => new Forms.Notifications.Frm_NotificationTest(), sender);
            }
            catch (Exception ex)
            {
                RJMessageBox.Show(
                    $"خطأ في فتح نموذج اختبار الإشعارات:\n\n{ex.Message}",
                    "خطأ",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error);
            }
        }

        private void القيوداليوميةToolStripMenuItem_Click(object sender, EventArgs e)
        {
            try
            {
                this.OpenChildForm(() => new Forms.Accounting.Frm_JournalEntries_Enhanced(), sender);
            }
            catch (Exception ex)
            {
                RJMessageBox.Show(
                    $"خطأ في فتح نموذج اختبار الإشعارات:\n\n{ex.Message}",
                    "خطأ",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error);
            }
        }
    }
}
