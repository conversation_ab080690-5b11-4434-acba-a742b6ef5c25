using SmartCreator;
using SmartCreator.Entities.Accounting;
using SmartCreator.Models;
using SmartCreator.Properties;
using SmartCreator.RJControls;
using SmartCreator.RJForms;
using SmartCreator.Service;
using SmartCreator.Services.Accounting;
using SmartCreator.Services.Security;
using SmartCreator.Settings;
using SmartCreator.Utils;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using SmartCreator.Utils;

namespace SmartCreator.Forms.Accounting
{
    /// <summary>
    /// نموذج إدارة القيود المحاسبية
    /// </summary>
    public partial class Frm_JournalEntries : RJChildForm
    {
        #region المتغيرات

        private readonly JournalEntryService _journalEntryService;
        private readonly AccountService _accountService;
        private readonly UserActivityService _activityService;
        private List<JournalEntry> _journalEntries;
        private List<Account> _accounts;
        private JournalEntry? _selectedEntry;
        private bool _isLoading = false;

        #endregion

        #region البناء

        public Frm_JournalEntries(JournalEntryService journalEntryService,
            AccountService accountService, UserActivityService activityService)
        {
            _journalEntryService = journalEntryService;
            _accountService = accountService;
            _activityService = activityService;
            _journalEntries = new List<JournalEntry>();
            _accounts = new List<Account>();

            InitializeComponent();
            SetupForm();
            _ = LoadDataAsync(); // Fire and forget

  
            Set_Font();

            utils utils = new utils();
            utils.Control_textSize1(this);

          
        }

        #endregion

        #region إعداد النموذج

        private void Set_Font()
        {
            lblTitle.Font= Program.GetCustomFont(Resources.DroidKufi_Bold, 14 * utils.ScaleFactor, FontStyle.Bold);
            lblJournalEntries.Font=lblDetails.Font= Program.GetCustomFont(Resources.DroidKufi_Bold, 10 * utils.ScaleFactor, FontStyle.Bold);
            Font fnt = Program.GetCustomFont(Resources.DroidKufi_Bold, 12 * utils.ScaleFactor, FontStyle.Bold);
            
            //btn_Acive.Font = fnt;
            //btn_Host.Font = fnt;
            //btn_IpBinding.Font = fnt;
            //btn_WalledGarden.Font = fnt;

        }

        /// <summary>
        /// إعداد النموذج
        /// </summary>
        private void SetupForm()
        {
            // إعداد التواريخ الافتراضية
            dtpFromDate.Value = DateTime.Today.AddMonths(-1);
            dtpToDate.Value = DateTime.Today;

            // إعداد أعمدة جدول القيود
            SetupJournalEntriesGrid();

            // إعداد أعمدة جدول التفاصيل
            SetupDetailsGrid();

            // إعداد قوائم الفلاتر
            SetupFilterComboBoxes();

            // تطبيق الصلاحيات
            ApplyPermissions();
        }

        /// <summary>
        /// إعداد أعمدة جدول القيود
        /// </summary>
        private void SetupJournalEntriesGrid()
        {
            dgvJournalEntries.AutoGenerateColumns = false;
            dgvJournalEntries.Columns.Clear();

            dgvJournalEntries.Columns.AddRange(new DataGridViewColumn[]
            {
                new DataGridViewTextBoxColumn
                {
                    Name = "EntryNumber",
                    HeaderText = "رقم القيد",
                    DataPropertyName = "EntryNumber",
                    Width = 120,
                    ReadOnly = true
                },
                new DataGridViewTextBoxColumn
                {
                    Name = "Date",
                    HeaderText = "التاريخ",
                    DataPropertyName = "Date",
                    Width = 100,
                    ReadOnly = true,
                    DefaultCellStyle = new DataGridViewCellStyle { Format = "yyyy-MM-dd" }
                },
                new DataGridViewTextBoxColumn
                {
                    Name = "Description",
                    HeaderText = "الوصف",
                    DataPropertyName = "Description",
                    Width = 250,
                    ReadOnly = true
                },
                new DataGridViewTextBoxColumn
                {
                    Name = "Type",
                    HeaderText = "النوع",
                    DataPropertyName = "Type",
                    Width = 120,
                    ReadOnly = true
                },
                new DataGridViewTextBoxColumn
                {
                    Name = "Status",
                    HeaderText = "الحالة",
                    DataPropertyName = "Status",
                    Width = 100,
                    ReadOnly = true
                },
                new DataGridViewTextBoxColumn
                {
                    Name = "TotalDebit",
                    HeaderText = "إجمالي المدين",
                    DataPropertyName = "TotalDebit",
                    Width = 120,
                    ReadOnly = true,
                    DefaultCellStyle = new DataGridViewCellStyle
                    {
                        Format = "N2",
                        Alignment = DataGridViewContentAlignment.MiddleRight
                    }
                },
                new DataGridViewTextBoxColumn
                {
                    Name = "TotalCredit",
                    HeaderText = "إجمالي الدائن",
                    DataPropertyName = "TotalCredit",
                    Width = 120,
                    ReadOnly = true,
                    DefaultCellStyle = new DataGridViewCellStyle
                    {
                        Format = "N2",
                        Alignment = DataGridViewContentAlignment.MiddleRight
                    }
                },
                new DataGridViewTextBoxColumn
                {
                    Name = "CreatedBy",
                    HeaderText = "أنشأ بواسطة",
                    DataPropertyName = "CreatedBy",
                    Width = 120,
                    ReadOnly = true
                }
            });
        }

        /// <summary>
        /// إعداد أعمدة جدول التفاصيل
        /// </summary>
        private void SetupDetailsGrid()
        {
            dgvDetails.AutoGenerateColumns = false;
            dgvDetails.Columns.Clear();

            dgvDetails.Columns.AddRange(new DataGridViewColumn[]
            {
                new DataGridViewTextBoxColumn
                {
                    Name = "LineNumber",
                    HeaderText = "الرقم",
                    DataPropertyName = "LineNumber",
                    Width = 60,
                    ReadOnly = true
                },
                new DataGridViewTextBoxColumn
                {
                    Name = "AccountCode",
                    HeaderText = "كود الحساب",
                    Width = 100,
                    ReadOnly = true
                },
                new DataGridViewTextBoxColumn
                {
                    Name = "AccountName",
                    HeaderText = "اسم الحساب",
                    Width = 200,
                    ReadOnly = true
                },
                new DataGridViewTextBoxColumn
                {
                    Name = "Description",
                    HeaderText = "البيان",
                    DataPropertyName = "Description",
                    Width = 200,
                    ReadOnly = true
                },
                new DataGridViewTextBoxColumn
                {
                    Name = "DebitAmount",
                    HeaderText = "مدين",
                    DataPropertyName = "DebitAmount",
                    Width = 120,
                    ReadOnly = true,
                    DefaultCellStyle = new DataGridViewCellStyle
                    {
                        Format = "N2",
                        Alignment = DataGridViewContentAlignment.MiddleRight
                    }
                },
                new DataGridViewTextBoxColumn
                {
                    Name = "CreditAmount",
                    HeaderText = "دائن",
                    DataPropertyName = "CreditAmount",
                    Width = 120,
                    ReadOnly = true,
                    DefaultCellStyle = new DataGridViewCellStyle
                    {
                        Format = "N2",
                        Alignment = DataGridViewContentAlignment.MiddleRight
                    }
                }
            });
        }

        /// <summary>
        /// إعداد قوائم الفلاتر
        /// </summary>
        private void SetupFilterComboBoxes()
        {
            // إعداد قائمة أنواع القيود
            cmbEntryType.Items.Clear();
            cmbEntryType.Items.Add("جميع الأنواع");
            foreach (JournalEntryType type in Enum.GetValues(typeof(JournalEntryType)))
            {
                cmbEntryType.Items.Add(new { Value = type, Text = JournalEntryTypeInfo.GetArabicName(type) });
            }
            cmbEntryType.DisplayMember = "Text";
            cmbEntryType.ValueMember = "Value";
            cmbEntryType.SelectedIndex = 0;

            // إعداد قائمة حالات القيود
            cmbEntryStatus.Items.Clear();
            cmbEntryStatus.Items.Add("جميع الحالات");
            foreach (JournalEntryStatus status in Enum.GetValues(typeof(JournalEntryStatus)))
            {
                cmbEntryStatus.Items.Add(new { Value = status, Text = JournalEntryStatusInfo.GetArabicName(status) });
            }
            cmbEntryStatus.DisplayMember = "Text";
            cmbEntryStatus.ValueMember = "Value";
            cmbEntryStatus.SelectedIndex = 0;
        }

        /// <summary>
        /// تطبيق الصلاحيات
        /// </summary>
        private void ApplyPermissions()
        {
            // يمكن إضافة منطق الصلاحيات هنا
            var canManageEntries = true; // HasPermission("MANAGE_JOURNAL_ENTRIES");

            btnAddEntry.Enabled = canManageEntries;
            btnEditEntry.Enabled = canManageEntries;
            btnDeleteEntry.Enabled = canManageEntries;
        }

        #endregion

        #region تحميل البيانات

        /// <summary>
        /// تحميل البيانات
        /// </summary>
        private async Task LoadDataAsync()
        {
            if (_isLoading) return;

            try
            {
                _isLoading = true;

                // عرض مؤشر التحميل
                ShowLoadingIndicator(true);

                // تحميل البيانات بشكل متوازي
                var journalEntriesTask = _journalEntryService.GetAllJournalEntriesAsync();
                var accountsTask = _accountService.GetActiveAccountsAsync();

                await Task.WhenAll(journalEntriesTask, accountsTask);

                _journalEntries = journalEntriesTask.Result;
                _accounts = accountsTask.Result;

                // تطبيق الفلاتر وعرض البيانات
                ApplyFiltersAndDisplay();

                // تحديث حالة الأزرار
                UpdateButtonStates();
            }
            catch (Exception ex)
            {
                RJMessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                _isLoading = false;
                ShowLoadingIndicator(false);
            }
        }

        /// <summary>
        /// عرض مؤشر التحميل
        /// </summary>
        private void ShowLoadingIndicator(bool show)
        {
            if (this.InvokeRequired)
            {
                this.Invoke(new Action<bool>(ShowLoadingIndicator), show);
                return;
            }

            lblStatus.Text = show ? "جاري التحميل..." : "جاهز";
            lblStatus.ForeColor = show ? Color.Orange : Color.Green;

            // تعطيل/تفعيل العناصر أثناء التحميل
            pnlFilters.Enabled = !show;
            pnlButtons.Enabled = !show;
            dgvJournalEntries.Enabled = !show;
        }

        #endregion

        #region الفلاتر والبحث

        /// <summary>
        /// تطبيق الفلاتر وعرض البيانات
        /// </summary>
        private void ApplyFiltersAndDisplay()
        {
            try
            {
                var filteredEntries = _journalEntries.AsEnumerable();

                // فلتر التاريخ
                filteredEntries = filteredEntries.Where(e =>
                    e.Date >= dtpFromDate.Value.Date &&
                    e.Date <= dtpToDate.Value.Date);

                // فلتر النوع
                if (cmbEntryType.SelectedIndex > 0 && cmbEntryType.SelectedItem != null)
                {
                    var selectedType = ((dynamic)cmbEntryType.SelectedItem).Value;
                    filteredEntries = filteredEntries.Where(e => e.Type == selectedType);
                }

                // فلتر الحالة
                if (cmbEntryStatus.SelectedIndex > 0 && cmbEntryStatus.SelectedItem != null)
                {
                    var selectedStatus = ((dynamic)cmbEntryStatus.SelectedItem).Value;
                    filteredEntries = filteredEntries.Where(e => e.Status == selectedStatus);
                }

                // فلتر البحث النصي
                if (!string.IsNullOrWhiteSpace(txtSearch.Text))
                {
                    var searchTerm = txtSearch.Text.Trim().ToLower();
                    filteredEntries = filteredEntries.Where(e =>
                        e.EntryNumber.ToLower().Contains(searchTerm) ||
                        e.Description.ToLower().Contains(searchTerm) ||
                        e.Reference.ToLower().Contains(searchTerm));
                }

                // عرض النتائج
                var resultList = filteredEntries.ToList();

                if (this.InvokeRequired)
                {
                    this.Invoke(new Action(() =>
                    {
                        dgvJournalEntries.DataSource = resultList;
                        lblTotalEntries.Text = $"إجمالي القيود: {resultList.Count}";

                        // تلوين الصفوف حسب الحالة
                        ColorizeJournalEntriesRows();
                    }));
                }
                else
                {
                    dgvJournalEntries.DataSource = resultList;
                    lblTotalEntries.Text = $"إجمالي القيود: {resultList.Count}";

                    // تلوين الصفوف حسب الحالة
                    ColorizeJournalEntriesRows();
                }
            }
            catch (Exception ex)
            {
                RJMessageBox.Show($"خطأ في تطبيق الفلاتر: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تلوين صفوف القيود حسب الحالة
        /// </summary>
        private void ColorizeJournalEntriesRows()
        {
            foreach (DataGridViewRow row in dgvJournalEntries.Rows)
            {
                if (row.DataBoundItem is JournalEntry entry)
                {
                    switch (entry.Status)
                    {
                        case JournalEntryStatus.Draft:
                            row.DefaultCellStyle.BackColor = Color.FromArgb(255, 248, 220); // أصفر فاتح
                            row.DefaultCellStyle.ForeColor = Color.DarkOrange;
                            break;
                        case JournalEntryStatus.Posted:
                            row.DefaultCellStyle.BackColor = Color.FromArgb(220, 255, 220); // أخضر فاتح
                            row.DefaultCellStyle.ForeColor = Color.DarkGreen;
                            break;
                        case JournalEntryStatus.Approved:
                            row.DefaultCellStyle.BackColor = Color.FromArgb(220, 220, 255); // أزرق فاتح
                            row.DefaultCellStyle.ForeColor = Color.DarkBlue;
                            break;
                        case JournalEntryStatus.Cancelled:
                            row.DefaultCellStyle.BackColor = Color.FromArgb(255, 220, 220); // أحمر فاتح
                            row.DefaultCellStyle.ForeColor = Color.DarkRed;
                            break;
                        default:
                            row.DefaultCellStyle.BackColor = Color.White;
                            row.DefaultCellStyle.ForeColor = Color.Black;
                            break;
                    }
                }
            }
        }

        #endregion

        #region تحديث حالة الأزرار

        /// <summary>
        /// تحديث حالة الأزرار
        /// </summary>
        private void UpdateButtonStates()
        {
            var hasSelection = _selectedEntry != null;
            var canEdit = hasSelection && (_selectedEntry.Status == JournalEntryStatus.Draft);
            var canDelete = hasSelection && (_selectedEntry.Status == JournalEntryStatus.Draft);

            if (this.InvokeRequired)
            {
                this.Invoke(new Action(() =>
                {
                    btnEditEntry.Enabled = canEdit;
                    btnDeleteEntry.Enabled = canDelete;
                    btnViewEntry.Enabled = hasSelection;
                    btnPostEntry.Enabled = hasSelection && (_selectedEntry.Status == JournalEntryStatus.Draft);
                    btnApproveEntry.Enabled = hasSelection && (_selectedEntry.Status == JournalEntryStatus.Posted);
                    btnCancelEntry.Enabled = hasSelection && (_selectedEntry.Status != JournalEntryStatus.Cancelled);
                }));
            }
            else
            {
                btnEditEntry.Enabled = canEdit;
                btnDeleteEntry.Enabled = canDelete;
                btnViewEntry.Enabled = hasSelection;
                btnPostEntry.Enabled = hasSelection && (_selectedEntry.Status == JournalEntryStatus.Draft);
                btnApproveEntry.Enabled = hasSelection && (_selectedEntry.Status == JournalEntryStatus.Posted);
                btnCancelEntry.Enabled = hasSelection && (_selectedEntry.Status != JournalEntryStatus.Cancelled);
            }
        }

        #endregion

        #region أحداث النموذج

        /// <summary>
        /// تغيير تحديد القيد
        /// </summary>
        private async void DgvJournalEntries_SelectionChanged(object sender, EventArgs e)
        {
            if (_isLoading) return;

            try
            {
                if (dgvJournalEntries.SelectedRows.Count > 0)
                {
                    var selectedRow = dgvJournalEntries.SelectedRows[0];
                    _selectedEntry = selectedRow.DataBoundItem as JournalEntry;

                    if (_selectedEntry != null)
                    {
                        lblSelectedEntry.Text = $"القيد المحدد: {_selectedEntry.EntryNumber} - {_selectedEntry.Description}";
                        await LoadEntryDetailsAsync();
                    }
                }
                else
                {
                    _selectedEntry = null;
                    lblSelectedEntry.Text = "لم يتم تحديد قيد";
                    dgvDetails.DataSource = null;
                }

                UpdateButtonStates();
            }
            catch (Exception ex)
            {
                RJMessageBox.Show($"خطأ في تحديد القيد: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تحميل تفاصيل القيد المحدد
        /// </summary>
        private async Task LoadEntryDetailsAsync()
        {
            if (_selectedEntry == null) return;

            try
            {
                var details = await _journalEntryService.GetJournalEntryDetailsAsync(_selectedEntry.Id);

                // إضافة معلومات الحساب لكل تفصيل
                foreach (var detail in details)
                {
                    var account = _accounts.FirstOrDefault(a => a.Id == detail.AccountId);
                    if (account != null)
                    {
                        detail.Account = account;
                    }
                }

                if (this.InvokeRequired)
                {
                    this.Invoke(new Action(() =>
                    {
                        dgvDetails.DataSource = details;
                        UpdateDetailsGridDisplay();
                    }));
                }
                else
                {
                    dgvDetails.DataSource = details;
                    UpdateDetailsGridDisplay();
                }
            }
            catch (Exception ex)
            {
                RJMessageBox.Show($"خطأ في تحميل تفاصيل القيد: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تحديث عرض جدول التفاصيل
        /// </summary>
        private void UpdateDetailsGridDisplay()
        {
            foreach (DataGridViewRow row in dgvDetails.Rows)
            {
                if (row.DataBoundItem is JournalEntryDetail detail && detail.Account != null)
                {
                    row.Cells["AccountCode"].Value = detail.Account.Code;
                    row.Cells["AccountName"].Value = detail.Account.Name;
                }
            }
        }

        /// <summary>
        /// البحث في النص
        /// </summary>
        private void TxtSearch_TextChanged(object sender, EventArgs e)
        {
            // تطبيق الفلاتر مع تأخير بسيط لتحسين الأداء
            if (_searchTimer != null)
            {
                _searchTimer.Stop();
                _searchTimer.Dispose();
            }

            _searchTimer = new System.Windows.Forms.Timer { Interval = 500 };
            _searchTimer.Tick += (s, args) =>
            {
                _searchTimer.Stop();
                _searchTimer.Dispose();
                _searchTimer = null;
                ApplyFiltersAndDisplay();
            };
            _searchTimer.Start();
        }

        private System.Windows.Forms.Timer _searchTimer;

        /// <summary>
        /// تطبيق الفلاتر
        /// </summary>
        private void BtnFilter_Click(object sender, EventArgs e)
        {
            ApplyFiltersAndDisplay();
        }

        /// <summary>
        /// مسح الفلاتر
        /// </summary>
        private void BtnClearFilter_Click(object sender, EventArgs e)
        {
            dtpFromDate.Value = DateTime.Today.AddMonths(-1);
            dtpToDate.Value = DateTime.Today;
            cmbEntryType.SelectedIndex = 0;
            cmbEntryStatus.SelectedIndex = 0;
            txtSearch.Text = "";
            ApplyFiltersAndDisplay();
        }

        #endregion

        #region أحداث الأزرار

        /// <summary>
        /// إضافة قيد جديد
        /// </summary>
        private async void BtnAddEntry_Click(object sender, EventArgs e)
        {
            try
            {
                var addForm = new Frm_AddEditJournalEntry(_journalEntryService, _accountService);
                if (addForm.ShowDialog() == DialogResult.OK)
                {
                    await LoadDataAsync();
                    RJMessageBox.Show("تم إضافة القيد بنجاح", "نجح",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                RJMessageBox.Show($"خطأ في إضافة القيد: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تعديل قيد
        /// </summary>
        private async void BtnEditEntry_Click(object sender, EventArgs e)
        {
            if (_selectedEntry == null) return;

            try
            {
                var editForm = new Frm_AddEditJournalEntry(_journalEntryService, _accountService, _selectedEntry);
                if (editForm.ShowDialog() == DialogResult.OK)
                {
                    await LoadDataAsync();
                    RJMessageBox.Show("تم تعديل القيد بنجاح", "نجح",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                RJMessageBox.Show($"خطأ في تعديل القيد: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// حذف قيد
        /// </summary>
        private async void BtnDeleteEntry_Click(object sender, EventArgs e)
        {
            if (_selectedEntry == null) return;

            var result = RJMessageBox.Show(
                $"هل أنت متأكد من حذف القيد '{_selectedEntry.EntryNumber}'؟\n\nسيتم حذف جميع تفاصيل القيد أيضاً.",
                "تأكيد الحذف", MessageBoxButtons.YesNo, MessageBoxIcon.Warning);

            if (result == DialogResult.Yes)
            {
                try
                {
                    await _journalEntryService.DeleteJournalEntryAsync(_selectedEntry.Id);
                    await LoadDataAsync();
                    RJMessageBox.Show("تم حذف القيد بنجاح", "نجح",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                catch (Exception ex)
                {
                    RJMessageBox.Show($"خطأ في حذف القيد: {ex.Message}", "خطأ",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        /// <summary>
        /// عرض تفاصيل القيد
        /// </summary>
        private void BtnViewEntry_Click(object sender, EventArgs e)
        {
            if (_selectedEntry == null) return;

            try
            {
                var viewForm = new Frm_ViewJournalEntry(_journalEntryService, _accountService, _selectedEntry);
                viewForm.ShowDialog();
            }
            catch (Exception ex)
            {
                RJMessageBox.Show($"خطأ في عرض القيد: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// ترحيل القيد
        /// </summary>
        private async void BtnPostEntry_Click(object sender, EventArgs e)
        {
            if (_selectedEntry == null) return;

            var result = RJMessageBox.Show(
                $"هل أنت متأكد من ترحيل القيد '{_selectedEntry.EntryNumber}'؟\n\nلن يمكن تعديله بعد الترحيل.",
                "تأكيد الترحيل", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                try
                {
                    _selectedEntry.Status = JournalEntryStatus.Posted;
                    await _journalEntryService.UpdateJournalEntryAsync(_selectedEntry);
                    await LoadDataAsync();
                    RJMessageBox.Show("تم ترحيل القيد بنجاح", "نجح",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                catch (Exception ex)
                {
                    RJMessageBox.Show($"خطأ في ترحيل القيد: {ex.Message}", "خطأ",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        /// <summary>
        /// اعتماد القيد
        /// </summary>
        private async void BtnApproveEntry_Click(object sender, EventArgs e)
        {
            if (_selectedEntry == null) return;

            var result = RJMessageBox.Show(
                $"هل أنت متأكد من اعتماد القيد '{_selectedEntry.EntryNumber}'؟",
                "تأكيد الاعتماد", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                try
                {
                    _selectedEntry.Status = JournalEntryStatus.Approved;
                    _selectedEntry.ApprovedBy = Global_Variable.CurrentUser?.Username;
                    _selectedEntry.ApprovedDate = DateTime.Now;
                    await _journalEntryService.UpdateJournalEntryAsync(_selectedEntry);
                    await LoadDataAsync();
                    RJMessageBox.Show("تم اعتماد القيد بنجاح", "نجح",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                catch (Exception ex)
                {
                    RJMessageBox.Show($"خطأ في اعتماد القيد: {ex.Message}", "خطأ",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        /// <summary>
        /// إلغاء القيد
        /// </summary>
        private async void BtnCancelEntry_Click(object sender, EventArgs e)
        {
            if (_selectedEntry == null) return;

            var result = RJMessageBox.Show(
                $"هل أنت متأكد من إلغاء القيد '{_selectedEntry.EntryNumber}'؟",
                "تأكيد الإلغاء", MessageBoxButtons.YesNo, MessageBoxIcon.Warning);

            if (result == DialogResult.Yes)
            {
                try
                {
                    _selectedEntry.Status = JournalEntryStatus.Cancelled;
                    await _journalEntryService.UpdateJournalEntryAsync(_selectedEntry);
                    await LoadDataAsync();
                    RJMessageBox.Show("تم إلغاء القيد بنجاح", "نجح",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                catch (Exception ex)
                {
                    RJMessageBox.Show($"خطأ في إلغاء القيد: {ex.Message}", "خطأ",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        /// <summary>
        /// تحديث البيانات
        /// </summary>
        private async void BtnRefresh_Click(object sender, EventArgs e)
        {
            await LoadDataAsync();
        }

        /// <summary>
        /// تصدير البيانات
        /// </summary>
        private void BtnExport_Click(object sender, EventArgs e)
        {
            try
            {
                var saveDialog = new SaveFileDialog
                {
                    Filter = "Excel Files|*.xlsx|CSV Files|*.csv",
                    FileName = $"القيود_المحاسبية_{DateTime.Now:yyyyMMdd}.xlsx"
                };

                if (saveDialog.ShowDialog() == DialogResult.OK)
                {
                    ExportJournalEntriesToFile(saveDialog.FileName);
                    RJMessageBox.Show("تم تصدير البيانات بنجاح", "نجح",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                RJMessageBox.Show($"خطأ في تصدير البيانات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region وظائف مساعدة

        /// <summary>
        /// تصدير القيود إلى ملف
        /// </summary>
        private void ExportJournalEntriesToFile(string fileName)
        {
            var filteredEntries = dgvJournalEntries.DataSource as List<JournalEntry> ?? new List<JournalEntry>();

            var lines = new List<string>
            {
                "رقم القيد,التاريخ,الوصف,النوع,الحالة,إجمالي المدين,إجمالي الدائن,أنشأ بواسطة"
            };

            foreach (var entry in filteredEntries)
            {
                var line = $"{entry.EntryNumber}," +
                          $"{entry.Date:yyyy-MM-dd}," +
                          $"\"{entry.Description}\"," +
                          $"{JournalEntryTypeInfo.GetArabicName(entry.Type)}," +
                          $"{JournalEntryStatusInfo.GetArabicName(entry.Status)}," +
                          $"{entry.TotalDebit:N2}," +
                          $"{entry.TotalCredit:N2}," +
                          $"{entry.CreatedBy}";
                lines.Add(line);
            }

            System.IO.File.WriteAllLines(fileName, lines, System.Text.Encoding.UTF8);
        }

        #endregion

        private void Frm_JournalEntries_SizeChanged(object sender, EventArgs e)
        {
            this.Refresh();
            pnlMain.Refresh();
            dgvDetails.Refresh();
            dgvJournalEntries.Refresh();
            pnlFilters.Refresh();
            pnlJournalEntries.Refresh();
            pnlDetails.Refresh();

        }
    }
}
