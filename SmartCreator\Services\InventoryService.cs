using SmartCreator.Data;
using SmartCreator.Entities.Accounting;
using SmartCreator.Models;
using SmartCreator.RJControls;
using SmartCreator.Services.Security;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace SmartCreator.Services
{
    /// <summary>
    /// خدمة إدارة المخزون
    /// </summary>
    public class InventoryService
    {
        private readonly Smart_DataAccess _dataAccess;
        private readonly UserActivityService _activityService;
        private readonly NotificationService _notificationService;

        public InventoryService()
        {
            _dataAccess = new Smart_DataAccess();
            _activityService = new UserActivityService(_dataAccess);
            _notificationService = new NotificationService(_dataAccess);
        }

        /// <summary>
        /// تحديث مخزون المنتج (زيادة أو نقصان)
        /// </summary>
        public async Task<bool> UpdateStockAsync(int productId, int quantity, string reason, bool isIncrease = true)
        {
            try
            {
                var product = _dataAccess.GetSingleById<Product>(productId);
                if (product == null)
                {
                    RJMessageBox.Show("المنتج غير موجود", "خطأ", System.Windows.Forms.MessageBoxButtons.OK, System.Windows.Forms.MessageBoxIcon.Error);
                    return false;
                }

                var oldStock = product.Stock;
                var newStock = isIncrease ? product.Stock + quantity : product.Stock - quantity;

                // التحقق من عدم السماح بمخزون سالب
                if (newStock < 0)
                {
                    RJMessageBox.Show("لا يمكن أن يكون المخزون أقل من الصفر", "خطأ", System.Windows.Forms.MessageBoxButtons.OK, System.Windows.Forms.MessageBoxIcon.Warning);
                    return false;
                }

                // تحديث المخزون
                product.Stock = newStock;
                product.LastStockUpdate = DateTime.Now;
                product.LastUpdateBy = Global_Variable.CurrentUser?.Username ?? "System";
                product.StockUpdateReason = reason;

                var query = $@"
                    UPDATE Product 
                    SET Stock = {newStock}, 
                        LastStockUpdate = '{DateTime.Now:yyyy-MM-dd HH:mm:ss}',
                        LastUpdateBy = '{product.LastUpdateBy}',
                        StockUpdateReason = '{reason}'
                    WHERE Id = {productId}";

                var result = _dataAccess.Execute(query);

                if (result > 0)
                {
                    var action = isIncrease ? "زيادة مخزون" : "تقليل مخزون";
                    var description = $"تم {action} المنتج '{product.Name}' من {oldStock} إلى {newStock} (التغيير: {(isIncrease ? "+" : "-")}{quantity}) - السبب: {reason}";

                    await _activityService.LogActivityAsync(
                        Global_Variable.CurrentUser?.Id ?? 1,
                        action,
                        "إدارة المخزون",
                        description,
                        "Product",
                        productId,
                        new { Stock = oldStock },
                        new { Stock = newStock }
                    );

                    // التحقق من إشعارات المخزون المنخفض
                    await CheckLowStockNotificationAsync(product);

                    // إنشاء سجل حركة مخزون
                    await CreateStockMovementRecordAsync(productId, oldStock, newStock, quantity, reason, isIncrease);

                    RJMessageBox.Show($"تم تحديث المخزون بنجاح\nالمخزون السابق: {oldStock}\nالمخزون الحالي: {newStock}", 
                        "نجح", System.Windows.Forms.MessageBoxButtons.OK, System.Windows.Forms.MessageBoxIcon.Information);
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                await _activityService.LogActivityAsync(
                    Global_Variable.CurrentUser?.Id ?? 1,
                    "خطأ في تحديث المخزون",
                    "إدارة المخزون",
                    $"خطأ في تحديث مخزون المنتج ID: {productId} - {ex.Message}",
                    "Product",
                    productId,
                    null,
                    null,
                    "Error",
                    false,
                    ex.Message
                );

                RJMessageBox.Show($"خطأ في تحديث المخزون: {ex.Message}", "خطأ", System.Windows.Forms.MessageBoxButtons.OK, System.Windows.Forms.MessageBoxIcon.Error);
                return false;
            }
        }

        /// <summary>
        /// تعيين مخزون المنتج إلى قيمة محددة
        /// </summary>
        public async Task<bool> SetStockAsync(int productId, int newStock, string reason)
        {
            try
            {
                var product = _dataAccess.GetSingleById<Product>(productId);
                if (product == null)
                {
                    RJMessageBox.Show("المنتج غير موجود", "خطأ", System.Windows.Forms.MessageBoxButtons.OK, System.Windows.Forms.MessageBoxIcon.Error);
                    return false;
                }

                if (newStock < 0)
                {
                    RJMessageBox.Show("لا يمكن أن يكون المخزون أقل من الصفر", "خطأ", System.Windows.Forms.MessageBoxButtons.OK, System.Windows.Forms.MessageBoxIcon.Warning);
                    return false;
                }

                var oldStock = product.Stock;
                var difference = newStock - oldStock;

                // تحديث المخزون
                product.Stock = newStock;
                product.LastStockUpdate = DateTime.Now;
                product.LastUpdateBy = Global_Variable.CurrentUser?.Username ?? "System";
                product.StockUpdateReason = reason;

                var query = $@"
                    UPDATE Product 
                    SET Stock = {newStock}, 
                        LastStockUpdate = '{DateTime.Now:yyyy-MM-dd HH:mm:ss}',
                        LastUpdateBy = '{product.LastUpdateBy}',
                        StockUpdateReason = '{reason}'
                    WHERE Id = {productId}";

                var result = _dataAccess.Execute(query);

                if (result > 0)
                {
                    var description = $"تم تعديل مخزون المنتج '{product.Name}' من {oldStock} إلى {newStock} (التغيير: {(difference >= 0 ? "+" : "")}{difference}) - السبب: {reason}";

                    await _activityService.LogActivityAsync(
                        Global_Variable.CurrentUser?.Id ?? 1,
                        "تعديل مخزون",
                        "إدارة المخزون",
                        description,
                        "Product",
                        productId,
                        new { Stock = oldStock },
                        new { Stock = newStock }
                    );

                    // التحقق من إشعارات المخزون المنخفض
                    await CheckLowStockNotificationAsync(product);

                    // إنشاء سجل حركة مخزون
                    await CreateStockMovementRecordAsync(productId, oldStock, newStock, Math.Abs(difference), reason, difference >= 0);

                    RJMessageBox.Show($"تم تعديل المخزون بنجاح\nالمخزون السابق: {oldStock}\nالمخزون الحالي: {newStock}", 
                        "نجح", System.Windows.Forms.MessageBoxButtons.OK, System.Windows.Forms.MessageBoxIcon.Information);
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                await _activityService.LogActivityAsync(
                    Global_Variable.CurrentUser?.Id ?? 1,
                    "خطأ في تعديل المخزون",
                    "إدارة المخزون",
                    $"خطأ في تعديل مخزون المنتج ID: {productId} - {ex.Message}",
                    "Product",
                    productId,
                    null,
                    null,
                    "Error",
                    false,
                    ex.Message
                );

                RJMessageBox.Show($"خطأ في تعديل المخزون: {ex.Message}", "خطأ", System.Windows.Forms.MessageBoxButtons.OK, System.Windows.Forms.MessageBoxIcon.Error);
                return false;
            }
        }

        /// <summary>
        /// الحصول على المنتجات التي تحتاج إعادة طلب
        /// </summary>
        public async Task<List<Product>> GetLowStockProductsAsync()
        {
            try
            {
                var query = $@"
                    SELECT * FROM Product 
                    WHERE Rb='{Global_Variable.Mk_resources.RB_SN}' 
                    AND Stock <= LowStockThreshold 
                    AND Active = 1
                    ORDER BY Stock ASC, Name";

                var products = _dataAccess.Load<Product>(query);

                await _activityService.LogActivityAsync(
                    Global_Variable.CurrentUser?.Id ?? 1,
                    "عرض المنتجات منخفضة المخزون",
                    "إدارة المخزون",
                    $"تم عرض {products.Count} منتج يحتاج إعادة طلب",
                    "Product"
                );

                return products;
            }
            catch (Exception ex)
            {
                await _activityService.LogActivityAsync(
                    Global_Variable.CurrentUser?.Id ?? 1,
                    "خطأ في عرض المنتجات منخفضة المخزون",
                    "إدارة المخزون",
                    $"خطأ: {ex.Message}",
                    "Product",
                    null,
                    null,
                    null,
                    "Error",
                    false,
                    ex.Message
                );
                throw;
            }
        }

        /// <summary>
        /// التحقق من إشعارات المخزون المنخفض
        /// </summary>
        private async Task CheckLowStockNotificationAsync(Product product)
        {
            try
            {
                if (product.NeedsReorder)
                {
                    var title = "تنبيه مخزون منخفض";
                    var message = $"المنتج '{product.Name}' (الكود: {product.Code}) وصل إلى الحد الأدنى للمخزون. المخزون الحالي: {product.Stock}، الحد الأدنى: {product.LowStockThreshold}";

                    await _notificationService.CreateNotificationAsync(
                        title,
                        message,
                        "Warning",
                        "High",
                        Global_Variable.CurrentUser?.Id,
                        Global_Variable.CurrentUser?.Username,
                        "إدارة المخزون",
                        product.Id,
                        "Product",
                        $"/Products/Details/{product.Id}",
                        DateTime.Now.AddDays(30)
                    );
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إنشاء إشعار المخزون: {ex.Message}");
            }
        }

        /// <summary>
        /// إنشاء سجل حركة مخزون
        /// </summary>
        private async Task CreateStockMovementRecordAsync(int productId, int oldStock, int newStock, int quantity, string reason, bool isIncrease)
        {
            try
            {
                var movementType = isIncrease ? "زيادة" : "نقصان";
                var query = $@"
                    INSERT INTO StockMovements 
                    (ProductId, OldStock, NewStock, Quantity, MovementType, Reason, CreatedBy, CreatedDate, Rb)
                    VALUES 
                    ({productId}, {oldStock}, {newStock}, {quantity}, '{movementType}', '{reason}', 
                     '{Global_Variable.CurrentUser?.Username ?? "System"}', '{DateTime.Now:yyyy-MM-dd HH:mm:ss}', 
                     '{Global_Variable.Mk_resources.RB_SN}')";

                _dataAccess.Execute(query);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إنشاء سجل حركة المخزون: {ex.Message}");
            }
        }
    }
}
