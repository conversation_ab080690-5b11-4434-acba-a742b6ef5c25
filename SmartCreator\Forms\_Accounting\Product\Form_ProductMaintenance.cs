﻿using SmartCreator.Data;
using SmartCreator.Entities;
//using SmartCreator.Entities.Accounts;
using SmartCreator.Entities.EnumType;
//using SmartCreator.Forms.Accounting.Accounts;
using SmartCreator.Models;
using SmartCreator.Properties;
using SmartCreator.RJControls;
using SmartCreator.Settings;
using SmartCreator.Utils;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Windows.Markup;

namespace SmartCreator.Forms.Accounting
{
    public partial class Form_ProductMaintenance : RJForms.RJChildForm
    {
        Smart_DataAccess Smart_DA;
        public bool add = true;
        public bool succes=false;
        Entities.Accounts.Product product;
        public Form_ProductMaintenance()
        {
            InitializeComponent();
            Smart_DA=new Smart_DataAccess();
            Get_Umo();
            Get_Accounts();
            lblTitle.Text = "اضافة صنف جديد";
            btnSave.Text = "اضافة";
            btnSave.IconChar = FontAwesome.Sharp.IconChar.Plus;

            btnSave.BackColor = RJColors.Confirm;

            txt_Price.Text = "0";
            //int sn = (int)Smart_DA.Get_BatchCards_My_Sequence("Product");
            txt_code.Text = (Smart_DA.Get_BatchCards_My_Sequence("Product")+1).ToString();
            //sn += 1;
            //txt_code.Text = sn+"";
            Set_Font();
            
        }
        public Form_ProductMaintenance(Entities.Accounts.Product _product)
        {
            InitializeComponent();
            Smart_DA = new Smart_DataAccess();
            txt_code.Enabled = false;
            Get_Umo();
            Get_Accounts();

            lblTitle.Text = "تعديل الصنف";
            btnSave.Text = "تعديل";
            btnSave.IconChar=FontAwesome.Sharp.IconChar.Edit;
            btnSave.BackColor = RJColors.DefaultFormBorderColor;

            product = _product;

            txt_code.Text =product.Code;
            txt_code.Enabled = false;
            txt_Description.Text = product.Description;
            txt_name.Text = product.Name;
            txt_Price.Text = product.Price.ToString();
            check_Active.Checked=Convert.ToBoolean( product.Active);
            
            if (product.Product_UomId != null)
            {
                try
                {
                    for (int i = 0; i < Cbox_Uom.Items.Count; ++i)
                    {
                        var selectedItem = (KeyValuePair<int, string>)Cbox_Uom.Items[i];
                        int key = selectedItem.Key;
                        string Value = selectedItem.Value;
                        if (key == product.Product_UomId)
                        {
                            Cbox_Uom.SelectedIndex = i;
                            break;
                        }
                    }
                }
                catch { }
            }

            //string Account_Income = SmartCreator.Entities.Accounts.Account.AccountTypeChoices.Where(p => p.Value == product.Code.ToString()).First().Text;
            if (product.Account_IncomeId != null)
            {
                try
                {
                    for (int i = 0; i < Cbox_Account_Income.Items.Count; ++i)
                    {
                        var selectedItem = (KeyValuePair<int, string>)Cbox_Account_Income.Items[i];
                        int key = selectedItem.Key;
                        string Value = selectedItem.Value;
                        if (key == product.Account_IncomeId)
                        {
                            Cbox_Account_Income.SelectedIndex = i;
                            break;
                        }
                    }
                }
                catch { }
            }

            if (product.Account_ExpenseId != null)
            {
                try
                {
                    for (int i = 0; i < Cbox_Account_Expense.Items.Count; ++i)
                    {
                        var selectedItem = (KeyValuePair<int, string>)Cbox_Account_Expense.Items[i];
                        int key = selectedItem.Key;
                        string Value = selectedItem.Value;
                        if (key == product.Account_ExpenseId)
                        {
                            Cbox_Account_Expense.SelectedIndex = i;
                            break;
                        }
                    }
                }
                catch { }
            }

            Set_Font();

        }
        private void Get_Umo()
        {
            try
            {
                List<Entities.Accounts.ProductUoM> sp = Smart_DA.Load<Entities.Accounts.ProductUoM>($"select * from ProductUoM where Rb='{Global_Variable.Mk_resources.RB_SN}'");
                Dictionary<int, string> comboSource = new Dictionary<int, string>();
                comboSource.Add(0, "");
                foreach (Entities.Accounts.ProductUoM s in sp)
                    comboSource.Add(s.Id, s.Name);

                Cbox_Uom.DataSource = new BindingSource(comboSource, null);
                Cbox_Uom.DisplayMember = "Value";
                Cbox_Uom.ValueMember = "Key";
                Cbox_Uom.SelectedIndex = -1;
                //if(sp.Count<=0)
                //    Cbox_Uom.Text = "";

                //Cbox_Uom.RightToLeft = RightToLeft.No;
                //Cbox_Uom.label.RightToLeft = RightToLeft.No;



            }
            catch { }
        }
        private void Get_Accounts()
        {
            try
            {
                List<Entities.Accounts.Account> sp = Smart_DA.Load<Entities.Accounts.Account>($"select * from Account where Rb='{Global_Variable.Mk_resources.RB_SN}'");
                Dictionary<int, string> comboSource = new Dictionary<int, string>();
                comboSource.Add(0, "");
                foreach (Entities.Accounts.Account s in sp)
                    comboSource.Add(s.Id, s.Name);

                Cbox_Account_Expense.DataSource =  new BindingSource(comboSource, null);
                Cbox_Account_Income.DataSource = new BindingSource(comboSource, null);


                Cbox_Account_Expense.DisplayMember = Cbox_Account_Income.DisplayMember = "Value";
                Cbox_Account_Expense.ValueMember = Cbox_Account_Income.ValueMember = "Key";
                Cbox_Account_Expense.SelectedIndex = -1;
                Cbox_Account_Income.SelectedIndex = -1;
                //if (sp.Count <= 0)
                Cbox_Account_Income.Text = Cbox_Account_Expense.Text = "";

                //Cbox_Uom.RightToLeft = RightToLeft.No;
                //Cbox_Uom.label.RightToLeft = RightToLeft.No;



            }
            catch { }
        }
         
        private void Set_Font()
        {


            System.Drawing.Font title_font = Program.GetCustomFont(Resources.DroidKufi_Bold, 14, FontStyle.Bold);
            btnSave.Font = title_font;
            lblTitle.Font = title_font;

            //System.Drawing.Font DGV_font = CustomFonts.Get_Custom_Font("Cairo_Medium", 10, false);
            System.Drawing.Font lbl_font = Program.GetCustomFont(Resources.DroidSansArabic, 10, FontStyle.Regular);
            //System.Drawing.Font DGV_font = Program.GetCustomFont(Resources.Cairo_Medium, 9, FontStyle.Regular);
            btnSave.Font  =  Program.GetCustomFont(Resources.DroidKufi_Bold, 9.75f, FontStyle.Bold);

             rjLabel5.Font  = rjLabel1.Font = rjLabel2.Font = rjLabel3.Font = rjLabel4.Font = rjLabel5.Font = rjLabel6.Font = rjLabel7.Font =
            txt_code.Font = txt_name.Font = 
            lbl_font;
            this.Focus();

            utils utils = new utils();
            utils.Control_textSize1(this);
        }

        private void btnSave_Click(object sender, EventArgs e)
        {
            Smart_DataAccess smart_DataAccess = new Smart_DataAccess();
            if (add)
            {
                long foundsp = smart_DataAccess.Get_int_FromDB($"SELECT COUNT(*) FROM Product where Code='{txt_code.Text}'  and  Rb='{Global_Variable.Mk_resources.RB_SN}';");
                
                if (foundsp > 0)
                {
                    RJMessageBox.Show("رقم او كود المنتج موجود مسبقا");
                    return;
                }
                if (txt_code.Text == "")
                {
                    RJMessageBox.Show("رقم الصنف مطلوب");
                    return;
                }
                if (txt_name.Text == "")
                {
                    RJMessageBox.Show("اسم المنتج مطلوب");
                    return;
                }
                if (!float.TryParse(txt_Price.Text, out float value))
                {
                    RJMessageBox.Show("السعر يجب ان تكون رقم صحيح او رقم عشري");
                    return;
                }
                if (Cbox_Uom.Text=="" && Cbox_Uom.SelectedIndex == -1)
                {
                    RJMessageBox.Show("حدد الوحده للصنف");
                    return;
                }

                Entities.Accounts.Product sp = new Entities.Accounts.Product();
                sp.Code = txt_code.Text;
                sp.Name = txt_name.Text;
                sp.Price = Convert.ToInt32(txt_Price.Text);

                sp.Product_UomId = (int?)Cbox_Uom.SelectedValue;
                sp.Account_IncomeId = (int?)Cbox_Account_Income.SelectedValue;
                sp.Account_ExpenseId = (int?)Cbox_Account_Expense.SelectedValue;

                if (Cbox_Account_Income.SelectedIndex == 0)
                    sp.Account_IncomeId = null;
                if (Cbox_Account_Expense.SelectedIndex == 0)
                    sp.Account_ExpenseId=null;
                if (Cbox_Uom.SelectedIndex == 0)
                    sp.Product_UomId = null;


                sp.Active = Convert.ToInt16(check_Active.Checked);
                sp.Rb = Global_Variable.Mk_resources.RB_SN;

            
                lock (Smart_DataAccess.Lock_object)
                {
                    List<string> Fields = new List<string>();
                    string[] aFields = { "Code", "Name", "Price", "Account_IncomeId", "Account_ExpenseId", "Product_UomId", "Rb", "Active", "Description" };

                    Fields.AddRange(aFields);


                    int new_sp = smart_DataAccess.InsertTable(Fields, sp, "Product");
                    if (new_sp > 0)
                    {
                        //smart_DataAccess.InsertTable<SellingPoint>(Fields, sp);
                        RJMessageBox.Show("تمت عمليه الاضافة");
                        succes = true;
                       int x = Smart_DA.Update_MySequence("Product", Convert.ToInt32(txt_code.Text));
                        
                        this.Close();
                        return;
                    }
                    RJMessageBox.Show("خطاء");
                    return;
                }
            }
            else
            {
                Smart_DataAccess sql_DataAccess = new Smart_DataAccess();
                List<string> Fields = new List<string>();
                string[] aFields = {  "Name", "Price", "Account_IncomeId", "Account_ExpenseId", "Product_UomId", "Active", "Description" };
                Fields.AddRange(aFields);
                try
                {
                    lock (Smart_DataAccess.Lock_object)
                    {
                        var dataa = new Entities.Accounts.Product();
                        dataa.Id = product.Id;
                        dataa.Name = txt_name.Text;
                        dataa.Product_UomId = (int?)Cbox_Uom.SelectedValue;
                        dataa.Account_ExpenseId = (int?)Cbox_Account_Expense.SelectedValue;
                        dataa.Account_IncomeId = (int?)Cbox_Account_Income.SelectedValue;

                        dataa.Active = Convert.ToInt16(check_Active.Checked);
                        dataa.Description = txt_Description.Text;
                        dataa.Price = (float)Convert.ToDouble(txt_Price.Text);

                        if (Cbox_Account_Income.SelectedIndex == 0)
                            dataa.Account_IncomeId = null;
                        if (Cbox_Account_Expense.SelectedIndex == 0)
                            dataa.Account_ExpenseId = null;
                        if (Cbox_Uom.SelectedIndex == 0)
                            dataa.Product_UomId = null;


                        //var data = new Product
                        //{
                        //    Id = product.Id,
                        //    Name = txt_name.Text,

                        //    Product_uomId = (int?)Cbox_Uom.SelectedValue,


                        //    Account_ExpenseId = (int?)Cbox_Account_Expense.SelectedValue,
                        //    Account_IncomeId = (int?)Cbox_Account_Income.SelectedValue,

                        //    Active = Convert.ToInt16(check_Active.Checked),
                        //    Description = txt_Description.Text,
                        //    Price = (float)Convert.ToDouble(txt_Price.Text),
                        //};

                        string sqlquery = UtilsSql.GetUpdateSql<Entities.Accounts.Product>("Product", Fields, $" where Id=@Id and   Rb='{Global_Variable.Mk_resources.RB_SN}'");
                        int r = sql_DataAccess.UpateTable(dataa, sqlquery);
                    }
                }
                catch { }
            }
            succes = true;
            this.Close();

        }

        private void btn_Add_Uom_Click(object sender, EventArgs e)
        {
            Form_UoM frm=new Form_UoM();
            frm.ShowDialog();
            if(frm.succes)
                Get_Umo();
        }
    }
}
