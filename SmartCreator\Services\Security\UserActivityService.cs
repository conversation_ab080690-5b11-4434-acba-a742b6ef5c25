using Newtonsoft.Json;
using SmartCreator.Data;
using SmartCreator.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace SmartCreator.Services.Security
{
    /// <summary>
    /// خدمة مراقبة أنشطة المستخدمين
    /// </summary>
    public class UserActivityService
    {
        private readonly Smart_DataAccess _dataAccess;

        public UserActivityService(Smart_DataAccess dataAccess)
        {
            _dataAccess = dataAccess;
        }

        // Constructor بدون معاملات للاختبار
        public UserActivityService()
        {
            _dataAccess = null;
        }

        /// <summary>
        /// تسجيل نشاط مستخدم
        /// </summary>
        public async Task LogActivityAsync(int userId, string action, string module, string description = null,
            string entityType = null, int? entityId = null, object oldValues = null, object newValues = null,
            string severity = SeverityLevels.INFO, bool isSuccessful = true, string errorMessage = null)
        {
            try
            {
                // إذا كان _dataAccess null (في حالة الاختبار)، لا نفعل شيء
                if (_dataAccess == null)
                {
                    await Task.CompletedTask;
                    return;
                }
                var activity = new UserActivity
                {
                    UserId = userId,
                    Action = action,
                    Module = module,
                    Description = description,
                    EntityType = entityType,
                    EntityId = entityId,
                    OldValues = oldValues != null ? JsonConvert.SerializeObject(oldValues) : null,
                    NewValues = newValues != null ? JsonConvert.SerializeObject(newValues) : null,
                    IpAddress = GetClientIpAddress(),
                    UserAgent = GetUserAgent(),
                    Timestamp = DateTime.Now,
                    ActivityType = GetActivityTypeFromAction(action),
                    Severity = severity,
                    IsSuccessful = isSuccessful,
                    ErrorMessage = errorMessage
                };

                var query = @"
                    INSERT INTO UserActivities
                    (UserId, Action, Module, SubModule, Description, EntityType, EntityId,
                     OldValues, NewValues, IpAddress, UserAgent, Timestamp, ActivityType,
                     Severity, IsSuccessful, ErrorMessage)
                    VALUES
                    (@UserId, @Action, @Module, @SubModule, @Description, @EntityType, @EntityId,
                     @OldValues, @NewValues, @IpAddress, @UserAgent, @Timestamp, @ActivityType,
                     @Severity, @IsSuccessful, @ErrorMessage)";

                await _dataAccess.ExecuteAsync(query, activity);
            }
            catch (Exception ex)
            {
                // تسجيل الخطأ في ملف منفصل لتجنب التكرار اللانهائي
                System.Diagnostics.Debug.WriteLine($"خطأ في تسجيل النشاط: {ex.Message}");
            }
        }

        /// <summary>
        /// الحصول على أنشطة مستخدم معين
        /// </summary>
        public async Task<List<UserActivity>> GetUserActivitiesAsync(int userId, DateTime? fromDate = null,
            DateTime? toDate = null, string module = null, int pageSize = 100, int pageNumber = 1)
        {
            try
            {
                var whereConditions = new List<string> { "UserId = @UserId" };
                var parameters = new Dictionary<string, object> { { "UserId", userId } };

                if (fromDate.HasValue)
                {
                    whereConditions.Add("Timestamp >= @FromDate");
                    parameters.Add("FromDate", fromDate.Value);
                }

                if (toDate.HasValue)
                {
                    whereConditions.Add("Timestamp <= @ToDate");
                    parameters.Add("ToDate", toDate.Value);
                }

                if (!string.IsNullOrEmpty(module))
                {
                    whereConditions.Add("Module = @Module");
                    parameters.Add("Module", module);
                }

                var offset = (pageNumber - 1) * pageSize;
                parameters.Add("Offset", offset);
                parameters.Add("PageSize", pageSize);

                var query = $@"
                    SELECT * FROM UserActivities
                    WHERE {string.Join(" AND ", whereConditions)}
                    ORDER BY Timestamp DESC
                    OFFSET @Offset ROWS FETCH NEXT @PageSize ROWS ONLY";

                return await _dataAccess.GetDataAsync<UserActivity>(query, parameters);
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في جلب أنشطة المستخدم: {ex.Message}");
            }
        }

        /// <summary>
        /// الحصول على جميع الأنشطة
        /// </summary>
        public async Task<List<UserActivity>> GetAllActivitiesAsync(DateTime? fromDate = null, DateTime? toDate = null,
            string module = null, string severity = null, int pageSize = 100, int pageNumber = 1)
        {
            try
            {
                var whereConditions = new List<string>();
                var parameters = new Dictionary<string, object>();

                if (fromDate.HasValue)
                {
                    whereConditions.Add("Timestamp >= @FromDate");
                    parameters.Add("FromDate", fromDate.Value);
                }

                if (toDate.HasValue)
                {
                    whereConditions.Add("Timestamp <= @ToDate");
                    parameters.Add("ToDate", toDate.Value);
                }

                if (!string.IsNullOrEmpty(module))
                {
                    whereConditions.Add("Module = @Module");
                    parameters.Add("Module", module);
                }

                if (!string.IsNullOrEmpty(severity))
                {
                    whereConditions.Add("Severity = @Severity");
                    parameters.Add("Severity", severity);
                }

                var offset = (pageNumber - 1) * pageSize;
                parameters.Add("Offset", offset);
                parameters.Add("PageSize", pageSize);

                var whereClause = whereConditions.Count > 0 ? $"WHERE {string.Join(" AND ", whereConditions)}" : "";

                var query = $@"
                    SELECT ua.*, u.Username, u.FullName
                    FROM UserActivities ua
                    INNER JOIN Users u ON ua.UserId = u.Id
                    {whereClause}
                    ORDER BY ua.Timestamp DESC
                    OFFSET @Offset ROWS FETCH NEXT @PageSize ROWS ONLY";

                return await _dataAccess.GetDataAsync<UserActivity>(query, parameters);
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في جلب الأنشطة: {ex.Message}");
            }
        }

        /// <summary>
        /// الحصول على إحصائيات الأنشطة
        /// </summary>
        public async Task<Dictionary<string, int>> GetActivityStatisticsAsync(DateTime? fromDate = null, DateTime? toDate = null)
        {
            try
            {
                var whereConditions = new List<string>();
                var parameters = new Dictionary<string, object>();

                if (fromDate.HasValue)
                {
                    whereConditions.Add("Timestamp >= @FromDate");
                    parameters.Add("FromDate", fromDate.Value);
                }

                if (toDate.HasValue)
                {
                    whereConditions.Add("Timestamp <= @ToDate");
                    parameters.Add("ToDate", toDate.Value);
                }

                var whereClause = whereConditions.Count > 0 ? $"WHERE {string.Join(" AND ", whereConditions)}" : "";

                var query = $@"
                    SELECT
                        ActivityType,
                        COUNT(*) as Count
                    FROM UserActivities
                    {whereClause}
                    GROUP BY ActivityType";

                var results = await _dataAccess.GetDataAsync<dynamic>(query, parameters);
                return results.ToDictionary(r => (string)r.ActivityType, r => (int)r.Count);
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في جلب إحصائيات الأنشطة: {ex.Message}");
            }
        }

        /// <summary>
        /// حذف الأنشطة القديمة
        /// </summary>
        public async Task<int> CleanupOldActivitiesAsync(int daysToKeep = 90)
        {
            try
            {
                var cutoffDate = DateTime.Now.AddDays(-daysToKeep);
                var query = "DELETE FROM UserActivities WHERE Timestamp < @CutoffDate";
                var parameters = new { CutoffDate = cutoffDate };

                return await _dataAccess.ExecuteAsync(query, parameters);
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تنظيف الأنشطة القديمة: {ex.Message}");
            }
        }

        /// <summary>
        /// تسجيل محاولة دخول
        /// </summary>
        public async Task LogLoginAttemptAsync(int? userId, string username, bool isSuccessful, string errorMessage = null)
        {
            var action = isSuccessful ? ActivityActions.USER_LOGIN : ActivityActions.FAILED_LOGIN_ATTEMPT;
            var description = isSuccessful ? $"تسجيل دخول ناجح للمستخدم {username}" : $"محاولة دخول فاشلة للمستخدم {username}";
            var severity = isSuccessful ? SeverityLevels.INFO : SeverityLevels.WARNING;

            await LogActivityAsync(userId ?? 0, action, ActivityModules.AUTHENTICATION, description,
                "User", userId, null, null, severity, isSuccessful, errorMessage);
        }

        /// <summary>
        /// تسجيل تسجيل خروج
        /// </summary>
        public async Task LogLogoutAsync(int userId, string username)
        {
            await LogActivityAsync(userId, ActivityActions.USER_LOGOUT, ActivityModules.AUTHENTICATION,
                $"تسجيل خروج للمستخدم {username}", "User", userId);
        }

        /// <summary>
        /// الحصول على عنوان IP الخاص بالعميل
        /// </summary>
        private string GetClientIpAddress()
        {
            try
            {
                // في تطبيق Windows Forms، يمكن الحصول على IP المحلي
                var hostName = System.Net.Dns.GetHostName();
                var addresses = System.Net.Dns.GetHostAddresses(hostName);
                var ipv4Address = addresses.FirstOrDefault(a => a.AddressFamily == System.Net.Sockets.AddressFamily.InterNetwork);
                return ipv4Address?.ToString() ?? "127.0.0.1";
            }
            catch
            {
                return "127.0.0.1";
            }
        }

        /// <summary>
        /// الحصول على معلومات المتصفح/التطبيق
        /// </summary>
        private string GetUserAgent()
        {
            try
            {
                var osVersion = Environment.OSVersion.ToString();
                var appVersion = System.Reflection.Assembly.GetExecutingAssembly().GetName().Version?.ToString();
                return $"SmartCreator Desktop App v{appVersion} on {osVersion}";
            }
            catch
            {
                return "SmartCreator Desktop App";
            }
        }

        /// <summary>
        /// تحديد نوع النشاط من الإجراء
        /// </summary>
        private string GetActivityTypeFromAction(string action)
        {
            if (action.Contains("دخول") || action.Contains("LOGIN"))
                return ActivityTypes.LOGIN;
            if (action.Contains("خروج") || action.Contains("LOGOUT"))
                return ActivityTypes.LOGOUT;
            if (action.Contains("إنشاء") || action.Contains("CREATE"))
                return ActivityTypes.CREATE;
            if (action.Contains("تعديل") || action.Contains("UPDATE"))
                return ActivityTypes.UPDATE;
            if (action.Contains("حذف") || action.Contains("DELETE"))
                return ActivityTypes.DELETE;
            if (action.Contains("عرض") || action.Contains("VIEW"))
                return ActivityTypes.VIEW;
            if (action.Contains("تصدير") || action.Contains("EXPORT"))
                return ActivityTypes.EXPORT;
            if (action.Contains("طباعة") || action.Contains("PRINT"))
                return ActivityTypes.PRINT;

            return ActivityTypes.VIEW; // افتراضي
        }
    }
}
