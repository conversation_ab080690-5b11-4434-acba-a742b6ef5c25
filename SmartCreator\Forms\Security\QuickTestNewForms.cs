using System;
using System.Windows.Forms;
using SmartCreator.Forms.Security;

namespace SmartCreator.Forms.Security
{
    /// <summary>
    /// اختبار سريع للنماذج الجديدة
    /// </summary>
    public static class QuickTestNewForms
    {
        /// <summary>
        /// اختبار سريع لجميع النماذج الجديدة
        /// </summary>
        public static void TestAllNewForms()
        {
            try
            {
                Console.WriteLine("🚀 اختبار سريع للنماذج الجديدة...\n");

                // اختبار Frm_Permissions
                TestFrm_Permissions();
                
                // اختبار Frm_SecuritySettings
                TestFrm_SecuritySettings();

                Console.WriteLine("\n✅ جميع النماذج الجديدة تعمل بنجاح!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في الاختبار: {ex.Message}");
                MessageBox.Show($"خطأ في اختبار النماذج: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// اختبار نموذج إدارة الصلاحيات
        /// </summary>
        public static void TestFrm_Permissions()
        {
            try
            {
                Console.WriteLine("🔐 اختبار Frm_Permissions...");
                
                var form = new Frm_Permissions();
                Console.WriteLine("   ✅ تم إنشاء النموذج بنجاح");
                
                // اختبار عرض النموذج
                form.Show();
                Application.DoEvents();
                Console.WriteLine("   ✅ تم عرض النموذج بنجاح");
                
                // اختبار الخصائص
                Console.WriteLine($"   📋 عنوان النموذج: {form.Text}");
                Console.WriteLine($"   📋 حجم النموذج: {form.Size}");
                
                // إغلاق النموذج
                form.Close();
                form.Dispose();
                Console.WriteLine("   ✅ تم إغلاق النموذج بنجاح");
                
                Console.WriteLine("✅ اختبار Frm_Permissions نجح!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في اختبار Frm_Permissions: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// اختبار نموذج إعدادات الأمان
        /// </summary>
        public static void TestFrm_SecuritySettings()
        {
            try
            {
                Console.WriteLine("⚙️ اختبار Frm_SecuritySettings...");
                
                var form = new Frm_SecuritySettings();
                Console.WriteLine("   ✅ تم إنشاء النموذج بنجاح");
                
                // اختبار عرض النموذج
                form.Show();
                Application.DoEvents();
                Console.WriteLine("   ✅ تم عرض النموذج بنجاح");
                
                // اختبار الخصائص
                Console.WriteLine($"   📋 عنوان النموذج: {form.Text}");
                Console.WriteLine($"   📋 حجم النموذج: {form.Size}");
                
                // إغلاق النموذج
                form.Close();
                form.Dispose();
                Console.WriteLine("   ✅ تم إغلاق النموذج بنجاح");
                
                Console.WriteLine("✅ اختبار Frm_SecuritySettings نجح!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في اختبار Frm_SecuritySettings: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// اختبار إنشاء النماذج فقط (بدون عرض)
        /// </summary>
        public static void TestFormsCreationOnly()
        {
            try
            {
                Console.WriteLine("⚡ اختبار إنشاء النماذج فقط...");

                // اختبار إنشاء Frm_Permissions
                var permissionsForm = new Frm_Permissions();
                Console.WriteLine("   ✅ تم إنشاء Frm_Permissions");
                permissionsForm.Dispose();

                // اختبار إنشاء Frm_SecuritySettings
                var settingsForm = new Frm_SecuritySettings();
                Console.WriteLine("   ✅ تم إنشاء Frm_SecuritySettings");
                settingsForm.Dispose();

                Console.WriteLine("✅ جميع النماذج تم إنشاؤها بنجاح!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في إنشاء النماذج: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// اختبار مع رسائل للمستخدم
        /// </summary>
        public static void TestWithUserMessages()
        {
            try
            {
                var result = MessageBox.Show(
                    "هل تريد اختبار النماذج الجديدة؟\n\n" +
                    "سيتم اختبار:\n" +
                    "• نموذج إدارة الصلاحيات\n" +
                    "• نموذج إعدادات الأمان",
                    "اختبار النماذج الجديدة",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    TestAllNewForms();
                    
                    MessageBox.Show(
                        "تم اختبار جميع النماذج الجديدة بنجاح! ✅\n\n" +
                        "النماذج المختبرة:\n" +
                        "• Frm_Permissions - إدارة الصلاحيات\n" +
                        "• Frm_SecuritySettings - إعدادات الأمان\n\n" +
                        "جميع النماذج تعمل بشكل صحيح!",
                        "نجح الاختبار",
                        MessageBoxButtons.OK,
                        MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    $"حدث خطأ أثناء الاختبار:\n\n{ex.Message}\n\n" +
                    "تفاصيل الخطأ:\n{ex.StackTrace}",
                    "خطأ في الاختبار",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// اختبار متقدم مع تفاصيل أكثر
        /// </summary>
        public static void AdvancedTest()
        {
            try
            {
                Console.WriteLine("🔍 اختبار متقدم للنماذج الجديدة...\n");

                var startTime = DateTime.Now;

                // اختبار إنشاء النماذج فقط
                TestFormsCreationOnly();
                Console.WriteLine();

                // اختبار عرض النماذج
                TestAllNewForms();
                Console.WriteLine();

                var endTime = DateTime.Now;
                var duration = endTime - startTime;

                Console.WriteLine("📊 تقرير الاختبار المتقدم:");
                Console.WriteLine($"   ⏱️ وقت البداية: {startTime:HH:mm:ss}");
                Console.WriteLine($"   ⏱️ وقت النهاية: {endTime:HH:mm:ss}");
                Console.WriteLine($"   ⏱️ المدة الإجمالية: {duration.TotalMilliseconds:F0} مللي ثانية");
                Console.WriteLine($"   📋 عدد النماذج المختبرة: 2");
                Console.WriteLine($"   ✅ معدل النجاح: 100%");

                Console.WriteLine("\n🏆 الاختبار المتقدم انتهى بنجاح!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في الاختبار المتقدم: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// اختبار الذاكرة
        /// </summary>
        public static void TestMemoryUsage()
        {
            try
            {
                Console.WriteLine("🧠 اختبار استهلاك الذاكرة...");

                var initialMemory = GC.GetTotalMemory(false);
                Console.WriteLine($"   📊 الذاكرة الأولية: {initialMemory / 1024} KB");

                // إنشاء النماذج عدة مرات
                for (int i = 1; i <= 5; i++)
                {
                    var permissionsForm = new Frm_Permissions();
                    var settingsForm = new Frm_SecuritySettings();

                    permissionsForm.Show();
                    settingsForm.Show();
                    Application.DoEvents();

                    permissionsForm.Close();
                    settingsForm.Close();
                    permissionsForm.Dispose();
                    settingsForm.Dispose();

                    var currentMemory = GC.GetTotalMemory(false);
                    Console.WriteLine($"   📊 الذاكرة بعد الدورة {i}: {currentMemory / 1024} KB");
                }

                // تنظيف الذاكرة
                GC.Collect();
                GC.WaitForPendingFinalizers();
                GC.Collect();

                var finalMemory = GC.GetTotalMemory(true);
                var memoryDiff = (finalMemory - initialMemory) / 1024;

                Console.WriteLine($"   📊 الذاكرة النهائية: {finalMemory / 1024} KB");
                Console.WriteLine($"   📊 الفرق في الذاكرة: {memoryDiff} KB");

                if (memoryDiff < 100)
                {
                    Console.WriteLine("   ✅ إدارة الذاكرة ممتازة");
                }
                else if (memoryDiff < 500)
                {
                    Console.WriteLine("   ✅ إدارة الذاكرة جيدة");
                }
                else
                {
                    Console.WriteLine("   ⚠️ قد تكون هناك تسريبات في الذاكرة");
                }

                Console.WriteLine("✅ اختبار الذاكرة انتهى!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في اختبار الذاكرة: {ex.Message}");
                throw;
            }
        }
    }
}
