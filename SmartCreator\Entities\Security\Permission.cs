using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace SmartCreator.Entities
{
    /// <summary>
    /// كيان الصلاحيات
    /// </summary>
    [Table("Permissions")]
    public class Permission
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(100)]
        public string Name { get; set; }

        [Required]
        [StringLength(100)]
        public string Code { get; set; }

        [StringLength(500)]
        public string Description { get; set; }

        [Required]
        [StringLength(50)]
        public string Module { get; set; }

        [Required]
        [StringLength(50)]
        public string Category { get; set; }

        public bool IsActive { get; set; } = true;

        public DateTime CreatedDate { get; set; } = DateTime.Now;

        // Navigation Properties
        public virtual ICollection<UserPermission> UserPermissions { get; set; } = new List<UserPermission>();
    }

    /// <summary>
    /// ربط المستخدمين بالصلاحيات
    /// </summary>
    [Table("UserPermissions")]
    public class UserPermission
    {
        [Key]
        public int Id { get; set; }

        [Required]
        public int UserId { get; set; }

        [Required]
        public int PermissionId { get; set; }

        public bool IsGranted { get; set; } = true;

        public DateTime GrantedDate { get; set; } = DateTime.Now;

        public int? GrantedBy { get; set; }

        [StringLength(500)]
        public string Notes { get; set; }

        // Navigation Properties
        [ForeignKey("UserId")]
        public virtual User User { get; set; }

        [ForeignKey("PermissionId")]
        public virtual Permission Permission { get; set; }
    }

    /// <summary>
    /// أنواع الصلاحيات المحددة مسبقاً
    /// </summary>
    public static class PermissionCodes
    {
        // صلاحيات النظام العامة
        public const string SYSTEM_ADMIN = "SYSTEM_ADMIN";
        public const string VIEW_DASHBOARD = "VIEW_DASHBOARD";
        public const string MANAGE_USERS = "MANAGE_USERS";
        public const string MANAGE_PERMISSIONS = "MANAGE_PERMISSIONS";
        public const string VIEW_AUDIT_LOGS = "VIEW_AUDIT_LOGS";
        public const string BACKUP_RESTORE = "BACKUP_RESTORE";

        // صلاحيات المحاسبة
        public const string ACCOUNTING_VIEW = "ACCOUNTING_VIEW";
        public const string ACCOUNTING_ADD = "ACCOUNTING_ADD";
        public const string ACCOUNTING_EDIT = "ACCOUNTING_EDIT";
        public const string ACCOUNTING_DELETE = "ACCOUNTING_DELETE";
        public const string ACCOUNTING_REPORTS = "ACCOUNTING_REPORTS";

        // صلاحيات الحسابات
        public const string ACCOUNTS_VIEW = "ACCOUNTS_VIEW";
        public const string ACCOUNTS_ADD = "ACCOUNTS_ADD";
        public const string ACCOUNTS_EDIT = "ACCOUNTS_EDIT";
        public const string ACCOUNTS_DELETE = "ACCOUNTS_DELETE";

        // صلاحيات القيود
        public const string ENTRIES_VIEW = "ENTRIES_VIEW";
        public const string ENTRIES_ADD = "ENTRIES_ADD";
        public const string ENTRIES_EDIT = "ENTRIES_EDIT";
        public const string ENTRIES_DELETE = "ENTRIES_DELETE";
        public const string ENTRIES_POST = "ENTRIES_POST";

        // صلاحيات التقارير
        public const string REPORTS_VIEW = "REPORTS_VIEW";
        public const string REPORTS_EXPORT = "REPORTS_EXPORT";
        public const string REPORTS_PRINT = "REPORTS_PRINT";

        // صلاحيات المخزون
        public const string INVENTORY_VIEW = "INVENTORY_VIEW";
        public const string INVENTORY_ADD = "INVENTORY_ADD";
        public const string INVENTORY_EDIT = "INVENTORY_EDIT";
        public const string INVENTORY_DELETE = "INVENTORY_DELETE";

        // صلاحيات المبيعات
        public const string SALES_VIEW = "SALES_VIEW";
        public const string SALES_ADD = "SALES_ADD";
        public const string SALES_EDIT = "SALES_EDIT";
        public const string SALES_DELETE = "SALES_DELETE";

        // صلاحيات المشتريات
        public const string PURCHASES_VIEW = "PURCHASES_VIEW";
        public const string PURCHASES_ADD = "PURCHASES_ADD";
        public const string PURCHASES_EDIT = "PURCHASES_EDIT";
        public const string PURCHASES_DELETE = "PURCHASES_DELETE";
    }

    /// <summary>
    /// فئات الصلاحيات
    /// </summary>
    public static class PermissionCategories
    {
        public const string SYSTEM = "النظام";
        public const string ACCOUNTING = "المحاسبة";
        public const string INVENTORY = "المخزون";
        public const string SALES = "المبيعات";
        public const string PURCHASES = "المشتريات";
        public const string REPORTS = "التقارير";
        public const string USERS = "المستخدمين";
    }

    /// <summary>
    /// وحدات النظام
    /// </summary>
    public static class SystemModules
    {
        public const string CORE = "النواة";
        public const string ACCOUNTING = "المحاسبة";
        public const string INVENTORY = "المخزون";
        public const string SALES = "المبيعات";
        public const string PURCHASES = "المشتريات";
        public const string REPORTS = "التقارير";
        public const string SECURITY = "الأمان";
    }
}
