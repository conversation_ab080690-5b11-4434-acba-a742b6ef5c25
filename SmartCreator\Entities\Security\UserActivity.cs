using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace SmartCreator.Entities
{
    /// <summary>
    /// كيان أنشطة المستخدمين
    /// </summary>
    [Table("UserActivities")]
    public class UserActivity
    {
        [Key]
        public int Id { get; set; }

        [Required]
        public int UserId { get; set; }

        [Required]
        [StringLength(100)]
        public string Action { get; set; }
        public string Username { get; set; }

        [Required]
        [StringLength(100)]
        public string Module { get; set; }

        [StringLength(100)]
        public string SubModule { get; set; }

        [StringLength(500)]
        public string Description { get; set; }

        [StringLength(50)]
        public string EntityType { get; set; }

        public int? EntityId { get; set; }

        public string OldValues { get; set; }

        public string NewValues { get; set; }

        [Required]
        [StringLength(45)]
        public string IpAddress { get; set; }

        [StringLength(500)]
        public string UserAgent { get; set; }

        [Required]
        public DateTime Timestamp { get; set; } = DateTime.Now;

        [Required]
        [StringLength(20)]
        public string ActivityType { get; set; }

        [StringLength(20)]
        public string Severity { get; set; } = "Info";

        public bool IsSuccessful { get; set; } = true;

        [StringLength(1000)]
        public string ErrorMessage { get; set; }

        // Navigation Properties
        [ForeignKey("UserId")]
        public virtual User User { get; set; }

        /// <summary>
        /// تحويل من AuditLog إلى UserActivity
        /// </summary>
        public static UserActivity FromAuditLog(AuditLog auditLog)
        {
            return new UserActivity
            {
                Id = auditLog.Id,
                Username = auditLog.Username ?? "غير محدد",
                Action = auditLog.ActionText,
                Module = auditLog.Module,
                Description = auditLog.Changes ?? auditLog.ErrorMessage ?? "لا توجد تفاصيل",
                IsSuccessful = auditLog.Success,
                Severity = auditLog.SeverityText,
                IpAddress = auditLog.IPAddress ?? "",
                Timestamp = auditLog.Timestamp
            };
        }

        /// <summary>
        /// تحويل إلى AuditLog
        /// </summary>
        public AuditLog ToAuditLog()
        {
            return new AuditLog
            {
                Username = this.Username,
                Action = this.Action,
                Module = this.Module,
                Changes = this.Description,
                Success = this.IsSuccessful,
                Severity = this.Severity switch
                {
                    "حرج" => AuditSeverity.Critical,
                    "خطأ" => AuditSeverity.Error,
                    "تحذير" => AuditSeverity.Warning,
                    "معلومات" => AuditSeverity.Information,
                    "تصحيح" => AuditSeverity.Debug,
                    _ => AuditSeverity.Information
                },
                IPAddress = this.IpAddress,
                Timestamp = this.Timestamp,
                EntityType = this.Module,
                ErrorMessage = this.IsSuccessful ? null : this.Description
            };
        }


    }

    /// <summary>
    /// أنواع الأنشطة
    /// </summary>
    public static class ActivityTypes
    {
        public const string LOGIN = "تسجيل دخول";
        public const string LOGOUT = "تسجيل خروج";
        public const string CREATE = "إنشاء";
        public const string UPDATE = "تعديل";
        public const string DELETE = "حذف";
        public const string VIEW = "عرض";
        public const string EXPORT = "تصدير";
        public const string IMPORT = "استيراد";
        public const string PRINT = "طباعة";
        public const string BACKUP = "نسخ احتياطي";
        public const string RESTORE = "استعادة";
        public const string PERMISSION_CHANGE = "تغيير صلاحية";
        public const string PASSWORD_CHANGE = "تغيير كلمة مرور";
        public const string FAILED_LOGIN = "فشل تسجيل دخول";
        public const string ACCOUNT_LOCKED = "قفل حساب";
        public const string ACCOUNT_UNLOCKED = "إلغاء قفل حساب";
    }

    /// <summary>
    /// مستويات الخطورة
    /// </summary>
    public static class SeverityLevels
    {
        public const string INFO = "Info";
        public const string WARNING = "Warning";
        public const string ERROR = "Error";
        public const string CRITICAL = "Critical";
    }

    /// <summary>
    /// وحدات النظام للأنشطة
    /// </summary>
    public static class ActivityModules
    {
        public const string AUTHENTICATION = "المصادقة";
        public const string USER_MANAGEMENT = "إدارة المستخدمين";
        public const string PERMISSIONS = "الصلاحيات";
        public const string ACCOUNTING = "المحاسبة";
        public const string ACCOUNTS = "الحسابات";
        public const string JOURNAL_ENTRIES = "القيود";
        public const string REPORTS = "التقارير";
        public const string INVENTORY = "المخزون";
        public const string SALES = "المبيعات";
        public const string PURCHASES = "المشتريات";
        public const string SYSTEM = "النظام";
    }

    /// <summary>
    /// الإجراءات المحددة
    /// </summary>
    public static class ActivityActions
    {
        // إجراءات المصادقة
        public const string USER_LOGIN = "تسجيل دخول المستخدم";
        public const string USER_LOGOUT = "تسجيل خروج المستخدم";
        public const string FAILED_LOGIN_ATTEMPT = "محاولة دخول فاشلة";
        public const string PASSWORD_CHANGED = "تغيير كلمة المرور";
        public const string ACCOUNT_LOCKED = "قفل الحساب";
        public const string ACCOUNT_UNLOCKED = "إلغاء قفل الحساب";

        // إجراءات إدارة المستخدمين
        public const string USER_CREATED = "إنشاء مستخدم";
        public const string USER_UPDATED = "تعديل مستخدم";
        public const string USER_DELETED = "حذف مستخدم";
        public const string USER_ACTIVATED = "تفعيل مستخدم";
        public const string USER_DEACTIVATED = "إلغاء تفعيل مستخدم";

        // إجراءات الصلاحيات
        public const string PERMISSION_GRANTED = "منح صلاحية";
        public const string PERMISSION_REVOKED = "سحب صلاحية";
        public const string PERMISSIONS_UPDATED = "تحديث الصلاحيات";

        // إجراءات المحاسبة
        public const string ACCOUNT_CREATED = "إنشاء حساب";
        public const string ACCOUNT_UPDATED = "تعديل حساب";
        public const string ACCOUNT_DELETED = "حذف حساب";
        public const string ENTRY_CREATED = "إنشاء قيد";
        public const string ENTRY_UPDATED = "تعديل قيد";
        public const string ENTRY_DELETED = "حذف قيد";
        public const string ENTRY_POSTED = "ترحيل قيد";

        // إجراءات التقارير
        public const string REPORT_VIEWED = "عرض تقرير";
        public const string REPORT_EXPORTED = "تصدير تقرير";
        public const string REPORT_PRINTED = "طباعة تقرير";

        // إجراءات النظام
        public const string SYSTEM_BACKUP = "نسخ احتياطي للنظام";
        public const string SYSTEM_RESTORE = "استعادة النظام";
        public const string DATABASE_BACKUP = "نسخ احتياطي لقاعدة البيانات";
        public const string SETTINGS_UPDATED = "تحديث الإعدادات";
    }
}
