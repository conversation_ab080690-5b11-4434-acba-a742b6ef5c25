using SmartCreator.Data;
using SmartCreator.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace SmartCreator.Services.Security
{
    /// <summary>
    /// خدمة إدارة الصلاحيات
    /// </summary>
    public class PermissionService
    {
        private readonly Smart_DataAccess _dataAccess;
        private readonly UserActivityService _activityService;

        public PermissionService(Smart_DataAccess dataAccess, UserActivityService activityService)
        {
            _dataAccess = dataAccess;
            _activityService = activityService;
        }

        /// <summary>
        /// الحصول على جميع الصلاحيات
        /// </summary>
        public async Task<List<Permission>> GetAllPermissionsAsync()
        {
            try
            {
                var query = "SELECT * FROM Permissions WHERE IsActive = 1 ORDER BY Module, Category, Name";
                return await _dataAccess.GetDataAsync<Permission>(query);
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في جلب الصلاحيات: {ex.Message}");
            }
        }

        /// <summary>
        /// الحصول على صلاحيات مستخدم معين
        /// </summary>
        public async Task<List<Permission>> GetUserPermissionsAsync(int userId)
        {
            try
            {
                var query = @"
                    SELECT p.* FROM Permissions p
                    INNER JOIN UserPermissions up ON p.Id = up.PermissionId
                    WHERE up.UserId = @UserId AND up.IsGranted = 1 AND p.IsActive = 1
                    ORDER BY p.Module, p.Category, p.Name";

                var parameters = new { UserId = userId };
                return await _dataAccess.GetDataAsync<Permission>(query, parameters);
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في جلب صلاحيات المستخدم: {ex.Message}");
            }
        }

        /// <summary>
        /// التحقق من وجود صلاحية لمستخدم معين
        /// </summary>
        public async Task<bool> HasPermissionAsync(int userId, string permissionCode)
        {
            try
            {
                var query = @"
                    SELECT COUNT(*) FROM UserPermissions up
                    INNER JOIN Permissions p ON up.PermissionId = p.Id
                    WHERE up.UserId = @UserId AND p.Code = @PermissionCode
                    AND up.IsGranted = 1 AND p.IsActive = 1";

                var parameters = new { UserId = userId, PermissionCode = permissionCode };
                var count = await _dataAccess.ExecuteScalarAsync<int>(query, parameters);
                return count > 0;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في التحقق من الصلاحية: {ex.Message}");
            }
        }

        /// <summary>
        /// منح صلاحية لمستخدم
        /// </summary>
        public async Task<bool> GrantPermissionAsync(int userId, int permissionId, int grantedBy, string notes = null)
        {
            try
            {
                // التحقق من وجود الصلاحية مسبقاً
                var existingQuery = "SELECT COUNT(*) FROM UserPermissions WHERE UserId = @UserId AND PermissionId = @PermissionId";
                var existingParams = new { UserId = userId, PermissionId = permissionId };
                var exists = await _dataAccess.ExecuteScalarAsync<int>(existingQuery, existingParams) > 0;

                if (exists)
                {
                    // تحديث الصلاحية الموجودة
                    var updateQuery = @"
                        UPDATE UserPermissions
                        SET IsGranted = 1, GrantedDate = @GrantedDate, GrantedBy = @GrantedBy, Notes = @Notes
                        WHERE UserId = @UserId AND PermissionId = @PermissionId";

                    var updateParams = new
                    {
                        UserId = userId,
                        PermissionId = permissionId,
                        GrantedDate = DateTime.Now,
                        GrantedBy = grantedBy,
                        Notes = notes
                    };

                    await _dataAccess.ExecuteAsync(updateQuery, updateParams);
                }
                else
                {
                    // إضافة صلاحية جديدة
                    var insertQuery = @"
                        INSERT INTO UserPermissions (UserId, PermissionId, IsGranted, GrantedDate, GrantedBy, Notes)
                        VALUES (@UserId, @PermissionId, 1, @GrantedDate, @GrantedBy, @Notes)";

                    var insertParams = new
                    {
                        UserId = userId,
                        PermissionId = permissionId,
                        GrantedDate = DateTime.Now,
                        GrantedBy = grantedBy,
                        Notes = notes
                    };

                    await _dataAccess.ExecuteAsync(insertQuery, insertParams);
                }

                // تسجيل النشاط
                await _activityService.LogActivityAsync(grantedBy, ActivityActions.PERMISSION_GRANTED,
                    ActivityModules.PERMISSIONS, $"منح صلاحية للمستخدم {userId}", "UserPermission", permissionId);

                return true;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في منح الصلاحية: {ex.Message}");
            }
        }

        /// <summary>
        /// سحب صلاحية من مستخدم
        /// </summary>
        public async Task<bool> RevokePermissionAsync(int userId, int permissionId, int revokedBy)
        {
            try
            {
                var query = @"
                    UPDATE UserPermissions
                    SET IsGranted = 0, GrantedDate = @RevokedDate, GrantedBy = @RevokedBy
                    WHERE UserId = @UserId AND PermissionId = @PermissionId";

                var parameters = new
                {
                    UserId = userId,
                    PermissionId = permissionId,
                    RevokedDate = DateTime.Now,
                    RevokedBy = revokedBy
                };

                var result = await _dataAccess.ExecuteAsync(query, parameters);

                // تسجيل النشاط
                await _activityService.LogActivityAsync(revokedBy, ActivityActions.PERMISSION_REVOKED,
                    ActivityModules.PERMISSIONS, $"سحب صلاحية من المستخدم {userId}", "UserPermission", permissionId);

                return result > 0;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في سحب الصلاحية: {ex.Message}");
            }
        }

        /// <summary>
        /// تحديث صلاحيات مستخدم بالكامل
        /// </summary>
        public async Task<bool> UpdateUserPermissionsAsync(int userId, List<int> permissionIds, int updatedBy)
        {
            try
            {
                // حذف جميع الصلاحيات الحالية
                var deleteQuery = "DELETE FROM UserPermissions WHERE UserId = @UserId";
                await _dataAccess.ExecuteAsync(deleteQuery, new { UserId = userId });

                // إضافة الصلاحيات الجديدة
                foreach (var permissionId in permissionIds)
                {
                    await GrantPermissionAsync(userId, permissionId, updatedBy);
                }

                // تسجيل النشاط
                await _activityService.LogActivityAsync(updatedBy, ActivityActions.PERMISSIONS_UPDATED,
                    ActivityModules.PERMISSIONS, $"تحديث صلاحيات المستخدم {userId}", "User", userId);

                return true;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تحديث صلاحيات المستخدم: {ex.Message}");
            }
        }

        /// <summary>
        /// الحصول على الصلاحيات حسب الوحدة
        /// </summary>
        public async Task<List<Permission>> GetPermissionsByModuleAsync(string module)
        {
            try
            {
                var query = "SELECT * FROM Permissions WHERE Module = @Module AND IsActive = 1 ORDER BY Category, Name";
                var parameters = new { Module = module };
                return await _dataAccess.GetDataAsync<Permission>(query, parameters);
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في جلب صلاحيات الوحدة: {ex.Message}");
            }
        }

        /// <summary>
        /// إنشاء الصلاحيات الافتراضية
        /// </summary>
        public async Task<bool> CreateDefaultPermissionsAsync()
        {
            try
            {
                var permissions = GetDefaultPermissions();

                foreach (var permission in permissions)
                {
                    // التحقق من وجود الصلاحية
                    var existsQuery = "SELECT COUNT(*) FROM Permissions WHERE Code = @Code";
                    var exists = await _dataAccess.ExecuteScalarAsync<int>(existsQuery, new { Code = permission.Code }) > 0;

                    if (!exists)
                    {
                        var insertQuery = @"
                            INSERT INTO Permissions (Name, Code, Description, Module, Category, IsActive, CreatedDate)
                            VALUES (@Name, @Code, @Description, @Module, @Category, @IsActive, @CreatedDate)";

                        await _dataAccess.ExecuteAsync(insertQuery, permission);
                    }
                }

                return true;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في إنشاء الصلاحيات الافتراضية: {ex.Message}");
            }
        }

        /// <summary>
        /// الحصول على قائمة الصلاحيات الافتراضية
        /// </summary>
        private List<Permission> GetDefaultPermissions()
        {
            return new List<Permission>
            {
                // صلاحيات النظام
                new Permission { Name = "مدير النظام", Code = PermissionCodes.SYSTEM_ADMIN, Description = "صلاحيات كاملة للنظام", Module = SystemModules.CORE, Category = PermissionCategories.SYSTEM },
                new Permission { Name = "عرض لوحة التحكم", Code = PermissionCodes.VIEW_DASHBOARD, Description = "عرض لوحة التحكم الرئيسية", Module = SystemModules.CORE, Category = PermissionCategories.SYSTEM },
                new Permission { Name = "إدارة المستخدمين", Code = PermissionCodes.MANAGE_USERS, Description = "إضافة وتعديل وحذف المستخدمين", Module = SystemModules.SECURITY, Category = PermissionCategories.USERS },
                new Permission { Name = "إدارة الصلاحيات", Code = PermissionCodes.MANAGE_PERMISSIONS, Description = "منح وسحب الصلاحيات", Module = SystemModules.SECURITY, Category = PermissionCategories.USERS },
                new Permission { Name = "عرض سجل الأحداث", Code = PermissionCodes.VIEW_AUDIT_LOGS, Description = "عرض سجل أنشطة المستخدمين", Module = SystemModules.SECURITY, Category = PermissionCategories.SYSTEM },

                // صلاحيات المحاسبة
                new Permission { Name = "عرض المحاسبة", Code = PermissionCodes.ACCOUNTING_VIEW, Description = "عرض بيانات المحاسبة", Module = SystemModules.ACCOUNTING, Category = PermissionCategories.ACCOUNTING },
                new Permission { Name = "إضافة بيانات محاسبية", Code = PermissionCodes.ACCOUNTING_ADD, Description = "إضافة بيانات محاسبية جديدة", Module = SystemModules.ACCOUNTING, Category = PermissionCategories.ACCOUNTING },
                new Permission { Name = "تعديل بيانات محاسبية", Code = PermissionCodes.ACCOUNTING_EDIT, Description = "تعديل البيانات المحاسبية", Module = SystemModules.ACCOUNTING, Category = PermissionCategories.ACCOUNTING },
                new Permission { Name = "حذف بيانات محاسبية", Code = PermissionCodes.ACCOUNTING_DELETE, Description = "حذف البيانات المحاسبية", Module = SystemModules.ACCOUNTING, Category = PermissionCategories.ACCOUNTING },

                // صلاحيات الحسابات
                new Permission { Name = "عرض الحسابات", Code = PermissionCodes.ACCOUNTS_VIEW, Description = "عرض دليل الحسابات", Module = SystemModules.ACCOUNTING, Category = PermissionCategories.ACCOUNTING },
                new Permission { Name = "إضافة حسابات", Code = PermissionCodes.ACCOUNTS_ADD, Description = "إضافة حسابات جديدة", Module = SystemModules.ACCOUNTING, Category = PermissionCategories.ACCOUNTING },
                new Permission { Name = "تعديل حسابات", Code = PermissionCodes.ACCOUNTS_EDIT, Description = "تعديل الحسابات الموجودة", Module = SystemModules.ACCOUNTING, Category = PermissionCategories.ACCOUNTING },
                new Permission { Name = "حذف حسابات", Code = PermissionCodes.ACCOUNTS_DELETE, Description = "حذف الحسابات", Module = SystemModules.ACCOUNTING, Category = PermissionCategories.ACCOUNTING },

                // صلاحيات التقارير
                new Permission { Name = "عرض التقارير", Code = PermissionCodes.REPORTS_VIEW, Description = "عرض التقارير المختلفة", Module = SystemModules.REPORTS, Category = PermissionCategories.REPORTS },
                new Permission { Name = "تصدير التقارير", Code = PermissionCodes.REPORTS_EXPORT, Description = "تصدير التقارير", Module = SystemModules.REPORTS, Category = PermissionCategories.REPORTS },
                new Permission { Name = "طباعة التقارير", Code = PermissionCodes.REPORTS_PRINT, Description = "طباعة التقارير", Module = SystemModules.REPORTS, Category = PermissionCategories.REPORTS }
            };
        }
    }
}
