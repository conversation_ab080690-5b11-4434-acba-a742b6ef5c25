using SmartCreator.Data;
using SmartCreator.Entities;
using SmartCreator.RJForms;
using SmartCreator.Services;
using SmartCreator.Services.Security;
using SmartCreator.Forms.Security;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace SmartCreator.Forms.Security
{
    /// <summary>
    /// واجهة عرض سجل الأحداث والمراقبة
    /// </summary>
    public partial class Frm_AuditLog : RJChildForm
    {
        private readonly UserManagementService _userService;
        private readonly ActivityService _activityService;
        private List<UserActivity> _activities;
        private List<User> _users;
        private int _currentPage = 1;
        private int _pageSize = 100;
        private int _totalRecords = 0;

        public Frm_AuditLog()
        {
            InitializeComponent();
            _userService = new UserManagementService();
            _activityService = new ActivityService();
            InitializeForm();
        }

        /// <summary>
        /// تهيئة النموذج
        /// </summary>
        private void InitializeForm()
        {
            // إعداد DataGridView
            SetupDataGridView();

            // إعداد المرشحات
            SetupFilters();

            // إعداد الأحداث
            SetupEvents();

            // تحميل البيانات
            LoadDataAsync();
        }

        /// <summary>
        /// إعداد DataGridView
        /// </summary>
        private void SetupDataGridView()
        {
            // إعداد التنسيق الأساسي
            ApplyBasicDataGridViewStyling(dgvActivities);

            // إعداد الأعمدة للأنشطة
            SetupActivitiesColumns();

            // إضافة تأثيرات التفاعل
            AddDataGridViewInteractionEffects(dgvActivities);

            // إعداد البحث
            txtSearch.TextChanged += (s, e) =>
            {
                ApplyDataGridViewSearchFilter(dgvActivities, txtSearch.Text);
            };
        }

        /// <summary>
        /// إعداد المرشحات
        /// </summary>
        private void SetupFilters()
        {
            // إعداد التواريخ
            dtpFromDate.Value = DateTime.Now.AddDays(-7);
            dtpToDate.Value = DateTime.Now;

            // إعداد قائمة الوحدات
            cmbModule.Items.Clear();
            cmbModule.Items.Add("جميع الوحدات");
            cmbModule.Items.AddRange(new string[]
            {
                ActivityModules.AUTHENTICATION,
                ActivityModules.USER_MANAGEMENT,
                ActivityModules.PERMISSIONS,
                ActivityModules.ACCOUNTING,
                ActivityModules.ACCOUNTS,
                ActivityModules.JOURNAL_ENTRIES,
                ActivityModules.REPORTS,
                ActivityModules.SYSTEM
            });
            cmbModule.SelectedIndex = 0;

            // إعداد قائمة مستويات الخطورة
            cmbSeverity.Items.Clear();
            cmbSeverity.Items.Add("جميع المستويات");
            cmbSeverity.Items.AddRange(new string[]
            {
                SeverityLevels.INFO,
                SeverityLevels.WARNING,
                SeverityLevels.ERROR,
                SeverityLevels.CRITICAL
            });
            cmbSeverity.SelectedIndex = 0;

            // إعداد حجم الصفحة
            cmbPageSize.Items.AddRange(new string[] { "50", "100", "200", "500" });
            cmbPageSize.SelectedIndex = 1; // 100
        }

        /// <summary>
        /// إعداد الأحداث
        /// </summary>
        private void SetupEvents()
        {
            btnFilter.Click += BtnFilter_Click;
            btnExport.Click += BtnExport_Click;
            btnRefresh.Click += BtnRefresh_Click;
            btnClearFilters.Click += BtnClearFilters_Click;

            btnFirstPage.Click += (s, e) => NavigateToPage(1);
            btnPreviousPage.Click += (s, e) => NavigateToPage(_currentPage - 1);
            btnNextPage.Click += (s, e) => NavigateToPage(_currentPage + 1);
            btnLastPage.Click += (s, e) => NavigateToPage(GetTotalPages());

            cmbPageSize.OnSelectedIndexChanged += CmbPageSize_OnSelectedIndexChanged;
            dgvActivities.CellDoubleClick += DgvActivities_CellDoubleClick;
        }

        /// <summary>
        /// تحميل البيانات
        /// </summary>
        private async void LoadDataAsync()
        {
            try
            {
                // تحميل المستخدمين للمرشح
                await LoadUsersAsync();

                // تحميل الأنشطة
                await LoadActivitiesAsync();

                // تحديث إحصائيات
                await UpdateStatisticsAsync();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تحميل المستخدمين
        /// </summary>
        private async Task LoadUsersAsync()
        {
            try
            {
                // محاكاة تحميل المستخدمين
                _users = new List<User>
                {
                    new User { Id = 1, Username = "admin", FullName = "المدير العام" },
                    new User { Id = 2, Username = "user1", FullName = "مستخدم 1" },
                    new User { Id = 3, Username = "user2", FullName = "مستخدم 2" }
                };

                cmbUser.Items.Clear();
                cmbUser.Items.Add("جميع المستخدمين");
                foreach (var user in _users)
                {
                    cmbUser.Items.Add($"{user.Username} - {user.FullName}");
                }
                cmbUser.SelectedIndex = 0;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل المستخدمين: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تحميل الأنشطة
        /// </summary>
        private async Task LoadActivitiesAsync()
        {
            try
            {
                lblStatus.Text = "جاري تحميل البيانات...";

                var fromDate = dtpFromDate.Value.Date;
                var toDate = dtpToDate.Value.Date.AddDays(1).AddSeconds(-1);
                var module = GetSelectedModule();
                var severity = GetSelectedSeverity();

                _activities = await _activityService.GetAllActivitiesAsync(
                    fromDate, toDate, module, severity, _pageSize, _currentPage);

                dgvActivities.DataSource = _activities;

                // تلوين الصفوف
                ColorizeRows();

                // تحديث معلومات الصفحات
                UpdatePaginationInfo();

                lblStatus.Text = $"تم تحميل {_activities.Count} سجل";
            }
            catch (Exception ex)
            {
                lblStatus.Text = "خطأ في تحميل البيانات";
                MessageBox.Show($"خطأ في تحميل الأنشطة: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تحديث الإحصائيات
        /// </summary>
        private async Task UpdateStatisticsAsync()
        {
            try
            {
                var fromDate = dtpFromDate.Value.Date;
                var toDate = dtpToDate.Value.Date.AddDays(1).AddSeconds(-1);

                var statistics = await _activityService.GetActivityStatisticsAsync(fromDate, toDate);

                // عرض الإحصائيات في labels أو chart
                DisplayStatistics(statistics);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحميل الإحصائيات: {ex.Message}");
            }
        }

        /// <summary>
        /// عرض الإحصائيات
        /// </summary>
        private void DisplayStatistics(Dictionary<string, int> statistics)
        {
            var totalActivities = statistics.Values.Sum();
            var loginCount = statistics.ContainsKey("Login") ? statistics["Login"] : 0;
            var errorCount = statistics.Where(s => s.Key.Contains("خطأ") || s.Key.Contains("فشل")).Sum(s => s.Value);

            lblTotalActivities.Text = $"إجمالي الأنشطة: {totalActivities}";
            lblLoginCount.Text = $"عمليات الدخول: {loginCount}";
            lblErrorCount.Text = $"الأخطاء: {errorCount}";
        }

        /// <summary>
        /// تلوين الصفوف حسب مستوى الخطورة
        /// </summary>
        private void ColorizeRows()
        {
            foreach (DataGridViewRow row in dgvActivities.Rows)
            {
                if (row.DataBoundItem is UserActivity activity)
                {
                    switch (activity.Severity)
                    {
                        case "خطأ":
                        case "Error":
                            row.DefaultCellStyle.BackColor = Color.FromArgb(255, 235, 235);
                            row.DefaultCellStyle.ForeColor = Color.DarkRed;
                            break;
                        case "تحذير":
                        case "Warning":
                            row.DefaultCellStyle.BackColor = Color.FromArgb(255, 248, 220);
                            row.DefaultCellStyle.ForeColor = Color.DarkOrange;
                            break;
                        case "حرج":
                        case "Critical":
                            row.DefaultCellStyle.BackColor = Color.FromArgb(220, 53, 69);
                            row.DefaultCellStyle.ForeColor = Color.White;
                            break;
                        default:
                            if (!activity.IsSuccessful)
                            {
                                row.DefaultCellStyle.BackColor = Color.FromArgb(248, 215, 218);
                                row.DefaultCellStyle.ForeColor = Color.DarkRed;
                            }
                            break;
                    }
                }
            }
        }

        /// <summary>
        /// الحصول على الوحدة المحددة
        /// </summary>
        private string GetSelectedModule()
        {
            return cmbModule.SelectedIndex <= 0 ? null : cmbModule.SelectedItem.ToString();
        }

        /// <summary>
        /// الحصول على مستوى الخطورة المحدد
        /// </summary>
        private string GetSelectedSeverity()
        {
            return cmbSeverity.SelectedIndex <= 0 ? null : cmbSeverity.SelectedItem.ToString();
        }

        /// <summary>
        /// الحصول على المستخدم المحدد
        /// </summary>
        private int? GetSelectedUserId()
        {
            if (cmbUser.SelectedIndex <= 0) return null;

            var selectedText = cmbUser.SelectedItem.ToString();
            var username = selectedText.Split('-')[0].Trim();
            var user = _users?.FirstOrDefault(u => u.Username == username);
            return user?.Id;
        }

        #region أحداث الأزرار

        /// <summary>
        /// تطبيق المرشحات
        /// </summary>
        private async void BtnFilter_Click(object sender, EventArgs e)
        {
            _currentPage = 1;
            await LoadActivitiesAsync();
            await UpdateStatisticsAsync();
        }

        /// <summary>
        /// تصدير البيانات
        /// </summary>
        private async void BtnExport_Click(object sender, EventArgs e)
        {
            try
            {
                var saveDialog = new SaveFileDialog
                {
                    Filter = "Excel Files|*.xlsx|CSV Files|*.csv",
                    FileName = $"سجل_الأحداث_{DateTime.Now:yyyyMMdd}.xlsx"
                };

                if (saveDialog.ShowDialog() == DialogResult.OK)
                {
                    // تصدير البيانات
                    if (saveDialog.FileName.EndsWith(".csv"))
                    {
                        ExportDataGridViewToCSV(dgvActivities, saveDialog.FileName);
                    }
                    else
                    {
                        // تصدير جميع البيانات (بدون تقسيم صفحات)
                        var allActivities = await _activityService.GetAllActivitiesAsync(
                            dtpFromDate.Value.Date, dtpToDate.Value.Date.AddDays(1).AddSeconds(-1),
                            GetSelectedModule(), GetSelectedSeverity(), 10000, 1);

                        ExportToFile(allActivities, saveDialog.FileName);
                    }

                    MessageBox.Show("تم تصدير البيانات بنجاح", "نجح",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تصدير البيانات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تحديث البيانات
        /// </summary>
        private async void BtnRefresh_Click(object sender, EventArgs e)
        {
            LoadDataAsync();
        }

        /// <summary>
        /// مسح المرشحات
        /// </summary>
        private async void BtnClearFilters_Click(object sender, EventArgs e)
        {
            dtpFromDate.Value = DateTime.Now.AddDays(-7);
            dtpToDate.Value = DateTime.Now;
            cmbModule.SelectedIndex = 0;
            cmbSeverity.SelectedIndex = 0;
            cmbUser.SelectedIndex = 0;
            txtSearch.Text = "";

            _currentPage = 1;
            await LoadActivitiesAsync();
        }

        /// <summary>
        /// تغيير حجم الصفحة
        /// </summary>
        private async void CmbPageSize_OnSelectedIndexChanged(object sender, EventArgs e)
        {
            if (int.TryParse(cmbPageSize.SelectedItem?.ToString(), out int pageSize))
            {
                _pageSize = pageSize;
                _currentPage = 1;
                await LoadActivitiesAsync();
            }
        }

        /// <summary>
        /// النقر المزدوج على سجل النشاط
        /// </summary>
        private void DgvActivities_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0 && dgvActivities.Rows[e.RowIndex].DataBoundItem is UserActivity activity)
            {
                ShowActivityDetails(activity);
            }
        }

        /// <summary>
        /// عرض تفاصيل النشاط
        /// </summary>
        private void ShowActivityDetails(UserActivity activity)
        {
            var details = $"تفاصيل النشاط:\n\n" +
                         $"التاريخ والوقت: {activity.Timestamp:yyyy-MM-dd HH:mm:ss}\n" +
                         $"المستخدم: {activity.Username}\n" +
                         $"الإجراء: {activity.Action}\n" +
                         $"الوحدة: {activity.Module}\n" +
                         $"المستوى: {activity.Severity}\n" +
                         $"نجح: {(activity.IsSuccessful ? "نعم" : "لا")}\n" +
                         $"عنوان IP: {activity.IpAddress}\n\n" +
                         $"الوصف:\n{activity.Description}";

            MessageBox.Show(details, "تفاصيل النشاط", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        /// <summary>
        /// التنقل إلى صفحة محددة
        /// </summary>
        private async void NavigateToPage(int pageNumber)
        {
            var totalPages = GetTotalPages();
            if (pageNumber >= 1 && pageNumber <= totalPages)
            {
                _currentPage = pageNumber;
                await LoadActivitiesAsync();
            }
        }

        /// <summary>
        /// الحصول على إجمالي عدد الصفحات
        /// </summary>
        private int GetTotalPages()
        {
            return _totalRecords > 0 ? (int)Math.Ceiling((double)_totalRecords / _pageSize) : 1;
        }

        /// <summary>
        /// تحديث معلومات الصفحات
        /// </summary>
        private void UpdatePaginationInfo()
        {
            var totalPages = GetTotalPages();
            lblPageInfo.Text = $"صفحة {_currentPage} من {totalPages}";

            btnFirstPage.Enabled = _currentPage > 1;
            btnPreviousPage.Enabled = _currentPage > 1;
            btnNextPage.Enabled = _currentPage < totalPages;
            btnLastPage.Enabled = _currentPage < totalPages;
        }

        /// <summary>
        /// تصدير البيانات إلى ملف
        /// </summary>
        private void ExportToFile(List<UserActivity> activities, string fileName)
        {
            try
            {
                var extension = System.IO.Path.GetExtension(fileName).ToLower();

                if (extension == ".csv")
                {
                    ExportToCsv(activities, fileName);
                }
                else if (extension == ".xlsx")
                {
                    ExportToExcel(activities, fileName);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تصدير البيانات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تصدير إلى CSV
        /// </summary>
        private void ExportToCsv(List<UserActivity> activities, string fileName)
        {
            var csv = new System.Text.StringBuilder();
            csv.AppendLine("التاريخ والوقت,المستخدم,الإجراء,الوحدة,المستوى,نجح,عنوان IP,الوصف");

            foreach (var activity in activities)
            {
                csv.AppendLine($"{activity.Timestamp:yyyy-MM-dd HH:mm:ss}," +
                              $"{activity.Username}," +
                              $"{activity.Action}," +
                              $"{activity.Module}," +
                              $"{activity.Severity}," +
                              $"{(activity.IsSuccessful ? "نعم" : "لا")}," +
                              $"{activity.IpAddress}," +
                              $"\"{activity.Description}\"");
            }

            System.IO.File.WriteAllText(fileName, csv.ToString(), System.Text.Encoding.UTF8);
        }

        /// <summary>
        /// تصدير إلى Excel
        /// </summary>
        private void ExportToExcel(List<UserActivity> activities, string fileName)
        {
            // يمكن استخدام مكتبة مثل EPPlus أو ClosedXML
            // هنا نستخدم CSV كبديل مؤقت
            ExportToCsv(activities, fileName.Replace(".xlsx", ".csv"));
        }

        #endregion

        #region - DataGridView Helper Methods

        /// <summary>
        /// تطبيق التنسيق الأساسي على RJDataGridView
        /// </summary>
        private void ApplyBasicDataGridViewStyling(RJControls.RJDataGridView dgv)
        {
            // الإعدادات الأساسية
            dgv.AllowUserToAddRows = false;
            dgv.AllowUserToDeleteRows = false;
            dgv.ReadOnly = true;
            dgv.MultiSelect = false;
            dgv.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dgv.RightToLeft = RightToLeft.Yes;

            // إعدادات المظهر
            dgv.BackgroundColor = Color.White;
            dgv.BorderStyle = BorderStyle.None;
            dgv.CellBorderStyle = DataGridViewCellBorderStyle.SingleHorizontal;
            dgv.ColumnHeadersBorderStyle = DataGridViewHeaderBorderStyle.None;
            dgv.EnableHeadersVisualStyles = false;
            dgv.GridColor = Color.FromArgb(229, 226, 244);
            dgv.RowHeadersVisible = false;

            // إعدادات الصفوف
            dgv.RowTemplate.Height = 35;
            dgv.AlternatingRowsDefaultCellStyle.BackColor = Color.FromArgb(248, 249, 250);

            // إعدادات رؤوس الأعمدة
            dgv.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(123, 104, 238);
            dgv.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            dgv.ColumnHeadersDefaultCellStyle.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            dgv.ColumnHeadersDefaultCellStyle.SelectionBackColor = Color.FromArgb(123, 104, 238);
            dgv.ColumnHeadersDefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dgv.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            dgv.ColumnHeadersHeight = 40;

            // إعدادات الخلايا
            dgv.DefaultCellStyle.BackColor = Color.White;
            dgv.DefaultCellStyle.ForeColor = Color.FromArgb(64, 64, 64);
            dgv.DefaultCellStyle.Font = new Font("Segoe UI", 9F);
            dgv.DefaultCellStyle.SelectionBackColor = Color.FromArgb(229, 226, 244);
            dgv.DefaultCellStyle.SelectionForeColor = Color.FromArgb(64, 64, 64);
            dgv.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dgv.DefaultCellStyle.Padding = new Padding(5);
        }

        /// <summary>
        /// إعداد أعمدة الأنشطة
        /// </summary>
        private void SetupActivitiesColumns()
        {
            dgvActivities.AutoGenerateColumns = false;
            dgvActivities.Columns.Clear();

            dgvActivities.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Id",
                HeaderText = "المعرف",
                DataPropertyName = "Id",
                Visible = false
            });

            dgvActivities.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Timestamp",
                HeaderText = "التوقيت",
                DataPropertyName = "Timestamp",
                Width = 140,
                MinimumWidth = 120,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "dd/MM/yyyy HH:mm:ss" }
            });

            dgvActivities.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Username",
                HeaderText = "المستخدم",
                DataPropertyName = "Username",
                Width = 120,
                MinimumWidth = 100
            });

            dgvActivities.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Module",
                HeaderText = "الوحدة",
                DataPropertyName = "Module",
                Width = 100,
                MinimumWidth = 80
            });

            dgvActivities.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Action",
                HeaderText = "الإجراء",
                DataPropertyName = "Action",
                Width = 150,
                MinimumWidth = 120
            });

            dgvActivities.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Severity",
                HeaderText = "المستوى",
                DataPropertyName = "Severity",
                Width = 80,
                MinimumWidth = 60
            });

            dgvActivities.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Description",
                HeaderText = "الوصف",
                DataPropertyName = "Description",
                Width = 200,
                MinimumWidth = 150,
                AutoSizeMode = DataGridViewAutoSizeColumnMode.Fill
            });

            dgvActivities.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "IpAddress",
                HeaderText = "عنوان IP",
                DataPropertyName = "IpAddress",
                Width = 120,
                MinimumWidth = 100
            });

            dgvActivities.Columns.Add(new DataGridViewCheckBoxColumn
            {
                Name = "IsSuccessful",
                HeaderText = "نجح",
                DataPropertyName = "IsSuccessful",
                Width = 60
            });

            // تخصيص عرض مستوى الخطورة
            dgvActivities.CellFormatting += DgvActivities_CellFormatting;
        }

        /// <summary>
        /// إضافة تأثيرات التفاعل للـ DataGridView
        /// </summary>
        private void AddDataGridViewInteractionEffects(RJControls.RJDataGridView dgv)
        {
            // تأثير عند مرور الماوس
            dgv.CellMouseEnter += (sender, e) =>
            {
                if (e.RowIndex >= 0)
                {
                    dgv.Rows[e.RowIndex].DefaultCellStyle.BackColor = Color.FromArgb(240, 245, 249);
                }
            };

            dgv.CellMouseLeave += (sender, e) =>
            {
                if (e.RowIndex >= 0)
                {
                    dgv.Rows[e.RowIndex].DefaultCellStyle.BackColor =
                        e.RowIndex % 2 == 0 ? Color.White : Color.FromArgb(248, 249, 250);
                }
            };
        }

        /// <summary>
        /// تطبيق فلتر البحث على DataGridView
        /// </summary>
        private void ApplyDataGridViewSearchFilter(RJControls.RJDataGridView dgv, string searchText)
        {
            if (string.IsNullOrWhiteSpace(searchText))
            {
                // إظهار جميع الصفوف
                foreach (DataGridViewRow row in dgv.Rows)
                {
                    row.Visible = true;
                }
                return;
            }

            // إخفاء الصفوف التي لا تحتوي على نص البحث
            foreach (DataGridViewRow row in dgv.Rows)
            {
                bool found = false;
                foreach (DataGridViewCell cell in row.Cells)
                {
                    if (cell.Value != null &&
                        cell.Value.ToString().IndexOf(searchText, StringComparison.OrdinalIgnoreCase) >= 0)
                    {
                        found = true;
                        break;
                    }
                }
                row.Visible = found;
            }
        }

        /// <summary>
        /// تصدير DataGridView إلى CSV
        /// </summary>
        private void ExportDataGridViewToCSV(RJControls.RJDataGridView dgv, string fileName)
        {
            try
            {
                using (var writer = new System.IO.StreamWriter(fileName, false, System.Text.Encoding.UTF8))
                {
                    // كتابة رؤوس الأعمدة
                    var headers = new string[dgv.Columns.Count];
                    for (int i = 0; i < dgv.Columns.Count; i++)
                    {
                        headers[i] = dgv.Columns[i].HeaderText;
                    }
                    writer.WriteLine(string.Join(",", headers));

                    // كتابة البيانات
                    foreach (DataGridViewRow row in dgv.Rows)
                    {
                        if (!row.IsNewRow && row.Visible)
                        {
                            var values = new string[dgv.Columns.Count];
                            for (int i = 0; i < dgv.Columns.Count; i++)
                            {
                                values[i] = row.Cells[i].Value?.ToString() ?? "";
                            }
                            writer.WriteLine(string.Join(",", values));
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تصدير البيانات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تنسيق خلايا الأنشطة
        /// </summary>
        private void DgvActivities_CellFormatting(object sender, DataGridViewCellFormattingEventArgs e)
        {
            if (e.ColumnIndex == dgvActivities.Columns["Severity"].Index && e.Value != null)
            {
                string severity = e.Value.ToString();
                switch (severity.ToLower())
                {
                    case "info":
                        e.Value = "معلومات";
                        e.CellStyle.ForeColor = Color.FromArgb(23, 162, 184);
                        break;
                    case "warning":
                        e.Value = "تحذير";
                        e.CellStyle.ForeColor = Color.FromArgb(255, 193, 7);
                        break;
                    case "error":
                        e.Value = "خطأ";
                        e.CellStyle.ForeColor = Color.FromArgb(220, 53, 69);
                        break;
                    case "success":
                        e.Value = "نجح";
                        e.CellStyle.ForeColor = Color.FromArgb(40, 167, 69);
                        break;
                    default:
                        e.Value = severity;
                        break;
                }
                e.FormattingApplied = true;
            }
        }

        #endregion
    }
}
