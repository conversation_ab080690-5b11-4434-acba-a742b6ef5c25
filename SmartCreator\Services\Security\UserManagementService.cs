using SmartCreator.Data;
using SmartCreator.Entities;
using SmartCreator.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;

namespace SmartCreator.Services
{
    /// <summary>
    /// خدمة إدارة المستخدمين والصلاحيات
    /// </summary>
    public class UserManagementService
    {
        private readonly Smart_DataAccess _dataAccess;

        public UserManagementService()
        {
            _dataAccess = new Smart_DataAccess();
        }

        #region إدارة المستخدمين

        /// <summary>
        /// الحصول على جميع المستخدمين
        /// </summary>
        public async Task<List<User>> GetAllUsersAsync()
        {
            try
            {
                var query = "SELECT * FROM Users ORDER BY FullName";
                return await _dataAccess.GetDataAsync<User>(query);
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في جلب المستخدمين: {ex.Message}");
            }
        }

        /// <summary>
        /// الحصول على مستخدم باسم المستخدم
        /// </summary>
        public async Task<User> GetUserByUsernameAsync(string username)
        {
            try
            {
                var query = "SELECT * FROM Users WHERE Username = @Username";
                var users = await _dataAccess.GetDataAsync<User>(query, new { Username = username });
                return users.FirstOrDefault();
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في جلب المستخدم: {ex.Message}");
            }
        }

        /// <summary>
        /// التحقق من صحة كلمة المرور
        /// </summary>
        public async Task<bool> ValidatePasswordAsync(int userId, string password)
        { 
            try 
            {
                var user = await GetUserByIdAsync(userId);
                if (user == null) return false;

                var hashedPassword = HashPassword(password, user.Salt);
                return hashedPassword == user.PasswordHash;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في التحقق من كلمة المرور: {ex.Message}");
            }
        }

        /// <summary>
        /// تغيير كلمة المرور
        /// </summary>
        public async Task<bool> ChangePasswordAsync(int userId, string newPassword, int changedBy)
        {
            try
            {
                var salt = GenerateSalt();
                var hashedPassword = HashPassword(newPassword, salt);

                var query = @"
                    UPDATE Users SET
                        PasswordHash = @PasswordHash,
                        Salt = @Salt,
                        LastPasswordChangeDate = @LastPasswordChangeDate,
                        ModifiedDate = @ModifiedDate,
                        ModifiedBy = @ModifiedBy
                    WHERE Id = @Id";

                var parameters = new
                {
                    Id = userId,
                    PasswordHash = hashedPassword,
                    Salt = salt,
                    LastPasswordChangeDate = DateTime.Now,
                    ModifiedDate = DateTime.Now,
                    ModifiedBy = changedBy
                };

                var result = await _dataAccess.ExecuteAsync(query, parameters);
                return result > 0;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تغيير كلمة المرور: {ex.Message}");
            }
        }

        /// <summary>
        /// الحصول على مستخدم بالمعرف
        /// </summary>
        public async Task<User> GetUserByIdAsync(int id)
        {
            try
            {
                var query = "SELECT * FROM Users WHERE Id = @Id";
                var users = await _dataAccess.GetDataAsync<User>(query, new { Id = id });
                return users.FirstOrDefault();
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في جلب المستخدم: {ex.Message}");
            }
        }

        /// <summary>
        /// إنشاء مستخدم جديد
        /// </summary>
        public async Task<User> CreateUserAsync(User user, string password, int createdBy)
        {
            try
            {
                // التحقق من عدم تكرار اسم المستخدم
                var existingUser = await GetUserByUsernameAsync(user.Username);
                if (existingUser != null)
                {
                    //throw new Exception("اسم المستخدم موجود مسبقاً");
                    RJMessageBox.Show("اسم المستخدم موجود مسبقاً");
                }

                // تشفير كلمة المرور
                var salt = GenerateSalt();
                var hashedPassword = HashPassword(password, salt);

                user.PasswordHash = hashedPassword;
                user.Salt = salt;
                user.LastPasswordChangeDate = DateTime.Now;
                user.CreatedDate = DateTime.Now;
                user.CreatedBy = createdBy;
                user.Rb = Global_Variable.Mk_resources.RB_SN;
                user.UpdateFullName();

                var query = @"
                    INSERT INTO Users
                    (Username, Email, PasswordHash, Salt, FirstName, LastName, FullName, Phone,
                     Department, Position, IsActive, CreatedDate, CreatedBy,Notes,Rb)
                    VALUES
                    (@Username, @Email, @PasswordHash, @Salt, @FirstName, @LastName, @FullName, @Phone,
                     @Department, @Position, @IsActive, @CreatedDate, @CreatedBy,@Notes,@Rb);
                    select last_insert_rowid();)";

                var userId = await _dataAccess.ExecuteScalarAsync<int>(query, user);
                user.Id = userId;

                return user;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في إنشاء المستخدم: {ex.Message}");
            }
        }

        /// <summary>
        /// تحديث مستخدم
        /// </summary>
        public async Task<bool> UpdateUserAsync(User user, int updatedBy)
        {
            try
            {
                user.UpdatedDate = DateTime.Now;
                user.ModifiedBy = updatedBy;
                //user.UpdateFullName();

                var query = @"
                    UPDATE Users SET
                        Email = @Email, FirstName = @FirstName, FullName = @FullName,
                        Phone = @Phone,  IsActive = @IsActive,
                        IsLocked = @IsLocked, FailedLoginAttempts = @FailedLoginAttempts,
                        ModifiedDate = @ModifiedDate, ModifiedBy = @ModifiedBy,
                        Notes = @Notes
                    WHERE Id = @Id";

                var result = await _dataAccess.ExecuteAsync(query, user);
                return result > 0;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تحديث المستخدم: {ex.Message}");
            }
        }

        /// <summary>
        /// حذف مستخدم
        /// </summary>
        public async Task<bool> DeleteUserAsync(int userId, int deletedBy)
        {
            try
            {
                // حذف صلاحيات المستخدم أولاً
                await _dataAccess.ExecuteAsync("DELETE FROM UserPermissions WHERE UserId = @UserId", new { UserId = userId });

                // حذف المستخدم
                var query = "DELETE FROM Users WHERE Id = @Id";
                var result = await _dataAccess.ExecuteAsync(query, new { Id = userId });

                return result > 0;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في حذف المستخدم: {ex.Message}");
            }
        }



        #endregion

        #region إدارة الصلاحيات

        /// <summary>
        /// الحصول على جميع الصلاحيات
        /// </summary>
        public async Task<List<Permission>> GetAllPermissionsAsync()
        {
            try
            {
                var query = "SELECT * FROM Permissions WHERE IsActive = 1 ORDER BY Module, Category, Name";
                return await _dataAccess.GetDataAsync<Permission>(query);
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في جلب الصلاحيات: {ex.Message}");
            }
        }

        /// <summary>
        /// الحصول على صلاحيات مستخدم معين
        /// </summary>
        public async Task<List<Permission>> GetUserPermissionsAsync(int userId)
        {
            try
            {
                var query = @"
                    SELECT p.* FROM Permissions p
                    INNER JOIN UserPermissions up ON p.Id = up.PermissionId
                    WHERE up.UserId = @UserId AND up.IsGranted = 1 AND p.IsActive = 1
                    ORDER BY p.Module, p.Category, p.Name";

                var parameters = new { UserId = userId };
                return await _dataAccess.GetDataAsync<Permission>(query, parameters);
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في جلب صلاحيات المستخدم: {ex.Message}");
            }
        }

        /// <summary>
        /// تحديث صلاحيات مستخدم بالكامل
        /// </summary>
        public async Task<bool> UpdateUserPermissionsAsync(int userId, List<int> permissionIds, int updatedBy)
        {
            try
            {
                // حذف جميع الصلاحيات الحالية
                var deleteQuery = "DELETE FROM UserPermissions WHERE UserId = @UserId";
                await _dataAccess.ExecuteAsync(deleteQuery, new { UserId = userId });

                // إضافة الصلاحيات الجديدة
                foreach (var permissionId in permissionIds)
                {
                    var insertQuery = @"
                        INSERT INTO UserPermissions (UserId, PermissionId, IsGranted, GrantedDate, GrantedBy)
                        VALUES (@UserId, @PermissionId, 1, @GrantedDate, @GrantedBy)";

                    var insertParams = new
                    {
                        UserId = userId,
                        PermissionId = permissionId,
                        GrantedDate = DateTime.Now,
                        GrantedBy = updatedBy
                    };

                    await _dataAccess.ExecuteAsync(insertQuery, insertParams);
                }

                return true;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تحديث صلاحيات المستخدم: {ex.Message}");
            }
        }

        #endregion

        #region مراقبة الأنشطة

        /// <summary>
        /// تسجيل نشاط مستخدم
        /// </summary>
        public async Task LogActivityAsync(int userId, string action, string module, string description = null,
            string entityType = null, int? entityId = null, string severity = "Info", bool isSuccessful = true)
        {
            try
            {
                var activity = new UserActivity
                {
                    UserId = userId,
                    Action = action,
                    Module = module,
                    Description = description,
                    EntityType = entityType,
                    EntityId = entityId,
                    IpAddress = GetClientIpAddress(),
                    UserAgent = GetUserAgent(),
                    Timestamp = DateTime.Now,
                    ActivityType = GetActivityTypeFromAction(action),
                    Severity = severity,
                    IsSuccessful = isSuccessful
                };

                var query = @"
                    INSERT INTO UserActivities
                    (UserId, Action, Module, Description, EntityType, EntityId,
                     IpAddress, UserAgent, Timestamp, ActivityType, Severity, IsSuccessful)
                    VALUES
                    (@UserId, @Action, @Module, @Description, @EntityType, @EntityId,
                     @IpAddress, @UserAgent, @Timestamp, @ActivityType, @Severity, @IsSuccessful)";

                await _dataAccess.ExecuteAsync(query, activity);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تسجيل النشاط: {ex.Message}");
            }
        }

        /// <summary>
        /// الحصول على أنشطة مستخدم معين
        /// </summary>
        public async Task<List<UserActivity>> GetUserActivitiesAsync(int userId, DateTime? fromDate = null,
            DateTime? toDate = null, string module = null, int pageSize = 100, int pageNumber = 1)
        {
            try
            {
                var whereConditions = new List<string> { "UserId = @UserId" };
                var parameters = new Dictionary<string, object> { { "UserId", userId } };

                if (fromDate.HasValue)
                {
                    whereConditions.Add("Timestamp >= @FromDate");
                    parameters.Add("FromDate", fromDate.Value);
                }

                if (toDate.HasValue)
                {
                    whereConditions.Add("Timestamp <= @ToDate");
                    parameters.Add("ToDate", toDate.Value);
                }

                if (!string.IsNullOrEmpty(module))
                {
                    whereConditions.Add("Module = @Module");
                    parameters.Add("Module", module);
                }

                var offset = (pageNumber - 1) * pageSize;
                parameters.Add("Offset", offset);
                parameters.Add("PageSize", pageSize);

                var query = $@"
                    SELECT * FROM UserActivities
                    WHERE {string.Join(" AND ", whereConditions)}
                    ORDER BY Timestamp DESC
                    OFFSET @Offset ROWS FETCH NEXT @PageSize ROWS ONLY";

                return await _dataAccess.GetDataAsync<UserActivity>(query, parameters);
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في جلب أنشطة المستخدم: {ex.Message}");
            }
        }

        #endregion

        #region وظائف مساعدة

        private string GenerateSalt()
        {
            var salt = "admin_salt_123";
            return salt;

            var saltBytes = new byte[32];
            using (var rng = RandomNumberGenerator.Create())
            {
                rng.GetBytes(saltBytes);
            }
            return Convert.ToBase64String(saltBytes);
        }

        private string HashPassword(string password, string salt)
        {
            var passwordHash = Convert.ToBase64String(System.Text.Encoding.UTF8.GetBytes(password + salt));
            return passwordHash;


            using (var sha256 = SHA256.Create())
            {
                var saltedPassword = password + salt;
                var hashedBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(saltedPassword));
                return Convert.ToBase64String(hashedBytes);
            }
        }

        private string GetClientIpAddress()
        {
            try
            {
                var hostName = System.Net.Dns.GetHostName();
                var addresses = System.Net.Dns.GetHostAddresses(hostName);
                var ipv4Address = addresses.FirstOrDefault(a => a.AddressFamily == System.Net.Sockets.AddressFamily.InterNetwork);
                return ipv4Address?.ToString() ?? "127.0.0.1";
            }
            catch
            {
                return "127.0.0.1";
            }
        }

        private string GetUserAgent()
        {
            try
            {
                var osVersion = Environment.OSVersion.ToString();
                var appVersion = System.Reflection.Assembly.GetExecutingAssembly().GetName().Version?.ToString();
                return $"SmartCreator Desktop App v{appVersion} on {osVersion}";
            }
            catch
            {
                return "SmartCreator Desktop App";
            }
        }

        private string GetActivityTypeFromAction(string action)
        {
            if (action.Contains("دخول") || action.Contains("LOGIN"))
                return ActivityTypes.LOGIN;
            if (action.Contains("خروج") || action.Contains("LOGOUT"))
                return ActivityTypes.LOGOUT;
            if (action.Contains("إنشاء") || action.Contains("CREATE"))
                return ActivityTypes.CREATE;
            if (action.Contains("تعديل") || action.Contains("UPDATE"))
                return ActivityTypes.UPDATE;
            if (action.Contains("حذف") || action.Contains("DELETE"))
                return ActivityTypes.DELETE;

            return ActivityTypes.VIEW;
        }

        #endregion
    }
}
