﻿using Newtonsoft.Json;
using SmartCreator.Data;
using SmartCreator.Entities.Accounting;
using SmartCreator.Models;
using SmartCreator.Properties;
using SmartCreator.RJControls;
using SmartCreator.RJForms;
using SmartCreator.Service;
using SmartCreator.Settings;
using SmartCreator.Utils;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using System.Windows.Forms;
using System.Xml;

//using Account = SmartCreator.Entities.Accounting.Account;


namespace SmartCreator.Forms.Accounting.Accounts
{
    public partial class Frm_Account_Manual : RJChildForm
    {
        #region Fields

        Smart_DataAccess smart_DataAccess = null;
        private readonly AccountService _accountService;
        private List<SmartCreator.Entities.Accounting.Account> _accounts = new List<Account>();
        private Account? _selectedAccount = null;
        private bool _isLoading = false;
        private System.Windows.Forms.Timer _searchTimer;
        private bool _isEditMode = false;
        private Account _editingAccount = null;

        // تحسينات جديدة
        private readonly Dictionary<string, string> _validationErrors = new Dictionary<string, string>();
        private bool _hasUnsavedChanges = false;
        private string _lastGeneratedCode = "";
        private readonly List<string> _operationHistory = new List<string>();

        #endregion
        public Frm_Account_Manual()
        {
            InitializeComponent();
            DatabaseHelper _dbHelper = new DatabaseHelper();
            _accountService = new AccountService(_dbHelper);
            SetupTreeView();
            SetupContextMenu();

            smart_DataAccess = new Smart_DataAccess();
            this.Text = "الدليل المحاسبي";

            //if (UIAppearance.DGV_RTL == false)
            //{
            //    dgv.RightToLeft = RightToLeft.No;
            //}
            utils utils1 = new utils();
            utils1.Control_textSize1(this);

            this.Text = "Accounts";
            if (UIAppearance.Language_ar)
            {
                this.Text = "الدليل المحاسبي";
                //System.Drawing.Font title_font = btnRefresh.Font = Program.GetCustomFont(Resources.DroidSansArabic, 11, FontStyle.Bold);
                //rjLabel1.Font = rjLabel5.Font = Program.GetCustomFont(Resources.DroidSansArabic, 9, FontStyle.Regular);
                //btnAddNew.Font = btnEdit.Font = btnDelete.Font = btnRefresh.Font = Program.GetCustomFont(Resources.DroidKufi_Bold, 10, FontStyle.Bold);

                //dgv.AllowUserToOrderColumns = true;
                //dgv.ColumnHeadersDefaultCellStyle.Font = Program.GetCustomFont(Resources.DroidSansArabic, 9f, FontStyle.Regular);
                //dgv.ColumnHeadersDefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
                //dgv.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
                utils utils = new utils();
                utils.Control_textSize1(this);
            }

            // تحميل البيانات عند إنشاء النموذج
            this.Load += Frm_Account_Manual_Load;
            this.FormClosing += Frm_Account_Manual_FormClosing;
        }

        private void SetupTreeView()
        {
            treeViewAccounts.ItemHeight = 24;
            treeViewAccounts.AfterSelect += TreeViewAccounts_AfterSelect;
            treeViewAccounts.NodeMouseDoubleClick += (s, e) => EditAccountAsync();

            // ربط أحداث البحث
            txtSearch.TextChanged += TxtSearch_TextChanged;
            txtSearch.KeyDown += TxtSearch_KeyDown;

            // إعداد لوحة معلومات الحساب
            SetupAccountDetailsPanel();
        }

        private void TxtSearch_TextChanged(object sender, EventArgs e)
        {
            // تأخير البحث لتحسين الأداء
            if (_searchTimer != null)
            {
                _searchTimer.Stop();
                _searchTimer.Dispose();
            }

            _searchTimer = new System.Windows.Forms.Timer();
            _searchTimer.Interval = 500; // نصف ثانية
            _searchTimer.Tick += (s, args) =>
            {
                _searchTimer.Stop();
                _searchTimer.Dispose();
                _searchTimer = null;
                SearchAccounts();
            };
            _searchTimer.Start();
        }

        private void TxtSearch_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                SearchAccounts();
                e.Handled = true;
            }
            else if (e.KeyCode == Keys.Escape)
            {
                ClearSearch();
                e.Handled = true;
            }
        }

        private void SetupContextMenu()
        {
            //var contextMenu = new ContextMenuStrip();
            //contextMenu.RightToLeft = RightToLeft.Yes;

            // إضافة حساب رئيسي
            var addMainItem = new ToolStripMenuItem("➕ إضافة حساب رئيسي");
            addMainItem.Click += (s, e) => AddMainAccountAsync();
            contextMenu.Items.Add(addMainItem);

            // إضافة حساب فرعي
            var addChildItem = new ToolStripMenuItem("🔗 إضافة حساب فرعي");
            addChildItem.Click += (s, e) => AddChildAccountAsync();
            contextMenu.Items.Add(addChildItem);

            contextMenu.Items.Add(new ToolStripSeparator());

            // تعديل الحساب
            var editItem = new ToolStripMenuItem("✏️ تعديل الحساب");
            editItem.Click +=  (s, e) =>  EditAccountAsync();
            contextMenu.Items.Add(editItem);

            // حذف الحساب
            var deleteItem = new ToolStripMenuItem("🗑️ حذف الحساب");
            deleteItem.Click +=  (s, e) =>  DeleteAccountAsync();
            contextMenu.Items.Add(deleteItem);

            contextMenu.Items.Add(new ToolStripSeparator());

            // كشف حساب
            var statementItem = new ToolStripMenuItem("📊 كشف حساب");
            statementItem.Click += (s, e) => ShowAccountStatement();
            contextMenu.Items.Add(statementItem);

            // حركات الحساب
            var movementsItem = new ToolStripMenuItem("📈 حركات الحساب");
            movementsItem.Click += (s, e) => ShowAccountMovements();
            contextMenu.Items.Add(movementsItem);

            contextMenu.Items.Add(new ToolStripSeparator());

            // نسخ معلومات الحساب
            var copyItem = new ToolStripMenuItem("📋 نسخ معلومات الحساب");
            copyItem.Click += (s, e) => CopyAccountInfo();
            contextMenu.Items.Add(copyItem);

            contextMenu.Items.Add(new ToolStripSeparator());

            // وظائف الشجرة
            var expandAllItem = new ToolStripMenuItem("🔽 توسيع جميع العقد");
            expandAllItem.Click += (s, e) => ExpandAllNodes();
            contextMenu.Items.Add(expandAllItem);

            var collapseAllItem = new ToolStripMenuItem("🔼 طي جميع العقد");
            collapseAllItem.Click += (s, e) => CollapseAllNodes();
            contextMenu.Items.Add(collapseAllItem);

            var refreshItem = new ToolStripMenuItem("🔄 تحديث الشجرة");
            refreshItem.Click += (s, e) => RefreshTree();
            contextMenu.Items.Add(refreshItem);

            var statsItem = new ToolStripMenuItem("📊 إحصائيات الشجرة");
            statsItem.Click += (s, e) => ShowTreeStatistics();
            contextMenu.Items.Add(statsItem);

            contextMenu.Items.Add(new ToolStripSeparator());

            var exportImageItem = new ToolStripMenuItem("🖼️ تصدير كصورة");
            exportImageItem.Click += (s, e) => ExportTreeAsImage();
            contextMenu.Items.Add(exportImageItem);

            var advancedSearchItem = new ToolStripMenuItem("🔍 البحث المتقدم");
            advancedSearchItem.Click += (s, e) => ShowAdvancedSearch();
            contextMenu.Items.Add(advancedSearchItem);

            // تحديث قائمة السياق عند فتحها
            contextMenu.Opening += (s, e) =>
            {
                var hasSelection = _selectedAccount != null;
                addChildItem.Enabled = hasSelection;
                editItem.Enabled = hasSelection;
                deleteItem.Enabled = hasSelection && !(_selectedAccount?.IsParent ?? false);
                statementItem.Enabled = hasSelection;
                movementsItem.Enabled = hasSelection;
                copyItem.Enabled = hasSelection;
            };

            treeViewAccounts.ContextMenuStrip = contextMenu;
        }

        private void SetupAccountDetailsPanel()
        {
            // إعداد ComboBox للأنواع
            cmbAccountType.Items.Clear();
            cmbAccountType.Items.Add("أصول");
            cmbAccountType.Items.Add("خصوم");
            cmbAccountType.Items.Add("حقوق ملكية");
            cmbAccountType.Items.Add("إيرادات");
            cmbAccountType.Items.Add("مصروفات");

            // إعداد ComboBox للطبيعة
            cmbAccountNature.Items.Clear();
            cmbAccountNature.Items.Add("مدين");
            cmbAccountNature.Items.Add("دائن");

            // ربط الأحداث
            btnNewAccount.Click += BtnNewAccount_Click;
            btnSaveAccount.Click += BtnSaveAccount_Click;
            btnCancelEdit.Click += BtnCancelEdit_Click;

            // ربط أحداث التحقق الفوري
            txtAccountCode.TextChanged += TxtAccountCode_TextChanged;
            txtAccountName.TextChanged += TxtAccountName_TextChanged;
            txtAccountBalance.TextChanged += TxtAccountBalance_TextChanged;
            cmbParentAccount.SelectedIndexChanged += CmbParentAccount_SelectedIndexChanged;

            // إخفاء أزرار التحرير في البداية
            SetEditMode(false);
            ClearAccountForm();
        }

        #region Event Handlers للتحقق الفوري

        private void TxtAccountCode_TextChanged(object sender, EventArgs e)
        {
            ValidateAccountCodeRealTime();
            _hasUnsavedChanges = true;
        }

        private void TxtAccountName_TextChanged(object sender, EventArgs e)
        {
            ValidateAccountNameRealTime();
            _hasUnsavedChanges = true;
        }

        private void TxtAccountBalance_TextChanged(object sender, EventArgs e)
        {
            ValidateAccountBalanceRealTime();
            _hasUnsavedChanges = true;
        }

        private void CmbParentAccount_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (_isEditMode && cmbParentAccount.SelectedIndex > 0)
            {
                SuggestChildAccountCode();
            }
            _hasUnsavedChanges = true;
        }

        private void ValidateAccountCodeRealTime()
        {
            var code = txtAccountCode.Text.Trim();

            if (string.IsNullOrWhiteSpace(code))
            {
                SetFieldValidationState(txtAccountCode, false, "كود الحساب مطلوب");
                return;
            }

            if (code.Length < 2)
            {
                SetFieldValidationState(txtAccountCode, false, "كود قصير جداً");
                return;
            }

            if (_accounts.Any(a => a.Code == code && a.Id != (_editingAccount?.Id ?? 0)))
            {
                SetFieldValidationState(txtAccountCode, false, "كود مكرر");
                return;
            }

            SetFieldValidationState(txtAccountCode, true, "كود صحيح");
        }

        private void ValidateAccountNameRealTime()
        {
            var name = txtAccountName.Text.Trim();

            if (string.IsNullOrWhiteSpace(name))
            {
                SetFieldValidationState(txtAccountName, false, "اسم الحساب مطلوب");
                return;
            }

            if (name.Length < 3)
            {
                SetFieldValidationState(txtAccountName, false, "اسم قصير جداً");
                return;
            }

            if (_accounts.Any(a => a.Name.Equals(name, StringComparison.OrdinalIgnoreCase) &&
                                  a.Id != (_editingAccount?.Id ?? 0)))
            {
                SetFieldValidationState(txtAccountName, false, "اسم مكرر");
                return;
            }

            SetFieldValidationState(txtAccountName, true, "اسم صحيح");
        }

        private void ValidateAccountBalanceRealTime()
        {
            var balanceText = txtAccountBalance.Text.Trim();

            if (string.IsNullOrWhiteSpace(balanceText))
            {
                SetFieldValidationState(txtAccountBalance, true, "");
                return;
            }

            if (!decimal.TryParse(balanceText, out decimal balance))
            {
                SetFieldValidationState(txtAccountBalance, false, "رقم غير صحيح");
                return;
            }

            if (balance < -********* || balance > *********)
            {
                SetFieldValidationState(txtAccountBalance, false, "خارج النطاق");
                return;
            }

            SetFieldValidationState(txtAccountBalance, true, $"الرصيد: {balance:N2}");
        }

        private void SetFieldValidationState(Control field, bool isValid, string message)
        {
            if (field is RJTextBox textBox)
            {
                textBox.BorderColor = isValid ? Color.Green : Color.Red;
                toolTip1.SetToolTip(textBox, message);
            }
        }

        #endregion

        #region اقتراح رقم الحساب الفرعي

        private void SuggestChildAccountCode()
        {
            try
            {
                if (cmbParentAccount.SelectedIndex <= 0)
                    return;

                var selectedText = cmbParentAccount.Items[cmbParentAccount.SelectedIndex].ToString();
                var parentCode = selectedText.Split('-')[0].Trim();
                var parentAccount = _accounts.FirstOrDefault(a => a.Code == parentCode);

                if (parentAccount == null)
                    return;

                var suggestedCode = GenerateChildAccountCode(parentAccount);

                if (!string.IsNullOrEmpty(suggestedCode) && txtAccountCode.Text.Trim() == "")
                {
                    txtAccountCode.Text = suggestedCode;
                    _lastGeneratedCode = suggestedCode;

                    // إظهار رسالة للمستخدم
                    Global_Variable.Update_Um_StatusBar_Prograss($"تم اقتراح الكود: {suggestedCode}", 0);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في اقتراح كود الحساب: {ex.Message}");
            }
        }

        private string GenerateChildAccountCode(Account parentAccount)
        {
            try
            {
                // الحصول على جميع الحسابات الفرعية للحساب الأب
                var childAccounts = _accounts
                    .Where(a => a.ParentId == parentAccount.Id)
                    .Select(a => a.Code)
                    .ToList();

                // تحديد نمط الترقيم بناءً على كود الحساب الأب
                var parentCode = parentAccount.Code;
                var suggestedCode = "";

                // نمط 1: إضافة أرقام متسلسلة (مثل: 1000 -> 1001, 1002, ...)
                if (System.Text.RegularExpressions.Regex.IsMatch(parentCode, @"^\d+$"))
                {
                    suggestedCode = GenerateNumericChildCode(parentCode, childAccounts);
                }
                // نمط 2: إضافة أرقام بعد نقطة (مثل: ACC -> ACC.01, ACC.02, ...)
                else if (System.Text.RegularExpressions.Regex.IsMatch(parentCode, @"^[A-Za-z\u0600-\u06FF]+$"))
                {
                    suggestedCode = GenerateAlphaNumericChildCode(parentCode, childAccounts);
                }
                // نمط 3: إضافة أرقام في النهاية (مثل: ACC100 -> ACC101, ACC102, ...)
                else
                {
                    suggestedCode = GenerateMixedChildCode(parentCode, childAccounts);
                }

                return suggestedCode;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في توليد كود الحساب الفرعي: {ex.Message}");
                return "";
            }
        }

        private string GenerateNumericChildCode(string parentCode, List<string> existingCodes)
        {
            if (!int.TryParse(parentCode, out int parentNum))
                return "";

            // البحث عن أكبر رقم فرعي
            int maxChild = 0;
            var pattern = $@"^{parentNum}(\d+)$";

            foreach (var code in existingCodes)
            {
                var match = System.Text.RegularExpressions.Regex.Match(code, pattern);
                if (match.Success && int.TryParse(match.Groups[1].Value, out int childNum))
                {
                    maxChild = Math.Max(maxChild, childNum);
                }
            }

            // اقتراح الرقم التالي
            var nextChild = maxChild + 1;
            var suggestedCode = $"{parentNum}{nextChild:D2}"; // مع إضافة صفر في البداية إذا لزم الأمر

            // التأكد من عدم وجود الكود
            while (existingCodes.Contains(suggestedCode) || _accounts.Any(a => a.Code == suggestedCode))
            {
                nextChild++;
                suggestedCode = $"{parentNum}{nextChild:D2}";
            }

            return suggestedCode;
        }

        private string GenerateAlphaNumericChildCode(string parentCode, List<string> existingCodes)
        {
            // البحث عن أكبر رقم فرعي
            int maxChild = 0;
            var pattern = $@"^{System.Text.RegularExpressions.Regex.Escape(parentCode)}\.?(\d+)$";

            foreach (var code in existingCodes)
            {
                var match = System.Text.RegularExpressions.Regex.Match(code, pattern);
                if (match.Success && int.TryParse(match.Groups[1].Value, out int childNum))
                {
                    maxChild = Math.Max(maxChild, childNum);
                }
            }

            // اقتراح الرقم التالي
            var nextChild = maxChild + 1;
            var suggestedCode = $"{parentCode}.{nextChild:D2}";

            // التأكد من عدم وجود الكود
            while (existingCodes.Contains(suggestedCode) || _accounts.Any(a => a.Code == suggestedCode))
            {
                nextChild++;
                suggestedCode = $"{parentCode}.{nextChild:D2}";
            }

            return suggestedCode;
        }

        private string GenerateMixedChildCode(string parentCode, List<string> existingCodes)
        {
            // استخراج الجزء الرقمي من نهاية الكود
            var match = System.Text.RegularExpressions.Regex.Match(parentCode, @"^(.+?)(\d*)$");
            if (!match.Success)
                return "";

            var prefix = match.Groups[1].Value;
            var numPart = match.Groups[2].Value;

            if (string.IsNullOrEmpty(numPart))
            {
                // إذا لم يكن هناك جزء رقمي، ابدأ من 01
                return GenerateAlphaNumericChildCode(parentCode, existingCodes);
            }

            if (!int.TryParse(numPart, out int parentNum))
                return "";

            // البحث عن أكبر رقم فرعي
            int maxChild = parentNum;
            var pattern = $@"^{System.Text.RegularExpressions.Regex.Escape(prefix)}(\d+)$";

            foreach (var code in existingCodes)
            {
                var codeMatch = System.Text.RegularExpressions.Regex.Match(code, pattern);
                if (codeMatch.Success && int.TryParse(codeMatch.Groups[1].Value, out int childNum))
                {
                    maxChild = Math.Max(maxChild, childNum);
                }
            }

            // اقتراح الرقم التالي
            var nextChild = maxChild + 1;
            var suggestedCode = $"{prefix}{nextChild}";

            // التأكد من عدم وجود الكود
            while (existingCodes.Contains(suggestedCode) || _accounts.Any(a => a.Code == suggestedCode))
            {
                nextChild++;
                suggestedCode = $"{prefix}{nextChild}";
            }

            return suggestedCode;
        }

        #endregion

        private void SetEditMode(bool isEditMode)
        {
            _isEditMode = isEditMode;

            // إظهار/إخفاء أزرار التحرير
            btnSaveAccount.Visible = isEditMode;
            btnCancelEdit.Visible = isEditMode;
            btnNewAccount.Visible = !isEditMode;

            // تمكين/تعطيل الحقول
            txtAccountCode.Enabled = isEditMode;
            txtAccountName.Enabled = isEditMode;
            txtAccountNameEnglish.Enabled = isEditMode;
            cmbAccountType.Enabled = isEditMode;
            cmbAccountNature.Enabled = isEditMode;
            cmbParentAccount.Enabled = isEditMode;
            txtAccountBalance.Enabled = isEditMode;
            txtAccountDescription.Enabled = isEditMode;
            chkIsActive.Enabled = isEditMode;
            chkIsParent.Enabled = isEditMode;

            // تغيير لون الخلفية لتوضيح وضع التعديل
            if (isEditMode)
            {
                //pnlAccountDetails.BackColor = Color.FromArgb(245, 250, 255); // لون أزرق فاتح
                lblAccountDetails.Text = _editingAccount == null ? "📝 إضافة حساب جديد" : "✏️ تعديل الحساب";
            }
            else
            {
                //pnlAccountDetails.BackColor = Color.FromArgb(250, 252, 253); // اللون الافتراضي
                lblAccountDetails.Text = "📋 معلومات الحساب";
            }
        }

        private void ClearAccountForm()
        {
            txtAccountCode.Text = "";
            txtAccountName.Text = "";
            txtAccountNameEnglish.Text = "";
            cmbAccountType.SelectedIndex = -1;
            cmbAccountNature.SelectedIndex = -1;
            cmbParentAccount.SelectedIndex = -1;
            txtAccountBalance.Text = "0";
            txtAccountDescription.Text = "";
            chkIsActive.Checked = true;
            chkIsParent.Checked = false;

            _editingAccount = null;
        }

        private void LoadAccountToForm(Account account)
        {
            if (account == null) return;

            txtAccountCode.Text = account.Code;
            txtAccountName.Text = account.Name;
            txtAccountNameEnglish.Text = account.NameEnglish ?? "";

            // تحديد نوع الحساب
            switch (account.Type)
            {
                case AccountType.Assets:
                    cmbAccountType.SelectedIndex = 0;
                    break;
                case AccountType.Liabilities:
                    cmbAccountType.SelectedIndex = 1;
                    break;
                case AccountType.Equity:
                    cmbAccountType.SelectedIndex = 2;
                    break;
                case AccountType.Revenue:
                    cmbAccountType.SelectedIndex = 3;
                    break;
                case AccountType.Expenses:
                    cmbAccountType.SelectedIndex = 4;
                    break;
            }

            // تحديد طبيعة الحساب
            cmbAccountNature.SelectedIndex = account.Nature == AccountNature.Debit ? 0 : 1;

            // تحديد الحساب الأب
            LoadParentAccounts();
            if (account.ParentId.HasValue)
            {
                var parentAccount = _accounts.FirstOrDefault(a => a.Id == account.ParentId.Value);
                if (parentAccount != null)
                {
                    for (int i = 0; i < cmbParentAccount.Items.Count; i++)
                    {
                        if (cmbParentAccount.Items[i].ToString().StartsWith(parentAccount.Code))
                        {
                            cmbParentAccount.SelectedIndex = i;
                            break;
                        }
                    }
                }
            }

            txtAccountBalance.Text = account.Balance.ToString("F2");
            txtAccountDescription.Text = account.Description ?? "";
            chkIsActive.Checked = account.IsActive;
            chkIsParent.Checked = account.IsParent;

            _editingAccount = account;
        }

        private void LoadParentAccounts()
        {
            cmbParentAccount.Items.Clear();
            cmbParentAccount.Items.Add("-- بدون حساب أب --");

            if (_accounts != null)
            {
                var parentAccounts = _accounts.Where(a => a.IsParent).OrderBy(a => a.Code);
                foreach (var account in parentAccounts)
                {
                    cmbParentAccount.Items.Add($"{account.Code} - {account.Name}");
                }
            }

            cmbParentAccount.SelectedIndex = 0;
        }

        private void BtnNewAccount_Click(object sender, EventArgs e)
        {
            ClearAccountForm();
            SetEditMode(true);
            txtAccountCode.Focus();
        }

        private void BtnSaveAccount_Click(object sender, EventArgs e)
        {
            try
            {
                if (!ValidateAccountForm())
                    return;

                // التحقق من التغييرات غير المحفوظة
                if (!ConfirmSaveChanges())
                    return;

                var account = CreateAccountFromForm();
                var savedAccountCode = account.Code;
                var operationType = _editingAccount == null ? "إضافة" : "تعديل";

                if (_editingAccount == null)
                {
                    // إضافة حساب جديد
                    var result = _accountService.AddAccountAsync(account);
                    LogOperation($"تم إضافة الحساب: {account.Code} - {account.Name}");
                    Global_Variable.Update_Um_StatusBar_Prograss("تم إضافة الحساب بنجاح", 0);
                }
                else
                {
                    // تعديل حساب موجود
                    account.Id = _editingAccount.Id;
                    var result = _accountService.UpdateAccountAsync(account);
                    LogOperation($"تم تعديل الحساب: {account.Code} - {account.Name}");
                    Global_Variable.Update_Um_StatusBar_Prograss("تم تعديل الحساب بنجاح", 0);
                }

                // إعادة تحميل البيانات
                LoadAccountsAsync();
                SetEditMode(false);
                ClearAccountForm();
                _hasUnsavedChanges = false;

                // تحديد الحساب المحفوظ في الشجرة
                Task.Delay(500).ContinueWith(_ =>
                {
                    this.BeginInvoke(new MethodInvoker(() =>
                    {
                        ExpandAndSelectAccount(savedAccountCode);
                    }));
                });

                // إظهار رسالة نجاح مع تفاصيل
                ShowSuccessMessage(operationType, account);
            }
            catch (Exception ex)
            {
                LogOperation($"خطأ في حفظ الحساب: {ex.Message}");
                RJMessageBox.Show($"خطأ في حفظ الحساب:\n{ex.Message}",
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private bool ConfirmSaveChanges()
        {
            if (!_hasUnsavedChanges)
                return true;

            var result = RJMessageBox.Show(
                "هناك تغييرات غير محفوظة. هل تريد المتابعة؟",
                "تأكيد الحفظ",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Question);

            return result == DialogResult.Yes;
        }

        private void ShowSuccessMessage(string operationType, Account account)
        {
            var message = $"تم {operationType} الحساب بنجاح!\n\n";
            message += $"الكود: {account.Code}\n";
            message += $"الاسم: {account.Name}\n";
            message += $"النوع: {GetAccountTypeDisplayName(account.Type)}\n";
            message += $"الرصيد: {account.Balance:N2}";

            RJMessageBox.Show(message, "نجح العملية",
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void LogOperation(string operation)
        {
            var logEntry = $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] {operation}";
            _operationHistory.Add(logEntry);

            // الاحتفاظ بآخر 100 عملية فقط
            if (_operationHistory.Count > 100)
            {
                _operationHistory.RemoveAt(0);
            }

            System.Diagnostics.Debug.WriteLine(logEntry);
        }

        #region قائمة العمليات والتتبع

        private void ShowOperationHistory()
        {
            if (_operationHistory.Count == 0)
            {
                RJMessageBox.Show("لا توجد عمليات مسجلة", "سجل العمليات",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            var historyForm = new Form
            {
                Text = "سجل العمليات",
                Size = new Size(600, 400),
                StartPosition = FormStartPosition.CenterParent,
                FormBorderStyle = FormBorderStyle.Sizable
            };

            var listBox = new ListBox
            {
                Dock = DockStyle.Fill,
                Font = new Font("Courier New", 9),
                HorizontalScrollbar = true
            };

            // إضافة العمليات بترتيب عكسي (الأحدث أولاً)
            for (int i = _operationHistory.Count - 1; i >= 0; i--)
            {
                listBox.Items.Add(_operationHistory[i]);
            }

            var panel = new Panel { Dock = DockStyle.Bottom, Height = 40 };
            var btnClose = new Button
            {
                Text = "إغلاق",
                Size = new Size(80, 30),
                Anchor = AnchorStyles.Right,
                Location = new Point(510, 5)
            };

            var btnClear = new Button
            {
                Text = "مسح السجل",
                Size = new Size(80, 30),
                Anchor = AnchorStyles.Right,
                Location = new Point(420, 5)
            };

            btnClose.Click += (s, e) => historyForm.Close();
            btnClear.Click += (s, e) =>
            {
                var result = RJMessageBox.Show("هل تريد مسح سجل العمليات؟", "تأكيد",
                    MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                if (result == DialogResult.Yes)
                {
                    _operationHistory.Clear();
                    listBox.Items.Clear();
                }
            };

            panel.Controls.AddRange(new Control[] { btnClose, btnClear });
            historyForm.Controls.AddRange(new Control[] { listBox, panel });
            historyForm.ShowDialog(this);
        }

        private void ShowAccountStatistics()
        {
            if (_accounts == null || _accounts.Count == 0)
            {
                RJMessageBox.Show("لا توجد حسابات لعرض الإحصائيات", "إحصائيات",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            var stats = GenerateAccountStatistics();
            var statsForm = CreateStatisticsForm(stats);
            statsForm.ShowDialog(this);
        }

        private string GenerateAccountStatistics()
        {
            var stats = new StringBuilder();
            stats.AppendLine("📊 إحصائيات الدليل المحاسبي");
            stats.AppendLine("=".PadRight(50, '='));
            stats.AppendLine();

            // إحصائيات عامة
            stats.AppendLine("📈 إحصائيات عامة:");
            stats.AppendLine($"   • إجمالي الحسابات: {_accounts.Count:N0}");
            stats.AppendLine($"   • الحسابات الرئيسية: {_accounts.Count(a => a.ParentId == null || a.ParentId == 0):N0}");
            stats.AppendLine($"   • الحسابات الفرعية: {_accounts.Count(a => a.ParentId != null && a.ParentId != 0):N0}");
            stats.AppendLine($"   • الحسابات النشطة: {_accounts.Count(a => a.IsActive):N0}");
            stats.AppendLine($"   • الحسابات غير النشطة: {_accounts.Count(a => !a.IsActive):N0}");
            stats.AppendLine();

            // إحصائيات حسب النوع
            stats.AppendLine("📋 إحصائيات حسب النوع:");
            foreach (AccountType type in Enum.GetValues(typeof(AccountType)))
            {
                var count = _accounts.Count(a => a.Type == type);
                var percentage = _accounts.Count > 0 ? (count * 100.0 / _accounts.Count) : 0;
                stats.AppendLine($"   • {GetAccountTypeDisplayName(type)}: {count:N0} ({percentage:F1}%)");
            }
            stats.AppendLine();

            // إحصائيات حسب الطبيعة
            stats.AppendLine("⚖️ إحصائيات حسب الطبيعة:");
            var debitCount = _accounts.Count(a => a.Nature == AccountNature.Debit);
            var creditCount = _accounts.Count(a => a.Nature == AccountNature.Credit);
            stats.AppendLine($"   • مدين: {debitCount:N0} ({(debitCount * 100.0 / _accounts.Count):F1}%)");
            stats.AppendLine($"   • دائن: {creditCount:N0} ({(creditCount * 100.0 / _accounts.Count):F1}%)");
            stats.AppendLine();

            // إحصائيات الأرصدة
            stats.AppendLine("💰 إحصائيات الأرصدة:");
            var totalDebitBalance = _accounts.Where(a => a.Balance > 0).Sum(a => a.Balance);
            var totalCreditBalance = _accounts.Where(a => a.Balance < 0).Sum(a => Math.Abs(a.Balance));
            var zeroBalanceCount = _accounts.Count(a => a.Balance == 0);

            stats.AppendLine($"   • إجمالي الأرصدة المدينة: {totalDebitBalance:N2}");
            stats.AppendLine($"   • إجمالي الأرصدة الدائنة: {totalCreditBalance:N2}");
            stats.AppendLine($"   • الحسابات برصيد صفر: {zeroBalanceCount:N0}");
            stats.AppendLine($"   • متوسط الرصيد: {_accounts.Average(a => Math.Abs(a.Balance)):N2}");
            stats.AppendLine();

            // إحصائيات التسلسل الهرمي
            stats.AppendLine("🌳 إحصائيات التسلسل الهرمي:");
            var maxLevel = _accounts.Any() ? _accounts.Max(a => a.Level) : 0;
            stats.AppendLine($"   • أقصى مستوى: {maxLevel}");

            for (int level = 1; level <= maxLevel; level++)
            {
                var levelCount = _accounts.Count(a => a.Level == level);
                stats.AppendLine($"   • المستوى {level}: {levelCount:N0} حساب");
            }

            return stats.ToString();
        }

        private Form CreateStatisticsForm(string statistics)
        {
            var form = new Form
            {
                Text = "إحصائيات الدليل المحاسبي",
                Size = new Size(600, 500),
                StartPosition = FormStartPosition.CenterParent,
                FormBorderStyle = FormBorderStyle.Sizable
            };

            var textBox = new TextBox
            {
                Multiline = true,
                ScrollBars = ScrollBars.Both,
                Dock = DockStyle.Fill,
                Text = statistics,
                ReadOnly = true,
                Font = new Font("Courier New", 10),
                BackColor = Color.White
            };

            var panel = new Panel { Dock = DockStyle.Bottom, Height = 40 };
            var btnClose = new Button
            {
                Text = "إغلاق",
                Size = new Size(80, 30),
                Anchor = AnchorStyles.Right,
                Location = new Point(510, 5)
            };

            var btnExport = new Button
            {
                Text = "تصدير",
                Size = new Size(80, 30),
                Anchor = AnchorStyles.Right,
                Location = new Point(420, 5)
            };

            btnClose.Click += (s, e) => form.Close();
            btnExport.Click += (s, e) => ExportStatistics(statistics);

            panel.Controls.AddRange(new Control[] { btnClose, btnExport });
            form.Controls.AddRange(new Control[] { textBox, panel });

            return form;
        }

        private void ExportStatistics(string statistics)
        {
            try
            {
                var saveDialog = new SaveFileDialog
                {
                    Filter = "Text Files (*.txt)|*.txt|All Files (*.*)|*.*",
                    Title = "تصدير الإحصائيات",
                    FileName = $"AccountStatistics_{DateTime.Now:yyyyMMdd_HHmmss}.txt"
                };

                if (saveDialog.ShowDialog() == DialogResult.OK)
                {
                    File.WriteAllText(saveDialog.FileName, statistics, Encoding.UTF8);
                    RJMessageBox.Show($"تم تصدير الإحصائيات بنجاح:\n{saveDialog.FileName}",
                        "نجح التصدير", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                RJMessageBox.Show($"خطأ في تصدير الإحصائيات:\n{ex.Message}",
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        private void BtnCancelEdit_Click(object sender, EventArgs e)
        {
            SetEditMode(false);
            ClearAccountForm();

            // إذا كان هناك حساب محدد، عرض تفاصيله
            if (_selectedAccount != null)
            {
                LoadAccountToForm(_selectedAccount);
            }
        }

        private bool ValidateAccountForm()
        {
            _validationErrors.Clear();
            bool isValid = true;

            // التحقق من كود الحساب
            if (!ValidateAccountCode())
                isValid = false;

            // التحقق من اسم الحساب
            if (!ValidateAccountName())
                isValid = false;

            // التحقق من نوع الحساب
            if (!ValidateAccountType())
                isValid = false;

            // التحقق من طبيعة الحساب
            if (!ValidateAccountNature())
                isValid = false;

            // التحقق من الرصيد
            if (!ValidateAccountBalance())
                isValid = false;

            // التحقق من الحساب الأب
            if (!ValidateParentAccount())
                isValid = false;

            // التحقق من عدم تكرار الاسم
            if (!ValidateAccountNameUniqueness())
                isValid = false;

            // عرض الأخطاء إذا وجدت
            if (!isValid)
            {
                ShowValidationErrors();
            }

            return isValid;
        }

        private bool ValidateAccountCode()
        {
            var code = txtAccountCode.Text.Trim();

            if (string.IsNullOrWhiteSpace(code))
            {
                _validationErrors.Add("كود الحساب", "يرجى إدخال كود الحساب");
                return false;
            }

            // التحقق من طول الكود
            if (code.Length < 2 || code.Length > 20)
            {
                _validationErrors.Add("كود الحساب", "يجب أن يكون كود الحساب بين 2 و 20 حرف");
                return false;
            }

            // التحقق من صحة الأحرف (أرقام وحروف فقط)
            if (!System.Text.RegularExpressions.Regex.IsMatch(code, @"^[a-zA-Z0-9\u0600-\u06FF]+$"))
            {
                _validationErrors.Add("كود الحساب", "يجب أن يحتوي كود الحساب على أرقام وحروف فقط");
                return false;
            }

            // التحقق من عدم تكرار كود الحساب
            if (_accounts.Any(a => a.Code == code && a.Id != (_editingAccount?.Id ?? 0)))
            {
                _validationErrors.Add("كود الحساب", "كود الحساب موجود مسبقاً، يرجى اختيار كود آخر");
                return false;
            }

            return true;
        }

        private bool ValidateAccountName()
        {
            var name = txtAccountName.Text.Trim();

            if (string.IsNullOrWhiteSpace(name))
            {
                _validationErrors.Add("اسم الحساب", "يرجى إدخال اسم الحساب");
                return false;
            }

            if (name.Length < 3 || name.Length > 100)
            {
                _validationErrors.Add("اسم الحساب", "يجب أن يكون اسم الحساب بين 3 و 100 حرف");
                return false;
            }

            return true;
        }

        private bool ValidateAccountNameUniqueness()
        {
            var name = txtAccountName.Text.Trim();

            if (_accounts.Any(a => a.Name.Equals(name, StringComparison.OrdinalIgnoreCase) &&
                                  a.Id != (_editingAccount?.Id ?? 0)))
            {
                _validationErrors.Add("اسم الحساب", "اسم الحساب موجود مسبقاً، يرجى اختيار اسم آخر");
                return false;
            }

            return true;
        }

        private bool ValidateAccountType()
        {
            if (cmbAccountType.SelectedIndex == -1)
            {
                _validationErrors.Add("نوع الحساب", "يرجى اختيار نوع الحساب");
                return false;
            }

            return true;
        }

        private bool ValidateAccountNature()
        {
            if (cmbAccountNature.SelectedIndex == -1)
            {
                _validationErrors.Add("طبيعة الحساب", "يرجى اختيار طبيعة الحساب");
                return false;
            }

            return true;
        }

        private bool ValidateAccountBalance()
        {
            var balanceText = txtAccountBalance.Text.Trim();

            if (string.IsNullOrWhiteSpace(balanceText))
            {
                txtAccountBalance.Text = "0";
                return true;
            }

            if (!decimal.TryParse(balanceText, out decimal balance))
            {
                _validationErrors.Add("الرصيد", "يرجى إدخال رصيد صحيح");
                return false;
            }

            if (balance < -********* || balance > *********)
            {
                _validationErrors.Add("الرصيد", "الرصيد خارج النطاق المسموح");
                return false;
            }

            return true;
        }

        private bool ValidateParentAccount()
        {
            // إذا كان حساب فرعي، يجب أن يكون له حساب أب
            if (cmbParentAccount.SelectedIndex > 0)
            {
                var selectedText = cmbParentAccount.Items[cmbParentAccount.SelectedIndex].ToString();
                var parentCode = selectedText.Split('-')[0].Trim();
                var parentAccount = _accounts.FirstOrDefault(a => a.Code == parentCode);

                if (parentAccount == null)
                {
                    _validationErrors.Add("الحساب الأب", "الحساب الأب المحدد غير موجود");
                    return false;
                }

                // التحقق من عدم إنشاء دورة (الحساب لا يمكن أن يكون أب لنفسه)
                if (_editingAccount != null && parentAccount.Id == _editingAccount.Id)
                {
                    _validationErrors.Add("الحساب الأب", "لا يمكن للحساب أن يكون أب لنفسه");
                    return false;
                }

                // التحقق من عدم إنشاء دورة في التسلسل الهرمي
                if (_editingAccount != null && IsCircularReference(parentAccount.Id, _editingAccount.Id))
                {
                    _validationErrors.Add("الحساب الأب", "هذا الاختيار سينشئ دورة في التسلسل الهرمي");
                    return false;
                }
            }

            return true;
        }

        private bool IsCircularReference(int parentId, int accountId)
        {
            var parent = _accounts.FirstOrDefault(a => a.Id == parentId);
            while (parent != null)
            {
                if (parent.Id == accountId)
                    return true;

                if (parent.ParentId.HasValue)
                    parent = _accounts.FirstOrDefault(a => a.Id == parent.ParentId.Value);
                else
                    break;
            }

            return false;
        }

        private void ShowValidationErrors()
        {
            var errorMessage = "يرجى تصحيح الأخطاء التالية:\n\n";
            foreach (var error in _validationErrors)
            {
                errorMessage += $"• {error.Key}: {error.Value}\n";
            }

            RJMessageBox.Show(errorMessage, "تحقق من البيانات",
                MessageBoxButtons.OK, MessageBoxIcon.Warning);

            // التركيز على أول حقل به خطأ
            FocusFirstErrorField();
        }

        private void FocusFirstErrorField()
        {
            if (_validationErrors.ContainsKey("كود الحساب"))
                txtAccountCode.Focus();
            else if (_validationErrors.ContainsKey("اسم الحساب"))
                txtAccountName.Focus();
            else if (_validationErrors.ContainsKey("نوع الحساب"))
                cmbAccountType.Focus();
            else if (_validationErrors.ContainsKey("طبيعة الحساب"))
                cmbAccountNature.Focus();
            else if (_validationErrors.ContainsKey("الرصيد"))
                txtAccountBalance.Focus();
            else if (_validationErrors.ContainsKey("الحساب الأب"))
                cmbParentAccount.Focus();
        }

        private Account CreateAccountFromForm()
        {
            var account = new Account
            {
                Code = txtAccountCode.Text.Trim(),
                Name = txtAccountName.Text.Trim(),
                NameEnglish = txtAccountNameEnglish.Text.Trim(),
                IsActive = chkIsActive.Checked,
                IsParent = chkIsParent.Checked,
                Description = txtAccountDescription.Text.Trim()
            };

            // تحديد نوع الحساب
            switch (cmbAccountType.SelectedIndex)
            {
                case 0:
                    account.Type = AccountType.Assets;
                    break;
                case 1:
                    account.Type = AccountType.Liabilities;
                    break;
                case 2:
                    account.Type = AccountType.Equity;
                    break;
                case 3:
                    account.Type = AccountType.Revenue;
                    break;
                case 4:
                    account.Type = AccountType.Expenses;
                    break;
            }

            // تحديد طبيعة الحساب
            account.Nature = cmbAccountNature.SelectedIndex == 0 ? AccountNature.Debit : AccountNature.Credit;

            // تحديد الحساب الأب
            if (cmbParentAccount.SelectedIndex > 0)
            {
                var selectedText = cmbParentAccount.Items[cmbParentAccount.SelectedIndex].ToString();
                var parentCode = selectedText.Split('-')[0].Trim();
                var parentAccount = _accounts.FirstOrDefault(a => a.Code == parentCode);
                if (parentAccount != null)
                {
                    account.ParentId = parentAccount.Id;
                    account.Level = parentAccount.Level + 1;
                    account.IsParent = false; // الحسابات الفرعية ليست حسابات أب افتراضياً
                }
            }
            else
            {
                account.ParentId = null;
                account.Level = 1;
                // الحسابات الرئيسية يمكن أن تكون حسابات أب
            }

            // تحديد الرصيد
            if (decimal.TryParse(txtAccountBalance.Text, out decimal balance))
            {
                account.Balance = balance;
            }

            return account;
        }

        private void DisplayAccountInfo(Account account)
        {
            if (account == null)
            {
                lblAccountInfo.Text = "اختر حساباً من الشجرة لعرض تفاصيله";
                return;
            }

            if (!_isEditMode)
            {
                LoadAccountToForm(account);
            }

            // عرض معلومات إضافية في التسمية
            var info = new StringBuilder();
            info.AppendLine($"📋 {account.Name}");
            info.AppendLine($"🔢 الكود: {account.Code}");
            info.AppendLine($"📊 النوع: {GetAccountTypeDisplayName(account.Type)}");
            info.AppendLine($"⚖️ الطبيعة: {(account.Nature == AccountNature.Debit ? "مدين" : "دائن")}");
            info.AppendLine($"💰 الرصيد: {account.Balance:N2}");
            info.AppendLine($"📈 المستوى: {account.Level}");
            info.AppendLine($"✅ الحالة: {(account.IsActive ? "نشط" : "غير نشط")}");

            if (account.ParentId.HasValue)
            {
                var parent = _accounts.FirstOrDefault(a => a.Id == account.ParentId.Value);
                if (parent != null)
                {
                    info.AppendLine($"👆 الحساب الأب: {parent.Name}");
                }
            }

            var childrenCount = _accounts.Count(a => a.ParentId == account.Id);
            if (childrenCount > 0)
            {
                info.AppendLine($"👇 الحسابات الفرعية: {childrenCount}");
            }

            lblAccountInfo.Text = info.ToString();
        }

        private string GetAccountTypeDisplayName(AccountType type)
        {
            return type switch
            {
                AccountType.Assets => "أصول",
                AccountType.Liabilities => "خصوم",
                AccountType.Equity => "حقوق ملكية",
                AccountType.Revenue => "إيرادات",
                AccountType.Expenses => "مصروفات",
                _ => "غير محدد"
            };
        }

        private int GetAccountTypeIndex(AccountType type)
        {
            return type switch
            {
                AccountType.Assets => 0,
                AccountType.Liabilities => 1,
                AccountType.Equity => 2,
                AccountType.Revenue => 3,
                AccountType.Expenses => 4,
                _ => -1
            };
        }

        #region Data Loading

        private  void LoadAccountsAsync()
        {
            try
            {
                _isLoading = true;
                Global_Variable.Update_Um_StatusBar_Prograss("جاري تحميل الحسابات...", 0);

                //lblStatus.Text = "جاري تحميل الحسابات...";
                //lblStatus.ForeColor = Color.Blue;

                _accounts = _accountService.GetAll(true);
                BuildAccountTree();

                Global_Variable.Update_Um_StatusBar_Prograss($"تم تحميل {_accounts.Count} حساب بنجاح", 0);
                //xlblStatus.Text = $"تم تحميل {_accounts.Count} حساب بنجاح";
                //lblStatus.ForeColor = Color.Green;

                UpdateButtonStates();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الحسابات:\n{ex.Message}",
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                //lblStatus.Text = "فشل في تحميل الحسابات";
                //lblStatus.ForeColor = Color.Red;

                // في حالة الخطأ، إظهار رسالة في الشجرة
                treeViewAccounts.Nodes.Clear();
                var errorNode = new TreeNode($"خطأ في تحميل البيانات: {ex.Message}");
                errorNode.ForeColor = Color.Red;
                treeViewAccounts.Nodes.Add(errorNode);
            }
            finally
            {
                _isLoading = false;
            }
        }

        #endregion

        #region Tree Building

        private void BuildAccountTree()
        {
            treeViewAccounts.BeginUpdate();
            treeViewAccounts.Nodes.Clear();

            try
            {
                if (_accounts == null || _accounts.Count == 0)
                {
                    // إضافة عقدة تشير إلى عدم وجود حسابات
                    var emptyNode = new TreeNode("لا توجد حسابات محاسبية");
                    emptyNode.ForeColor = Color.Gray;
                    treeViewAccounts.Nodes.Add(emptyNode);
                    return;
                }

                var rootAccounts = _accounts
                    .Where(a => a.ParentId == null || a.ParentId == 0)
                    .OrderBy(a => a.Code)
                    .ToList();

                foreach (var account in rootAccounts)
                {
                    var node = CreateAccountNode(account);
                    treeViewAccounts.Nodes.Add(node);
                    AddChildNodes(node, account);
                }

                // توسيع المستوى الأول
                foreach (TreeNode node in treeViewAccounts.Nodes)
                {
                    node.Expand();
                }
            }
            catch(Exception ex)
            {
                MessageBox.Show($"خطأ في بناء الشجرة المحاسبية:\n{ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                treeViewAccounts.EndUpdate();
            }
        }

        private TreeNode CreateAccountNode(Account account)
        {
            var nodeText = $"{account.Code} - {account.Name}";
            if (account.Balance != 0)
            {
                //nodeText += $" ({account.Balance:N2})";
            }

            var node = new TreeNode(nodeText)
            {
                Tag = account,
                ImageIndex = GetAccountImageIndex(account),
                SelectedImageIndex = GetAccountImageIndex(account)
            };

            // تلوين العقدة حسب نوع الحساب
            if (account.IsParent)
            {
                node.ForeColor = Color.DarkBlue;
                node.NodeFont = new Font(treeViewAccounts.Font, FontStyle.Bold);
            }
            else if (!account.IsActive)
            {
                node.ForeColor = Color.Gray;
            }

            return node;
        }

        private void AddChildNodes(TreeNode parentNode, Account parentAccount)
        {
            var childAccounts = _accounts
                .Where(a => a.ParentId.HasValue && a.ParentId.Value == parentAccount.Id)
                .OrderBy(a => a.Code)
                .ToList();

            foreach (var childAccount in childAccounts)
            {
                var childNode = CreateAccountNode(childAccount);
                parentNode.Nodes.Add(childNode);
                AddChildNodes(childNode, childAccount);
            }
        }

        private int GetAccountImageIndex(Account account)
        {
            // يمكن إضافة أيقونات مختلفة حسب نوع الحساب
            return account.IsParent ? 0 : 1;
        }

        #endregion

        #region Event Handlers

        private void TreeViewAccounts_AfterSelect(object sender, TreeViewEventArgs e)
        {
            _selectedAccount = e.Node?.Tag as Account;
            DisplayAccountInfo(_selectedAccount);
            UpdateButtonStates();
        }

        private void UpdateAccountDetails()
        {
            DisplayAccountInfo(_selectedAccount);
        }

        private void UpdateButtonStates()
        {
            var hasSelection = _selectedAccount != null;

            btnAddChild.Enabled = hasSelection;
            btnEdit.Enabled = hasSelection;
            btnDelete.Enabled = hasSelection && !(_selectedAccount?.IsParent ?? false);
            btnAccountStatement.Enabled = hasSelection;
            btnAccountMovements.Enabled = hasSelection;
            //btnTrialBalance.Enabled = _accounts.Any();
            //btnPrintTree.Enabled = _accounts.Any();

            //// أزرار التصدير
            //btnExportJson.Enabled = _accounts.Any();
            //btnExportXml.Enabled = _accounts.Any();
            //btnExportCsv.Enabled = _accounts.Any();
            //btnExportExcel.Enabled = _accounts.Any();
        }

        #endregion

        #region Account Operations

        private void AddMainAccountAsync()
        {
            try
            {
                // إعداد النموذج لإضافة حساب رئيسي
                ClearAccountForm();

                // تعيين القيم الافتراضية للحساب الرئيسي
                cmbParentAccount.SelectedIndex = 0; // بدون حساب أب
                chkIsActive.Checked = true;
                chkIsParent.Checked = true; // افتراضياً حساب رئيسي

                // تفعيل وضع التعديل
                SetEditMode(true);
                txtAccountCode.Focus();

                Global_Variable.Update_Um_StatusBar_Prograss("إضافة حساب رئيسي جديد", 0);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إعداد إضافة الحساب:\n{ex.Message}",
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void AddChildAccountAsync()
        {
            if (_selectedAccount == null)
            {
                MessageBox.Show("يرجى اختيار حساب أب لإضافة حساب فرعي إليه", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            try
            {
                // إعداد النموذج لإضافة حساب فرعي
                ClearAccountForm();

                // تعيين القيم الافتراضية للحساب الفرعي
                cmbAccountType.SelectedIndex = GetAccountTypeIndex(_selectedAccount.Type);
                cmbAccountNature.SelectedIndex = _selectedAccount.Nature == AccountNature.Debit ? 0 : 1;

                // تحديد الحساب الأب
                LoadParentAccounts();
                for (int i = 0; i < cmbParentAccount.Items.Count; i++)
                {
                    if (cmbParentAccount.Items[i].ToString().StartsWith(_selectedAccount.Code))
                    {
                        cmbParentAccount.SelectedIndex = i;
                        break;
                    }
                }

                chkIsActive.Checked = true;
                chkIsParent.Checked = false;

                // تفعيل وضع التعديل
                SetEditMode(true);
                txtAccountCode.Focus();

                Global_Variable.Update_Um_StatusBar_Prograss($"إضافة حساب فرعي للحساب: {_selectedAccount.Name} - أدخل البيانات واضغط حفظ", 0);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إعداد إضافة الحساب الفرعي:\n{ex.Message}",
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private  void EditAccountAsync()
        {
            if (_selectedAccount == null)
            {
                MessageBox.Show("يرجى اختيار حساب للتعديل", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            try
            {
                // استخدام لوحة التفاصيل للتعديل
                LoadAccountToForm(_selectedAccount);
                SetEditMode(true);
                Global_Variable.Update_Um_StatusBar_Prograss("وضع التعديل مفعل", 0);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تعديل الحساب:\n{ex.Message}",
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private   void DeleteAccountAsync()
        {
            if (_selectedAccount == null)
            {
                MessageBox.Show("يرجى اختيار حساب للحذف", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            if (_selectedAccount.IsParent)
            {
                MessageBox.Show("لا يمكن حذف حساب أب يحتوي على حسابات فرعية", "تحذير",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var result = MessageBox.Show($"هل أنت متأكد من حذف الحساب '{_selectedAccount.Name}'؟",
                "تأكيد الحذف", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                try
                {
                     _accountService.DeleteAccountAsync(_selectedAccount.Id);
                    LoadAccountsAsync();
                    Global_Variable.Update_Um_StatusBar_Prograss("تم حذف الحساب بنجاح", 0);
                    //lblStatus.ForeColor = Color.Green;
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في حذف الحساب:\n{ex.Message}",
                        "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        #endregion

        #region Search and Navigation

        private void SearchAccounts()
        {
            var searchText = txtSearch.Text.Trim();
            if (string.IsNullOrWhiteSpace(searchText))
            {
                BuildAccountTree();
                return;
            }

            var filteredAccounts = _accounts.Where(a =>
                a.Code.IndexOf(searchText, StringComparison.OrdinalIgnoreCase) >= 0 ||
                a.Name.IndexOf(searchText, StringComparison.OrdinalIgnoreCase) >= 0 ||
                (!string.IsNullOrEmpty(a.NameEnglish) && a.NameEnglish.IndexOf(searchText, StringComparison.OrdinalIgnoreCase) >= 0)
            ).ToList();

            BuildFilteredTree(filteredAccounts);
            Global_Variable.Update_Um_StatusBar_Prograss($"تم العثور على {filteredAccounts.Count} حساب", 0);
        }

        private void ClearSearch()
        {
            txtSearch.Text = "";
            BuildAccountTree();
            Global_Variable.Update_Um_StatusBar_Prograss("تم مسح البحث وإعادة عرض جميع الحسابات", 0);
        }

        private void ExpandAllNodes()
        {
            treeViewAccounts.ExpandAll();
            Global_Variable.Update_Um_StatusBar_Prograss("تم توسيع جميع العقد", 0);
        }

        private void CollapseAllNodes()
        {
            treeViewAccounts.CollapseAll();
            Global_Variable.Update_Um_StatusBar_Prograss("تم طي جميع العقد", 0);
        }

        private void RefreshTree()
        {
            LoadAccountsAsync();
        }

        private void ShowTreeStatistics()
        {
            if (_accounts == null || _accounts.Count == 0)
            {
                MessageBox.Show("لا توجد حسابات لعرض الإحصائيات", "معلومات",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            var stats = new StringBuilder();
            stats.AppendLine("إحصائيات الشجرة المحاسبية");
            stats.AppendLine("=".PadRight(40, '='));
            stats.AppendLine($"إجمالي الحسابات: {_accounts.Count}");
            stats.AppendLine($"الحسابات الرئيسية: {_accounts.Count(a => a.ParentId == null || a.ParentId == 0)}");
            stats.AppendLine($"الحسابات الفرعية: {_accounts.Count(a => a.ParentId != null && a.ParentId != 0)}");
            stats.AppendLine($"الحسابات النشطة: {_accounts.Count(a => a.IsActive)}");
            stats.AppendLine($"الحسابات غير النشطة: {_accounts.Count(a => !a.IsActive)}");
            stats.AppendLine($"حسابات الأصول: {_accounts.Count(a => a.Type == AccountType.Assets)}");
            stats.AppendLine($"حسابات الخصوم: {_accounts.Count(a => a.Type == AccountType.Liabilities)}");
            stats.AppendLine($"حسابات حقوق الملكية: {_accounts.Count(a => a.Type == AccountType.Equity)}");
            stats.AppendLine($"حسابات الإيرادات: {_accounts.Count(a => a.Type == AccountType.Revenue)}");
            stats.AppendLine($"حسابات المصروفات: {_accounts.Count(a => a.Type == AccountType.Expenses)}");
            stats.AppendLine();
            stats.AppendLine($"إجمالي الأرصدة المدينة: {_accounts.Where(a => a.Balance > 0).Sum(a => a.Balance):N2}");
            stats.AppendLine($"إجمالي الأرصدة الدائنة: {_accounts.Where(a => a.Balance < 0).Sum(a => Math.Abs(a.Balance)):N2}");

            var statsForm = new Form
            {
                Text = "إحصائيات الشجرة المحاسبية",
                Size = new Size(500, 400),
                StartPosition = FormStartPosition.CenterParent
            };

            var textBox = new System.Windows.Forms.TextBox
            {
                Multiline = true,
                ScrollBars = ScrollBars.Both,
                Dock = DockStyle.Fill,
                Text = stats.ToString(),
                ReadOnly = true,
                Font = new Font("Courier New", 10)
            };

            statsForm.Controls.Add(textBox);
            statsForm.ShowDialog(this);
        }

        private void ExportTreeAsImage()
        {
            try
            {
                var saveDialog = new SaveFileDialog
                {
                    Filter = "PNG Files (*.png)|*.png|JPEG Files (*.jpg)|*.jpg|Bitmap Files (*.bmp)|*.bmp",
                    Title = "حفظ الشجرة كصورة",
                    FileName = $"AccountTree_{DateTime.Now:yyyyMMdd_HHmmss}.png"
                };

                if (saveDialog.ShowDialog() == DialogResult.OK)
                {
                    // إنشاء صورة من TreeView
                    var bitmap = new Bitmap(treeViewAccounts.Width, treeViewAccounts.Height);
                    treeViewAccounts.DrawToBitmap(bitmap, new Rectangle(0, 0, treeViewAccounts.Width, treeViewAccounts.Height));

                    var extension = Path.GetExtension(saveDialog.FileName).ToLower();
                    switch (extension)
                    {
                        case ".png":
                            bitmap.Save(saveDialog.FileName, System.Drawing.Imaging.ImageFormat.Png);
                            break;
                        case ".jpg":
                        case ".jpeg":
                            bitmap.Save(saveDialog.FileName, System.Drawing.Imaging.ImageFormat.Jpeg);
                            break;
                        case ".bmp":
                            bitmap.Save(saveDialog.FileName, System.Drawing.Imaging.ImageFormat.Bmp);
                            break;
                        default:
                            bitmap.Save(saveDialog.FileName, System.Drawing.Imaging.ImageFormat.Png);
                            break;
                    }

                    bitmap.Dispose();

                    MessageBox.Show($"تم حفظ الشجرة كصورة بنجاح:\n{saveDialog.FileName}",
                        "نجح الحفظ", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ الشجرة كصورة:\n{ex.Message}",
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void SaveTreeSettings()
        {
            try
            {
                var settings = new
                {
                    ExpandedNodes = GetExpandedNodesCodes(),
                    SelectedNodeCode = _selectedAccount?.Code,
                    SearchText = txtSearch.Text,
                    TreeViewWidth = treeViewAccounts.Width,
                    TreeViewHeight = treeViewAccounts.Height
                };

                var json = JsonConvert.SerializeObject(settings, Newtonsoft.Json.Formatting.Indented);
                var settingsPath = Path.Combine(Application.StartupPath, "TreeSettings.json");
                File.WriteAllText(settingsPath, json, Encoding.UTF8);

                Global_Variable.Update_Um_StatusBar_Prograss("تم حفظ إعدادات الشجرة", 0);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ إعدادات الشجرة:\n{ex.Message}",
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void LoadTreeSettings()
        {
            try
            {
                var settingsPath = Path.Combine(Application.StartupPath, "TreeSettings.json");
                if (!File.Exists(settingsPath)) return;

                var json = File.ReadAllText(settingsPath, Encoding.UTF8);
                var settings = JsonConvert.DeserializeObject<dynamic>(json);

                if (settings.SearchText != null)
                    txtSearch.Text = settings.SearchText;

                if (settings.ExpandedNodes != null)
                    RestoreExpandedNodes(settings.ExpandedNodes.ToObject<string[]>());

                if (settings.SelectedNodeCode != null)
                    ExpandAndSelectAccount(settings.SelectedNodeCode.ToString());

                Global_Variable.Update_Um_StatusBar_Prograss("تم تحميل إعدادات الشجرة", 0);
            }
            catch (Exception ex)
            {
                // تجاهل أخطاء تحميل الإعدادات
                Console.WriteLine($"خطأ في تحميل إعدادات الشجرة: {ex.Message}");
            }
        }

        private string[] GetExpandedNodesCodes()
        {
            var expandedCodes = new List<string>();
            GetExpandedNodesRecursive(treeViewAccounts.Nodes, expandedCodes);
            return expandedCodes.ToArray();
        }

        private void GetExpandedNodesRecursive(TreeNodeCollection nodes, List<string> expandedCodes)
        {
            foreach (TreeNode node in nodes)
            {
                if (node.IsExpanded && node.Tag is Account account)
                {
                    expandedCodes.Add(account.Code);
                }
                GetExpandedNodesRecursive(node.Nodes, expandedCodes);
            }
        }

        private void RestoreExpandedNodes(string[] expandedCodes)
        {
            foreach (var code in expandedCodes)
            {
                ExpandNodeByCode(code);
            }
        }

        private void ExpandNodeByCode(string accountCode)
        {
            foreach (TreeNode node in treeViewAccounts.Nodes)
            {
                ExpandNodeRecursive(node, accountCode);
            }
        }

        private bool ExpandNodeRecursive(TreeNode node, string accountCode)
        {
            if (node.Tag is Account account && account.Code == accountCode)
            {
                node.Expand();
                return true;
            }

            foreach (TreeNode childNode in node.Nodes)
            {
                if (ExpandNodeRecursive(childNode, accountCode))
                {
                    node.Expand();
                    return true;
                }
            }

            return false;
        }

        private void ShowAdvancedSearch()
        {
            var searchForm = new Form
            {
                Text = "البحث المتقدم",
                Size = new Size(500, 400),
                StartPosition = FormStartPosition.CenterParent,
                FormBorderStyle = FormBorderStyle.FixedDialog,
                MaximizeBox = false,
                MinimizeBox = false
            };

            var panel = new System.Windows.Forms.Panel { Dock = DockStyle.Fill, Padding = new Padding(10) };
            searchForm.Controls.Add(panel);

            // حقول البحث
            var lblCode = new System.Windows.Forms.Label { Text = "كود الحساب:", Location = new Point(10, 20), Size = new Size(100, 23) };
            var txtCode = new System.Windows.Forms.TextBox { Location = new Point(120, 20), Size = new Size(200, 23) };

            var lblName = new System.Windows.Forms.Label { Text = "اسم الحساب:", Location = new Point(10, 50), Size = new Size(100, 23) };
            var txtName = new System.Windows.Forms.TextBox { Location = new Point(120, 50), Size = new Size(200, 23) };

            var lblType = new System.Windows.Forms.Label { Text = "نوع الحساب:", Location = new Point(10, 80), Size = new Size(100, 23) };
            var cmbType = new System.Windows.Forms.ComboBox { Location = new Point(120, 80), Size = new Size(200, 23), DropDownStyle = ComboBoxStyle.DropDownList };
            cmbType.Items.AddRange(new[] { "الكل", "أصول", "خصوم", "حقوق ملكية", "إيرادات", "مصروفات" });
            cmbType.SelectedIndex = 0;

            var lblNature = new System.Windows.Forms.Label { Text = "طبيعة الحساب:", Location = new Point(10, 110), Size = new Size(100, 23) };
            var cmbNature = new System.Windows.Forms.ComboBox { Location = new Point(120, 110), Size = new Size(200, 23), DropDownStyle = ComboBoxStyle.DropDownList };
            cmbNature.Items.AddRange(new[] { "الكل", "مدين", "دائن" });
            cmbNature.SelectedIndex = 0;

            var chkActiveOnly = new System.Windows.Forms.CheckBox { Text = "الحسابات النشطة فقط", Location = new Point(120, 140), Size = new Size(200, 23), Checked = true };
            var chkParentOnly = new System.Windows.Forms.CheckBox { Text = "الحسابات الرئيسية فقط", Location = new Point(120, 170), Size = new Size(200, 23) };

            // أزرار
            var btnSearch = new System.Windows.Forms.Button { Text = "بحث", Location = new Point(120, 210), Size = new Size(80, 30) };
            var btnClear = new System.Windows.Forms.Button { Text = "مسح", Location = new Point(210, 210), Size = new Size(80, 30) };
            var btnClose = new System.Windows.Forms.Button { Text = "إغلاق", Location = new Point(300, 210), Size = new Size(80, 30) };

            panel.Controls.AddRange(new System.Windows.Forms.Control[] { lblCode, txtCode, lblName, txtName, lblType, cmbType, lblNature, cmbNature, chkActiveOnly, chkParentOnly, btnSearch, btnClear, btnClose });

            btnSearch.Click += (s, e) =>
            {
                var filteredAccounts = _accounts.Where(a =>
                    (string.IsNullOrEmpty(txtCode.Text) || a.Code.IndexOf(txtCode.Text, StringComparison.OrdinalIgnoreCase) >= 0) &&
                    (string.IsNullOrEmpty(txtName.Text) || a.Name.IndexOf(txtName.Text, StringComparison.OrdinalIgnoreCase) >= 0) &&
                    (cmbType.SelectedIndex == 0 || a.Type.ToString() == GetAccountTypeFromIndex(cmbType.SelectedIndex)) &&
                    (cmbNature.SelectedIndex == 0 || a.Nature.ToString() == GetAccountNatureFromIndex(cmbNature.SelectedIndex)) &&
                    (!chkActiveOnly.Checked || a.IsActive) &&
                    (!chkParentOnly.Checked || a.IsParent)
                ).ToList();

                BuildFilteredTree(filteredAccounts);
                Global_Variable.Update_Um_StatusBar_Prograss($"تم العثور على {filteredAccounts.Count} حساب", 0);
                searchForm.Close();
            };

            btnClear.Click += (s, e) =>
            {
                txtCode.Text = "";
                txtName.Text = "";
                cmbType.SelectedIndex = 0;
                cmbNature.SelectedIndex = 0;
                chkActiveOnly.Checked = true;
                chkParentOnly.Checked = false;
            };

            btnClose.Click += (s, e) => searchForm.Close();

            searchForm.ShowDialog(this);
        }

        private string GetAccountTypeFromIndex(int index)
        {
            return index switch
            {
                1 => "Assets",
                2 => "Liabilities",
                3 => "Equity",
                4 => "Revenue",
                5 => "Expenses",
                _ => ""
            };
        }

        private string GetAccountNatureFromIndex(int index)
        {
            return index switch
            {
                1 => "Debit",
                2 => "Credit",
                _ => ""
            };
        }

        private void BuildFilteredTree(List<Account> filteredAccounts)
        {
            treeViewAccounts.BeginUpdate();
            treeViewAccounts.Nodes.Clear();

            try
            {
                foreach (var account in filteredAccounts.OrderBy(a => a.Code))
                {
                    var node = CreateAccountNode(account);
                    treeViewAccounts.Nodes.Add(node);
                }

                treeViewAccounts.ExpandAll();
            }
            finally
            {
                treeViewAccounts.EndUpdate();
            }
        }



        private void ExpandAndSelectAccount(string accountCode)
        {
            foreach (TreeNode node in treeViewAccounts.Nodes)
            {
                if (FindAndSelectNode(node, accountCode))
                {
                    break;
                }
            }
        }

        private bool FindAndSelectNode(TreeNode node, string accountCode)
        {
            if (node.Tag is Account account && account.Code == accountCode)
            {
                treeViewAccounts.SelectedNode = node;
                node.EnsureVisible();
                return true;
            }

            foreach (TreeNode childNode in node.Nodes)
            {
                if (FindAndSelectNode(childNode, accountCode))
                {
                    node.Expand();
                    return true;
                }
            }

            return false;
        }

        #endregion

        #region Reports and Statements

        private void ShowTrialBalance()
        {
            //try
            //{
            //    var trialBalanceForm = new TrialBalanceForm(_accountService);
            //    trialBalanceForm.ShowDialog();
            //}
            //catch (Exception ex)
            //{
            //    MessageBox.Show($"خطأ في عرض ميزان المراجعة:\n{ex.Message}",
            //        "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            //}
        }

        private void ShowAccountStatement()
        {
            if (_selectedAccount == null)
            {
                MessageBox.Show("يرجى اختيار حساب لعرض كشف الحساب", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            try
            {
                // إنشاء تقرير كشف حساب بسيط
                var statement = new StringBuilder();
                statement.AppendLine("كشف حساب");
                statement.AppendLine("=".PadRight(50, '='));
                statement.AppendLine($"كود الحساب: {_selectedAccount.Code}");
                statement.AppendLine($"اسم الحساب: {_selectedAccount.Name}");
                statement.AppendLine($"نوع الحساب: {_selectedAccount.Type}");
                statement.AppendLine($"طبيعة الحساب: {_selectedAccount.Nature}");
                statement.AppendLine($"الرصيد الحالي: {_selectedAccount.Balance:N2}");
                statement.AppendLine($"تاريخ التقرير: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
                statement.AppendLine();
                statement.AppendLine("ملاحظة: سيتم إضافة تفاصيل الحركات المحاسبية قريباً");

                // عرض التقرير في نافذة منفصلة
                var reportForm = new Form
                {
                    Text = $"كشف حساب - {_selectedAccount.Name}",
                    Size = new Size(600, 400),
                    StartPosition = FormStartPosition.CenterParent
                };

                var textBox = new System.Windows.Forms.TextBox
                {
                    Multiline = true,
                    ScrollBars = ScrollBars.Both,
                    Dock = DockStyle.Fill,
                    Text = statement.ToString(),
                    ReadOnly = true,
                    Font = new Font("Courier New", 10)
                };

                reportForm.Controls.Add(textBox);
                reportForm.ShowDialog(this);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في عرض كشف الحساب:\n{ex.Message}",
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ShowAccountMovements()
        {
            if (_selectedAccount == null)
            {
                MessageBox.Show("يرجى اختيار حساب لعرض حركات الحساب", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            try
            {
                // إنشاء تقرير حركات الحساب
                var movements = new StringBuilder();
                movements.AppendLine("حركات الحساب");
                movements.AppendLine("=".PadRight(60, '='));
                movements.AppendLine($"كود الحساب: {_selectedAccount.Code}");
                movements.AppendLine($"اسم الحساب: {_selectedAccount.Name}");
                movements.AppendLine($"الرصيد الحالي: {_selectedAccount.Balance:N2}");
                movements.AppendLine($"تاريخ التقرير: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
                movements.AppendLine();
                movements.AppendLine("التاريخ".PadRight(12) + "البيان".PadRight(25) + "مدين".PadRight(12) + "دائن".PadRight(12) + "الرصيد");
                movements.AppendLine("-".PadRight(70, '-'));

                // إضافة حركة افتراضية للعرض
                movements.AppendLine($"{DateTime.Now:yyyy-MM-dd}".PadRight(12) +
                                   "رصيد افتتاحي".PadRight(25) +
                                   "".PadRight(12) +
                                   $"{_selectedAccount.Balance:N2}".PadRight(12) +
                                   $"{_selectedAccount.Balance:N2}");

                movements.AppendLine();
                movements.AppendLine("ملاحظة: سيتم ربط هذا التقرير بجدول الحركات المحاسبية قريباً");

                // عرض التقرير في نافذة منفصلة
                var reportForm = new Form
                {
                    Text = $"حركات الحساب - {_selectedAccount.Name}",
                    Size = new Size(700, 500),
                    StartPosition = FormStartPosition.CenterParent
                };

                var textBox = new System.Windows.Forms.TextBox
                {
                    Multiline = true,
                    ScrollBars = ScrollBars.Both,
                    Dock = DockStyle.Fill,
                    Text = movements.ToString(),
                    ReadOnly = true,
                    Font = new Font("Courier New", 9)
                };

                reportForm.Controls.Add(textBox);
                reportForm.ShowDialog(this);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في عرض حركات الحساب:\n{ex.Message}",
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void PrintAccountTree()
        {
            try
            {
                var report = GenerateTreeReport();
                // يمكن إضافة منطق الطباعة هنا
                MessageBox.Show("سيتم إضافة وظيفة الطباعة قريباً", "معلومات",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في طباعة الشجرة:\n{ex.Message}",
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private string GenerateTreeReport()
        {
            var report = new StringBuilder();
            report.AppendLine("تقرير شجرة الحسابات");
            report.AppendLine("==================");
            report.AppendLine($"تاريخ التقرير: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
            report.AppendLine($"إجمالي الحسابات: {_accounts?.Count ?? 0}");
            report.AppendLine();

            if (_accounts != null && _accounts.Any())
            {
                var rootAccounts = _accounts.Where(a => a.ParentId == null || a.ParentId == 0).OrderBy(a => a.Code);
                foreach (var account in rootAccounts)
                {
                    AppendAccountToReport(report, account, 0);
                }
            }

            return report.ToString();
        }

        private void AppendAccountToReport(StringBuilder report, Account account, int level)
        {
            var indent = new string(' ', level * 4);
            report.AppendLine($"{indent}{account.Code} - {account.Name} ({account.Balance:N2})");

            var children = _accounts?.Where(a => a.ParentId == account.Id).OrderBy(a => a.Code);
            if (children != null)
            {
                foreach (var child in children)
                {
                    AppendAccountToReport(report, child, level + 1);
                }
            }
        }

        private void CopyAccountInfo()
        {
            if (_selectedAccount == null) return;

            try
            {
                var info = $"كود الحساب: {_selectedAccount.Code}\n" +
                          $"اسم الحساب: {_selectedAccount.Name}\n" +
                          $"النوع: {_selectedAccount.Type}\n" +
                          $"الرصيد: {_selectedAccount.Balance:N2}";

                Clipboard.SetText(info);
                Global_Variable.Update_Um_StatusBar_Prograss("تم نسخ معلومات الحساب", 0);
                //lblStatus.ForeColor = Color.Green;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في نسخ المعلومات:\n{ex.Message}",
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region Export Functions

        private void ExportToJson()
        {
            try
            {
                // تشغيل في UI Thread مباشرة
                this.BeginInvoke(new MethodInvoker(() =>
                {
                    try
                    {
                        var saveDialog = new SaveFileDialog
                        {
                            Filter = "JSON Files (*.json)|*.json",
                            Title = "تصدير الشجرة المحاسبية إلى JSON",
                            FileName = $"ChartOfAccounts_{DateTime.Now:yyyyMMdd_HHmmss}.json"
                        };

                        if (saveDialog.ShowDialog(this) == DialogResult.OK)
                        {
                            var exportData = new
                            {
                                ExportDate = DateTime.Now,
                                TotalAccounts = _accounts.Count,
                                Accounts = _accounts.Select(a => new
                                {
                                    a.Id,
                                    a.Code,
                                    a.Name,
                                    a.NameEnglish,
                                    a.Type,
                                    a.Nature,
                                    a.ParentId,
                                    a.Level,
                                    a.IsParent,
                                    a.IsActive,
                                    a.Balance,
                                    a.Description
                                }).ToList()
                            };

                            var json = JsonConvert.SerializeObject(exportData, Newtonsoft.Json.Formatting.Indented);
                            File.WriteAllText(saveDialog.FileName, json, Encoding.UTF8);

                            Global_Variable.Update_Um_StatusBar_Prograss($"تم تصدير {_accounts.Count} حساب إلى JSON بنجاح", 0);
                            //lblStatus.ForeColor = Color.Green;

                            MessageBox.Show($"تم تصدير الشجرة المحاسبية بنجاح إلى:\n{saveDialog.FileName}",
                                "نجح التصدير", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        }
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"خطأ في تصدير JSON:\n{ex.Message}",
                            "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }));
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تصدير JSON:\n{ex.Message}",
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ExportToXml()
        {
            try
            {
                // تشغيل في UI Thread مباشرة
                this.BeginInvoke(new MethodInvoker(() =>
                {
                    try
                    {
                        var saveDialog = new SaveFileDialog
                        {
                            Filter = "XML Files (*.xml)|*.xml",
                            Title = "تصدير الشجرة المحاسبية إلى XML",
                            FileName = $"ChartOfAccounts_{DateTime.Now:yyyyMMdd_HHmmss}.xml"
                        };

                        if (saveDialog.ShowDialog(this) == DialogResult.OK)
                        {
                            var doc = new XmlDocument();
                            var root = doc.CreateElement("ChartOfAccounts");
                            doc.AppendChild(root);

                            var exportInfo = doc.CreateElement("ExportInfo");
                            exportInfo.SetAttribute("ExportDate", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
                            exportInfo.SetAttribute("TotalAccounts", _accounts.Count.ToString());
                            root.AppendChild(exportInfo);

                            var accountsElement = doc.CreateElement("Accounts");
                            root.AppendChild(accountsElement);

                            foreach (var account in _accounts)
                            {
                                var accountElement = doc.CreateElement("Account");
                                accountElement.SetAttribute("Id", account.Id.ToString());
                                accountElement.SetAttribute("Code", account.Code);
                                accountElement.SetAttribute("Name", account.Name);
                                accountElement.SetAttribute("NameEnglish", account.NameEnglish ?? "");
                                accountElement.SetAttribute("Type", account.Type.ToString());
                                accountElement.SetAttribute("Nature", account.Nature.ToString());
                                accountElement.SetAttribute("ParentId", account.ParentId?.ToString() ?? "");
                                accountElement.SetAttribute("Level", account.Level.ToString());
                                accountElement.SetAttribute("IsParent", account.IsParent.ToString());
                                accountElement.SetAttribute("IsActive", account.IsActive.ToString());
                                accountElement.SetAttribute("Balance", account.Balance.ToString());
                                accountElement.SetAttribute("Description", account.Description ?? "");

                                accountsElement.AppendChild(accountElement);
                            }

                            doc.Save(saveDialog.FileName);

                            Global_Variable.Update_Um_StatusBar_Prograss($"تم تصدير {_accounts.Count} حساب إلى XML بنجاح", 0);
                            //lblStatus.ForeColor = Color.Green;

                            MessageBox.Show($"تم تصدير الشجرة المحاسبية بنجاح إلى:\n{saveDialog.FileName}",
                                "نجح التصدير", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        }
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"خطأ في تصدير XML:\n{ex.Message}",
                            "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }));
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تصدير XML:\n{ex.Message}",
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ExportToCsv()
        {
            try
            {
                // تشغيل في UI Thread مباشرة
                this.BeginInvoke(new MethodInvoker(() =>
                {
                    try
                    {
                        var saveDialog = new SaveFileDialog
                        {
                            Filter = "CSV Files (*.csv)|*.csv",
                            Title = "تصدير الشجرة المحاسبية إلى CSV",
                            FileName = $"ChartOfAccounts_{DateTime.Now:yyyyMMdd_HHmmss}.csv"
                        };

                        if (saveDialog.ShowDialog(this) == DialogResult.OK)
                        {
                            var csv = new StringBuilder();

                            // إضافة العناوين
                            csv.AppendLine("Id,Code,Name,NameEnglish,Type,Nature,ParentId,Level,IsParent,IsActive,Balance,Description");

                            // إضافة البيانات
                            foreach (var account in _accounts)
                            {
                                csv.AppendLine($"{account.Id}," +
                                             $"\"{account.Code}\"," +
                                             $"\"{account.Name}\"," +
                                             $"\"{account.NameEnglish ?? ""}\"," +
                                             $"\"{account.Type}\"," +
                                             $"\"{account.Nature}\"," +
                                             $"{account.ParentId?.ToString() ?? ""}," +
                                             $"{account.Level}," +
                                             $"{account.IsParent}," +
                                             $"{account.IsActive}," +
                                             $"{account.Balance}," +
                                             $"\"{account.Description ?? ""}\"");
                            }

                            File.WriteAllText(saveDialog.FileName, csv.ToString(), Encoding.UTF8);

                            Global_Variable.Update_Um_StatusBar_Prograss($"تم تصدير {_accounts.Count} حساب إلى CSV بنجاح", 0);
                            //lblStatus.ForeColor = Color.Green;

                            MessageBox.Show($"تم تصدير الشجرة المحاسبية بنجاح إلى:\n{saveDialog.FileName}",
                                "نجح التصدير", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        }
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"خطأ في تصدير CSV:\n{ex.Message}",
                            "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }));
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تصدير CSV:\n{ex.Message}",
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ExportToExcel()
        {
            try
            {
                // تشغيل في UI Thread مباشرة
                this.BeginInvoke(new MethodInvoker(() =>
                {
                    try
                    {
                        // تصدير إلى CSV مع امتداد Excel
                        var saveDialog = new SaveFileDialog
                        {
                            Filter = "Excel Files (*.xlsx)|*.xlsx|CSV Files (*.csv)|*.csv",
                            Title = "تصدير الشجرة المحاسبية إلى Excel",
                            FileName = $"ChartOfAccounts_{DateTime.Now:yyyyMMdd_HHmmss}.csv"
                        };

                        if (saveDialog.ShowDialog(this) == DialogResult.OK)
                        {
                            // إنشاء CSV مباشرة بدلاً من استدعاء ExportToCsv
                            var csv = new StringBuilder();

                            // إضافة العناوين
                            csv.AppendLine("Id,Code,Name,NameEnglish,Type,Nature,ParentId,Level,IsParent,IsActive,Balance,Description");

                            // إضافة البيانات
                            foreach (var account in _accounts)
                            {
                                csv.AppendLine($"{account.Id}," +
                                             $"\"{account.Code}\"," +
                                             $"\"{account.Name}\"," +
                                             $"\"{account.NameEnglish ?? ""}\"," +
                                             $"\"{account.Type}\"," +
                                             $"\"{account.Nature}\"," +
                                             $"{account.ParentId?.ToString() ?? ""}," +
                                             $"{account.Level}," +
                                             $"{account.IsParent}," +
                                             $"{account.IsActive}," +
                                             $"{account.Balance}," +
                                             $"\"{account.Description ?? ""}\"");
                            }

                            File.WriteAllText(saveDialog.FileName, csv.ToString(), Encoding.UTF8);

                            Global_Variable.Update_Um_StatusBar_Prograss($"تم تصدير {_accounts.Count} حساب إلى Excel بنجاح", 0);
                            //lblStatus.ForeColor = Color.Green;

                            MessageBox.Show($"تم تصدير الشجرة المحاسبية بنجاح إلى:\n{saveDialog.FileName}\n\nيمكنك فتح الملف في Excel مباشرة.",
                                "نجح التصدير", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        }
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"خطأ في تصدير Excel:\n{ex.Message}",
                            "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }));
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تصدير Excel:\n{ex.Message}",
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region Import Functions

        private void  ImportFromJson()
        {
            try
            {
                // تشغيل في UI Thread مباشرة
                this.BeginInvoke(new MethodInvoker(async () =>
                {
                    try
                    {
                        var openDialog = new OpenFileDialog
                        {
                            Filter = "JSON Files (*.json)|*.json",
                            Title = "استيراد الشجرة المحاسبية من JSON"
                        };

                        if (openDialog.ShowDialog(this) == DialogResult.OK)
                        {
                            var result = MessageBox.Show("هل تريد استبدال الشجرة المحاسبية الحالية؟\n" +
                                                        "اختر 'نعم' للاستبدال الكامل أو 'لا' للإضافة فقط.",
                                "تأكيد الاستيراد", MessageBoxButtons.YesNoCancel, MessageBoxIcon.Question);

                            if (result == DialogResult.Cancel) return;

                            var json = File.ReadAllText(openDialog.FileName, Encoding.UTF8);
                            var importData = JsonConvert.DeserializeObject<dynamic>(json);

                            if (result == DialogResult.Yes)
                            {
                                // حذف الحسابات الموجودة
                                var existingAccounts = _accountService.GetAll();
                                foreach (var account in existingAccounts)
                                {
                                    try
                                    {
                                        _accountService.DeleteAccountAsync(account.Id);
                                    }
                                    catch { } // تجاهل أخطاء الحذف
                                }
                            }

                            // استيراد الحسابات الجديدة
                            var importedCount = 0;
                            foreach (var accountData in importData.Accounts)
                            {
                                try
                                {
                                    var account = new Account
                                    {
                                        Code = accountData.Code,
                                        Name = accountData.Name,
                                        NameEnglish = accountData.NameEnglish,
                                        Type = (AccountType)Enum.Parse(typeof(AccountType), accountData.Type.ToString()),
                                        Nature = (AccountNature)Enum.Parse(typeof(AccountNature), accountData.Nature.ToString()),
                                        ParentId = accountData.ParentId != "" ? (int?)accountData.ParentId : null,
                                        Level = accountData.Level,
                                        IsParent = accountData.IsParent,
                                        IsActive = accountData.IsActive,
                                        Balance = accountData.Balance,
                                        Description = accountData.Description
                                    };

                                    await _accountService.AddAccountAsync(account);
                                    importedCount++;
                                }
                                catch (Exception ex)
                                {
                                    // تسجيل الخطأ ومتابعة الاستيراد
                                    Console.WriteLine($"خطأ في استيراد الحساب {accountData.Code}: {ex.Message}");
                                }
                            }

                            LoadAccountsAsync();
                            Global_Variable.Update_Um_StatusBar_Prograss($"تم استيراد {importedCount} حساب من JSON بنجاح", 0);
                            //lblStatus.ForeColor = Color.Green;

                            MessageBox.Show($"تم استيراد {importedCount} حساب بنجاح!",
                                "نجح الاستيراد", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        }
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"خطأ في استيراد JSON:\n{ex.Message}",
                            "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }));
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في استيراد JSON:\n{ex.Message}",
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private  void ImportFromXml()
        {
            try
            {
                // تشغيل في UI Thread مباشرة
                this.BeginInvoke(new MethodInvoker(async () =>
                {
                    try
                    {
                        var openDialog = new OpenFileDialog
                        {
                            Filter = "XML Files (*.xml)|*.xml",
                            Title = "استيراد الشجرة المحاسبية من XML"
                        };

                        if (openDialog.ShowDialog(this) == DialogResult.OK)
                        {
                            var result = MessageBox.Show("هل تريد استبدال الشجرة المحاسبية الحالية؟\n" +
                                                        "اختر 'نعم' للاستبدال الكامل أو 'لا' للإضافة فقط.",
                                "تأكيد الاستيراد", MessageBoxButtons.YesNoCancel, MessageBoxIcon.Question);

                            if (result == DialogResult.Cancel) return;

                            var doc = new XmlDocument();
                            doc.Load(openDialog.FileName);

                            if (result == DialogResult.Yes)
                            {
                                // حذف الحسابات الموجودة
                                var existingAccounts = _accountService.GetAll();
                                foreach (var account in existingAccounts)
                                {
                                    try
                                    {
                                         _accountService.DeleteAccountAsync(account.Id);
                                    }
                                    catch { } // تجاهل أخطاء الحذف
                                }
                            }

                            // استيراد الحسابات الجديدة
                            var importedCount = 0;
                            var accountNodes = doc.SelectNodes("//Account");

                            if (accountNodes != null)
                            {
                                foreach (XmlNode accountNode in accountNodes)
                                {
                                    try
                                    {
                                        var account = new Account
                                        {
                                            Code = accountNode.Attributes?["Code"]?.Value ?? "",
                                            Name = accountNode.Attributes?["Name"]?.Value ?? "",
                                            NameEnglish = accountNode.Attributes?["NameEnglish"]?.Value,
                                            Type = (AccountType)Enum.Parse(typeof(AccountType), accountNode.Attributes?["Type"]?.Value ?? "Assets"),
                                            Nature = (AccountNature)Enum.Parse(typeof(AccountNature), accountNode.Attributes?["Nature"]?.Value ?? "Debit"),
                                            ParentId = string.IsNullOrEmpty(accountNode.Attributes?["ParentId"]?.Value) ? null : int.Parse(accountNode.Attributes["ParentId"].Value),
                                            Level = int.Parse(accountNode.Attributes?["Level"]?.Value ?? "1"),
                                            IsParent = bool.Parse(accountNode.Attributes?["IsParent"]?.Value ?? "false"),
                                            IsActive = bool.Parse(accountNode.Attributes?["IsActive"]?.Value ?? "true"),
                                            Balance = decimal.Parse(accountNode.Attributes?["Balance"]?.Value ?? "0"),
                                            Description = accountNode.Attributes?["Description"]?.Value
                                        };

                                        await _accountService.AddAccountAsync(account);
                                        importedCount++;
                                    }
                                    catch (Exception ex)
                                    {
                                        Console.WriteLine($"خطأ في استيراد حساب من XML: {ex.Message}");
                                    }
                                }
                            }

                            LoadAccountsAsync();
                            Global_Variable.Update_Um_StatusBar_Prograss($"تم استيراد {importedCount} حساب من XML بنجاح", 0);
                            //lblStatus.ForeColor = Color.Green;

                            MessageBox.Show($"تم استيراد {importedCount} حساب بنجاح!",
                                "نجح الاستيراد", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        }
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"خطأ في استيراد XML:\n{ex.Message}",
                            "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }));
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في استيراد XML:\n{ex.Message}",
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private  void ImportFromCsv()
        {
            try
            {
                // تشغيل في UI Thread مباشرة
                this.BeginInvoke(new MethodInvoker(async () =>
                {
                    try
                    {
                        var openDialog = new OpenFileDialog
                        {
                            Filter = "CSV Files (*.csv)|*.csv",
                            Title = "استيراد الشجرة المحاسبية من CSV"
                        };

                        if (openDialog.ShowDialog(this) == DialogResult.OK)
                        {
                            var result = MessageBox.Show("هل تريد استبدال الشجرة المحاسبية الحالية؟\n" +
                                                        "اختر 'نعم' للاستبدال الكامل أو 'لا' للإضافة فقط.",
                                "تأكيد الاستيراد", MessageBoxButtons.YesNoCancel, MessageBoxIcon.Question);

                            if (result == DialogResult.Cancel) return;

                            var lines = File.ReadAllLines(openDialog.FileName, Encoding.UTF8);

                            if (lines.Length < 2)
                            {
                                MessageBox.Show("ملف CSV فارغ أو لا يحتوي على بيانات صحيحة", "خطأ",
                                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                                return;
                            }

                            if (result == DialogResult.Yes)
                            {
                                // حذف الحسابات الموجودة
                                var existingAccounts = _accountService.GetAll();
                                foreach (var account in existingAccounts)
                                {
                                    try
                                    {
                                        _accountService.DeleteAccountAsync(account.Id);
                                    }
                                    catch { } // تجاهل أخطاء الحذف
                                }
                            }

                            // تخطي السطر الأول (العناوين)
                            var importedCount = 0;
                            for (int i = 1; i < lines.Length; i++)
                            {
                                try
                                {
                                    var fields = ParseCsvLine(lines[i]);
                                    if (fields.Length >= 12)
                                    {
                                        var account = new Account
                                        {
                                            Code = fields[1].Trim('"'),
                                            Name = fields[2].Trim('"'),
                                            NameEnglish = fields[3].Trim('"'),
                                            Type = (AccountType)Enum.Parse(typeof(AccountType), fields[4].Trim('"')),
                                            Nature = (AccountNature)Enum.Parse(typeof(AccountNature), fields[5].Trim('"')),
                                            ParentId = string.IsNullOrEmpty(fields[6]) ? null : int.Parse(fields[6]),
                                            Level = int.Parse(fields[7]),
                                            IsParent = bool.Parse(fields[8]),
                                            IsActive = bool.Parse(fields[9]),
                                            Balance = decimal.Parse(fields[10]),
                                            Description = fields[11].Trim('"')
                                        };

                                        await _accountService.AddAccountAsync(account);
                                        importedCount++;
                                    }
                                }
                                catch (Exception ex)
                                {
                                    Console.WriteLine($"خطأ في استيراد السطر {i}: {ex.Message}");
                                }
                            }

                            LoadAccountsAsync();
                            Global_Variable.Update_Um_StatusBar_Prograss($"تم استيراد {importedCount} حساب من CSV بنجاح", 0);
                            //lblStatus.ForeColor = Color.Green;

                            MessageBox.Show($"تم استيراد {importedCount} حساب بنجاح!",
                                "نجح الاستيراد", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        }
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"خطأ في استيراد CSV:\n{ex.Message}",
                            "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }));
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في استيراد CSV:\n{ex.Message}",
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ImportFromExcel()
        {
            try
            {
                MessageBox.Show("يرجى حفظ ملف Excel بتنسيق CSV أولاً، ثم استخدام خيار 'استيراد من CSV'.",
                    "معلومات", MessageBoxButtons.OK, MessageBoxIcon.Information);
                ImportFromCsv();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في استيراد Excel:\n{ex.Message}",
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private string[] ParseCsvLine(string line)
        {
            var result = new List<string>();
            var current = new StringBuilder();
            bool inQuotes = false;

            for (int i = 0; i < line.Length; i++)
            {
                char c = line[i];

                if (c == '"')
                {
                    inQuotes = !inQuotes;
                }
                else if (c == ',' && !inQuotes)
                {
                    result.Add(current.ToString());
                    current.Clear();
                }
                else
                {
                    current.Append(c);
                }
            }

            result.Add(current.ToString());
            return result.ToArray();
        }

        #endregion



        private  void btnAddMainAccount_Click(object sender, EventArgs e)
        {
            //treeView1.Refresh();
            //treeView1.Nodes.Add("Account");
            //treeView1.Refresh();
            //return;
            try
            {
                var addForm = new Frm_AddEditAccount(_accountService, null);
                if (addForm.ShowDialog() == DialogResult.OK)
                {
                    LoadAccountsAsync();
                    Global_Variable.Update_Um_StatusBar_Prograss($"تم إضافة الحساب الرئيسي بنجاح", 0);
                    //lblStatus.Text = "تم إضافة الحساب الرئيسي بنجاح";
                    //lblStatus.ForeColor = Color.Green;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إضافة الحساب:\n{ex.Message}",
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        //private  void Frm_Account_Manual_Load(object sender, EventArgs e)
        //{
        //    //treeView1.Nodes.Add("aaaaaaaaa");
        //    LoadAccountsAsync();

        //    // إذا لم توجد حسابات، اسأل المستخدم عن إنشاء بيانات تجريبية
        //    if (_accounts == null || _accounts.Count == 0)
        //    {
        //        var result = MessageBox.Show("لا توجد حسابات محاسبية في النظام.\nهل تريد إنشاء شجرة حسابات تجريبية؟",
        //            "إنشاء بيانات تجريبية", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

        //        if (result == DialogResult.Yes)
        //        {
        //            CreateSampleAccountsAsync();
        //        }
        //    }
        //    //this.Refresh();
        //    //treeView1.Refresh();

        //    // تحميل إعدادات الشجرة المحفوظة
        //    LoadTreeSettings();
        //}

        //private void Frm_Account_Manual_FormClosing(object sender, FormClosingEventArgs e)
        //{
        //    // حفظ إعدادات الشجرة عند الإغلاق
        //    SaveTreeSettings();
        //}

        private async void CreateSampleAccountsAsync()
        {
            try
            {
                Global_Variable.Update_Um_StatusBar_Prograss("جاري إنشاء البيانات التجريبية...", 0);

                await _accountService.InsertSampleDataAsync();
                LoadAccountsAsync();

                Global_Variable.Update_Um_StatusBar_Prograss("تم إنشاء البيانات التجريبية بنجاح", 0);
                MessageBox.Show("تم إنشاء شجرة الحسابات التجريبية بنجاح!", "نجح الإنشاء",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إنشاء البيانات التجريبية:\n{ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private  void btnRefresh_Click(object sender, EventArgs e)
        {
            LoadAccountsAsync();
            //BuildAccountTree();
        }

        private  void btnEdit_Click(object sender, EventArgs e)
        {
             EditAccountAsync();
        }

        private  void btnDelete_Click(object sender, EventArgs e)
        {
            DeleteAccountAsync();
        }

        private  void btnAccountStatement_Click(object sender, EventArgs e)
        {
            ShowAccountStatement();
        }

        private  void btnAccountMovements_Click(object sender, EventArgs e)
        {
            ShowAccountMovements();
        }

        private void btnStatistics_Click(object sender, EventArgs e)
        {
            ShowAccountStatistics();
        }

        private void btnOperationHistory_Click(object sender, EventArgs e)
        {
            ShowOperationHistory();
        }

        #region Event Handlers المحسنة

        private void Frm_Account_Manual_Load(object sender, EventArgs e)
        {
            try
            {
                LoadAccountsAsync();
                LoadTreeSettings();
                LoadUserPreferences();
                LogOperation("تم فتح نموذج الدليل المحاسبي");
            }
            catch (Exception ex)
            {
                LogOperation($"خطأ في تحميل النموذج: {ex.Message}");
                RJMessageBox.Show($"خطأ في تحميل النموذج:\n{ex.Message}",
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void Frm_Account_Manual_FormClosing(object sender, FormClosingEventArgs e)
        {
            try
            {
                // التحقق من التغييرات غير المحفوظة
                if (_hasUnsavedChanges && _isEditMode)
                {
                    var result = RJMessageBox.Show(
                        "هناك تغييرات غير محفوظة. هل تريد حفظها قبل الإغلاق؟",
                        "تغييرات غير محفوظة",
                        MessageBoxButtons.YesNoCancel,
                        MessageBoxIcon.Question);

                    if (result == DialogResult.Yes)
                    {
                        BtnSaveAccount_Click(sender, e);
                        if (_hasUnsavedChanges) // إذا فشل الحفظ
                        {
                            e.Cancel = true;
                            return;
                        }
                    }
                    else if (result == DialogResult.Cancel)
                    {
                        e.Cancel = true;
                        return;
                    }
                }

                SaveTreeSettings();
                SaveUserPreferences();
                LogOperation("تم إغلاق نموذج الدليل المحاسبي");
            }
            catch (Exception ex)
            {
                LogOperation($"خطأ في إغلاق النموذج: {ex.Message}");
                // لا نمنع الإغلاق في حالة خطأ في الحفظ
            }
        }

        #endregion

        #region إدارة تفضيلات المستخدم

        private void LoadUserPreferences()
        {
            try
            {
                var preferencesPath = Path.Combine(Application.StartupPath, "UserPreferences_Accounts.json");
                if (!File.Exists(preferencesPath)) return;

                var json = File.ReadAllText(preferencesPath, Encoding.UTF8);
                var preferences = JsonConvert.DeserializeObject<dynamic>(json);

                // تطبيق التفضيلات
                if (preferences?.AutoSuggestCodes != null)
                {
                    // يمكن إضافة خيار في الواجهة لتفعيل/تعطيل الاقتراح التلقائي
                }

                if (preferences?.DefaultAccountType != null)
                {
                    // تعيين نوع الحساب الافتراضي
                }

                LogOperation("تم تحميل تفضيلات المستخدم");
            }
            catch (Exception ex)
            {
                LogOperation($"خطأ في تحميل تفضيلات المستخدم: {ex.Message}");
            }
        }

        private void SaveUserPreferences()
        {
            try
            {
                var preferences = new
                {
                    AutoSuggestCodes = true,
                    DefaultAccountType = cmbAccountType.SelectedIndex,
                    LastUsedParent = cmbParentAccount.SelectedIndex,
                    FormSize = this.Size,
                    FormLocation = this.Location,
                    SplitterDistance = 500,
                    LastSaved = DateTime.Now
                };

                var json = JsonConvert.SerializeObject(preferences, Newtonsoft.Json.Formatting.Indented);
                var preferencesPath = Path.Combine(Application.StartupPath, "UserPreferences_Accounts.json");
                File.WriteAllText(preferencesPath, json, Encoding.UTF8);

                LogOperation("تم حفظ تفضيلات المستخدم");
            }
            catch (Exception ex)
            {
                LogOperation($"خطأ في حفظ تفضيلات المستخدم: {ex.Message}");
            }
        }

        #endregion

        #region معالجة الأخطاء المحسنة

        private void HandleDatabaseError(Exception ex)
        {
            LogOperation($"خطأ في قاعدة البيانات: {ex.Message}");

            var errorMessage = "حدث خطأ في الاتصال بقاعدة البيانات.\n\n";
            errorMessage += "تفاصيل الخطأ:\n";
            errorMessage += ex.Message + "\n\n";
            errorMessage += "الإجراءات المقترحة:\n";
            errorMessage += "• تحقق من الاتصال بقاعدة البيانات\n";
            errorMessage += "• تأكد من صحة إعدادات الاتصال\n";
            errorMessage += "• أعد تشغيل التطبيق\n";
            errorMessage += "• اتصل بالدعم الفني إذا استمر الخطأ";

            RJMessageBox.Show(errorMessage, "خطأ في قاعدة البيانات",
                MessageBoxButtons.OK, MessageBoxIcon.Error);
        }

        private void HandleValidationError(string field, string error)
        {
            LogOperation($"خطأ في التحقق - {field}: {error}");

            var errorMessage = $"خطأ في التحقق من البيانات:\n\n";
            errorMessage += $"الحقل: {field}\n";
            errorMessage += $"الخطأ: {error}\n\n";
            errorMessage += "يرجى تصحيح البيانات والمحاولة مرة أخرى.";

            RJMessageBox.Show(errorMessage, "خطأ في التحقق",
                MessageBoxButtons.OK, MessageBoxIcon.Warning);
        }

        private void HandleUnexpectedError(Exception ex, string operation)
        {
            LogOperation($"خطأ غير متوقع في {operation}: {ex.Message}");

            var errorMessage = $"حدث خطأ غير متوقع أثناء {operation}.\n\n";
            errorMessage += "تفاصيل الخطأ:\n";
            errorMessage += ex.Message + "\n\n";

            if (ex.InnerException != null)
            {
                errorMessage += "تفاصيل إضافية:\n";
                errorMessage += ex.InnerException.Message + "\n\n";
            }

            errorMessage += "يرجى المحاولة مرة أخرى أو الاتصال بالدعم الفني.";

            RJMessageBox.Show(errorMessage, "خطأ غير متوقع",
                MessageBoxButtons.OK, MessageBoxIcon.Error);
        }

        #endregion

        #region تحسينات الأداء

        private void OptimizeTreeViewPerformance()
        {
            // تحسين أداء TreeView للبيانات الكبيرة
            treeViewAccounts.BeginUpdate();
            try
            {
                // تعطيل الرسم المؤقت
                treeViewAccounts.Visible = false;

                // بناء الشجرة
                BuildAccountTree();

                // إعادة تفعيل الرسم
                treeViewAccounts.Visible = true;
            }
            finally
            {
                treeViewAccounts.EndUpdate();
            }
        }

        private void LoadAccountsWithProgress()
        {
            try
            {
                _isLoading = true;
                Global_Variable.Update_Um_StatusBar_Prograss("جاري تحميل الحسابات...", 0);

                // تحميل البيانات في خيط منفصل لتحسين الاستجابة
                Task.Run(() =>
                {
                    try
                    {
                        var accounts = _accountService.GetAll(true);

                        // العودة للخيط الرئيسي لتحديث الواجهة
                        this.BeginInvoke(new MethodInvoker(() =>
                        {
                            _accounts = accounts;
                            OptimizeTreeViewPerformance();
                            UpdateButtonStates();

                            Global_Variable.Update_Um_StatusBar_Prograss($"تم تحميل {_accounts.Count} حساب بنجاح", 0);
                            LogOperation($"تم تحميل {_accounts.Count} حساب");
                        }));
                    }
                    catch (Exception ex)
                    {
                        this.BeginInvoke(new MethodInvoker(() =>
                        {
                            HandleDatabaseError(ex);
                        }));
                    }
                    finally
                    {
                        this.BeginInvoke(new MethodInvoker(() =>
                        {
                            _isLoading = false;
                        }));
                    }
                });
            }
            catch (Exception ex)
            {
                HandleUnexpectedError(ex, "تحميل الحسابات");
                _isLoading = false;
            }
        }

        #endregion
    }
}
