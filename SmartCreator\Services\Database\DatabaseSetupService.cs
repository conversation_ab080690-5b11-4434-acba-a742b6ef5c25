using System;
using System.IO;
using System.Threading.Tasks;
using SmartCreator.Data;
using SmartCreator.Services;
using SmartCreator.Entities;
using System.Security.Cryptography;
using System.Text;

namespace SmartCreator.Services
{
    /// <summary>
    /// خدمة إعداد قاعدة البيانات وإنشاء الجداول المطلوبة
    /// </summary>
    public class DatabaseSetupService
    {
        private readonly Smart_DataAccess _dataAccess;

        public DatabaseSetupService()
        {
            _dataAccess = new Smart_DataAccess();
        }

        /// <summary>
        /// إنشاء جداول الأمان المطلوبة
        /// </summary>
        public async Task<bool> CreateSecurityTablesAsync()
        {
            try
            {
                Console.WriteLine("🗄️ بدء إنشاء جداول الأمان...");

                // إنشاء جدول المستخدمين
                await CreateUsersTableAsync();
                Console.WriteLine("   ✅ تم إنشاء جدول Users");

                // إنشاء جدول الأنشطة
                await CreateUserActivitiesTableAsync();
                Console.WriteLine("   ✅ تم إنشاء جدول UserActivities");

                // إنشاء جدول الصلاحيات
                await CreatePermissionsTableAsync();
                Console.WriteLine("   ✅ تم إنشاء جدول Permissions");

                // إنشاء جدول صلاحيات المستخدمين
                await CreateUserPermissionsTableAsync();
                Console.WriteLine("   ✅ تم إنشاء جدول UserPermissions");

                // إنشاء جدول الإشعارات
                await CreateNotificationsTableAsync();
                Console.WriteLine("   ✅ تم إنشاء جدول Notifications");

                // إنشاء الفهارس
                await CreateIndexesAsync();
                Console.WriteLine("   ✅ تم إنشاء الفهارس");

                // إدراج البيانات الأساسية
                await InsertDefaultDataAsync();
                Console.WriteLine("   ✅ تم إدراج البيانات الأساسية");

                Console.WriteLine("🎉 تم إنشاء جداول الأمان بنجاح!");
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في إنشاء جداول الأمان: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// إنشاء جدول المستخدمين
        /// </summary>
        private async Task CreateUsersTableAsync()
        {
            var sql = @"
                CREATE TABLE IF NOT EXISTS Users (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    Username NVARCHAR(50) NOT NULL UNIQUE,
                    Email NVARCHAR(150) NOT NULL UNIQUE,
                    PasswordHash NVARCHAR(255) NOT NULL,
                    Salt NVARCHAR(255) NOT NULL,
                    FirstName NVARCHAR(100) NOT NULL,
                    LastName NVARCHAR(100) NOT NULL,
                    FullName NVARCHAR(200) NOT NULL,
                    Phone NVARCHAR(20),
                    Department NVARCHAR(100),
                    Position NVARCHAR(100),
                    IsActive BOOLEAN NOT NULL DEFAULT 1,
                    IsLocked BOOLEAN NOT NULL DEFAULT 0,
                    FailedLoginAttempts INTEGER NOT NULL DEFAULT 0,
                    LastLoginDate DATETIME,
                    PasswordExpiryDate DATETIME,
                    CreatedDate DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    CreatedBy INTEGER,
                    ModifiedDate DATETIME,
                    ModifiedBy INTEGER
                )";

            await Task.Run(() => _dataAccess.Execute(sql));
        }

        /// <summary>
        /// إنشاء جدول أنشطة المستخدمين
        /// </summary>
        private async Task CreateUserActivitiesTableAsync()
        {
            var sql = @"
                CREATE TABLE IF NOT EXISTS UserActivities (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    UserId INTEGER NOT NULL,
                    Username NVARCHAR(50),
                    Action NVARCHAR(100) NOT NULL,
                    Module NVARCHAR(50) NOT NULL,
                    SubModule NVARCHAR(50),
                    Description NTEXT,
                    EntityType NVARCHAR(50),
                    EntityId INTEGER,
                    OldValues NTEXT,
                    NewValues NTEXT,
                    IpAddress NVARCHAR(45),
                    UserAgent NTEXT,
                    Timestamp DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    ActivityType NVARCHAR(50) NOT NULL,
                    Severity NVARCHAR(20) NOT NULL DEFAULT 'Info',
                    IsSuccessful BOOLEAN NOT NULL DEFAULT 1,
                    ErrorMessage NTEXT
                )";

            await Task.Run(() => _dataAccess.Execute(sql));
        }

        /// <summary>
        /// إنشاء جدول الصلاحيات
        /// </summary>
        private async Task CreatePermissionsTableAsync()
        {
            var sql = @"
                CREATE TABLE IF NOT EXISTS Permissions (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    Name NVARCHAR(100) NOT NULL,
                    Code NVARCHAR(50) NOT NULL UNIQUE,
                    Description NTEXT,
                    Module NVARCHAR(50) NOT NULL,
                    Category NVARCHAR(50) NOT NULL,
                    IsActive BOOLEAN NOT NULL DEFAULT 1,
                    CreatedDate DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    CreatedBy INTEGER,
                    ModifiedDate DATETIME,
                    ModifiedBy INTEGER
                )";

            await Task.Run(() => _dataAccess.Execute(sql));
        }

        /// <summary>
        /// إنشاء جدول صلاحيات المستخدمين
        /// </summary>
        private async Task CreateUserPermissionsTableAsync()
        {
            var sql = @"
                CREATE TABLE IF NOT EXISTS UserPermissions (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    UserId INTEGER NOT NULL,
                    PermissionId INTEGER NOT NULL,
                    IsGranted BOOLEAN NOT NULL DEFAULT 1,
                    GrantedDate DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    GrantedBy INTEGER NOT NULL,
                    ExpiryDate DATETIME,
                    IsActive BOOLEAN NOT NULL DEFAULT 1,
                    UNIQUE(UserId, PermissionId)
                )";

            await Task.Run(() => _dataAccess.Execute(sql));
        }

        /// <summary>
        /// إنشاء جدول الإشعارات
        /// </summary>
        private async Task CreateNotificationsTableAsync()
        {
            var sql = @"
                CREATE TABLE IF NOT EXISTS Notifications (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    Title NVARCHAR(200) NOT NULL,
                    Message NVARCHAR(1000) NOT NULL,
                    Type NVARCHAR(50) NOT NULL DEFAULT 'Info',
                    Priority NVARCHAR(20) NOT NULL DEFAULT 'Normal',
                    IsRead BOOLEAN NOT NULL DEFAULT 0,
                    CreatedDate DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    ReadDate DATETIME,
                    UserId INTEGER,
                    Username NVARCHAR(100),
                    Module NVARCHAR(100),
                    EntityId INTEGER,
                    EntityType NVARCHAR(100),
                    ActionUrl NVARCHAR(500),
                    AdditionalData NTEXT,
                    ExpiryDate DATETIME,
                    IsActive BOOLEAN NOT NULL DEFAULT 1,
                    Rb NVARCHAR(50),
                    FOREIGN KEY (UserId) REFERENCES Users(Id)
                )";

            await Task.Run(() => _dataAccess.Execute(sql));
        }

        /// <summary>
        /// إنشاء الفهارس
        /// </summary>
        private async Task CreateIndexesAsync()
        {
            var indexes = new[]
            {
                "CREATE INDEX IF NOT EXISTS IX_Users_Username ON Users(Username)",
                "CREATE INDEX IF NOT EXISTS IX_Users_Email ON Users(Email)",
                "CREATE INDEX IF NOT EXISTS IX_Users_IsActive ON Users(IsActive)",
                "CREATE INDEX IF NOT EXISTS IX_UserActivities_UserId ON UserActivities(UserId)",
                "CREATE INDEX IF NOT EXISTS IX_UserActivities_Timestamp ON UserActivities(Timestamp)",
                "CREATE INDEX IF NOT EXISTS IX_UserActivities_Action ON UserActivities(Action)",
                "CREATE INDEX IF NOT EXISTS IX_Permissions_Code ON Permissions(Code)",
                "CREATE INDEX IF NOT EXISTS IX_UserPermissions_UserId ON UserPermissions(UserId)",
                "CREATE INDEX IF NOT EXISTS IX_Notifications_UserId ON Notifications(UserId)",
                "CREATE INDEX IF NOT EXISTS IX_Notifications_IsRead ON Notifications(IsRead)",
                "CREATE INDEX IF NOT EXISTS IX_Notifications_CreatedDate ON Notifications(CreatedDate)",
                "CREATE INDEX IF NOT EXISTS IX_Notifications_Type ON Notifications(Type)",
                "CREATE INDEX IF NOT EXISTS IX_Notifications_IsActive ON Notifications(IsActive)"
            };

            foreach (var index in indexes)
            {
                await Task.Run(() => _dataAccess.Execute(index));
            }
        }

        /// <summary>
        /// إدراج البيانات الأساسية
        /// </summary>
        private async Task InsertDefaultDataAsync()
        {
            // إدراج الصلاحيات الأساسية
            await InsertDefaultPermissionsAsync();

            // إنشاء مستخدم مدير افتراضي
            await CreateDefaultAdminUserAsync();

            // إنشاء مستخدم تجريبي
            await CreateDefaultTestUserAsync();
        }

        /// <summary>
        /// إدراج الصلاحيات الافتراضية
        /// </summary>
        private async Task InsertDefaultPermissionsAsync()
        {
            var permissions = new[]
            {
                new { Name = "مدير النظام", Code = "SYSTEM_ADMIN", Description = "صلاحيات كاملة للنظام", Module = "CORE", Category = "SYSTEM" },
                new { Name = "عرض لوحة التحكم", Code = "VIEW_DASHBOARD", Description = "عرض لوحة التحكم الرئيسية", Module = "CORE", Category = "SYSTEM" },
                new { Name = "إدارة المستخدمين", Code = "MANAGE_USERS", Description = "إضافة وتعديل وحذف المستخدمين", Module = "SECURITY", Category = "USERS" },
                new { Name = "عرض المستخدمين", Code = "VIEW_USERS", Description = "عرض قائمة المستخدمين", Module = "SECURITY", Category = "USERS" },
                new { Name = "إضافة مستخدم", Code = "ADD_USER", Description = "إضافة مستخدم جديد", Module = "SECURITY", Category = "USERS" },
                new { Name = "تعديل مستخدم", Code = "EDIT_USER", Description = "تعديل بيانات المستخدم", Module = "SECURITY", Category = "USERS" },
                new { Name = "حذف مستخدم", Code = "DELETE_USER", Description = "حذف مستخدم", Module = "SECURITY", Category = "USERS" },
                new { Name = "إدارة الصلاحيات", Code = "MANAGE_PERMISSIONS", Description = "إدارة صلاحيات المستخدمين", Module = "SECURITY", Category = "PERMISSIONS" },
                new { Name = "عرض الأنشطة", Code = "VIEW_ACTIVITIES", Description = "عرض سجل أنشطة المستخدمين", Module = "SECURITY", Category = "AUDIT" }
            };

            var sql = @"
                INSERT OR IGNORE INTO Permissions (Name, Code, Description, Module, Category, CreatedDate)
                VALUES (@Name, @Code, @Description, @Module, @Category, CURRENT_TIMESTAMP)";

            foreach (var permission in permissions)
            {
                await Task.Run(() => _dataAccess.Execute(sql, permission));
            }
        }

        /// <summary>
        /// إنشاء مستخدم مدير افتراضي
        /// </summary>
        private async Task CreateDefaultAdminUserAsync()
        {
            // التحقق من وجود المستخدم
            var existsQuery = "SELECT COUNT(*) FROM Users WHERE Username = @Username";
            var exists = await Task.Run(() => _dataAccess.Get_int_FromDB($"SELECT COUNT(*) FROM Users WHERE Username = 'admin'")) > 0;

            if (!exists)
            {
                // إنشاء كلمة مرور مشفرة
                var salt = GenerateSalt();
                var passwordHash = HashPassword("admin123", salt);

                var sql = @"
                    INSERT INTO Users (Username, Email, PasswordHash, Salt, FirstName, LastName, FullName,
                                     Department, Position, IsActive, CreatedDate)
                    VALUES (@Username, @Email, @PasswordHash, @Salt, @FirstName, @LastName, @FullName,
                            @Department, @Position, @IsActive, CURRENT_TIMESTAMP)";

                var adminUser = new
                {
                    Username = "admin",
                    Email = "<EMAIL>",
                    PasswordHash = passwordHash,
                    Salt = salt,
                    FirstName = "مدير",
                    LastName = "النظام",
                    FullName = "مدير النظام",
                    Department = "تقنية المعلومات",
                    Position = "مدير النظام",
                    IsActive = true
                };

                await Task.Run(() => _dataAccess.Execute(sql, adminUser));

                // منح صلاحيات مدير النظام
                var permissionSql = @"
                    INSERT OR IGNORE INTO UserPermissions (UserId, PermissionId, GrantedBy)
                    SELECT 1, Id, 1 FROM Permissions WHERE Code = 'SYSTEM_ADMIN'";
                await Task.Run(() => _dataAccess.Execute(permissionSql));

                Console.WriteLine("   ✅ تم إنشاء مستخدم admin (كلمة المرور: admin123)");
            }
        }

        /// <summary>
        /// إنشاء مستخدم تجريبي
        /// </summary>
        private async Task CreateDefaultTestUserAsync()
        {
            // التحقق من وجود المستخدم
            var existsQuery = "SELECT COUNT(*) FROM Users WHERE Username = @Username";
            var exists = await Task.Run(() => _dataAccess.Get_int_FromDB($"SELECT COUNT(*) FROM Users WHERE Username = 'test'")) > 0;

            if (!exists)
            {
                // إنشاء كلمة مرور مشفرة
                var salt = GenerateSalt();
                var passwordHash = HashPassword("test123", salt);

                var sql = @"
                    INSERT INTO Users (Username, Email, PasswordHash, Salt, FirstName, LastName, FullName,
                                     Department, Position, IsActive, CreatedDate)
                    VALUES (@Username, @Email, @PasswordHash, @Salt, @FirstName, @LastName, @FullName,
                            @Department, @Position, @IsActive, CURRENT_TIMESTAMP)";

                var testUser = new
                {
                    Username = "test",
                    Email = "<EMAIL>",
                    PasswordHash = passwordHash,
                    Salt = salt,
                    FirstName = "مستخدم",
                    LastName = "تجريبي",
                    FullName = "مستخدم تجريبي",
                    Department = "الاختبار",
                    Position = "مختبر",
                    IsActive = true
                };

                await Task.Run(() => _dataAccess.Execute(sql, testUser));

                // منح صلاحيات أساسية
                var permissionSql = @"
                    INSERT OR IGNORE INTO UserPermissions (UserId, PermissionId, GrantedBy)
                    SELECT 2, Id, 1 FROM Permissions WHERE Code IN ('VIEW_DASHBOARD', 'VIEW_USERS')";
                await Task.Run(() => _dataAccess.Execute(permissionSql));

                Console.WriteLine("   ✅ تم إنشاء مستخدم test (كلمة المرور: test123)");
            }
        }

        /// <summary>
        /// توليد Salt عشوائي
        /// </summary>
        private string GenerateSalt()
        {
            var saltBytes = new byte[32];
            using (var rng = RandomNumberGenerator.Create())
            {
                rng.GetBytes(saltBytes);
            }
            return Convert.ToBase64String(saltBytes);
        }

        /// <summary>
        /// تشفير كلمة المرور
        /// </summary>
        private string HashPassword(string password, string salt)
        {
            using (var sha256 = SHA256.Create())
            {
                var saltedPassword = password + salt;
                var saltedPasswordBytes = Encoding.UTF8.GetBytes(saltedPassword);
                var hashBytes = sha256.ComputeHash(saltedPasswordBytes);
                return Convert.ToBase64String(hashBytes);
            }
        }

        /// <summary>
        /// التحقق من وجود الجداول
        /// </summary>
        public async Task<bool> CheckTablesExistAsync()
        {
            try
            {
                var tables = new[] { "Users", "UserActivities", "Permissions", "UserPermissions", "Notifications" };

                foreach (var table in tables)
                {
                    var sql = $"SELECT name FROM sqlite_master WHERE type='table' AND name='{table}'";
                    var result = await Task.Run(() => _dataAccess.GetAnyDB<string>(sql));

                    if (string.IsNullOrEmpty(result))
                    {
                        Console.WriteLine($"❌ جدول {table} غير موجود");
                        return false;
                    }
                }

                Console.WriteLine("✅ جميع الجداول موجودة");
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في فحص الجداول: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// عرض معلومات قاعدة البيانات
        /// </summary>
        public async Task ShowDatabaseInfoAsync()
        {
            try
            {
                Console.WriteLine("📊 معلومات قاعدة البيانات:");

                // عدد المستخدمين
                var usersCount = await Task.Run(() => _dataAccess.Get_int_FromDB("SELECT COUNT(*) FROM Users"));
                Console.WriteLine($"   👥 عدد المستخدمين: {usersCount}");

                // عدد الأنشطة
                var activitiesCount = await Task.Run(() => _dataAccess.Get_int_FromDB("SELECT COUNT(*) FROM UserActivities"));
                Console.WriteLine($"   📝 عدد الأنشطة: {activitiesCount}");

                // عدد الصلاحيات
                var permissionsCount = await Task.Run(() => _dataAccess.Get_int_FromDB("SELECT COUNT(*) FROM Permissions"));
                Console.WriteLine($"   🔐 عدد الصلاحيات: {permissionsCount}");

                // عدد صلاحيات المستخدمين
                var userPermissionsCount = await Task.Run(() => _dataAccess.Get_int_FromDB("SELECT COUNT(*) FROM UserPermissions"));
                Console.WriteLine($"   🔗 عدد صلاحيات المستخدمين: {userPermissionsCount}");

                // عدد الإشعارات
                var notificationsCount = await Task.Run(() => _dataAccess.Get_int_FromDB("SELECT COUNT(*) FROM Notifications"));
                Console.WriteLine($"   🔔 عدد الإشعارات: {notificationsCount}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في عرض معلومات قاعدة البيانات: {ex.Message}");
            }
        }
    }
}
