using SmartCreator.Entities;
using SmartCreator.RJControls;
using SmartCreator.RJForms;
using SmartCreator.Services;
using SmartCreator.ViewModels;
using System;
using System.Drawing;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace SmartCreator.Forms.Notifications
{
    /// <summary>
    /// نموذج اختبار الإشعارات
    /// </summary>
    public partial class Frm_NotificationTest : RJChildForm
    {
        #region Fields
        private readonly NotificationService _notificationService;
        #endregion

        #region Constructor
        public Frm_NotificationTest()
        {
            InitializeComponent();
            _notificationService = new NotificationService();

            this.FormIcon = FontAwesome.Sharp.IconChar.Flask;

            SetupForm();
            SetupEvents();
        }
        #endregion

        #region Setup Methods
        /// <summary>
        /// إعداد النموذج
        /// </summary>
        private void SetupForm()
        {
            try
            {
                this.Size = new Size(600, 500);
                this.MinimumSize = new Size(500, 400);

                // إعداد ComboBox
                SetupComboBoxes();

                // إعداد الخطوط
                ApplyTheme();
            }
            catch (Exception ex)
            {
                RJMessageBox.Show($"خطأ في إعداد النموذج: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// إعداد ComboBox
        /// </summary>
        private void SetupComboBoxes()
        {
            try
            {
                // إعداد أنواع الإشعارات
                cmbType.Items.Clear();
                cmbType.Items.Add(NotificationTypes.INFO);
                cmbType.Items.Add(NotificationTypes.WARNING);
                cmbType.Items.Add(NotificationTypes.ERROR);
                cmbType.Items.Add(NotificationTypes.SUCCESS);
                cmbType.Items.Add(NotificationTypes.SECURITY);
                cmbType.Items.Add(NotificationTypes.SYSTEM);
                cmbType.Items.Add(NotificationTypes.USER);
                cmbType.Items.Add(NotificationTypes.UPDATE);
                cmbType.SelectedIndex = 0;

                // إعداد مستويات الأهمية
                cmbPriority.Items.Clear();
                cmbPriority.Items.Add(NotificationPriorities.LOW);
                cmbPriority.Items.Add(NotificationPriorities.NORMAL);
                cmbPriority.Items.Add(NotificationPriorities.MEDIUM);
                cmbPriority.Items.Add(NotificationPriorities.HIGH);
                cmbPriority.SelectedIndex = 1; // Normal
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إعداد ComboBox: {ex.Message}");
            }
        }

        /// <summary>
        /// إعداد الأحداث
        /// </summary>
        private void SetupEvents()
        {
            try
            {
                btnCreateInfo.Click += BtnCreateInfo_Click;
                btnCreateWarning.Click += BtnCreateWarning_Click;
                btnCreateError.Click += BtnCreateError_Click;
                btnCreateSuccess.Click += BtnCreateSuccess_Click;
                btnCreateSecurity.Click += BtnCreateSecurity_Click;
                btnCreateCustom.Click += BtnCreateCustom_Click;
                btnCreateMultiple.Click += BtnCreateMultiple_Click;
                btnClearAll.Click += BtnClearAll_Click;
                btnOpenNotifications.Click += BtnOpenNotifications_Click;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إعداد الأحداث: {ex.Message}");
            }
        }

        /// <summary>
        /// تطبيق السمة
        /// </summary>
        private void ApplyTheme()
        {
            try
            {
                var arabicFont = Program.GetCustomFont(Properties.Resources.DroidSansArabic, 9, FontStyle.Regular);
                var boldFont = Program.GetCustomFont(Properties.Resources.DroidKufi_Bold, 10, FontStyle.Bold);

                foreach (Control control in this.Controls)
                {
                    if (control is RJLabel)
                    {
                        if (control.Name.Contains("Title"))
                            control.Font = boldFont;
                        else
                            control.Font = arabicFont;
                    }
                    else if (control is RJButton || control is RJTextBox)
                    {
                        control.Font = arabicFont;
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تطبيق السمة: {ex.Message}");
            }
        }
        #endregion

        #region Event Handlers
        /// <summary>
        /// إنشاء إشعار معلومات
        /// </summary>
        private async void BtnCreateInfo_Click(object sender, EventArgs e)
        {
            await CreateNotificationAsync(
                "إشعار معلومات",
                "هذا إشعار معلومات تجريبي للاختبار",
                NotificationTypes.INFO,
                NotificationPriorities.NORMAL
            );
        }

        /// <summary>
        /// إنشاء إشعار تحذير
        /// </summary>
        private async void BtnCreateWarning_Click(object sender, EventArgs e)
        {
            await CreateNotificationAsync(
                "تحذير مهم",
                "هذا تحذير مهم يتطلب انتباهك",
                NotificationTypes.WARNING,
                NotificationPriorities.MEDIUM
            );
        }

        /// <summary>
        /// إنشاء إشعار خطأ
        /// </summary>
        private async void BtnCreateError_Click(object sender, EventArgs e)
        {
            await CreateNotificationAsync(
                "خطأ في النظام",
                "حدث خطأ في النظام يتطلب تدخل فوري",
                NotificationTypes.ERROR,
                NotificationPriorities.HIGH
            );
        }

        /// <summary>
        /// إنشاء إشعار نجاح
        /// </summary>
        private async void BtnCreateSuccess_Click(object sender, EventArgs e)
        {
            await CreateNotificationAsync(
                "تمت العملية بنجاح",
                "تمت العملية المطلوبة بنجاح",
                NotificationTypes.SUCCESS,
                NotificationPriorities.LOW
            );
        }

        /// <summary>
        /// إنشاء إشعار أمني
        /// </summary>
        private async void BtnCreateSecurity_Click(object sender, EventArgs e)
        {
            await CreateNotificationAsync(
                "تنبيه أمني",
                "تم اكتشاف نشاط مشبوه في النظام",
                NotificationTypes.SECURITY,
                NotificationPriorities.HIGH
            );
        }

        /// <summary>
        /// إنشاء إشعار مخصص
        /// </summary>
        private async void BtnCreateCustom_Click(object sender, EventArgs e)
        {
            if (string.IsNullOrWhiteSpace(txtCustomTitle.Texts) ||
                string.IsNullOrWhiteSpace(txtCustomMessage.Texts))
            {
                RJMessageBox.Show("يرجى إدخال العنوان والرسالة", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var type = cmbType.SelectedItem?.ToString() ?? NotificationTypes.INFO;
            var priority = cmbPriority.SelectedItem?.ToString() ?? NotificationPriorities.NORMAL;

            await CreateNotificationAsync(
                txtCustomTitle.Texts,
                txtCustomMessage.Texts,
                type,
                priority
            );
        }

        /// <summary>
        /// إنشاء إشعارات متعددة
        /// </summary>
        private async void BtnCreateMultiple_Click(object sender, EventArgs e)
        {
            try
            {
                lblStatus.Text = "جاري إنشاء إشعارات متعددة...";

                var notifications = new[]
                {
                    new { Title = "إشعار 1", Message = "رسالة الإشعار الأول", Type = NotificationTypes.INFO, Priority = NotificationPriorities.LOW },
                    new { Title = "إشعار 2", Message = "رسالة الإشعار الثاني", Type = NotificationTypes.WARNING, Priority = NotificationPriorities.MEDIUM },
                    new { Title = "إشعار 3", Message = "رسالة الإشعار الثالث", Type = NotificationTypes.SUCCESS, Priority = NotificationPriorities.NORMAL },
                    new { Title = "إشعار 4", Message = "رسالة الإشعار الرابع", Type = NotificationTypes.ERROR, Priority = NotificationPriorities.HIGH },
                    new { Title = "إشعار 5", Message = "رسالة الإشعار الخامس", Type = NotificationTypes.SECURITY, Priority = NotificationPriorities.HIGH }
                };

                foreach (var notification in notifications)
                {
                    await _notificationService.CreateNotificationAsync(
                        notification.Title,
                        notification.Message,
                        notification.Type,
                        notification.Priority,
                        module: "Test"
                    );
                }

                lblStatus.Text = "تم إنشاء 5 إشعارات بنجاح";
                RJMessageBox.Show("تم إنشاء 5 إشعارات تجريبية بنجاح", "نجح",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                lblStatus.Text = "خطأ في إنشاء الإشعارات";
                RJMessageBox.Show($"خطأ: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// مسح جميع الإشعارات
        /// </summary>
        private async void BtnClearAll_Click(object sender, EventArgs e)
        {
            try
            {
                var result = RJMessageBox.Show(
                    "هل تريد حذف جميع الإشعارات؟",
                    "تأكيد الحذف",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    // هنا يمكن إضافة دالة لحذف جميع الإشعارات
                    lblStatus.Text = "تم حذف جميع الإشعارات";
                    RJMessageBox.Show("تم حذف جميع الإشعارات", "نجح",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                RJMessageBox.Show($"خطأ: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// فتح نموذج الإشعارات
        /// </summary>
        private void BtnOpenNotifications_Click(object sender, EventArgs e)
        {
            try
            {
                var notificationsForm = new Frm_Notifications();
                notificationsForm.ShowDialog();
            }
            catch (Exception ex)
            {
                RJMessageBox.Show($"خطأ: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// إنشاء إشعار
        /// </summary>
        private async Task CreateNotificationAsync(string title, string message, string type, string priority)
        {
            try
            {
                lblStatus.Text = "جاري إنشاء الإشعار...";

                var success = await _notificationService.CreateNotificationAsync(
                    title, message, type, priority, module: "Test");

                if (success)
                {
                    lblStatus.Text = "تم إنشاء الإشعار بنجاح";
                    RJMessageBox.Show("تم إنشاء الإشعار بنجاح", "نجح",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    lblStatus.Text = "فشل في إنشاء الإشعار";
                    RJMessageBox.Show("فشل في إنشاء الإشعار", "خطأ",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                lblStatus.Text = "خطأ في إنشاء الإشعار";
                RJMessageBox.Show($"خطأ: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        #endregion
    }
}
