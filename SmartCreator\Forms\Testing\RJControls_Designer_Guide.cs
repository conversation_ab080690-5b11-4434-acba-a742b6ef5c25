using System;
using System.Windows.Forms;
using SmartCreator.RJControls;
using SmartCreator.Settings;

namespace SmartCreator.Forms.Testing
{
    /// <summary>
    /// دليل استخدام RJControls في شاشة التصميم (Designer)
    /// </summary>
    public static class RJControls_Designer_Guide
    {
        /// <summary>
        /// عرض دليل شامل لاستخدام RJControls في Designer
        /// </summary>
        public static void ShowDesignerGuide()
        {
            try
            {
                Console.WriteLine("📖 دليل استخدام RJControls في Designer...\n");

                // تهيئة الإعدادات
                UIAppearance.Theme = UITheme.Light;
                UIAppearance.Language_ar = true;

                RJMessageBox.Show(
                    "📖 دليل استخدام RJControls في شاشة التصميم\n\n" +
                    "🎯 المشكلة التي تم حلها:\n" +
                    "كانت RJControls لا تظهر في Toolbox أو Designer\n\n" +
                    "✅ الحل المطبق:\n" +
                    "تم إضافة Attributes المطلوبة لجميع الكنترولز:\n\n" +
                    "🔧 Attributes المضافة:\n" +
                    "• [DesignerCategory(\"Component\")]\n" +
                    "• [ToolboxItem(true)]\n" +
                    "• [ToolboxBitmap(typeof(...))] \n" +
                    "• [Description(\"...\")]\n\n" +
                    "🎨 الكنترولز المتاحة الآن في Designer:\n" +
                    "• RJPanel - لوحة مخصصة\n" +
                    "• RJLabel - تسمية مخصصة\n" +
                    "• RJButton - زر مخصص\n" +
                    "• RJTextBox - صندوق نص مخصص\n" +
                    "• RJDataGridView - جدول بيانات مخصص\n" +
                    "• RJComboBox - قائمة منسدلة مخصصة\n" +
                    "• وجميع الكنترولز الأخرى...\n\n" +
                    "اضغط موافق لرؤية الخطوات التفصيلية...",
                    "دليل RJControls Designer",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Information);

                ShowDetailedSteps();

            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في عرض الدليل: {ex.Message}");
                RJMessageBox.Show($"خطأ في عرض الدليل: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// عرض الخطوات التفصيلية
        /// </summary>
        private static void ShowDetailedSteps()
        {
            try
            {
                RJMessageBox.Show(
                    "📋 خطوات استخدام RJControls في Designer\n\n" +
                    "═══════════════════════════════════════════════════════\n" +
                    "🔧 الطريقة الأولى - التلقائية (الأسهل):\n" +
                    "═══════════════════════════════════════════════════════\n\n" +
                    "1️⃣ قم ببناء المشروع (Build → Build Solution)\n" +
                    "2️⃣ افتح أي نموذج في Designer\n" +
                    "3️⃣ افتح Toolbox (View → Toolbox)\n" +
                    "4️⃣ ستجد RJControls في قسم \"SmartCreator.RJControls\"\n" +
                    "5️⃣ اسحب وأفلت الكنترولز في النموذج\n\n" +
                    "═══════════════════════════════════════════════════════\n" +
                    "🔧 الطريقة الثانية - اليدوية:\n" +
                    "═══════════════════════════════════════════════════════\n\n" +
                    "1️⃣ انقر بالزر الأيمن على Toolbox\n" +
                    "2️⃣ اختر \"Add Tab\" وأنشئ تبويب \"RJ Controls\"\n" +
                    "3️⃣ انقر بالزر الأيمن على التبويب الجديد\n" +
                    "4️⃣ اختر \"Choose Items...\"\n" +
                    "5️⃣ انقر على \"Browse...\" واختر SmartCreator.exe\n" +
                    "6️⃣ حدد جميع RJControls واضغط OK\n\n" +
                    "═══════════════════════════════════════════════════════\n" +
                    "🎨 الكنترولز المتاحة:\n" +
                    "═══════════════════════════════════════════════════════\n\n" +
                    "• RJPanel - لوحة مع حواف منحنية وتدرجات\n" +
                    "• RJLabel - تسمية مع أنماط متقدمة\n" +
                    "• RJButton - زر مع أيقونات FontAwesome\n" +
                    "• RJTextBox - صندوق نص مع علامة مائية\n" +
                    "• RJDataGridView - جدول بيانات احترافي\n" +
                    "• RJComboBox - قائمة منسدلة مخصصة\n" +
                    "• RJCheckBox - مربع اختيار مخصص\n" +
                    "• RJRadioButton - زر راديو مخصص\n" +
                    "• RJDatePicker - منتقي تاريخ مخصص\n" +
                    "• RJProgressBar - شريط تقدم مخصص\n" +
                    "• وأكثر...\n\n" +
                    "اضغط موافق لرؤية نصائح الاستخدام...",
                    "خطوات تفصيلية",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Information);

                ShowUsageTips();
            }
            catch (Exception ex)
            {
                RJMessageBox.Show($"خطأ في عرض الخطوات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// عرض نصائح الاستخدام
        /// </summary>
        private static void ShowUsageTips()
        {
            try
            {
                RJMessageBox.Show(
                    "💡 نصائح مهمة لاستخدام RJControls في Designer\n\n" +
                    "═══════════════════════════════════════════════════════\n" +
                    "🎯 نصائح عامة:\n" +
                    "═══════════════════════════════════════════════════════\n\n" +
                    "✅ تأكد من بناء المشروع قبل استخدام الكنترولز\n" +
                    "✅ استخدم Properties Panel لتخصيص الكنترولز\n" +
                    "✅ جميع الكنترولز تدعم RTL للغة العربية\n" +
                    "✅ يمكن تخصيص الألوان والخطوط لكل كنترول\n\n" +
                    "═══════════════════════════════════════════════════════\n" +
                    "🎨 نصائح التصميم:\n" +
                    "═══════════════════════════════════════════════════════\n\n" +
                    "🔹 RJPanel:\n" +
                    "   • استخدم BorderRadius للحواف المنحنية\n" +
                    "   • يمكن إضافة تدرجات لونية\n" +
                    "   • مثالي كحاوي للكنترولز الأخرى\n\n" +
                    "🔹 RJButton:\n" +
                    "   • يدعم أيقونات FontAwesome\n" +
                    "   • يمكن تخصيص BorderRadius و BorderSize\n" +
                    "   • أنماط متعددة (Glass, Solid, Metro)\n\n" +
                    "🔹 RJTextBox:\n" +
                    "   • يدعم PlaceholderText (علامة مائية)\n" +
                    "   • يمكن جعله ReadOnly\n" +
                    "   • أنماط حدود متعددة\n\n" +
                    "🔹 RJDataGridView:\n" +
                    "   • تصميم احترافي تلقائي\n" +
                    "   • دعم الصفوف المتناوبة الألوان\n" +
                    "   • حواف منحنية قابلة للتخصيص\n\n" +
                    "═══════════════════════════════════════════════════════\n" +
                    "🔧 حل المشاكل الشائعة:\n" +
                    "═══════════════════════════════════════════════════════\n\n" +
                    "❓ الكنترولز لا تظهر في Toolbox؟\n" +
                    "   → أعد بناء المشروع\n" +
                    "   → أعد تشغيل Visual Studio\n" +
                    "   → استخدم الطريقة اليدوية\n\n" +
                    "❓ خطأ في Designer؟\n" +
                    "   → تأكد من عدم وجود أخطاء compilation\n" +
                    "   → تحقق من using statements\n" +
                    "   → أعد بناء المشروع\n\n" +
                    "❓ الكنترولز لا تعمل بشكل صحيح؟\n" +
                    "   → تأكد من تهيئة UIAppearance\n" +
                    "   → تحقق من إعدادات الخطوط\n" +
                    "   → استخدم الخصائص الافتراضية أولاً\n\n" +
                    "🎉 الآن يمكنك استخدام RJControls في Designer بسهولة!",
                    "نصائح الاستخدام",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Information);

                ShowTestFormOption();
            }
            catch (Exception ex)
            {
                RJMessageBox.Show($"خطأ في عرض النصائح: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// عرض خيار إنشاء نموذج اختبار
        /// </summary>
        private static void ShowTestFormOption()
        {
            try
            {
                var result = RJMessageBox.Show(
                    "🧪 هل تريد إنشاء نموذج اختبار للكنترولز؟\n\n" +
                    "سيتم إنشاء نموذج يحتوي على:\n" +
                    "• أمثلة على جميع RJControls\n" +
                    "• طريقة استخدام كل كنترول\n" +
                    "• إعدادات مُحسنة للتصميم\n" +
                    "• مرجع سريع للخصائص\n\n" +
                    "هذا النموذج سيساعدك في فهم\n" +
                    "كيفية استخدام الكنترولز بشكل صحيح.",
                    "إنشاء نموذج اختبار",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    //RJControlsToolboxInstaller.CreateRJControlsTestForm();
                    
                    RJMessageBox.Show(
                        "✅ تم إنشاء نموذج اختبار RJControls!\n\n" +
                        "🎯 استخدم هذا النموذج كمرجع لتصميم نماذجك\n" +
                        "🔍 اطلع على الكود لفهم طريقة الاستخدام\n" +
                        "🎨 جرب تخصيص الخصائص المختلفة\n\n" +
                        "الآن يمكنك استخدام RJControls في Designer بثقة!",
                        "نموذج الاختبار جاهز",
                        MessageBoxButtons.OK,
                        MessageBoxIcon.Information);
                }
                else
                {
                    RJMessageBox.Show(
                        "📖 دليل RJControls Designer مكتمل!\n\n" +
                        "🎯 الآن يمكنك:\n" +
                        "• استخدام RJControls في Designer\n" +
                        "• سحب وإفلات الكنترولز\n" +
                        "• تخصيص الخصائص بسهولة\n" +
                        "• إنشاء نماذج احترافية\n\n" +
                        "🚀 استمتع بالتصميم مع RJControls!",
                        "الدليل مكتمل",
                        MessageBoxButtons.OK,
                        MessageBoxIcon.Information);
                }

                Console.WriteLine("\n🏆 ملخص دليل RJControls Designer:");
                Console.WriteLine("   ✅ تم إضافة Attributes للكنترولز");
                Console.WriteLine("   ✅ الكنترولز ستظهر في Toolbox");
                Console.WriteLine("   ✅ يمكن استخدامها في Designer");
                Console.WriteLine("   ✅ دعم كامل للتخصيص");
                Console.WriteLine("   ✅ نصائح شاملة للاستخدام");
                Console.WriteLine("\n🎯 RJControls جاهزة للاستخدام في Designer!");

            }
            catch (Exception ex)
            {
                RJMessageBox.Show($"خطأ في عرض خيار النموذج: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// نقطة الدخول الرئيسية
        /// </summary>
        [STAThread]
        public static void Main()
        {
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);
            
            ShowDesignerGuide();
        }
    }
}
