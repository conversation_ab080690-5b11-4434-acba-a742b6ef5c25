﻿using iTextSharp.text;
using SmartCreator.Data;
using SmartCreator.Entities;
using SmartCreator.Models;
using SmartCreator.Properties;
using SmartCreator.RJControls;
using SmartCreator.RJForms;
using SmartCreator.Services;
using SmartCreator.Services.Security;
using SmartCreator.Settings;
using SmartCreator.Utils;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace SmartCreator.Forms.Security
{
    public partial class Frm_Users : RJChildForm
    {
        string Filter_Type = "Active";
        bool FirstLoad = true;
        List<Acive_Host_Users> ActiveUsers = null;
        List<Host_Users> HostUsers = null;


        private readonly UserManagementService _userService;
        private readonly PermissionService _permissionService;
        private readonly ActivityService _activityService;
        private List<User> _users;
        private List<Permission> _permissions;
        private User _selectedUser;
        private bool _isLoadingPermissions = false;


        public Frm_Users()
        {
            try
            {
                InitializeComponent();

                _userService = new UserManagementService();
                _permissionService = new PermissionService(new Smart_DataAccess(), new UserActivityService(new Smart_DataAccess()));
                _activityService = new ActivityService();
                InitializeForm();
                //this.RightToLeft = RightToLeft.No;
                //this.RightToLeftLayout = false;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إنشاء النموذج: {ex.Message}\n\nتفاصيل الخطأ: {ex.StackTrace}",
                    "خطأ في الإنشاء", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }

            btnAddUser.Font = btnClose.Font=btnDeleteUser.Font=btnEditUser.Font=btnRefresh.Font = Program.GetCustomFont(Resources.DroidKufi_Bold, 9f, FontStyle.Bold);


            utils utils = new utils();
            utils.Control_textSize1(this);

            if (UIAppearance.DGV_RTL == false)
                dgvUsers.RightToLeft = RightToLeft.No;


        }
        private void InitializeForm()
        {
            try
            {
                // إعداد DataGridView للمستخدمين
                SetupUsersDataGridView();

                // إعداد DataGridView للأنشطة
                //SetupActivitiesDataGridView();

                // إعداد الأحداث
                SetupEvents();

                // تحميل البيانات
                LoadDataAsync();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تهيئة النموذج: {ex.Message}\n\nتفاصيل الخطأ: {ex.StackTrace}",
                    "خطأ في التهيئة", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        /// <summary>
        /// إعداد الأحداث
        /// </summary>
        private void SetupEvents()
        {
            //// أحداث المستخدمين
            //btnAddUser.Click += BtnAddUser_Click;
            //btnEditUser.Click += BtnEditUser_Click;
            //btnDeleteUser.Click += BtnDeleteUser_Click;
            //btnRefresh.Click += BtnRefresh_Click;
            //dgvUsers.SelectionChanged += DgvUsers_SelectionChanged;
            //txtSearchUsers.TextChanged += TxtSearchUsers_TextChanged;

            //// أحداث الصلاحيات
            //btnSavePermissions.Click += BtnSavePermissions_Click;
            //btnCancelPermissions.Click += BtnCancelPermissions_Click;
            //treePermissions.AfterCheck += TreePermissions_AfterCheck;

            //// أحداث الأنشطة
            //btnFilterActivities.Click += BtnFilterActivities_Click;
            //btnExportActivities.Click += BtnExportActivities_Click;
            //if (btnViewUserActivities != null)
            //    btnViewUserActivities.Click += BtnViewUserActivities_Click;

            //// إعداد التواريخ الافتراضية
            //dtpFromDate.Value = DateTime.Now.AddDays(-30);
            //dtpToDate.Value = DateTime.Now;
        }
        private void timer1_Tick(object sender, EventArgs e)
        {
            timer1.Stop();

            LoadDataAsync();

        }

        private void Frm_Users_Load(object sender, EventArgs e)
        {
            timer1.Start();
        }

        /// <summary>
        /// تحميل البيانات
        /// </summary>
        private async void LoadDataAsync()
        {

            try
            {
                // تحميل المستخدمين
                await LoadUsersAsync();

                // تحميل الصلاحيات
                //await LoadPermissionsAsync();

                // تحميل وحدات الأنشطة
                //LoadActivityModules();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        /// <summary>
        /// تحميل المستخدمين
        /// </summary>
        private async Task LoadUsersAsync()
        {
            try
            {
                _users = await _userService.GetAllUsersAsync().ConfigureAwait(false);

                // التأكد من تحديث UI في الـ UI thread
                if (this.InvokeRequired)
                {
                    this.Invoke(new Action(() =>
                    {
                        dgvUsers.DataSource = _users;
                        UpdateButtonStates();
                    }));
                }
                else
                {
                    dgvUsers.DataSource = _users;
                    UpdateButtonStates();
                }
            }
            catch (Exception ex)
            {
                // التأكد من عرض الرسالة في الـ UI thread
                if (this.InvokeRequired)
                {
                    this.Invoke(new Action(() =>
                    {
                        MessageBox.Show($"خطأ في تحميل المستخدمين: {ex.Message}", "خطأ",
                            MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }));
                }
                else
                {
                    MessageBox.Show($"خطأ في تحميل المستخدمين: {ex.Message}", "خطأ",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        /// إعداد DataGridView للمستخدمين
        /// </summary>
        private void SetupUsersDataGridView()
        {
            try
            {
                // إعداد التنسيق الأساسي
                ApplyBasicDataGridViewStyling(dgvUsers);

                // إعداد الأعمدة للمستخدمين
                SetupUsersColumns();

                // إضافة تأثيرات التفاعل
                AddDataGridViewInteractionEffects(dgvUsers);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إعداد جدول المستخدمين: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        /// <summary>
        /// إضافة تأثيرات التفاعل للـ DataGridView
        /// </summary>
        private void AddDataGridViewInteractionEffects(RJControls.RJDataGridView dgv)
        {
            // تأثير عند مرور الماوس
            dgv.CellMouseEnter += (sender, e) =>
            {
                if (e.RowIndex >= 0)
                {
                    dgv.Rows[e.RowIndex].DefaultCellStyle.BackColor = Color.FromArgb(240, 245, 249);
                }
            };

            dgv.CellMouseLeave += (sender, e) =>
            {
                if (e.RowIndex >= 0)
                {
                    dgv.Rows[e.RowIndex].DefaultCellStyle.BackColor =
                        e.RowIndex % 2 == 0 ? Color.White : Color.FromArgb(248, 249, 250);
                }
            };
        }

        /// <summary>
        /// تطبيق التنسيق الأساسي على RJDataGridView
        /// </summary>
        private void ApplyBasicDataGridViewStyling(RJControls.RJDataGridView dgv)
        {
            // الإعدادات الأساسية
            dgv.AllowUserToAddRows = false;
            dgv.AllowUserToDeleteRows = false;
            dgv.ReadOnly = true;
            dgv.MultiSelect = false;
            dgv.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dgv.RightToLeft = RightToLeft.Yes;

            // إعدادات المظهر
            dgv.BackgroundColor = Color.White;
            dgv.BorderStyle = BorderStyle.None;
            dgv.CellBorderStyle = DataGridViewCellBorderStyle.SingleHorizontal;
            dgv.ColumnHeadersBorderStyle = DataGridViewHeaderBorderStyle.None;
            dgv.EnableHeadersVisualStyles = false;
            dgv.GridColor = Color.FromArgb(229, 226, 244);
            dgv.RowHeadersVisible = false;

            // إعدادات الصفوف
            dgv.RowTemplate.Height = 35;
            dgv.AlternatingRowsDefaultCellStyle.BackColor = Color.FromArgb(248, 249, 250);

            // إعدادات رؤوس الأعمدة
            dgv.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(123, 104, 238);
            dgv.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            //dgv.ColumnHeadersDefaultCellStyle.Font = new System.Drawing.Font("Segoe UI", 10F, FontStyle.Bold);
            dgv.ColumnHeadersDefaultCellStyle.SelectionBackColor = Color.FromArgb(123, 104, 238);
            dgv.ColumnHeadersDefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dgv.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            dgv.ColumnHeadersHeight = 40;

            // إعدادات الخلايا
            dgv.DefaultCellStyle.BackColor = Color.White;
            dgv.DefaultCellStyle.ForeColor = Color.FromArgb(64, 64, 64);
            //dgv.DefaultCellStyle.Font = new System.Drawing.Font("Segoe UI", 9F);
            dgv.DefaultCellStyle.SelectionBackColor = Color.FromArgb(229, 226, 244);
            dgv.DefaultCellStyle.SelectionForeColor = Color.FromArgb(64, 64, 64);
            dgv.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dgv.DefaultCellStyle.Padding = new Padding(5);
        }

        /// <summary>
        /// إعداد أعمدة المستخدمين
        /// </summary>
        private void SetupUsersColumns()
        {
            dgvUsers.AutoGenerateColumns = false;
            dgvUsers.Columns.Clear();

            dgvUsers.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Id",
                HeaderText = "المعرف",
                DataPropertyName = "Id",
                Visible = false
            });

            dgvUsers.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Username",
                HeaderText = "اسم المستخدم",
                DataPropertyName = "Username",
                Width = 120,
                MinimumWidth = 100
            });

            dgvUsers.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "FullName",
                HeaderText = "الاسم الكامل",
                DataPropertyName = "FullName",
                Width = 150,
                MinimumWidth = 120
            });

            dgvUsers.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Email",
                HeaderText = "البريد الإلكتروني",
                DataPropertyName = "Email",
                Width = 180,
                MinimumWidth = 150
            });

            dgvUsers.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Role",
                HeaderText = "الدور",
                DataPropertyName = "Role",
                Width = 100,
                MinimumWidth = 80
            });

            dgvUsers.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "IsActive",
                HeaderText = "الحالة",
                DataPropertyName = "IsActive",
                Width = 80,
                MinimumWidth = 60
            });

            dgvUsers.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "LastLogin",
                HeaderText = "آخر دخول",
                DataPropertyName = "LastLogin",
                Width = 130,
                MinimumWidth = 110,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "dd/MM/yyyy HH:mm" }
            });

            dgvUsers.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "CreatedAt",
                HeaderText = "تاريخ الإنشاء",
                DataPropertyName = "CreatedAt",
                Width = 130,
                MinimumWidth = 110,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "dd/MM/yyyy" }
            });

            // تخصيص عرض الحالة
            dgvUsers.CellFormatting += DgvUsers_CellFormatting;
        }
        /// <summary>
        /// تنسيق خلايا المستخدمين
        /// </summary>
        private void DgvUsers_CellFormatting(object sender, DataGridViewCellFormattingEventArgs e)
        {
            if (e.ColumnIndex == dgvUsers.Columns["IsActive"].Index && e.Value != null)
            {
                bool isActive = Convert.ToBoolean(e.Value);
                e.Value = isActive ? "نشط" : "غير نشط";
                e.CellStyle.ForeColor = isActive ? Color.FromArgb(40, 167, 69) : Color.FromArgb(220, 53, 69);
                e.FormattingApplied = true;
            }
        }

        /// <summary>
        /// تحديث حالة الأزرار
        /// </summary>
        private void UpdateButtonStates()
        {
            var hasSelection = dgvUsers.SelectedRows.Count > 0;
            btnEditUser.Enabled = hasSelection;
            btnDeleteUser.Enabled = hasSelection;
        }
        #region أحداث المستخدمين

        /// <summary>
        /// إضافة مستخدم جديد
        /// </summary>
        private async void BtnAddUser_Click(object sender, EventArgs e)
        {
            try
            {
                var addForm = new Frm_AddEditUser(_userService);
                if (addForm.ShowDialog() == DialogResult.OK)
                {
                    await LoadUsersAsync();
                    MessageBox.Show("تم إضافة المستخدم بنجاح", "نجح",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إضافة المستخدم: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تعديل مستخدم
        /// </summary>
        private async void BtnEditUser_Click(object sender, EventArgs e)
        {
            if (_selectedUser == null) return;

            try
            {
                var editForm = new Frm_AddEditUser(_userService, _selectedUser);
                if (editForm.ShowDialog() == DialogResult.OK)
                {
                    await LoadUsersAsync();
                    MessageBox.Show("تم تعديل المستخدم بنجاح", "نجح",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تعديل المستخدم: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// حذف مستخدم
        /// </summary>
        private async void BtnDeleteUser_Click(object sender, EventArgs e)
        {
            if (_selectedUser == null) return;

            var result = MessageBox.Show(
                $"هل أنت متأكد من حذف المستخدم '{_selectedUser.Username}'؟\nسيتم حذف جميع صلاحياته أيضاً.",
                "تأكيد الحذف", MessageBoxButtons.YesNo, MessageBoxIcon.Warning);

            if (result == DialogResult.Yes)
            {
                try
                {
                    await _userService.DeleteUserAsync(_selectedUser.Id, Global_Variable.CurrentUser.Id);
                    await LoadUsersAsync();
                    MessageBox.Show("تم حذف المستخدم بنجاح", "نجح",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في حذف المستخدم: {ex.Message}", "خطأ",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        /// <summary>
        /// تحديث البيانات
        /// </summary>
        private async void BtnRefresh_Click(object sender, EventArgs e)
        {
            LoadDataAsync();
        }

        /// <summary>
        /// تغيير تحديد المستخدم
        /// </summary>
        private async void DgvUsers_SelectionChanged(object sender, EventArgs e)
        {
            if (dgvUsers.SelectedRows.Count > 0)
            {
                var selectedRow = dgvUsers.SelectedRows[0];
                _selectedUser = selectedRow.DataBoundItem as User;

                if (_selectedUser != null)
                {
                    //lblSelectedUser.Text = $"المستخدم المحدد: {_selectedUser.DisplayName}";
                    //await LoadUserPermissionsAsync();
                    //await LoadUserActivitiesAsync();
                }
            }
            else
            {
                _selectedUser = null;
                //lblSelectedUser.Text = "لم يتم تحديد مستخدم";
                //ClearPermissionsTree();
                //dgvActivities.DataSource = null;
            }

            UpdateButtonStates();
        }
        /// <summary>
        /// فتح نموذج إدارة صلاحيات المستخدم
        /// </summary>
        private async void BtnPermissions_Click(object sender, EventArgs e)
        {
            if (_selectedUser == null)
            {
                MessageBox.Show("يرجى تحديد مستخدم أولاً", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            try
            {
                var permissionsForm = new Frm_UserPermissions(_permissionService, _selectedUser);
                if (permissionsForm.ShowDialog() == DialogResult.OK)
                {
                    MessageBox.Show("تم تحديث صلاحيات المستخدم بنجاح", "نجح التحديث",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);

                    // إعادة تحميل صلاحيات المستخدم في الشجرة
                    //await LoadUserPermissionsAsync();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح نموذج الصلاحيات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// البحث في المستخدمين
        /// </summary>
        private void TxtSearchUsers_TextChanged(object sender, EventArgs e)
        {
            // تطبيق فلتر البحث
            //ApplyDataGridViewSearchFilter(dgvUsers, txtSearchUsers.Text);
        }

        #endregion
    }
}
