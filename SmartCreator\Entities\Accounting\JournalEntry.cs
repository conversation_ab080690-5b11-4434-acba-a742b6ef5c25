﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SmartCreator.Entities.Accounting
{
    /// <summary>
    /// نموذج القيد اليومي
    /// </summary>
    public class JournalEntry:BaseEntity
    {
        //public int Id { get; set; }
        public string EntryNumber { get; set; } = string.Empty;
        public DateTime Date { get; set; }
        public string Description { get; set; } = string.Empty;
        public string Reference { get; set; } = string.Empty;
        public JournalEntryType Type { get; set; }
        public JournalEntryStatus Status { get; set; } = JournalEntryStatus.Draft;
        public decimal TotalDebit { get; set; }
        public decimal TotalCredit { get; set; }
        public bool IsBalanced => Math.Abs(TotalDebit - TotalCredit) < 0.01m;
        public int? SourceId { get; set; }
        public string SourceType { get; set; } = string.Empty;
        //public string CreatedBy { get; set; } = string.Empty;
        //public DateTime CreatedDate { get; set; }
        //public string? UpdatedBy { get; set; }
        //public DateTime? UpdatedDate { get; set; }
        public string? ApprovedBy { get; set; }
        public DateTime? ApprovedDate { get; set; }
        public string Notes { get; set; } = string.Empty;

        // Navigation Properties
        public List<JournalEntryDetail> Details { get; set; } = new List<JournalEntryDetail>();

        // Helper Methods
        public void AddDetail(int accountId, decimal debitAmount, decimal creditAmount, string description = "")
        {
            Details.Add(new JournalEntryDetail
            {
                JournalEntryId = Id,
                AccountId = accountId,
                DebitAmount = debitAmount,
                CreditAmount = creditAmount,
                Description = description
            });

            RecalculateTotals();
        }

        public void RecalculateTotals()
        {
            TotalDebit = Details.Sum(d => d.DebitAmount);
            TotalCredit = Details.Sum(d => d.CreditAmount);
        }
    }

    /// <summary>
    /// تفاصيل القيد اليومي
    /// </summary>
    public class JournalEntryDetail : BaseEntity
    {
        //public int Id { get; set; }
        public int JournalEntryId { get; set; }
        public int AccountId { get; set; }
        public decimal DebitAmount { get; set; }
        public decimal CreditAmount { get; set; }
        public string Description { get; set; } = string.Empty;
        public int LineNumber { get; set; }

        // Navigation Properties
        public JournalEntry? JournalEntry { get; set; }
        public Account? Account { get; set; }

        // Computed Properties
        public decimal Amount => DebitAmount > 0 ? DebitAmount : CreditAmount;
        public bool IsDebit => DebitAmount > 0;
        public bool IsCredit => CreditAmount > 0;
    }

    /// <summary>
    /// أنواع القيود اليومية
    /// </summary>
    public enum JournalEntryType
    {
        Manual = 1,           // قيد يدوي
        Sales = 2,            // قيد مبيعات
        Purchase = 3,         // قيد مشتريات
        Payment = 4,          // قيد دفع
        Receipt = 5,          // قيد قبض
        Expense = 6,          // قيد مصروف
        Adjustment = 7,       // قيد تسوية
        Opening = 8,          // قيد افتتاحي
        Closing = 9,          // قيد إقفال
        Transfer = 10         // قيد تحويل
    }

    /// <summary>
    /// حالات القيد اليومي
    /// </summary>
    public enum JournalEntryStatus
    {
        Draft = 1,      // مسودة
        Posted = 2,     // مرحل
        Approved = 3,   // معتمد
        Cancelled = 4   // ملغي
    }

    /// <summary>
    /// معلومات إضافية عن أنواع القيود
    /// </summary>
    public static class JournalEntryTypeInfo
    {
        public static readonly Dictionary<JournalEntryType, (string ArabicName, string EnglishName, string Prefix)> Types =
            new Dictionary<JournalEntryType, (string, string, string)>
            {
                { JournalEntryType.Manual, ("قيد يدوي", "Manual Entry", "MAN") },
                { JournalEntryType.Sales, ("قيد مبيعات", "Sales Entry", "SAL") },
                { JournalEntryType.Purchase, ("قيد مشتريات", "Purchase Entry", "PUR") },
                { JournalEntryType.Payment, ("قيد دفع", "Payment Entry", "PAY") },
                { JournalEntryType.Receipt, ("قيد قبض", "Receipt Entry", "REC") },
                { JournalEntryType.Expense, ("قيد مصروف", "Expense Entry", "EXP") },
                { JournalEntryType.Adjustment, ("قيد تسوية", "Adjustment Entry", "ADJ") },
                { JournalEntryType.Opening, ("قيد افتتاحي", "Opening Entry", "OPN") },
                { JournalEntryType.Closing, ("قيد إقفال", "Closing Entry", "CLS") },
                { JournalEntryType.Transfer, ("قيد تحويل", "Transfer Entry", "TRF") }
            };

        public static string GetArabicName(JournalEntryType type) => Types[type].ArabicName;
        public static string GetEnglishName(JournalEntryType type) => Types[type].EnglishName;
        public static string GetPrefix(JournalEntryType type) => Types[type].Prefix;
    }

    /// <summary>
    /// معلومات إضافية عن حالات القيود
    /// </summary>
    public static class JournalEntryStatusInfo
    {
        public static readonly Dictionary<JournalEntryStatus, (string ArabicName, string EnglishName)> Statuses =
            new Dictionary<JournalEntryStatus, (string, string)>
            {
                { JournalEntryStatus.Draft, ("مسودة", "Draft") },
                { JournalEntryStatus.Posted, ("مرحل", "Posted") },
                { JournalEntryStatus.Approved, ("معتمد", "Approved") },
                { JournalEntryStatus.Cancelled, ("ملغي", "Cancelled") }
            };

        public static string GetArabicName(JournalEntryStatus status) => Statuses[status].ArabicName;
        public static string GetEnglishName(JournalEntryStatus status) => Statuses[status].EnglishName;
    }

}
