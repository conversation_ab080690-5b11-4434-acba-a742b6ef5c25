using SmartCreator.Entities.Accounting;
using SmartCreator.RJControls;
using SmartCreator.RJForms;
using SmartCreator.Services.Products;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace SmartCreator.Forms._Accounting.Product
{
    public partial class Frm_ProductView : RJChildForm
    {
        private readonly ProductService _productService;
        private readonly int _productId;
        private Entities.Accounting.Product _product;

        public Frm_ProductView(int productId)
        {
            InitializeComponent();
            _productService = new ProductService();
            _productId = productId;
            InitializeForm();
        }

        private void InitializeForm()
        {
            this.Text = "عرض تفاصيل المنتج";
            this.Size = new Size(900, 700);
            this.StartPosition = FormStartPosition.CenterParent;
            
            // تهيئة الأحداث
            this.Load += Frm_ProductView_Load;
            
            // تهيئة أحداث الأزرار
            btnEdit.Click += BtnEdit_Click;
            btnClose.Click += BtnClose_Click;
            btnPrint.Click += BtnPrint_Click;
            btnUpdateStock.Click += BtnUpdateStock_Click;
        }

        private async void Frm_ProductView_Load(object sender, EventArgs e)
        {
            await LoadProductAsync();
        }

        private async Task LoadProductAsync()
        {
            try
            {
                ShowLoading(true);
                
                _product = await _productService.GetProductByIdAsync(_productId);
                if (_product == null)
                {
                    RJMessageBox.Show("المنتج غير موجود", "خطأ",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                    this.Close();
                    return;
                }

                LoadProductToForm(_product);
            }
            catch (Exception ex)
            {
                RJMessageBox.Show($"خطأ في تحميل بيانات المنتج: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                this.Close();
            }
            finally
            {
                ShowLoading(false);
            }
        }

        private void LoadProductToForm(Entities.Accounting.Product product)
        {
            // المعلومات الأساسية
            lblCodeValue.Text = product.Code ?? "غير محدد";
            lblNameValue.Text = product.Name ?? "غير محدد";
            lblDescriptionValue.Text = product.Description ?? "غير محدد";
            lblPriceValue.Text = product.Price.ToString("N2") + " ريال";
            lblStockValue.Text = product.Stock.ToString();
            lblLowStockThresholdValue.Text = product.LowStockThreshold.ToString();
            lblActiveValue.Text = product.Str_Active ? "مفعل" : "غير مفعل";
            lblActiveValue.ForeColor = product.Str_Active ? Color.LightGreen : Color.LightCoral;
            
            // حالة المخزون
            lblStockStatusValue.Text = product.StockStatus;
            switch (product.StockStatus)
            {
                case "نفد المخزون":
                    lblStockStatusValue.ForeColor = Color.Red;
                    break;
                case "مخزون منخفض":
                    lblStockStatusValue.ForeColor = Color.Orange;
                    break;
                default:
                    lblStockStatusValue.ForeColor = Color.LightGreen;
                    break;
            }
            
            // معلومات التتبع
            lblLastUpdateValue.Text = product.LastStockUpdate?.ToString("yyyy/MM/dd HH:mm") ?? "غير محدد";
            lblLastUpdateByValue.Text = product.LastUpdateBy ?? "غير محدد";
            lblUpdateReasonValue.Text = product.StockUpdateReason ?? "غير محدد";
            
            // الحسابات المرتبطة
            lblIncomeAccountValue.Text = product.Account_IncomeId?.ToString() ?? "غير محدد";
            lblExpenseAccountValue.Text = product.Account_ExpenseId?.ToString() ?? "غير محدد";
            lblUomValue.Text = product.Product_UomId?.ToString() ?? "غير محدد";
            
            // إحصائيات
            lblTotalValueValue.Text = (product.Price * product.Stock).ToString("N2") + " ريال";
            lblNeedsReorderValue.Text = product.NeedsReorder ? "نعم" : "لا";
            lblNeedsReorderValue.ForeColor = product.NeedsReorder ? Color.Orange : Color.LightGreen;
            
            // تحديث حالة الأزرار
            UpdateButtonStates();
        }

        private void UpdateButtonStates()
        {
            bool isActive = _product?.Str_Active ?? false;
            btnEdit.Enabled = true;
            btnUpdateStock.Enabled = isActive;
            btnPrint.Enabled = true;
        }

        private void ShowLoading(bool show)
        {
            if (show)
            {
                Cursor = Cursors.WaitCursor;
                pnlMain.Enabled = false;
                pnlButtons.Enabled = false;
            }
            else
            {
                Cursor = Cursors.Default;
                pnlMain.Enabled = true;
                pnlButtons.Enabled = true;
            }
        }

        private async void BtnEdit_Click(object sender, EventArgs e)
        {
            if (_product == null) return;
            
            var editForm = new Frm_ProductEdit(_product.Id);
            if (editForm.ShowDialog() == DialogResult.OK)
            {
                await LoadProductAsync(); // إعادة تحميل البيانات بعد التعديل
            }
        }

        private void BtnClose_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void BtnPrint_Click(object sender, EventArgs e)
        {
            // TODO: تنفيذ الطباعة
            RJMessageBox.Show("سيتم تنفيذ الطباعة قريباً", "معلومات",
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private async void BtnUpdateStock_Click(object sender, EventArgs e)
        {
            if (_product == null) return;
            
            var updateForm = new Frm_InventoryUpdate(_product.Id);
            if (updateForm.ShowDialog() == DialogResult.OK)
            {
                await LoadProductAsync(); // إعادة تحميل البيانات بعد تحديث المخزون
            }
        }
    }
}
