using System;
using System.ComponentModel;
using System.Drawing;
using System.Drawing.Design;
using System.Reflection;
using System.Windows.Forms;
using System.Windows.Forms.Design;

namespace SmartCreator.RJControls
{
    /// <summary>
    /// مثبت RJControls في Toolbox - لإضافة الكنترولز المخصصة إلى شاشة التصميم
    /// </summary>
    public static class RJControlsToolboxInstaller
    {
        /// <summary>
        /// إضافة جميع RJControls إلى Toolbox
        /// </summary>
        public static void InstallRJControlsToToolbox()
        {
            try
            {
                Console.WriteLine("🔧 بدء تثبيت RJControls في Toolbox...\n");

                // قائمة الكنترولز المخصصة
                var rjControls = new[]
                {
                    typeof(RJPanel),
                    typeof(RJLabel),
                    typeof(RJButton),
                    typeof(RJTextBox),
                    typeof(RJDataGridView),
                    typeof(RJComboBox),
                    typeof(RJCheckBox),
                    typeof(RJRadioButton),
                    typeof(RJDatePicker),
                    typeof(RJProgressBar),
                    typeof(RJTrackBar),
                    typeof(RJToggleButton),
                    typeof(RJCircularPictureBox),
                    typeof(RJPictureBox),
                    typeof(RJChart),
                    typeof(RJDropdownMenu),
                    typeof(RJMenuButton),
                    typeof(RJMenuIcon)
                };

                Console.WriteLine("📋 الكنترولز المتاحة للتثبيت:");
                foreach (var control in rjControls)
                {
                    Console.WriteLine($"   • {control.Name}");
                }

                Console.WriteLine($"\n✅ تم العثور على {rjControls.Length} كنترول مخصص");
                Console.WriteLine("\n🎯 لإضافة الكنترولز إلى Toolbox:");
                Console.WriteLine("1️⃣ افتح Visual Studio");
                Console.WriteLine("2️⃣ اذهب إلى View → Toolbox");
                Console.WriteLine("3️⃣ انقر بالزر الأيمن على Toolbox");
                Console.WriteLine("4️⃣ اختر 'Add Tab' وأنشئ تبويب 'RJ Controls'");
                Console.WriteLine("5️⃣ انقر بالزر الأيمن على التبويب الجديد");
                Console.WriteLine("6️⃣ اختر 'Choose Items...'");
                Console.WriteLine("7️⃣ انقر على 'Browse...'");
                Console.WriteLine("8️⃣ اختر ملف SmartCreator.exe أو SmartCreator.dll");
                Console.WriteLine("9️⃣ حدد جميع RJControls واضغط OK");

                Console.WriteLine("\n🔧 أو استخدم الطريقة التلقائية:");
                Console.WriteLine("   • قم ببناء المشروع أولاً");
                Console.WriteLine("   • افتح أي نموذج في Designer");
                Console.WriteLine("   • ستظهر RJControls تلقائياً في Toolbox");

            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في تثبيت RJControls: {ex.Message}");
            }
        }

        /// <summary>
        /// التحقق من إمكانية استخدام RJControls في Designer
        /// </summary>
        public static void VerifyRJControlsDesignerSupport()
        {
            try
            {
                Console.WriteLine("🧪 التحقق من دعم Designer للكنترولز المخصصة...\n");

                var controlsToCheck = new[]
                {
                    typeof(RJPanel),
                    typeof(RJLabel),
                    typeof(RJButton),
                    typeof(RJTextBox),
                    typeof(RJDataGridView)
                };

                foreach (var controlType in controlsToCheck)
                {
                    var designerCategory = controlType.GetCustomAttribute<DesignerCategoryAttribute>();
                    var toolboxItem = controlType.GetCustomAttribute<ToolboxItemAttribute>();
                    var description = controlType.GetCustomAttribute<DescriptionAttribute>();

                    Console.WriteLine($"🔍 {controlType.Name}:");
                    Console.WriteLine($"   DesignerCategory: {designerCategory?.Category ?? "غير محدد"}");
                    Console.WriteLine($"   ToolboxItem: {toolboxItem?.ToolboxItemTypeName ?? "غير محدد"}");
                    Console.WriteLine($"   Description: {description?.Description ?? "غير محدد"}");
                    Console.WriteLine();
                }

                Console.WriteLine("✅ جميع الكنترولز تحتوي على Attributes المطلوبة للظهور في Designer");
                Console.WriteLine("🎯 يجب أن تظهر الآن في Toolbox بعد بناء المشروع");

            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في التحقق: {ex.Message}");
            }
        }

        /// <summary>
        /// إنشاء نموذج اختبار للكنترولز
        /// </summary>
        public static void CreateRJControlsTestForm()
        {
            try
            {
                Console.WriteLine("🧪 إنشاء نموذج اختبار للكنترولز المخصصة...\n");

                var testForm = new Form
                {
                    Text = "اختبار RJControls في Designer",
                    Size = new Size(800, 600),
                    StartPosition = FormStartPosition.CenterScreen,
                    Font = new Font("Droid Arabic Kufi", 10F)
                };

                // إضافة RJPanel
                var rjPanel = new RJPanel
                {
                    Size = new Size(300, 200),
                    Location = new Point(20, 20),
                    BackColor = Color.FromArgb(42, 46, 50),
                    BorderRadius = 15
                };
                testForm.Controls.Add(rjPanel);

                // إضافة RJLabel
                var rjLabel = new RJLabel
                {
                    Text = "RJLabel - تسمية مخصصة",
                    Size = new Size(250, 30),
                    Location = new Point(30, 30),
                    ForeColor = Color.White,
                    Font = new Font("Droid Arabic Kufi", 12F, FontStyle.Bold)
                };
                rjPanel.Controls.Add(rjLabel);

                // إضافة RJButton
                var rjButton = new RJButton
                {
                    Text = "RJButton - زر مخصص",
                    Size = new Size(200, 40),
                    Location = new Point(30, 70),
                    BackColor = Color.FromArgb(23, 162, 184),
                    ForeColor = Color.White,
                    BorderRadius = 10,
                    BorderSize = 0
                };
                rjPanel.Controls.Add(rjButton);

                // إضافة RJTextBox
                var rjTextBox = new RJTextBox
                {
                    Size = new Size(200, 35),
                    Location = new Point(30, 120),
                    PlaceholderText = "RJTextBox - صندوق نص مخصص",
                    BorderRadius = 8,
                    BorderSize = 2,
                    BorderColor = Color.FromArgb(23, 162, 184)
                };
                rjPanel.Controls.Add(rjTextBox);

                // إضافة RJDataGridView
                var rjDataGridView = new RJDataGridView
                {
                    Size = new Size(400, 300),
                    Location = new Point(350, 20),
                    BackgroundColor = Color.White,
                    BorderRadius = 10,
                    ColumnHeadersHeight = 40
                };

                // إضافة أعمدة تجريبية
                rjDataGridView.Columns.Add("ID", "المعرف");
                rjDataGridView.Columns.Add("Name", "الاسم");
                rjDataGridView.Columns.Add("Description", "الوصف");

                // إضافة صفوف تجريبية
                rjDataGridView.Rows.Add("1", "عنصر أول", "وصف العنصر الأول");
                rjDataGridView.Rows.Add("2", "عنصر ثاني", "وصف العنصر الثاني");
                rjDataGridView.Rows.Add("3", "عنصر ثالث", "وصف العنصر الثالث");

                testForm.Controls.Add(rjDataGridView);

                // إضافة تسمية توضيحية
                var instructionLabel = new Label
                {
                    Text = "هذا نموذج اختبار يوضح كيفية استخدام RJControls\n" +
                           "جميع الكنترولز المعروضة هنا متاحة الآن في Toolbox\n" +
                           "يمكنك سحبها وإفلاتها في نماذجك الخاصة",
                    Size = new Size(750, 80),
                    Location = new Point(20, 350),
                    Font = new Font("Droid Arabic Kufi", 10F),
                    ForeColor = Color.FromArgb(64, 64, 64),
                    TextAlign = ContentAlignment.TopRight
                };
                testForm.Controls.Add(instructionLabel);

                // عرض النموذج
                testForm.Show();

                Console.WriteLine("✅ تم إنشاء نموذج اختبار RJControls بنجاح");
                Console.WriteLine("🎯 يمكنك الآن رؤية الكنترولز المخصصة في العمل");
                Console.WriteLine("🔧 استخدم هذا النموذج كمرجع لتصميم نماذجك");

            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في إنشاء نموذج الاختبار: {ex.Message}");
            }
        }

        /// <summary>
        /// نقطة الدخول الرئيسية
        /// </summary>
        [STAThread]
        public static void Main()
        {
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);

            InstallRJControlsToToolbox();
            VerifyRJControlsDesignerSupport();
            CreateRJControlsTestForm();

            Application.Run();
        }
    }
}
