﻿using SmartCreator.Data;
using SmartCreator.Data.DirectORM;
using SmartCreator.Entities;
using SmartCreator.Entities.Accounts;
using SmartCreator.Entities.UserManager;
using SmartCreator.Models;
using SmartCreator.RJForms;
using SmartCreator.Service;
using SmartCreator.Utils;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Xml.Linq;
//using static System.Runtime.CompilerServices.RuntimeHelpers;

namespace SmartCreator.Forms.BatchCards
{
    public partial class Form_Add_BatchCards : RJChildForm
    {
        private DirectDbContext _db;
        Smart_DataAccess Smart_DataAccess=null;
        public Form_Add_BatchCards()
        {
            InitializeComponent();
            _db = new DirectDbContext(utils.Get_SmartDB_ConnectionString());


            Smart_DataAccess = new Smart_DataAccess();
            Get_Cbox_Profile_UserNanager();
            Get_SellingPoint();

            Get_batch_And_NumberPrint_Ids();



        }
        private void Get_batch_And_NumberPrint_Ids()
        {
            txt_BatchId.Text = (Smart_DataAccess.Get_BatchCards_My_Sequence("BatchCards")+1).ToString();
            txt_NumberId.Text = (Smart_DataAccess.Get_BatchCards_My_Sequence("NumberPrint") +1).ToString();
        }

        private void btnAdd_Click(object sender, EventArgs e)
        {
            if (!ValidateInputAsync())
            {
                //RJMessageBox.Show("ادخل البيانات بشكل صحيح");
                return;
            }

            var batch = new BatchCard
            {
                BatchNumber = Convert.ToInt32(txt_BatchId.Text),
                AddedDate = date.Value,
                BatchType = 0,
                ProfileName = CBox_Profile_UserMan.Text,
                Rb = Global_Variable.Mk_resources.RB_SN,
                Server = 0,
                SpCode = (string?)CBox_SellingPoint.SelectedValue,
                SpName = CBox_SellingPoint.Text,
                Sn_from = Convert.ToInt64(txt_Sn_From.Text),
                Sn_to = Convert.ToInt64(txt_to.Text),
                Count = (int)(Convert.ToDouble(txt_to.Text) - Convert.ToDouble(txt_Sn_From.Text)),
                
            };


            var NumberPrint = new NumberPrintCard
            {
                BatchNumber = Convert.ToInt32(txt_BatchId.Text),
                NumberPrint = Convert.ToInt32(txt_NumberId.Text),
                AddedDate = date.Value,
                BatchType = 0,
                ProfileName = CBox_Profile_UserMan.Text,
                Rb = Global_Variable.Mk_resources.RB_SN,
                Server = 0,
                SpCode = (string?)CBox_SellingPoint.SelectedValue,
                SpName = CBox_SellingPoint.Text,
                Sn_from = Convert.ToInt64(txt_Sn_From.Text),
                Sn_to = Convert.ToInt64(txt_to.Text),
                Count = (int)(Convert.ToDouble(txt_to.Text) - Convert.ToDouble(txt_Sn_From.Text)),

            };
            Smart_DataAccess.Add_Batch_Cards(batch, 0, false, "BatchCards");
            Smart_DataAccess.Add_NumberPrint_Cards(NumberPrint, 0, false, "NumberPrint");

            update_Users(batch, NumberPrint);

            RJMessageBox.Show("تم ادخال الدفعه الي قاعده البيانات");
            //return;
            this.Close();

        }

        private void update_Users(BatchCard batch, NumberPrintCard NumberPrint)
        {
            Sql_DataAccess da = new Sql_DataAccess();
            string Qury = $"update UmUser set [BatchCardId]={batch.BatchNumber} , [NumberPrint]={NumberPrint.NumberPrint} WHERE Sn >={batch.Sn_from} and Sn <= {batch.Sn_to}; ";
            int x = da.Execute(Qury);
           
            //UmUser um = new UmUser();
            //um.SN;
            //um.BatchCardId;
            //um.NumberPrint
            //    um.Sn_Name

            //var activePartners = _db.Update<UmUser>(x => x.IsActive);
        }
        private bool ValidateInputAsync()
        {

            // التحقق من الحقول المطلوبة
            if (string.IsNullOrWhiteSpace(txt_Sn_From.Text))
            {
                RJMessageBox.Show("يرجى إدخال كود الرقم التسلسل بشكل صحيح", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txt_Sn_From.Focus();
                return false;
            }
            if (string.IsNullOrWhiteSpace(CBox_Profile_UserMan.Text))
            {
                RJMessageBox.Show("يرجى اختيار الباقة", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                //txt_Sn_From.Focus();
                return false;
            }
            if (string.IsNullOrWhiteSpace(CBox_Profile_UserMan.Text))
            {
                RJMessageBox.Show("يرجى اختيار الباقة", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                //txt_Sn_From.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(txt_to.Text))
            {
                RJMessageBox.Show("يرجى إدخال كود الرقم التسلسل بشكل صحيح", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Warning);

                txt_to.Focus();
                return false;
            }
            if (string.IsNullOrWhiteSpace(txt_BatchId.Text))
            {
                RJMessageBox.Show("يرجى إدخال كود الرقم رقم الدفعة", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Warning);

                txt_BatchId.Focus();
                return false;
            }
            if (string.IsNullOrWhiteSpace(txt_NumberId.Text))
            {
                RJMessageBox.Show("يرجى إدخال كود  رقم الطبعة", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Warning);

                //txtName.Focus();
                return false;
            }

            {
                // التحقق من عدم تكرار رقم الطبعة او الدفعة (في حالة الإضافة أو تغيير الكود)
                var existingBatchID = Smart_DataAccess.Get_Batch_byBatchNumber_And_Server(Convert.ToInt32( txt_BatchId.Text),0);
                if (existingBatchID != null && existingBatchID.Count <=0)
                {
                    RJMessageBox.Show(" رقم الدفعة موجود مسبقاً، يرجى رقم آخر", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txt_BatchId.Focus();
                    return false;
                }

                var existingNumberPrintId = Smart_DataAccess.Get_NumberPrintCard_byNumberPrint_And_Server(Convert.ToInt32(txt_NumberId.Text.Trim()), 0);
                if (existingNumberPrintId != null && existingNumberPrintId.Count <= 0)
                {
                    RJMessageBox.Show(" رقم الطبعة موجود مسبقاً، يرجى رقم آخر", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txt_NumberId.Focus();
                    return false;
                }
            }

            return true;
        }

        private void Form_Add_BatchCards_Load(object sender, EventArgs e)
        {
            txt_Sn_From.Focus();
        }

        private void Get_Cbox_Profile_UserNanager()
        {
            try
            {
                var umProfil = new List<UmProfile>();
                umProfil.Add(new UmProfile { Id = 0, Name = "" });
                umProfil.AddRange(Global_Variable.UM_Profile);

                CBox_Profile_UserMan.DataSource = umProfil;
                CBox_Profile_UserMan.DisplayMember = "Name";
                CBox_Profile_UserMan.ValueMember = "Name";
                CBox_Profile_UserMan.SelectedIndex = -1;
                CBox_Profile_UserMan.Text = "";
            }
            catch { }
        }

        private void Get_SellingPoint()
        {
            //try
            //{
            //    Smart_DataAccess smart_DataAccess = new Smart_DataAccess();
            //    CBox_SellingPoint = smart_DataAccess.Get_ComboBox_SellingPoint();
            //}
            //catch { }

            Smart_DataAccess da = new Smart_DataAccess();
            try
            {


                CBox_SellingPoint.DataSource = da.Get_BindingSource_SellingPoint();
                CBox_SellingPoint.DisplayMember = "Value";
                CBox_SellingPoint.ValueMember = "Key";
                CBox_SellingPoint.SelectedIndex = 0;
                CBox_SellingPoint.Text = "";
            }
            catch { }

        }

    }
}
