﻿using SmartCreator.Data;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;

namespace SmartCreator.Entities
{
    //public class User
    //{
    //    public int Id { get; set; }
    //    public string Username { get; set; }
    //    public string Password { get; set; }
    //    public string FirstName { get; set; }
    //    public string LastName { get; set; }
    //    public Image ProfilePicture { get; set; }
    //    public string Email { get; set; }
    //    public string PhoneNumber { get; set; }
    //    public DateTime Birthdate { get; set; }

    //    public User()
    //    {

    //    }
    //    public User Login(string username, string pass)
    //    {
    //        var user = GetUsers().Find(x => x.Username == username && x.Password == pass);
    //        return user;
    //    }

    //    private List< User> GetUsers()
    //    {
    //       var UsersList = new List<User>();
    //       UsersList.Add(new User
    //       {
    //           Id = 1,
    //           Username = "admin",
    //           Password = "admin",
    //           FirstName = "Rummer John",
    //           LastName = "Cranel",
    //           ProfilePicture=Properties.Resources.RummerProfile,
    //           Email = "<EMAIL>",
    //           PhoneNumber="16861125 9634",
    //           Birthdate = new DateTime(1999, 10, 18)
    //       });

    //       UsersList.Add(new User
    //       {
    //           Id = 2,
    //           Username = "arely",
    //           Password = "arely123",
    //           FirstName = "Arely",
    //           LastName = "Smith",
    //           ProfilePicture=Properties.Resources.AralyProfile,
    //           Email = "<EMAIL>",
    //           PhoneNumber = "16850718 4565",
    //           Birthdate = new DateTime(2002, 04, 27)
    //       });

    //       return UsersList;
    //    }

    //}


    // <summary>
    /// نموذج المستخدم
    /// </summary>
    [Table("Users")]
    public class User : BaseEntity
    {
        [Column("Username")]
        [System.ComponentModel.DataAnnotations.Required]
        [System.ComponentModel.DataAnnotations.MaxLength(50)]
        //[Index(Name = "IX_User_Username", IsUnique = true)]
        public string Username { get; set; } = string.Empty;

        [Column("Email")]
        [System.ComponentModel.DataAnnotations.Required]
        [System.ComponentModel.DataAnnotations.MaxLength(150)]
        //[Index(Name = "IX_User_Email", IsUnique = true)]
        public string Email { get; set; } = string.Empty;

        [Column("PasswordHash")]
        [System.ComponentModel.DataAnnotations.Required]
        [System.ComponentModel.DataAnnotations.MaxLength(255)]
        public string PasswordHash { get; set; } = string.Empty;

        [Column("Salt")]
        [System.ComponentModel.DataAnnotations.Required]
        [System.ComponentModel.DataAnnotations.MaxLength(255)]
        public string Salt { get; set; } = string.Empty;

        [Column("FirstName")]
        [System.ComponentModel.DataAnnotations.Required]
        [System.ComponentModel.DataAnnotations.MaxLength(100)]
        public string FirstName { get; set; } = string.Empty;

        [Column("LastName")]
        [System.ComponentModel.DataAnnotations.Required]
        [System.ComponentModel.DataAnnotations.MaxLength(100)]
        public string LastName { get; set; } = string.Empty;

        [Column("FullName")]
        [System.ComponentModel.DataAnnotations.MaxLength(200)]
        public string FullName { get; set; } = string.Empty;

        [Column("Phone")]
        [System.ComponentModel.DataAnnotations.MaxLength(50)]
        public string? Phone { get; set; }

        [Column("Department")]
        [System.ComponentModel.DataAnnotations.MaxLength(100)]
        public string? Department { get; set; }

        [Column("Position")]
        [System.ComponentModel.DataAnnotations.MaxLength(100)]
        public string? Position { get; set; }

        [Column("IsActive")]
        [DefaultValue(true)]
        public bool IsActive { get; set; } = true;

        [Column("IsLocked")]
        [DefaultValue(false)]
        public bool IsLocked { get; set; } = false;

        [Column("FailedLoginAttempts")]
        [DefaultValue(0)]
        public int FailedLoginAttempts { get; set; } = 0;

        [Column("LastLoginDate")]
        public DateTime? LastLoginDate { get; set; }

        [Column("LastPasswordChangeDate")]
        public DateTime? LastPasswordChangeDate { get; set; }

        [Column("PasswordExpiryDate")]
        public DateTime? PasswordExpiryDate { get; set; }

        [Column("MustChangePassword")]
        [DefaultValue(false)]
        public bool MustChangePassword { get; set; } = false;

        [Column("TwoFactorEnabled")]
        [DefaultValue(false)]
        public bool TwoFactorEnabled { get; set; } = false;

        [Column("TwoFactorSecret")]
        [System.ComponentModel.DataAnnotations.MaxLength(255)]
        public string? TwoFactorSecret { get; set; }

        [Column("ProfileImagePath")]
        [System.ComponentModel.DataAnnotations.MaxLength(500)]
        public string? ProfileImagePath { get; set; }

        [Column("Notes")]
        public string? Notes { get; set; }

        [Column("CreatedBy")]
        public int? CreatedBy { get; set; }

        [Column("ModifiedBy")]
        public int? ModifiedBy { get; set; }

        [Column("ModifiedDate")]
        public DateTime? ModifiedDate { get; set; }

        // خصائص محسوبة
        [NotMapped]
        public string DisplayName => !string.IsNullOrEmpty(FullName) ? FullName : $"{FirstName} {LastName}";

        [NotMapped]
        public bool IsPasswordExpired => PasswordExpiryDate.HasValue && PasswordExpiryDate.Value <= DateTime.Now;

        [NotMapped]
        public bool IsAccountLocked => IsLocked || FailedLoginAttempts >= 5;

        //[NotMapped]
        //public List<Role> Roles { get; set; } = new List<Role>();

        [NotMapped]
        public List<string> Permissions { get; set; } = new List<string>();

        /// <summary>
        /// تحديث الاسم الكامل
        /// </summary>
        public void UpdateFullName()
        {
            //FullName = $"{FirstName} {LastName}".Trim();
        }

        /// <summary>
        /// تسجيل محاولة دخول فاشلة
        /// </summary>
        public void RecordFailedLogin()
        {
            FailedLoginAttempts++;
            if (FailedLoginAttempts >= 5)
            {
                IsLocked = true;
            }
        }

        /// <summary>
        /// إعادة تعيين محاولات الدخول الفاشلة
        /// </summary>
        public void ResetFailedLogins()
        {
            FailedLoginAttempts = 0;
            IsLocked = false;
        }

        /// <summary>
        /// تسجيل دخول ناجح
        /// </summary>
        public void RecordSuccessfulLogin()
        {
            LastLoginDate = DateTime.Now;
            ResetFailedLogins();
        }
    }

}
