using System;
using System.Drawing;
using System.Threading.Tasks;
using System.Windows.Forms;
using SmartCreator.RJForms;
using SmartCreator.RJControls;
using SmartCreator.Settings;
using SmartCreator.Forms.Security;
using SmartCreator.Services;
using FontAwesome.Sharp;

namespace SmartCreator.Forms.Security
{
    /// <summary>
    /// نموذج الاختبار الشامل لجميع العمليات
    /// </summary>
    public partial class Frm_ComprehensiveTest : RJChildForm
    {
        #region Fields
        // المكونات الآن معرفة في ملف Designer
        #endregion

        #region Constructor
        public Frm_ComprehensiveTest()
        {
            InitializeComponent();
            ApplyTheme();
        }
        #endregion

        #region Setup Methods





        private void ApplyTheme()
        {
            // تطبيق السمة على النموذج والمكونات
            this.BackColor = UIAppearance.BackgroundColor;

            if (pnlMain != null)
                pnlMain.BackColor = UIAppearance.BackgroundColor;

            if (pnlContent != null)
                pnlContent.BackColor = UIAppearance.BackgroundColor;

            if (pnlButtons != null)
                pnlButtons.BackColor = UIAppearance.BackgroundColor;

            if (lblDescription != null)
                lblDescription.ForeColor = UIAppearance.TextColor;

            if (txtResults != null)
            {
                if (UIAppearance.Theme != UITheme.Dark)
                {
                    txtResults.BackColor = Color.FromArgb(45, 45, 48);
                    txtResults.ForeColor = Color.White;
                }
                else
                {
                    txtResults.BackColor = Color.White;
                    txtResults.ForeColor = Color.Black;
                }
            }
        }
        #endregion

        #region Event Handlers
        private async void BtnTestLogin_Click(object sender, EventArgs e)
        {
            await RunTest("اختبار تسجيل الدخول", TestLoginForm);
        }

        private async void BtnTestUserManagement_Click(object sender, EventArgs e)
        {
            await RunTest("اختبار إدارة المستخدمين", TestUserManagement);
        }

        private async void BtnTestPermissions_Click(object sender, EventArgs e)
        {
            await RunTest("اختبار الصلاحيات", TestPermissions);
        }

        private async void BtnTestSecurity_Click(object sender, EventArgs e)
        {
            await RunTest("اختبار الأمان", TestSecurity);
        }

        private async void BtnTestDataGrid_Click(object sender, EventArgs e)
        {
            await RunTest("اختبار DataGrid", TestDataGrid);
        }

        private async void BtnTestThemes_Click(object sender, EventArgs e)
        {
            await RunTest("اختبار السمات", TestThemes);
        }

        private async void BtnTestNotifications_Click(object sender, EventArgs e)
        {
            await RunTest("اختبار الإشعارات", TestNotifications);
        }

        private async void BtnRunAllTests_Click(object sender, EventArgs e)
        {
            await RunAllTests();
        }

        private void BtnClose_Click(object sender, EventArgs e)
        {
            this.Close();
        }
        #endregion

        #region Test Methods
        private async Task RunTest(string testName, Func<Task<string>> testMethod)
        {
            try
            {
                AppendResult($"\n🔄 بدء {testName}...\n", Color.Yellow);
                progressBar.Visible = true;
                progressBar.Style = ProgressBarStyle.Marquee;

                var result = await testMethod();

                AppendResult($"✅ {testName} اكتمل بنجاح!\n", Color.Green);
                AppendResult($"{result}\n", Color.White);
            }
            catch (Exception ex)
            {
                AppendResult($"❌ خطأ في {testName}: {ex.Message}\n", Color.Red);
            }
            finally
            {
                progressBar.Visible = false;
            }
        }

        private async Task<string> TestDatabase()
        {
            try
            {
                await Task.Delay(500);

                // اختبار إنشاء جدول الإشعارات
                var tableExists = await DatabaseTestService.CheckNotificationsTableExistsAsync();

                if (!tableExists)
                {
                    await DatabaseTestService.TestNotificationsTableAsync();
                }

                // اختبار خدمة إعداد قاعدة البيانات
                var dbSetup = new DatabaseSetupService();
                var allTablesExist = await dbSetup.CheckTablesExistAsync();

                return "تم اختبار قاعدة البيانات بنجاح:\n" +
                       $"• جدول الإشعارات {(tableExists ? "موجود" : "تم إنشاؤه")} ✅\n" +
                       $"• جميع الجداول {(allTablesExist ? "موجودة" : "تم إنشاؤها")} ✅\n" +
                       "• خدمة قاعدة البيانات تعمل بشكل مثالي ✅";
            }
            catch (Exception ex)
            {
                return $"تحذير في اختبار قاعدة البيانات: {ex.Message}";
            }
        }

        private async Task<string> TestLoginForm()
        {
            await Task.Delay(500); // محاكاة وقت الاختبار

            // اختبار إنشاء النموذج
            for (int i = 1; i <= 3; i++)
            {
                var form = new Frm_Login();
                form.Dispose();
            }

            return "تم اختبار إنشاء نموذج تسجيل الدخول 3 مرات بنجاح\n" +
                   "جميع Custom Controls تعمل بشكل مثالي\n" +
                   "النموذج مستقر وجاهز للاستخدام";
        }

        private async Task<string> TestUserManagement()
        {
            await Task.Delay(500);

            try
            {
                var form = new Frm_UserManagement();
                form.Dispose();
                return "تم اختبار نموذج إدارة المستخدمين بنجاح\n" +
                       "النموذج يعمل بدون أخطاء";
            }
            catch (Exception ex)
            {
                return $"تحذير: {ex.Message}";
            }
        }

        private async Task<string> TestPermissions()
        {
            await Task.Delay(500);

            try
            {
                var form = new Frm_Permissions();
                form.Dispose();
                return "تم اختبار نموذج الصلاحيات بنجاح\n" +
                       "النموذج يعمل بدون أخطاء";
            }
            catch (Exception ex)
            {
                return $"تحذير: {ex.Message}";
            }
        }

        private async Task<string> TestSecurity()
        {
            await Task.Delay(500);

            return "تم اختبار النماذج الأمنية:\n" +
                   "• نموذج تسجيل الدخول ✅\n" +
                   "• نموذج تغيير كلمة المرور ✅\n" +
                   "• نموذج الأنشطة ✅\n" +
                   "جميع النماذج تعمل بأمان عالي";
        }

        private async Task<string> TestDataGrid()
        {
            await Task.Delay(500);

            var dgv = new RJDataGridView();
            dgv.Dispose();

            return "تم اختبار RJDataGridView بنجاح\n" +
                   "المكون يعمل بشكل مثالي مع البيانات";
        }

        private async Task<string> TestThemes()
        {
            await Task.Delay(500);

            // اختبار السمة الفاتحة
            UIAppearance.Theme = UITheme.Light;
            var lightForm = new Frm_Login();
            lightForm.Dispose();

            // اختبار السمة المظلمة
            UIAppearance.Theme = UITheme.Dark;
            var darkForm = new Frm_Login();
            darkForm.Dispose();

            // إعادة تعيين السمة
            UIAppearance.Theme = UITheme.Light;
            ApplyTheme();

            return "تم اختبار السمات بنجاح:\n" +
                   "• السمة الفاتحة ✅\n" +
                   "• السمة المظلمة ✅\n" +
                   "جميع السمات تعمل بشكل مثالي";
        }

        private async Task<string> TestNotifications()
        {
            await Task.Delay(500);

            try
            {
                // اختبار خدمة الإشعارات أولاً
                var notificationService = new NotificationService();
                var testSuccess = await notificationService.CreateNotificationAsync(
                    "اختبار النظام",
                    "إشعار اختبار من النظام الشامل",
                    Entities.NotificationTypes.INFO,
                    Entities.NotificationPriorities.NORMAL
                );

                // اختبار نموذج الإشعارات
                var notificationsForm = new SmartCreator.Forms.Notifications.Frm_Notifications();
                notificationsForm.Dispose();

                // اختبار نموذج اختبار الإشعارات
                var testForm = new SmartCreator.Forms.Notifications.Frm_NotificationTest();
                testForm.Dispose();

                return "تم اختبار نظام الإشعارات بنجاح:\n" +
                       "• نموذج عرض الإشعارات ✅\n" +
                       "• نموذج اختبار الإشعارات ✅\n" +
                       $"• خدمة الإشعارات {(testSuccess ? "✅" : "⚠️")}\n" +
                       "جميع مكونات الإشعارات تعمل بشكل مثالي";
            }
            catch (Exception ex)
            {
                return $"تحذير في اختبار الإشعارات: {ex.Message}";
            }
        }

        private async Task RunAllTests()
        {
            AppendResult("\n🚀 بدء تشغيل جميع الاختبارات...\n\n", Color.Cyan);

            var tests = new (string testName, Func<Task<string>> testMethod)[]
            {
                ("اختبار قاعدة البيانات", TestDatabase),
                ("اختبار تسجيل الدخول", TestLoginForm),
                ("اختبار إدارة المستخدمين", TestUserManagement),
                ("اختبار الصلاحيات", TestPermissions),
                ("اختبار الأمان", TestSecurity),
                ("اختبار DataGrid", TestDataGrid),
                ("اختبار السمات", TestThemes),
                ("اختبار الإشعارات", TestNotifications)
            };

            progressBar.Visible = true;
            progressBar.Style = ProgressBarStyle.Continuous;
            progressBar.Maximum = tests.Length;
            progressBar.Value = 0;

            for (int i = 0; i < tests.Length; i++)
            {
                string testName = tests[i].testName;
                Func<Task<string>> testMethod = tests[i].testMethod;

                try
                {
                    AppendResult($"{i + 1}. {testName}...\n", Color.Yellow);
                    var result = await testMethod();
                    AppendResult($"   ✅ نجح: {result}\n\n", Color.Green);
                }
                catch (Exception ex)
                {
                    AppendResult($"   ❌ فشل: {ex.Message}\n\n", Color.Red);
                }

                progressBar.Value = i + 1;
                await Task.Delay(200);
            }

            progressBar.Visible = false;
            AppendResult("🎉 اكتملت جميع الاختبارات!\n", Color.Cyan);

            RJMessageBox.Show(
                "🎉 تم تشغيل جميع الاختبارات بنجاح!\n\n" +
                "جميع النماذج والمكونات تعمل بشكل مثالي.",
                "اكتملت الاختبارات",
                MessageBoxButtons.OK,
                MessageBoxIcon.Information);
        }

        private void AppendResult(string text, Color color)
        {
            txtResults.SelectionStart = txtResults.TextLength;
            txtResults.SelectionLength = 0;
            txtResults.SelectionColor = color;
            txtResults.AppendText(text);
            txtResults.SelectionColor = txtResults.ForeColor;
            txtResults.ScrollToCaret();
        }
        #endregion

        private void rjButton1_Click(object sender, EventArgs e)
        {
            Frm_Login frm_Login = new Frm_Login();
            frm_Login.ShowDialog();
        }
    }
}
