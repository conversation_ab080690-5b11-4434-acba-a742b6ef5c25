using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using SmartCreator.Entities.Accounting;
using SmartCreator.RJControls;
using static SmartCreator.Entities.Accounting.JournalEntryTypeInfo;
using static SmartCreator.Entities.Accounting.JournalEntryStatusInfo;
using SmartCreator.RJForms;
using FontAwesome.Sharp;
using SmartCreator.Service;
using SmartCreator.Services.Accounting;
using SmartCreator.Services.Security;
using SmartCreator.Settings;

namespace SmartCreator.Forms.Accounting
{
    /// <summary>
    /// نموذج عرض القيد المحاسبي المحسن
    /// </summary>
    public partial class Frm_ViewJournalEntry_Enhanced : RJChildForm
    {
        #region المتغيرات

        private readonly JournalEntryService _journalEntryService;
        private readonly AccountService _accountService;
        private readonly UserActivityService _activityService;

        private JournalEntry _currentEntry;
        private List<JournalEntryDetail> _entryDetails;
        private List<Account> _accounts;

        // الأحداث
        public event EventHandler<JournalEntry> EntryUpdated;

        #endregion

        #region البناء

        /// <summary>
        /// Constructor افتراضي للـ Designer
        /// </summary>
        public Frm_ViewJournalEntry_Enhanced()
        {
            // إنشاء الخدمات بـ constructors افتراضية للـ Designer
            _journalEntryService = new JournalEntryService();
            _accountService = new AccountService();
            _activityService = new UserActivityService();
            _currentEntry = new JournalEntry { EntryNumber = "DESIGN-001", Description = "قيد تصميم" };

            InitializeComponent();

            // تجنب تحميل البيانات في وضع التصميم
            if (!DesignMode)
            {
                SetupForm();
                LoadDataAsync();
            }
        }

        /// <summary>
        /// Constructor مع القيد المحدد
        /// </summary>
        public Frm_ViewJournalEntry_Enhanced(JournalEntryService journalEntryService,
            AccountService accountService, UserActivityService activityService, JournalEntry entry)
        {
            _journalEntryService = journalEntryService;
            _accountService = accountService;
            _activityService = activityService;
            _currentEntry = entry;

            InitializeComponent();
            SetupForm();
            LoadDataAsync();
        }

        #endregion

        #region إعداد النموذج

        /// <summary>
        /// إعداد النموذج
        /// </summary>
        private void SetupForm()
        {
            // إعداد خصائص RJChildForm
            this.Text = $"عرض القيد - {_currentEntry.EntryNumber}";
            this.Caption = $"عرض القيد - {_currentEntry.EntryNumber}";
            this.FormIcon = FontAwesome.Sharp.IconChar.Eye;
            this.IsChildForm = true;
            this.Size = new Size(1000, 700);
            this.StartPosition = FormStartPosition.CenterScreen;

            // العناصر موجودة بالفعل في ملف Designer - لا حاجة لإضافتها في runtime

            // إعداد جدول التفاصيل
            SetupDetailsGrid();

            // تطبيق إعدادات المظهر
            ApplyThemeSettings();
        }

        /// <summary>
        /// إعداد جدول التفاصيل
        /// </summary>
        private void SetupDetailsGrid()
        {
            dgvDetails.AutoGenerateColumns = false;
            dgvDetails.AllowUserToAddRows = false;
            dgvDetails.AllowUserToDeleteRows = false;
            dgvDetails.ReadOnly = true;
            dgvDetails.SelectionMode = DataGridViewSelectionMode.FullRowSelect;

            // إعداد الأعمدة
            dgvDetails.Columns.Clear();
            dgvDetails.Columns.AddRange(new DataGridViewColumn[]
            {
                new DataGridViewTextBoxColumn
                {
                    Name = "LineNumber",
                    HeaderText = "م",
                    Width = 50
                },
                new DataGridViewTextBoxColumn
                {
                    Name = "AccountCode",
                    HeaderText = "رمز الحساب",
                    Width = 120
                },
                new DataGridViewTextBoxColumn
                {
                    Name = "AccountName",
                    HeaderText = "اسم الحساب",
                    Width = 250
                },
                new DataGridViewTextBoxColumn
                {
                    Name = "Description",
                    HeaderText = "البيان",
                    Width = 200
                },
                new DataGridViewTextBoxColumn
                {
                    Name = "DebitAmount",
                    HeaderText = "مدين",
                    Width = 120,
                    DefaultCellStyle = new DataGridViewCellStyle
                    {
                        Format = "N2",
                        Alignment = DataGridViewContentAlignment.MiddleRight
                    }
                },
                new DataGridViewTextBoxColumn
                {
                    Name = "CreditAmount",
                    HeaderText = "دائن",
                    Width = 120,
                    DefaultCellStyle = new DataGridViewCellStyle
                    {
                        Format = "N2",
                        Alignment = DataGridViewContentAlignment.MiddleRight
                    }
                }
            });
        }

        /// <summary>
        /// تطبيق إعدادات المظهر
        /// </summary>
        private void ApplyThemeSettings()
        {
            this.BackColor = UIAppearance.BackgroundColor;

            var font = new Font("Droid Arabic Kufi", 9F, FontStyle.Regular);
            this.Font = font;

            foreach (Control control in this.Controls)
            {
                ApplyFontToControl(control, font);
            }
        }

        /// <summary>
        /// تطبيق الخط على العنصر وعناصره الفرعية
        /// </summary>
        private void ApplyFontToControl(Control control, Font font)
        {
            control.Font = font;
            foreach (Control childControl in control.Controls)
            {
                ApplyFontToControl(childControl, font);
            }
        }

        /// <summary>
        /// إضافة العناصر إلى منطقة العميل في RJChildForm
        /// </summary>
        private void AddControlsToClientArea()
        {
            // الحصول على منطقة العميل من RJChildForm
            var clientArea = this.Controls.Find("pnlClientArea", true).FirstOrDefault() as Panel;
            if (clientArea != null)
            {
                // إضافة العناصر الرئيسية إلى منطقة العميل
                clientArea.Controls.Add(pnlTop);
                clientArea.Controls.Add(pnlMiddle);
                clientArea.Controls.Add(pnlBottom);

                // تحديث ترتيب العناصر
                pnlTop.BringToFront();
                pnlBottom.BringToFront();
                pnlMiddle.BringToFront();
            }
        }

        #endregion

        #region تحميل البيانات

        /// <summary>
        /// تحميل البيانات بشكل غير متزامن
        /// </summary>
        private async void LoadDataAsync()
        {
            try
            {
                // تحميل الحسابات
                _accounts = await Task.Run(() => _accountService.GetAllAccounts());

                // تحميل تفاصيل القيد
                await LoadEntryDetails();

                // ملء بيانات النموذج
                PopulateFormData();
            }
            catch (Exception ex)
            {
                RJMessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تحميل تفاصيل القيد
        /// </summary>
        private async Task LoadEntryDetails()
        {
            try
            {
                _entryDetails = await Task.Run(() => _journalEntryService.GetJournalEntryDetails(_currentEntry.Id));
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تحميل تفاصيل القيد: {ex.Message}");
            }
        }

        /// <summary>
        /// ملء بيانات النموذج
        /// </summary>
        private void PopulateFormData()
        {
            try
            {
                if (_currentEntry == null) return;

                // ملء المعلومات الأساسية
                lblEntryNumber.Text = $"رقم القيد: {_currentEntry.EntryNumber}";
                lblEntryDate.Text = $"التاريخ: {_currentEntry.Date:yyyy-MM-dd}";
                lblDescription.Text = $"البيان: {_currentEntry.Description}";
                lblEntryType.Text = $"النوع: {GetArabicName(_currentEntry.Type)}";
                lblEntryStatus.Text = $"الحالة: {GetArabicName(_currentEntry.Status)}";
                lblCreatedBy.Text = $"أنشئ بواسطة: {_currentEntry.CreatedBy}";
                lblCreatedDate.Text = $"تاريخ الإنشاء: {_currentEntry.CreatedDate:yyyy-MM-dd HH:mm}";

                // ملء تفاصيل القيد
                PopulateDetailsGrid();

                // تحديث الإحصائيات
                UpdateStatistics();
            }
            catch (Exception ex)
            {
                RJMessageBox.Show($"خطأ في ملء بيانات النموذج: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// ملء جدول التفاصيل
        /// </summary>
        private void PopulateDetailsGrid()
        {
            try
            {
                dgvDetails.Rows.Clear();

                if (_entryDetails == null) return;

                foreach (var detail in _entryDetails)
                {
                    var account = _accounts?.FirstOrDefault(a => a.Id == detail.AccountId);

                    var row = new DataGridViewRow();
                    row.CreateCells(dgvDetails);

                    row.Cells["LineNumber"].Value = detail.LineNumber;
                    row.Cells["AccountCode"].Value = account?.Code ?? "";
                    row.Cells["AccountName"].Value = account?.Name ?? "";
                    row.Cells["Description"].Value = detail.Description;
                    row.Cells["DebitAmount"].Value = detail.DebitAmount;
                    row.Cells["CreditAmount"].Value = detail.CreditAmount;

                    dgvDetails.Rows.Add(row);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في ملء جدول التفاصيل: {ex.Message}");
            }
        }

        /// <summary>
        /// تحديث الإحصائيات
        /// </summary>
        private void UpdateStatistics()
        {
            try
            {
                lblTotalDebit.Text = $"إجمالي المدين: {_currentEntry.TotalDebit:N2}";
                lblTotalCredit.Text = $"إجمالي الدائن: {_currentEntry.TotalCredit:N2}";

                var balance = _currentEntry.TotalDebit - _currentEntry.TotalCredit;
                var isBalanced = Math.Abs(balance) < 0.01m;

                lblBalance.Text = isBalanced ? "القيد متزن" : $"الفرق: {Math.Abs(balance):N2}";
                lblBalance.ForeColor = isBalanced ?
                    Color.FromArgb(40, 167, 69) :
                    Color.FromArgb(220, 53, 69);

                lblDetailsCount.Text = $"عدد الأسطر: {_entryDetails?.Count ?? 0}";
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في تحديث الإحصائيات: {ex.Message}");
            }
        }

        #endregion

        #region أحداث الأزرار

        /// <summary>
        /// تعديل القيد
        /// </summary>
        private void BtnEdit_Click(object sender, EventArgs e)
        {
            try
            {
                var editForm = new Frm_AddEditJournalEntry_Enhanced(_journalEntryService, _accountService, _activityService, _currentEntry);
                editForm.EntryUpdated += (s, entry) =>
                {
                    _currentEntry = entry;
                    LoadDataAsync();
                    EntryUpdated?.Invoke(this, entry);
                };
                editForm.ShowDialog();
            }
            catch (Exception ex)
            {
                RJMessageBox.Show($"خطأ في فتح نموذج التعديل: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// ترحيل القيد
        /// </summary>
        private void BtnPost_Click(object sender, EventArgs e)
        {
            try
            {
                if (_currentEntry.Status == JournalEntryStatus.Posted)
                {
                    RJMessageBox.Show("القيد مرحل بالفعل", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                var result = RJMessageBox.Show("هل أنت متأكد من ترحيل القيد؟\n\nلن يمكن تعديله بعد الترحيل.",
                    "تأكيد الترحيل", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    // محاكاة الترحيل
                    _currentEntry.Status = JournalEntryStatus.Posted;

                    RJMessageBox.Show("تم ترحيل القيد بنجاح", "ترحيل",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);

                    PopulateFormData();
                    EntryUpdated?.Invoke(this, _currentEntry);
                }
            }
            catch (Exception ex)
            {
                RJMessageBox.Show($"خطأ في ترحيل القيد: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// طباعة القيد
        /// </summary>
        private void BtnPrint_Click(object sender, EventArgs e)
        {
            try
            {
                RJMessageBox.Show($"تم إرسال القيد {_currentEntry.EntryNumber} للطباعة", "طباعة",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                RJMessageBox.Show($"خطأ في الطباعة: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// إغلاق النموذج
        /// </summary>
        private void BtnClose_Click(object sender, EventArgs e)
        {
            try
            {
                this.Close();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في الإغلاق: {ex.Message}");
            }
        }

        #endregion
    }
}
