using System;
using System.Threading.Tasks;
using System.Windows.Forms;
using SmartCreator.RJForms;

namespace SmartCreator.Services
{
    /// <summary>
    /// اختبار سريع لخدمة الإشعارات
    /// </summary>
    public static class NotificationServiceTest
    {
        /// <summary>
        /// تشغيل اختبار سريع لخدمة الإشعارات
        /// </summary>
        public static async Task RunQuickTestAsync()
        {
            try
            {
                var notificationService = new NotificationService();

                // اختبار إنشاء إشعار
                var success = await notificationService.CreateNotificationAsync(
                    "اختبار النظام",
                    "هذا إشعار اختبار للتأكد من عمل النظام بشكل صحيح",
                    Entities.NotificationTypes.INFO,
                    Entities.NotificationPriorities.NORMAL
                );

                if (success)
                {
                    // اختبار عد الإشعارات
                    var count = await notificationService.GetUnreadCountAsync();

                    // اختبار جلب الإشعارات
                    var notifications = await notificationService.GetAllNotificationsAsync();

                    RJMessageBox.Show(
                        $"✅ اختبار خدمة الإشعارات نجح!\n\n" +
                        $"📊 النتائج:\n" +
                        $"• تم إنشاء إشعار جديد بنجاح\n" +
                        $"• عدد الإشعارات غير المقروءة: {count}\n" +
                        $"• إجمالي الإشعارات: {notifications.Count}\n\n" +
                        $"🎉 النظام جاهز للاستخدام!",
                        "نتيجة الاختبار",
                        MessageBoxButtons.OK,
                        MessageBoxIcon.Information
                    );
                }
                else
                {
                    RJMessageBox.Show(
                        "❌ فشل في إنشاء الإشعار\n\n" +
                        "يرجى التحقق من:\n" +
                        "• اتصال قاعدة البيانات\n" +
                        "• وجود جدول Notifications\n" +
                        "• صحة البيانات المدخلة",
                        "خطأ في الاختبار",
                        MessageBoxButtons.OK,
                        MessageBoxIcon.Error
                    );
                }
            }
            catch (Exception ex)
            {
                RJMessageBox.Show(
                    $"❌ خطأ في اختبار خدمة الإشعارات:\n\n{ex.Message}",
                    "خطأ في الاختبار",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error
                );
            }
        }
    }
}
