using SmartCreator.Models;
using SmartCreator.Entities;
using SmartCreator.Services;
using SmartCreator.Services.Security;
using SmartCreator.RJControls;
using SmartCreator.Helpers;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace SmartCreator.Forms.Security
{
    /// <summary>
    /// نموذج إدارة صلاحيات المستخدم المحسن
    /// </summary>
    public partial class Frm_UserPermissions : Form
    {
        private readonly PermissionService _permissionService;
        private readonly SmartCreator.Entities.User _user;
        private List<SmartCreator.Entities.Permission> _allPermissions;
        private List<SmartCreator.Entities.Permission> _userPermissions;
        private bool _isLoadingPermissions = false;

        // عناصر التحكم
        private TreeView treePermissions;
        private RJButton btnSave;
        private RJButton btnCancel;
        private RJButton btnSelectAll;
        private RJButton btnDeselectAll;
        private RJButton btnExpandAll;
        private RJButton btnCollapseAll;
        private Label lblTitle;
        private Label lblUserInfo;
        private Panel pnlMain;
        private Panel pnlButtons;
        private TextBox txtSearch;
        private Label lblSearch;

        public Frm_UserPermissions(PermissionService permissionService, SmartCreator.Entities.User user)
        {
            _permissionService = permissionService;
            _user = user;
            InitializeComponent();
            InitializeFormComponents();
            SetupForm();
            LoadDataAsync();
        }

        /// <summary>
        /// تهيئة عناصر النموذج
        /// </summary>
        private void InitializeFormComponents()
        {
            this.SuspendLayout();

            // إعدادات النموذج
            this.Text = "إدارة صلاحيات المستخدم";
            this.Size = new Size(800, 600);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.Sizable;
            this.BackColor = Color.FromArgb(46, 51, 73);

            // اللوحة الرئيسية
            pnlMain = new Panel
            {
                Location = new Point(20, 20),
                Size = new Size(740, 480),
                BackColor = Color.FromArgb(37, 42, 64),
                BorderStyle = BorderStyle.FixedSingle,
                Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right | AnchorStyles.Bottom
            };

            // العنوان
            lblTitle = new Label
            {
                Text = "إدارة صلاحيات المستخدم",
                Font = new Font("Segoe UI", 16, FontStyle.Bold),
                ForeColor = Color.White,
                Location = new Point(20, 20),
                Size = new Size(300, 30)
            };

            // معلومات المستخدم
            lblUserInfo = new Label
            {
                Text = $"المستخدم: {_user?.DisplayName} ({_user?.Username})",
                Font = new Font("Segoe UI", 12),
                ForeColor = Color.LightBlue,
                Location = new Point(20, 55),
                Size = new Size(700, 25)
            };

            // البحث
            lblSearch = new Label
            {
                Text = "البحث في الصلاحيات:",
                ForeColor = Color.White,
                Location = new Point(20, 90),
                Size = new Size(120, 20)
            };

            txtSearch = new TextBox
            {
                Location = new Point(150, 88),
                Size = new Size(200, 25),
                BackColor = Color.FromArgb(74, 79, 99),
                ForeColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle
            };

            // أزرار التحكم السريع
            btnSelectAll = new RJButton
            {
                Text = "تحديد الكل",
                Location = new Point(370, 85),
                Size = new Size(80, 30),
                BackColor = Color.FromArgb(50, 226, 178),
                ForeColor = Color.White,
                BorderSize = 0,
                BorderRadius = 5
            };

            btnDeselectAll = new RJButton
            {
                Text = "إلغاء الكل",
                Location = new Point(460, 85),
                Size = new Size(80, 30),
                BackColor = Color.FromArgb(255, 159, 67),
                ForeColor = Color.White,
                BorderSize = 0,
                BorderRadius = 5
            };

            btnExpandAll = new RJButton
            {
                Text = "توسيع الكل",
                Location = new Point(550, 85),
                Size = new Size(80, 30),
                BackColor = Color.FromArgb(108, 92, 231),
                ForeColor = Color.White,
                BorderSize = 0,
                BorderRadius = 5
            };

            btnCollapseAll = new RJButton
            {
                Text = "طي الكل",
                Location = new Point(640, 85),
                Size = new Size(80, 30),
                BackColor = Color.FromArgb(132, 129, 132),
                ForeColor = Color.White,
                BorderSize = 0,
                BorderRadius = 5
            };

            // شجرة الصلاحيات
            treePermissions = new TreeView
            {
                Location = new Point(20, 125),
                Size = new Size(700, 330),
                BackColor = Color.FromArgb(74, 79, 99),
                ForeColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle,
                CheckBoxes = true,
                FullRowSelect = true,
                ShowLines = true,
                ShowPlusMinus = true,
                ShowRootLines = true,
                Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right | AnchorStyles.Bottom
            };

            // لوحة الأزرار
            pnlButtons = new Panel
            {
                Location = new Point(20, 520),
                Size = new Size(740, 50),
                BackColor = Color.Transparent,
                Anchor = AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right
            };

            // أزرار الحفظ والإلغاء
            btnSave = new RJButton
            {
                Text = "💾 حفظ الصلاحيات",
                Location = new Point(20, 10),
                Size = new Size(150, 35),
                BackColor = Color.FromArgb(24, 161, 251),
                ForeColor = Color.White,
                BorderSize = 0,
                BorderRadius = 8
            };

            btnCancel = new RJButton
            {
                Text = "❌ إلغاء",
                Location = new Point(190, 10),
                Size = new Size(100, 35),
                BackColor = Color.FromArgb(238, 82, 83),
                ForeColor = Color.White,
                BorderSize = 0,
                BorderRadius = 8
            };

            // إضافة العناصر للوحات
            pnlMain.Controls.AddRange(new Control[]
            {
                lblTitle, lblUserInfo, lblSearch, txtSearch,
                btnSelectAll, btnDeselectAll, btnExpandAll, btnCollapseAll,
                treePermissions
            });

            pnlButtons.Controls.AddRange(new Control[] { btnSave, btnCancel });

            // إضافة اللوحات للنموذج
            this.Controls.AddRange(new Control[] { pnlMain, pnlButtons });

            this.ResumeLayout(false);
        }

        /// <summary>
        /// إعداد النموذج
        /// </summary>
        private void SetupForm()
        {
            // ربط الأحداث
            btnSave.Click += BtnSave_Click;
            btnCancel.Click += BtnCancel_Click;
            btnSelectAll.Click += BtnSelectAll_Click;
            btnDeselectAll.Click += BtnDeselectAll_Click;
            btnExpandAll.Click += BtnExpandAll_Click;
            btnCollapseAll.Click += BtnCollapseAll_Click;
            txtSearch.TextChanged += TxtSearch_TextChanged;
            treePermissions.AfterCheck += TreePermissions_AfterCheck;
        }

        /// <summary>
        /// تحميل البيانات
        /// </summary>
        private async void LoadDataAsync()
        {
            try
            {
                // تحميل جميع الصلاحيات
                _allPermissions = await _permissionService.GetAllPermissionsAsync();

                // تحميل صلاحيات المستخدم الحالية
                _userPermissions = await _permissionService.GetUserPermissionsAsync(_user.Id);

                // بناء شجرة الصلاحيات
                BuildPermissionsTree();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// بناء شجرة الصلاحيات
        /// </summary>
        private void BuildPermissionsTree()
        {
            _isLoadingPermissions = true;
            treePermissions.Nodes.Clear();

            // تجميع الصلاحيات حسب الوحدة والفئة
            var groupedPermissions = _allPermissions
                .GroupBy(p => p.Module)
                .OrderBy(g => g.Key);

            var userPermissionIds = _userPermissions.Select(p => p.Id).ToHashSet();

            foreach (var moduleGroup in groupedPermissions)
            {
                var moduleNode = new TreeNode(moduleGroup.Key)
                {
                    Tag = "Module",
                    ForeColor = Color.LightBlue
                };

                var categoryGroups = moduleGroup
                    .GroupBy(p => p.Category ?? "عام")
                    .OrderBy(g => g.Key);

                foreach (var categoryGroup in categoryGroups)
                {
                    var categoryNode = new TreeNode(categoryGroup.Key)
                    {
                        Tag = "Category",
                        ForeColor = Color.LightGreen
                    };

                    foreach (var permission in categoryGroup.OrderBy(p => p.Name))
                    {
                        var permissionNode = new TreeNode($"{permission.Name} - {permission.Description}")
                        {
                            Tag = permission,
                            Checked = userPermissionIds.Contains(permission.Id),
                            ForeColor = Color.White
                        };
                        categoryNode.Nodes.Add(permissionNode);
                    }

                    // تحديث حالة عقدة الفئة
                    UpdateParentNodeState(categoryNode);
                    moduleNode.Nodes.Add(categoryNode);
                }

                // تحديث حالة عقدة الوحدة
                UpdateParentNodeState(moduleNode);
                treePermissions.Nodes.Add(moduleNode);
            }

            treePermissions.ExpandAll();
            _isLoadingPermissions = false;
        }

        /// <summary>
        /// تحديث حالة العقدة الأب
        /// </summary>
        private void UpdateParentNodeState(TreeNode parentNode)
        {
            if (parentNode.Nodes.Count == 0) return;

            var checkedChildren = 0;
            var totalChildren = 0;

            foreach (TreeNode childNode in parentNode.Nodes)
            {
                if (childNode.Tag is SmartCreator.Entities.Permission)
                {
                    totalChildren++;
                    if (childNode.Checked) checkedChildren++;
                }
                else
                {
                    // عقدة فرعية (فئة)
                    var childCheckedCount = GetCheckedPermissionsCount(childNode);
                    var childTotalCount = GetTotalPermissionsCount(childNode);

                    if (childCheckedCount > 0) checkedChildren++;
                    totalChildren++;
                }
            }

            if (checkedChildren == totalChildren && totalChildren > 0)
            {
                parentNode.Checked = true;
                parentNode.BackColor = Color.Transparent;
            }
            else if (checkedChildren > 0)
            {
                parentNode.Checked = false;
                parentNode.BackColor = Color.DarkBlue; // إشارة للتحديد الجزئي
            }
            else
            {
                parentNode.Checked = false;
                parentNode.BackColor = Color.Transparent;
            }
        }

        /// <summary>
        /// الحصول على عدد الصلاحيات المحددة في العقدة
        /// </summary>
        private int GetCheckedPermissionsCount(TreeNode node)
        {
            int count = 0;
            foreach (TreeNode childNode in node.Nodes)
            {
                if (childNode.Tag is SmartCreator.Entities.Permission && childNode.Checked)
                    count++;
                else if (!(childNode.Tag is SmartCreator.Entities.Permission))
                    count += GetCheckedPermissionsCount(childNode);
            }
            return count;
        }

        /// <summary>
        /// الحصول على العدد الإجمالي للصلاحيات في العقدة
        /// </summary>
        private int GetTotalPermissionsCount(TreeNode node)
        {
            int count = 0;
            foreach (TreeNode childNode in node.Nodes)
            {
                if (childNode.Tag is SmartCreator.Entities.Permission)
                    count++;
                else if (!(childNode.Tag is SmartCreator.Entities.Permission))
                    count += GetTotalPermissionsCount(childNode);
            }
            return count;
        }

        /// <summary>
        /// حدث تغيير حالة التحديد في الشجرة
        /// </summary>
        private void TreePermissions_AfterCheck(object sender, TreeViewEventArgs e)
        {
            if (_isLoadingPermissions) return;

            _isLoadingPermissions = true;

            if (e.Node.Tag is SmartCreator.Entities.Permission)
            {
                // عقدة صلاحية - تحديث العقد الأب
                UpdateParentNodesState(e.Node.Parent);
            }
            else
            {
                // عقدة وحدة أو فئة - تحديث العقد الفرعية
                SetChildNodesState(e.Node, e.Node.Checked);
            }

            _isLoadingPermissions = false;
        }

        /// <summary>
        /// تحديث حالة العقد الأب
        /// </summary>
        private void UpdateParentNodesState(TreeNode parentNode)
        {
            if (parentNode == null) return;

            UpdateParentNodeState(parentNode);
            UpdateParentNodesState(parentNode.Parent);
        }

        /// <summary>
        /// تعيين حالة العقد الفرعية
        /// </summary>
        private void SetChildNodesState(TreeNode parentNode, bool isChecked)
        {
            foreach (TreeNode childNode in parentNode.Nodes)
            {
                childNode.Checked = isChecked;
                if (childNode.Nodes.Count > 0)
                {
                    SetChildNodesState(childNode, isChecked);
                }
            }
        }

        /// <summary>
        /// البحث في الصلاحيات
        /// </summary>
        private void TxtSearch_TextChanged(object sender, EventArgs e)
        {
            var searchText = txtSearch.Text.ToLower();

            if (string.IsNullOrWhiteSpace(searchText))
            {
                // إظهار جميع العقد
                ShowAllNodes(treePermissions.Nodes);
            }
            else
            {
                // إخفاء/إظهار العقد حسب البحث
                FilterNodes(treePermissions.Nodes, searchText);
            }
        }

        /// <summary>
        /// إظهار جميع العقد
        /// </summary>
        private void ShowAllNodes(TreeNodeCollection nodes)
        {
            foreach (TreeNode node in nodes)
            {
                node.ForeColor = node.Tag is SmartCreator.Entities.Permission ? Color.White :
                                node.Tag?.ToString() == "Module" ? Color.LightBlue : Color.LightGreen;
                ShowAllNodes(node.Nodes);
            }
        }

        /// <summary>
        /// فلترة العقد حسب النص
        /// </summary>
        private bool FilterNodes(TreeNodeCollection nodes, string searchText)
        {
            bool hasVisibleChild = false;

            foreach (TreeNode node in nodes)
            {
                bool nodeMatches = node.Text.ToLower().Contains(searchText);
                bool childMatches = FilterNodes(node.Nodes, searchText);

                if (nodeMatches || childMatches)
                {
                    node.ForeColor = nodeMatches ? Color.Yellow :
                                    node.Tag is SmartCreator.Entities.Permission ? Color.White :
                                    node.Tag?.ToString() == "Module" ? Color.LightBlue : Color.LightGreen;
                    hasVisibleChild = true;
                }
                else
                {
                    node.ForeColor = Color.Gray;
                }
            }

            return hasVisibleChild;
        }

        /// <summary>
        /// تحديد جميع الصلاحيات
        /// </summary>
        private void BtnSelectAll_Click(object sender, EventArgs e)
        {
            _isLoadingPermissions = true;
            SetAllNodesState(treePermissions.Nodes, true);
            _isLoadingPermissions = false;
        }

        /// <summary>
        /// إلغاء تحديد جميع الصلاحيات
        /// </summary>
        private void BtnDeselectAll_Click(object sender, EventArgs e)
        {
            _isLoadingPermissions = true;
            SetAllNodesState(treePermissions.Nodes, false);
            _isLoadingPermissions = false;
        }

        /// <summary>
        /// تعيين حالة جميع العقد
        /// </summary>
        private void SetAllNodesState(TreeNodeCollection nodes, bool isChecked)
        {
            foreach (TreeNode node in nodes)
            {
                node.Checked = isChecked;
                node.BackColor = Color.Transparent;
                SetAllNodesState(node.Nodes, isChecked);
            }
        }

        /// <summary>
        /// توسيع جميع العقد
        /// </summary>
        private void BtnExpandAll_Click(object sender, EventArgs e)
        {
            treePermissions.ExpandAll();
        }

        /// <summary>
        /// طي جميع العقد
        /// </summary>
        private void BtnCollapseAll_Click(object sender, EventArgs e)
        {
            treePermissions.CollapseAll();
        }

        /// <summary>
        /// حفظ الصلاحيات
        /// </summary>
        private async void BtnSave_Click(object sender, EventArgs e)
        {
            try
            {
                btnSave.Enabled = false;
                btnSave.Text = "جاري الحفظ...";

                // جمع الصلاحيات المحددة
                var selectedPermissionIds = GetSelectedPermissionIds(treePermissions.Nodes);

                // تحديث صلاحيات المستخدم
                var success = await _permissionService.UpdateUserPermissionsAsync(
                    _user.Id, selectedPermissionIds, Global_Variable.CurrentUser?.Id ?? 1);

                if (success)
                {
                    // تسجيل النشاط
                    await SecurityHelper.LogActivityAsync(
                        "تحديث صلاحيات مستخدم",
                        "إدارة الصلاحيات",
                        $"تم تحديث صلاحيات المستخدم: {_user.Username}");

                    MessageBox.Show("تم حفظ الصلاحيات بنجاح", "نجح الحفظ",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);

                    this.DialogResult = DialogResult.OK;
                    this.Close();
                }
                else
                {
                    MessageBox.Show("فشل في حفظ الصلاحيات", "خطأ",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ الصلاحيات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                btnSave.Enabled = true;
                btnSave.Text = "💾 حفظ الصلاحيات";
            }
        }

        /// <summary>
        /// الحصول على معرفات الصلاحيات المحددة
        /// </summary>
        private List<int> GetSelectedPermissionIds(TreeNodeCollection nodes)
        {
            var selectedIds = new List<int>();

            foreach (TreeNode node in nodes)
            {
                if (node.Tag is Permission permission && node.Checked)
                {
                    selectedIds.Add(permission.Id);
                }

                if (node.Nodes.Count > 0)
                {
                    selectedIds.AddRange(GetSelectedPermissionIds(node.Nodes));
                }
            }

            return selectedIds;
        }

        /// <summary>
        /// إلغاء العملية
        /// </summary>
        private void BtnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }
    }
}
