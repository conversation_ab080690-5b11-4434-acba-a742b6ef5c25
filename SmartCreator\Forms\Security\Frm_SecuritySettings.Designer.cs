namespace SmartCreator.Forms.Security
{
    partial class Frm_SecuritySettings
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.pnlMain = new System.Windows.Forms.Panel();
            this.tabControl1 = new System.Windows.Forms.TabControl();
            this.tabPassword = new System.Windows.Forms.TabPage();
            this.pnlPassword = new SmartCreator.RJControls.RJPanel();
            this.numPasswordHistory = new System.Windows.Forms.NumericUpDown();
            this.chkPasswordHistory = new System.Windows.Forms.CheckBox();
            this.numPasswordExpiry = new System.Windows.Forms.NumericUpDown();
            this.lblPasswordExpiry = new SmartCreator.RJControls.RJLabel();
            this.numMinPasswordLength = new System.Windows.Forms.NumericUpDown();
            this.lblMinPasswordLength = new SmartCreator.RJControls.RJLabel();
            this.chkPasswordComplexity = new System.Windows.Forms.CheckBox();
            this.lblPasswordTitle = new SmartCreator.RJControls.RJLabel();
            this.tabSession = new System.Windows.Forms.TabPage();
            this.pnlSession = new SmartCreator.RJControls.RJPanel();
            this.numAutoLockTime = new System.Windows.Forms.NumericUpDown();
            this.chkAutoLock = new System.Windows.Forms.CheckBox();
            this.numSessionTimeout = new System.Windows.Forms.NumericUpDown();
            this.lblSessionTimeout = new SmartCreator.RJControls.RJLabel();
            this.lblSessionTitle = new SmartCreator.RJControls.RJLabel();
            this.tabLogin = new System.Windows.Forms.TabPage();
            this.pnlLogin = new SmartCreator.RJControls.RJPanel();
            this.numLockoutDuration = new System.Windows.Forms.NumericUpDown();
            this.lblLockoutDuration = new SmartCreator.RJControls.RJLabel();
            this.numMaxFailedAttempts = new System.Windows.Forms.NumericUpDown();
            this.lblMaxFailedAttempts = new SmartCreator.RJControls.RJLabel();
            this.chkLockAfterFailedAttempts = new System.Windows.Forms.CheckBox();
            this.lblLoginTitle = new SmartCreator.RJControls.RJLabel();
            this.tabAudit = new System.Windows.Forms.TabPage();
            this.pnlAudit = new SmartCreator.RJControls.RJPanel();
            this.numAuditRetention = new System.Windows.Forms.NumericUpDown();
            this.lblAuditRetention = new SmartCreator.RJControls.RJLabel();
            this.chkAuditDataChanges = new System.Windows.Forms.CheckBox();
            this.chkAuditPermissions = new System.Windows.Forms.CheckBox();
            this.chkAuditLogin = new System.Windows.Forms.CheckBox();
            this.lblAuditTitle = new SmartCreator.RJControls.RJLabel();
            this.tabBackup = new System.Windows.Forms.TabPage();
            this.pnlBackup = new SmartCreator.RJControls.RJPanel();
            this.btnBackupNow = new SmartCreator.RJControls.RJButton();
            this.numBackupRetention = new System.Windows.Forms.NumericUpDown();
            this.lblBackupRetention = new SmartCreator.RJControls.RJLabel();
            this.cmbBackupFrequency = new SmartCreator.RJControls.RJComboBox();
            this.lblBackupFrequency = new SmartCreator.RJControls.RJLabel();
            this.chkAutoBackup = new System.Windows.Forms.CheckBox();
            this.lblBackupTitle = new SmartCreator.RJControls.RJLabel();
            this.pnlButtons = new System.Windows.Forms.Panel();
            this.btnImportSettings = new SmartCreator.RJControls.RJButton();
            this.btnExportSettings = new SmartCreator.RJControls.RJButton();
            this.btnTestConnection = new SmartCreator.RJControls.RJButton();
            this.btnReset = new SmartCreator.RJControls.RJButton();
            this.btnSave = new SmartCreator.RJControls.RJButton();
            this.pnlClientArea.SuspendLayout();
            this.pnlMain.SuspendLayout();
            this.tabControl1.SuspendLayout();
            this.tabPassword.SuspendLayout();
            this.pnlPassword.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numPasswordHistory)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numPasswordExpiry)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMinPasswordLength)).BeginInit();
            this.tabSession.SuspendLayout();
            this.pnlSession.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numAutoLockTime)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numSessionTimeout)).BeginInit();
            this.tabLogin.SuspendLayout();
            this.pnlLogin.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numLockoutDuration)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMaxFailedAttempts)).BeginInit();
            this.tabAudit.SuspendLayout();
            this.pnlAudit.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numAuditRetention)).BeginInit();
            this.tabBackup.SuspendLayout();
            this.pnlBackup.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numBackupRetention)).BeginInit();
            this.pnlButtons.SuspendLayout();
            this.SuspendLayout();
            // 
            // pnlClientArea
            // 
            this.pnlClientArea.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.pnlClientArea.Controls.Add(this.pnlMain);
            this.pnlClientArea.Location = new System.Drawing.Point(5, 45);
            this.pnlClientArea.Size = new System.Drawing.Size(990, 700);
            // 
            // lblCaption
            // 
            this.lblCaption.Size = new System.Drawing.Size(78, 17);
            this.lblCaption.Text = "إعدادات الأمان";
            // 
            // pnlMain
            // 
            this.pnlMain.Controls.Add(this.tabControl1);
            this.pnlMain.Controls.Add(this.pnlButtons);
            this.pnlMain.Dock = System.Windows.Forms.DockStyle.Fill;
            this.pnlMain.Location = new System.Drawing.Point(0, 0);
            this.pnlMain.Name = "pnlMain";
            this.pnlMain.Padding = new System.Windows.Forms.Padding(10);
            this.pnlMain.Size = new System.Drawing.Size(990, 700);
            this.pnlMain.TabIndex = 0;
            // 
            // tabControl1
            // 
            this.tabControl1.Controls.Add(this.tabPassword);
            this.tabControl1.Controls.Add(this.tabSession);
            this.tabControl1.Controls.Add(this.tabLogin);
            this.tabControl1.Controls.Add(this.tabAudit);
            this.tabControl1.Controls.Add(this.tabBackup);
            this.tabControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tabControl1.Font = new System.Drawing.Font("Segoe UI", 10F);
            this.tabControl1.Location = new System.Drawing.Point(10, 10);
            this.tabControl1.Name = "tabControl1";
            this.tabControl1.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.tabControl1.RightToLeftLayout = true;
            this.tabControl1.SelectedIndex = 0;
            this.tabControl1.Size = new System.Drawing.Size(970, 630);
            this.tabControl1.TabIndex = 0;
            // 
            // tabPassword
            // 
            this.tabPassword.Controls.Add(this.pnlPassword);
            this.tabPassword.Location = new System.Drawing.Point(4, 26);
            this.tabPassword.Name = "tabPassword";
            this.tabPassword.Padding = new System.Windows.Forms.Padding(3);
            this.tabPassword.Size = new System.Drawing.Size(962, 600);
            this.tabPassword.TabIndex = 0;
            this.tabPassword.Text = "🔐 كلمة المرور";
            this.tabPassword.UseVisualStyleBackColor = true;
            // 
            // pnlPassword
            // 
            this.pnlPassword.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.pnlPassword.BorderRadius = 10;
            this.pnlPassword.Controls.Add(this.numPasswordHistory);
            this.pnlPassword.Controls.Add(this.chkPasswordHistory);
            this.pnlPassword.Controls.Add(this.numPasswordExpiry);
            this.pnlPassword.Controls.Add(this.lblPasswordExpiry);
            this.pnlPassword.Controls.Add(this.numMinPasswordLength);
            this.pnlPassword.Controls.Add(this.lblMinPasswordLength);
            this.pnlPassword.Controls.Add(this.chkPasswordComplexity);
            this.pnlPassword.Controls.Add(this.lblPasswordTitle);
            this.pnlPassword.Customizable = false;
            this.pnlPassword.Dock = System.Windows.Forms.DockStyle.Fill;
            this.pnlPassword.Location = new System.Drawing.Point(3, 3);
            this.pnlPassword.Name = "pnlPassword";
            this.pnlPassword.Size = new System.Drawing.Size(956, 594);
            this.pnlPassword.TabIndex = 0;
            // 
            // numPasswordHistory
            // 
            this.numPasswordHistory.Font = new System.Drawing.Font("Segoe UI", 9F);
            this.numPasswordHistory.Location = new System.Drawing.Point(30, 250);
            this.numPasswordHistory.Maximum = new decimal(new int[] {
            20,
            0,
            0,
            0});
            this.numPasswordHistory.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numPasswordHistory.Name = "numPasswordHistory";
            this.numPasswordHistory.Size = new System.Drawing.Size(100, 23);
            this.numPasswordHistory.TabIndex = 7;
            this.numPasswordHistory.Value = new decimal(new int[] {
            5,
            0,
            0,
            0});
            // 
            // chkPasswordHistory
            // 
            this.chkPasswordHistory.AutoSize = true;
            this.chkPasswordHistory.Font = new System.Drawing.Font("Segoe UI", 10F);
            this.chkPasswordHistory.Location = new System.Drawing.Point(30, 220);
            this.chkPasswordHistory.Name = "chkPasswordHistory";
            this.chkPasswordHistory.Size = new System.Drawing.Size(215, 23);
            this.chkPasswordHistory.TabIndex = 6;
            this.chkPasswordHistory.Text = "تذكر كلمات المرور السابقة (عدد)";
            this.chkPasswordHistory.UseVisualStyleBackColor = true;
            // 
            // numPasswordExpiry
            // 
            this.numPasswordExpiry.Font = new System.Drawing.Font("Segoe UI", 9F);
            this.numPasswordExpiry.Location = new System.Drawing.Point(30, 180);
            this.numPasswordExpiry.Maximum = new decimal(new int[] {
            365,
            0,
            0,
            0});
            this.numPasswordExpiry.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numPasswordExpiry.Name = "numPasswordExpiry";
            this.numPasswordExpiry.Size = new System.Drawing.Size(100, 23);
            this.numPasswordExpiry.TabIndex = 5;
            this.numPasswordExpiry.Value = new decimal(new int[] {
            90,
            0,
            0,
            0});
            // 
            // lblPasswordExpiry
            // 
            this.lblPasswordExpiry.AutoSize = true;
            this.lblPasswordExpiry.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.lblPasswordExpiry.Font = new System.Drawing.Font("Segoe UI", 9F);
            this.lblPasswordExpiry.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.lblPasswordExpiry.LinkLabel = false;
            this.lblPasswordExpiry.Location = new System.Drawing.Point(30, 150);
            this.lblPasswordExpiry.Name = "lblPasswordExpiry";
            this.lblPasswordExpiry.Size = new System.Drawing.Size(160, 15);
            this.lblPasswordExpiry.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.lblPasswordExpiry.TabIndex = 4;
            this.lblPasswordExpiry.Text = "انتهاء صلاحية كلمة المرور (أيام):";
            // 
            // numMinPasswordLength
            // 
            this.numMinPasswordLength.Font = new System.Drawing.Font("Segoe UI", 9F);
            this.numMinPasswordLength.Location = new System.Drawing.Point(30, 110);
            this.numMinPasswordLength.Maximum = new decimal(new int[] {
            50,
            0,
            0,
            0});
            this.numMinPasswordLength.Minimum = new decimal(new int[] {
            4,
            0,
            0,
            0});
            this.numMinPasswordLength.Name = "numMinPasswordLength";
            this.numMinPasswordLength.Size = new System.Drawing.Size(100, 23);
            this.numMinPasswordLength.TabIndex = 3;
            this.numMinPasswordLength.Value = new decimal(new int[] {
            8,
            0,
            0,
            0});
            // 
            // lblMinPasswordLength
            // 
            this.lblMinPasswordLength.AutoSize = true;
            this.lblMinPasswordLength.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.lblMinPasswordLength.Font = new System.Drawing.Font("Segoe UI", 9F);
            this.lblMinPasswordLength.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.lblMinPasswordLength.LinkLabel = false;
            this.lblMinPasswordLength.Location = new System.Drawing.Point(30, 80);
            this.lblMinPasswordLength.Name = "lblMinPasswordLength";
            this.lblMinPasswordLength.Size = new System.Drawing.Size(152, 15);
            this.lblMinPasswordLength.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.lblMinPasswordLength.TabIndex = 2;
            this.lblMinPasswordLength.Text = "الحد الأدنى لطول كلمة المرور:";
            // 
            // chkPasswordComplexity
            // 
            this.chkPasswordComplexity.AutoSize = true;
            this.chkPasswordComplexity.Font = new System.Drawing.Font("Segoe UI", 10F);
            this.chkPasswordComplexity.Location = new System.Drawing.Point(30, 50);
            this.chkPasswordComplexity.Name = "chkPasswordComplexity";
            this.chkPasswordComplexity.Size = new System.Drawing.Size(209, 23);
            this.chkPasswordComplexity.TabIndex = 1;
            this.chkPasswordComplexity.Text = "تطبيق قواعد تعقيد كلمة المرور";
            this.chkPasswordComplexity.UseVisualStyleBackColor = true;
            // 
            // lblPasswordTitle
            // 
            this.lblPasswordTitle.AutoSize = true;
            this.lblPasswordTitle.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.lblPasswordTitle.Font = new System.Drawing.Font("Segoe UI", 9F, System.Drawing.FontStyle.Bold);
            this.lblPasswordTitle.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.lblPasswordTitle.LinkLabel = false;
            this.lblPasswordTitle.Location = new System.Drawing.Point(20, 15);
            this.lblPasswordTitle.Name = "lblPasswordTitle";
            this.lblPasswordTitle.Size = new System.Drawing.Size(104, 15);
            this.lblPasswordTitle.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.lblPasswordTitle.TabIndex = 0;
            this.lblPasswordTitle.Text = "إعدادات كلمة المرور";
            // 
            // tabSession
            // 
            this.tabSession.Controls.Add(this.pnlSession);
            this.tabSession.Location = new System.Drawing.Point(4, 26);
            this.tabSession.Name = "tabSession";
            this.tabSession.Padding = new System.Windows.Forms.Padding(3);
            this.tabSession.Size = new System.Drawing.Size(962, 600);
            this.tabSession.TabIndex = 1;
            this.tabSession.Text = "⏱️ الجلسة";
            this.tabSession.UseVisualStyleBackColor = true;
            // 
            // pnlSession
            // 
            this.pnlSession.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.pnlSession.BorderRadius = 10;
            this.pnlSession.Controls.Add(this.numAutoLockTime);
            this.pnlSession.Controls.Add(this.chkAutoLock);
            this.pnlSession.Controls.Add(this.numSessionTimeout);
            this.pnlSession.Controls.Add(this.lblSessionTimeout);
            this.pnlSession.Controls.Add(this.lblSessionTitle);
            this.pnlSession.Customizable = false;
            this.pnlSession.Dock = System.Windows.Forms.DockStyle.Fill;
            this.pnlSession.Location = new System.Drawing.Point(3, 3);
            this.pnlSession.Name = "pnlSession";
            this.pnlSession.Size = new System.Drawing.Size(956, 594);
            this.pnlSession.TabIndex = 0;
            // 
            // numAutoLockTime
            // 
            this.numAutoLockTime.Font = new System.Drawing.Font("Segoe UI", 9F);
            this.numAutoLockTime.Location = new System.Drawing.Point(30, 150);
            this.numAutoLockTime.Maximum = new decimal(new int[] {
            60,
            0,
            0,
            0});
            this.numAutoLockTime.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numAutoLockTime.Name = "numAutoLockTime";
            this.numAutoLockTime.Size = new System.Drawing.Size(100, 23);
            this.numAutoLockTime.TabIndex = 4;
            this.numAutoLockTime.Value = new decimal(new int[] {
            15,
            0,
            0,
            0});
            // 
            // chkAutoLock
            // 
            this.chkAutoLock.AutoSize = true;
            this.chkAutoLock.Font = new System.Drawing.Font("Segoe UI", 10F);
            this.chkAutoLock.Location = new System.Drawing.Point(30, 120);
            this.chkAutoLock.Name = "chkAutoLock";
            this.chkAutoLock.Size = new System.Drawing.Size(236, 23);
            this.chkAutoLock.TabIndex = 3;
            this.chkAutoLock.Text = "قفل تلقائي عند عدم النشاط (دقائق)";
            this.chkAutoLock.UseVisualStyleBackColor = true;
            // 
            // numSessionTimeout
            // 
            this.numSessionTimeout.Font = new System.Drawing.Font("Segoe UI", 9F);
            this.numSessionTimeout.Location = new System.Drawing.Point(30, 80);
            this.numSessionTimeout.Maximum = new decimal(new int[] {
            480,
            0,
            0,
            0});
            this.numSessionTimeout.Minimum = new decimal(new int[] {
            5,
            0,
            0,
            0});
            this.numSessionTimeout.Name = "numSessionTimeout";
            this.numSessionTimeout.Size = new System.Drawing.Size(100, 23);
            this.numSessionTimeout.TabIndex = 2;
            this.numSessionTimeout.Value = new decimal(new int[] {
            30,
            0,
            0,
            0});
            // 
            // lblSessionTimeout
            // 
            this.lblSessionTimeout.AutoSize = true;
            this.lblSessionTimeout.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.lblSessionTimeout.Font = new System.Drawing.Font("Segoe UI", 9F);
            this.lblSessionTimeout.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.lblSessionTimeout.LinkLabel = false;
            this.lblSessionTimeout.Location = new System.Drawing.Point(30, 50);
            this.lblSessionTimeout.Name = "lblSessionTimeout";
            this.lblSessionTimeout.Size = new System.Drawing.Size(111, 15);
            this.lblSessionTimeout.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.lblSessionTimeout.TabIndex = 1;
            this.lblSessionTimeout.Text = "انتهاء الجلسة (دقائق):";
            // 
            // lblSessionTitle
            // 
            this.lblSessionTitle.AutoSize = true;
            this.lblSessionTitle.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.lblSessionTitle.Font = new System.Drawing.Font("Segoe UI", 9F, System.Drawing.FontStyle.Bold);
            this.lblSessionTitle.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.lblSessionTitle.LinkLabel = false;
            this.lblSessionTitle.Location = new System.Drawing.Point(20, 15);
            this.lblSessionTitle.Name = "lblSessionTitle";
            this.lblSessionTitle.Size = new System.Drawing.Size(83, 15);
            this.lblSessionTitle.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.lblSessionTitle.TabIndex = 0;
            this.lblSessionTitle.Text = "إعدادات الجلسة";
            // 
            // tabLogin
            // 
            this.tabLogin.Controls.Add(this.pnlLogin);
            this.tabLogin.Location = new System.Drawing.Point(4, 26);
            this.tabLogin.Name = "tabLogin";
            this.tabLogin.Padding = new System.Windows.Forms.Padding(3);
            this.tabLogin.Size = new System.Drawing.Size(962, 600);
            this.tabLogin.TabIndex = 2;
            this.tabLogin.Text = "🔑 تسجيل الدخول";
            this.tabLogin.UseVisualStyleBackColor = true;
            // 
            // pnlLogin
            // 
            this.pnlLogin.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.pnlLogin.BorderRadius = 10;
            this.pnlLogin.Controls.Add(this.numLockoutDuration);
            this.pnlLogin.Controls.Add(this.lblLockoutDuration);
            this.pnlLogin.Controls.Add(this.numMaxFailedAttempts);
            this.pnlLogin.Controls.Add(this.lblMaxFailedAttempts);
            this.pnlLogin.Controls.Add(this.chkLockAfterFailedAttempts);
            this.pnlLogin.Controls.Add(this.lblLoginTitle);
            this.pnlLogin.Customizable = false;
            this.pnlLogin.Dock = System.Windows.Forms.DockStyle.Fill;
            this.pnlLogin.Location = new System.Drawing.Point(3, 3);
            this.pnlLogin.Name = "pnlLogin";
            this.pnlLogin.Size = new System.Drawing.Size(956, 594);
            this.pnlLogin.TabIndex = 0;
            // 
            // numLockoutDuration
            // 
            this.numLockoutDuration.Font = new System.Drawing.Font("Segoe UI", 9F);
            this.numLockoutDuration.Location = new System.Drawing.Point(30, 200);
            this.numLockoutDuration.Maximum = new decimal(new int[] {
            1440,
            0,
            0,
            0});
            this.numLockoutDuration.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numLockoutDuration.Name = "numLockoutDuration";
            this.numLockoutDuration.Size = new System.Drawing.Size(100, 23);
            this.numLockoutDuration.TabIndex = 5;
            this.numLockoutDuration.Value = new decimal(new int[] {
            15,
            0,
            0,
            0});
            // 
            // lblLockoutDuration
            // 
            this.lblLockoutDuration.AutoSize = true;
            this.lblLockoutDuration.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.lblLockoutDuration.Font = new System.Drawing.Font("Segoe UI", 9F);
            this.lblLockoutDuration.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.lblLockoutDuration.LinkLabel = false;
            this.lblLockoutDuration.Location = new System.Drawing.Point(30, 170);
            this.lblLockoutDuration.Name = "lblLockoutDuration";
            this.lblLockoutDuration.Size = new System.Drawing.Size(98, 15);
            this.lblLockoutDuration.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.lblLockoutDuration.TabIndex = 4;
            this.lblLockoutDuration.Text = "مدة القفل (دقائق):";
            // 
            // numMaxFailedAttempts
            // 
            this.numMaxFailedAttempts.Font = new System.Drawing.Font("Segoe UI", 9F);
            this.numMaxFailedAttempts.Location = new System.Drawing.Point(30, 130);
            this.numMaxFailedAttempts.Maximum = new decimal(new int[] {
            10,
            0,
            0,
            0});
            this.numMaxFailedAttempts.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numMaxFailedAttempts.Name = "numMaxFailedAttempts";
            this.numMaxFailedAttempts.Size = new System.Drawing.Size(100, 23);
            this.numMaxFailedAttempts.TabIndex = 3;
            this.numMaxFailedAttempts.Value = new decimal(new int[] {
            3,
            0,
            0,
            0});
            // 
            // lblMaxFailedAttempts
            // 
            this.lblMaxFailedAttempts.AutoSize = true;
            this.lblMaxFailedAttempts.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.lblMaxFailedAttempts.Font = new System.Drawing.Font("Segoe UI", 9F);
            this.lblMaxFailedAttempts.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.lblMaxFailedAttempts.LinkLabel = false;
            this.lblMaxFailedAttempts.Location = new System.Drawing.Point(30, 100);
            this.lblMaxFailedAttempts.Name = "lblMaxFailedAttempts";
            this.lblMaxFailedAttempts.Size = new System.Drawing.Size(172, 15);
            this.lblMaxFailedAttempts.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.lblMaxFailedAttempts.TabIndex = 2;
            this.lblMaxFailedAttempts.Text = "عدد المحاولات الفاشلة المسموحة:";
            // 
            // chkLockAfterFailedAttempts
            // 
            this.chkLockAfterFailedAttempts.AutoSize = true;
            this.chkLockAfterFailedAttempts.Font = new System.Drawing.Font("Segoe UI", 10F);
            this.chkLockAfterFailedAttempts.Location = new System.Drawing.Point(30, 60);
            this.chkLockAfterFailedAttempts.Name = "chkLockAfterFailedAttempts";
            this.chkLockAfterFailedAttempts.Size = new System.Drawing.Size(235, 23);
            this.chkLockAfterFailedAttempts.TabIndex = 1;
            this.chkLockAfterFailedAttempts.Text = "قفل الحساب بعد المحاولات الفاشلة";
            this.chkLockAfterFailedAttempts.UseVisualStyleBackColor = true;
            // 
            // lblLoginTitle
            // 
            this.lblLoginTitle.AutoSize = true;
            this.lblLoginTitle.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.lblLoginTitle.Font = new System.Drawing.Font("Segoe UI", 9F, System.Drawing.FontStyle.Bold);
            this.lblLoginTitle.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.lblLoginTitle.LinkLabel = false;
            this.lblLoginTitle.Location = new System.Drawing.Point(20, 15);
            this.lblLoginTitle.Name = "lblLoginTitle";
            this.lblLoginTitle.Size = new System.Drawing.Size(119, 15);
            this.lblLoginTitle.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.lblLoginTitle.TabIndex = 0;
            this.lblLoginTitle.Text = "إعدادات تسجيل الدخول";
            // 
            // tabAudit
            // 
            this.tabAudit.Controls.Add(this.pnlAudit);
            this.tabAudit.Location = new System.Drawing.Point(4, 26);
            this.tabAudit.Name = "tabAudit";
            this.tabAudit.Padding = new System.Windows.Forms.Padding(3);
            this.tabAudit.Size = new System.Drawing.Size(962, 600);
            this.tabAudit.TabIndex = 3;
            this.tabAudit.Text = "📊 التدقيق";
            this.tabAudit.UseVisualStyleBackColor = true;
            // 
            // pnlAudit
            // 
            this.pnlAudit.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.pnlAudit.BorderRadius = 10;
            this.pnlAudit.Controls.Add(this.numAuditRetention);
            this.pnlAudit.Controls.Add(this.lblAuditRetention);
            this.pnlAudit.Controls.Add(this.chkAuditDataChanges);
            this.pnlAudit.Controls.Add(this.chkAuditPermissions);
            this.pnlAudit.Controls.Add(this.chkAuditLogin);
            this.pnlAudit.Controls.Add(this.lblAuditTitle);
            this.pnlAudit.Customizable = false;
            this.pnlAudit.Dock = System.Windows.Forms.DockStyle.Fill;
            this.pnlAudit.Location = new System.Drawing.Point(3, 3);
            this.pnlAudit.Name = "pnlAudit";
            this.pnlAudit.Size = new System.Drawing.Size(956, 594);
            this.pnlAudit.TabIndex = 0;
            // 
            // numAuditRetention
            // 
            this.numAuditRetention.Font = new System.Drawing.Font("Segoe UI", 9F);
            this.numAuditRetention.Location = new System.Drawing.Point(30, 190);
            this.numAuditRetention.Maximum = new decimal(new int[] {
            3650,
            0,
            0,
            0});
            this.numAuditRetention.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numAuditRetention.Name = "numAuditRetention";
            this.numAuditRetention.Size = new System.Drawing.Size(100, 23);
            this.numAuditRetention.TabIndex = 5;
            this.numAuditRetention.Value = new decimal(new int[] {
            365,
            0,
            0,
            0});
            // 
            // lblAuditRetention
            // 
            this.lblAuditRetention.AutoSize = true;
            this.lblAuditRetention.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.lblAuditRetention.Font = new System.Drawing.Font("Segoe UI", 9F);
            this.lblAuditRetention.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.lblAuditRetention.LinkLabel = false;
            this.lblAuditRetention.Location = new System.Drawing.Point(30, 160);
            this.lblAuditRetention.Name = "lblAuditRetention";
            this.lblAuditRetention.Size = new System.Drawing.Size(140, 15);
            this.lblAuditRetention.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.lblAuditRetention.TabIndex = 4;
            this.lblAuditRetention.Text = "فترة الاحتفاظ بالسجل (أيام):";
            // 
            // chkAuditDataChanges
            // 
            this.chkAuditDataChanges.AutoSize = true;
            this.chkAuditDataChanges.Font = new System.Drawing.Font("Segoe UI", 10F);
            this.chkAuditDataChanges.Location = new System.Drawing.Point(30, 120);
            this.chkAuditDataChanges.Name = "chkAuditDataChanges";
            this.chkAuditDataChanges.Size = new System.Drawing.Size(165, 23);
            this.chkAuditDataChanges.TabIndex = 3;
            this.chkAuditDataChanges.Text = "تسجيل تغييرات البيانات";
            this.chkAuditDataChanges.UseVisualStyleBackColor = true;
            // 
            // chkAuditPermissions
            // 
            this.chkAuditPermissions.AutoSize = true;
            this.chkAuditPermissions.Font = new System.Drawing.Font("Segoe UI", 10F);
            this.chkAuditPermissions.Location = new System.Drawing.Point(30, 90);
            this.chkAuditPermissions.Name = "chkAuditPermissions";
            this.chkAuditPermissions.Size = new System.Drawing.Size(182, 23);
            this.chkAuditPermissions.TabIndex = 2;
            this.chkAuditPermissions.Text = "تسجيل تغييرات الصلاحيات";
            this.chkAuditPermissions.UseVisualStyleBackColor = true;
            // 
            // chkAuditLogin
            // 
            this.chkAuditLogin.AutoSize = true;
            this.chkAuditLogin.Font = new System.Drawing.Font("Segoe UI", 10F);
            this.chkAuditLogin.Location = new System.Drawing.Point(30, 60);
            this.chkAuditLogin.Name = "chkAuditLogin";
            this.chkAuditLogin.Size = new System.Drawing.Size(158, 23);
            this.chkAuditLogin.TabIndex = 1;
            this.chkAuditLogin.Text = "تسجيل عمليات الدخول";
            this.chkAuditLogin.UseVisualStyleBackColor = true;
            // 
            // lblAuditTitle
            // 
            this.lblAuditTitle.AutoSize = true;
            this.lblAuditTitle.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.lblAuditTitle.Font = new System.Drawing.Font("Segoe UI", 9F, System.Drawing.FontStyle.Bold);
            this.lblAuditTitle.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.lblAuditTitle.LinkLabel = false;
            this.lblAuditTitle.Location = new System.Drawing.Point(20, 15);
            this.lblAuditTitle.Name = "lblAuditTitle";
            this.lblAuditTitle.Size = new System.Drawing.Size(85, 15);
            this.lblAuditTitle.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.lblAuditTitle.TabIndex = 0;
            this.lblAuditTitle.Text = "إعدادات التدقيق";
            // 
            // tabBackup
            // 
            this.tabBackup.Controls.Add(this.pnlBackup);
            this.tabBackup.Location = new System.Drawing.Point(4, 26);
            this.tabBackup.Name = "tabBackup";
            this.tabBackup.Padding = new System.Windows.Forms.Padding(3);
            this.tabBackup.Size = new System.Drawing.Size(962, 600);
            this.tabBackup.TabIndex = 4;
            this.tabBackup.Text = "💾 النسخ الاحتياطي";
            this.tabBackup.UseVisualStyleBackColor = true;
            // 
            // pnlBackup
            // 
            this.pnlBackup.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.pnlBackup.BorderRadius = 10;
            this.pnlBackup.Controls.Add(this.btnBackupNow);
            this.pnlBackup.Controls.Add(this.numBackupRetention);
            this.pnlBackup.Controls.Add(this.lblBackupRetention);
            this.pnlBackup.Controls.Add(this.cmbBackupFrequency);
            this.pnlBackup.Controls.Add(this.lblBackupFrequency);
            this.pnlBackup.Controls.Add(this.chkAutoBackup);
            this.pnlBackup.Controls.Add(this.lblBackupTitle);
            this.pnlBackup.Customizable = false;
            this.pnlBackup.Dock = System.Windows.Forms.DockStyle.Fill;
            this.pnlBackup.Location = new System.Drawing.Point(3, 3);
            this.pnlBackup.Name = "pnlBackup";
            this.pnlBackup.Size = new System.Drawing.Size(956, 594);
            this.pnlBackup.TabIndex = 0;
            // 
            // btnBackupNow
            // 
            this.btnBackupNow.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btnBackupNow.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btnBackupNow.BorderRadius = 5;
            this.btnBackupNow.BorderSize = 0;
            this.btnBackupNow.Design = SmartCreator.RJControls.ButtonDesign.Normal;
            this.btnBackupNow.FlatAppearance.BorderSize = 0;
            this.btnBackupNow.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(115)))), ((int)(((byte)(97)))), ((int)(((byte)(223)))));
            this.btnBackupNow.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(108)))), ((int)(((byte)(91)))), ((int)(((byte)(209)))));
            this.btnBackupNow.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnBackupNow.Font = new System.Drawing.Font("Segoe UI", 10F);
            this.btnBackupNow.ForeColor = System.Drawing.Color.White;
            this.btnBackupNow.IconChar = FontAwesome.Sharp.IconChar.None;
            this.btnBackupNow.IconColor = System.Drawing.Color.White;
            this.btnBackupNow.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btnBackupNow.IconSize = 24;
            this.btnBackupNow.Location = new System.Drawing.Point(30, 250);
            this.btnBackupNow.Name = "btnBackupNow";
            this.btnBackupNow.Size = new System.Drawing.Size(120, 35);
            this.btnBackupNow.Style = SmartCreator.RJControls.ControlStyle.Solid;
            this.btnBackupNow.TabIndex = 6;
            this.btnBackupNow.Text = "💾 نسخ احتياطي الآن";
            this.btnBackupNow.UseVisualStyleBackColor = false;
            // 
            // numBackupRetention
            // 
            this.numBackupRetention.Font = new System.Drawing.Font("Segoe UI", 9F);
            this.numBackupRetention.Location = new System.Drawing.Point(30, 210);
            this.numBackupRetention.Maximum = new decimal(new int[] {
            365,
            0,
            0,
            0});
            this.numBackupRetention.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numBackupRetention.Name = "numBackupRetention";
            this.numBackupRetention.Size = new System.Drawing.Size(100, 23);
            this.numBackupRetention.TabIndex = 5;
            this.numBackupRetention.Value = new decimal(new int[] {
            30,
            0,
            0,
            0});
            // 
            // lblBackupRetention
            // 
            this.lblBackupRetention.AutoSize = true;
            this.lblBackupRetention.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.lblBackupRetention.Font = new System.Drawing.Font("Segoe UI", 9F);
            this.lblBackupRetention.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.lblBackupRetention.LinkLabel = false;
            this.lblBackupRetention.Location = new System.Drawing.Point(30, 180);
            this.lblBackupRetention.Name = "lblBackupRetention";
            this.lblBackupRetention.Size = new System.Drawing.Size(136, 15);
            this.lblBackupRetention.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.lblBackupRetention.TabIndex = 4;
            this.lblBackupRetention.Text = "فترة الاحتفاظ بالنسخ (أيام):";
            // 
            // cmbBackupFrequency
            // 
            this.cmbBackupFrequency.AutoCompleteMode = System.Windows.Forms.AutoCompleteMode.None;
            this.cmbBackupFrequency.AutoCompleteSource = System.Windows.Forms.AutoCompleteSource.None;
            this.cmbBackupFrequency.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.cmbBackupFrequency.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.cmbBackupFrequency.BorderRadius = 0;
            this.cmbBackupFrequency.BorderSize = 1;
            this.cmbBackupFrequency.Customizable = false;
            this.cmbBackupFrequency.DataSource = null;
            this.cmbBackupFrequency.DropDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.cmbBackupFrequency.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cmbBackupFrequency.DropDownTextColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.cmbBackupFrequency.Font = new System.Drawing.Font("Segoe UI", 9F);
            this.cmbBackupFrequency.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.cmbBackupFrequency.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.cmbBackupFrequency.Location = new System.Drawing.Point(30, 130);
            this.cmbBackupFrequency.MinimumSize = new System.Drawing.Size(200, 30);
            this.cmbBackupFrequency.Name = "cmbBackupFrequency";
            this.cmbBackupFrequency.Padding = new System.Windows.Forms.Padding(1);
            this.cmbBackupFrequency.SelectedIndex = -1;
            this.cmbBackupFrequency.SelectedItem = null;
            this.cmbBackupFrequency.SelectedValue = null;
            this.cmbBackupFrequency.Size = new System.Drawing.Size(200, 30);
            this.cmbBackupFrequency.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.cmbBackupFrequency.TabIndex = 3;
            this.cmbBackupFrequency.Texts = "";
            // 
            // lblBackupFrequency
            // 
            this.lblBackupFrequency.AutoSize = true;
            this.lblBackupFrequency.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.lblBackupFrequency.Font = new System.Drawing.Font("Segoe UI", 9F);
            this.lblBackupFrequency.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.lblBackupFrequency.LinkLabel = false;
            this.lblBackupFrequency.Location = new System.Drawing.Point(30, 100);
            this.lblBackupFrequency.Name = "lblBackupFrequency";
            this.lblBackupFrequency.Size = new System.Drawing.Size(63, 15);
            this.lblBackupFrequency.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.lblBackupFrequency.TabIndex = 2;
            this.lblBackupFrequency.Text = "تكرار النسخ:";
            // 
            // chkAutoBackup
            // 
            this.chkAutoBackup.AutoSize = true;
            this.chkAutoBackup.Font = new System.Drawing.Font("Segoe UI", 10F);
            this.chkAutoBackup.Location = new System.Drawing.Point(30, 60);
            this.chkAutoBackup.Name = "chkAutoBackup";
            this.chkAutoBackup.Size = new System.Drawing.Size(149, 23);
            this.chkAutoBackup.TabIndex = 1;
            this.chkAutoBackup.Text = "نسخ احتياطي تلقائي";
            this.chkAutoBackup.UseVisualStyleBackColor = true;
            // 
            // lblBackupTitle
            // 
            this.lblBackupTitle.AutoSize = true;
            this.lblBackupTitle.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.lblBackupTitle.Font = new System.Drawing.Font("Segoe UI", 9F, System.Drawing.FontStyle.Bold);
            this.lblBackupTitle.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.lblBackupTitle.LinkLabel = false;
            this.lblBackupTitle.Location = new System.Drawing.Point(20, 15);
            this.lblBackupTitle.Name = "lblBackupTitle";
            this.lblBackupTitle.Size = new System.Drawing.Size(128, 15);
            this.lblBackupTitle.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.lblBackupTitle.TabIndex = 0;
            this.lblBackupTitle.Text = "إعدادات النسخ الاحتياطي";
            // 
            // pnlButtons
            // 
            this.pnlButtons.Controls.Add(this.btnImportSettings);
            this.pnlButtons.Controls.Add(this.btnExportSettings);
            this.pnlButtons.Controls.Add(this.btnTestConnection);
            this.pnlButtons.Controls.Add(this.btnReset);
            this.pnlButtons.Controls.Add(this.btnSave);
            this.pnlButtons.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.pnlButtons.Location = new System.Drawing.Point(10, 640);
            this.pnlButtons.Name = "pnlButtons";
            this.pnlButtons.Size = new System.Drawing.Size(970, 50);
            this.pnlButtons.TabIndex = 1;
            // 
            // btnImportSettings
            // 
            this.btnImportSettings.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btnImportSettings.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btnImportSettings.BorderRadius = 5;
            this.btnImportSettings.BorderSize = 0;
            this.btnImportSettings.Design = SmartCreator.RJControls.ButtonDesign.Normal;
            this.btnImportSettings.FlatAppearance.BorderSize = 0;
            this.btnImportSettings.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(115)))), ((int)(((byte)(97)))), ((int)(((byte)(223)))));
            this.btnImportSettings.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(108)))), ((int)(((byte)(91)))), ((int)(((byte)(209)))));
            this.btnImportSettings.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnImportSettings.Font = new System.Drawing.Font("Segoe UI", 10F);
            this.btnImportSettings.ForeColor = System.Drawing.Color.White;
            this.btnImportSettings.IconChar = FontAwesome.Sharp.IconChar.None;
            this.btnImportSettings.IconColor = System.Drawing.Color.White;
            this.btnImportSettings.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btnImportSettings.IconSize = 24;
            this.btnImportSettings.Location = new System.Drawing.Point(270, 10);
            this.btnImportSettings.Name = "btnImportSettings";
            this.btnImportSettings.Size = new System.Drawing.Size(120, 35);
            this.btnImportSettings.Style = SmartCreator.RJControls.ControlStyle.Solid;
            this.btnImportSettings.TabIndex = 4;
            this.btnImportSettings.Text = "📥 استيراد الإعدادات";
            this.btnImportSettings.UseVisualStyleBackColor = false;
            // 
            // btnExportSettings
            // 
            this.btnExportSettings.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btnExportSettings.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btnExportSettings.BorderRadius = 5;
            this.btnExportSettings.BorderSize = 0;
            this.btnExportSettings.Design = SmartCreator.RJControls.ButtonDesign.Normal;
            this.btnExportSettings.FlatAppearance.BorderSize = 0;
            this.btnExportSettings.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(115)))), ((int)(((byte)(97)))), ((int)(((byte)(223)))));
            this.btnExportSettings.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(108)))), ((int)(((byte)(91)))), ((int)(((byte)(209)))));
            this.btnExportSettings.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnExportSettings.Font = new System.Drawing.Font("Segoe UI", 10F);
            this.btnExportSettings.ForeColor = System.Drawing.Color.White;
            this.btnExportSettings.IconChar = FontAwesome.Sharp.IconChar.None;
            this.btnExportSettings.IconColor = System.Drawing.Color.White;
            this.btnExportSettings.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btnExportSettings.IconSize = 24;
            this.btnExportSettings.Location = new System.Drawing.Point(140, 10);
            this.btnExportSettings.Name = "btnExportSettings";
            this.btnExportSettings.Size = new System.Drawing.Size(120, 35);
            this.btnExportSettings.Style = SmartCreator.RJControls.ControlStyle.Solid;
            this.btnExportSettings.TabIndex = 3;
            this.btnExportSettings.Text = "📤 تصدير الإعدادات";
            this.btnExportSettings.UseVisualStyleBackColor = false;
            // 
            // btnTestConnection
            // 
            this.btnTestConnection.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btnTestConnection.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btnTestConnection.BorderRadius = 5;
            this.btnTestConnection.BorderSize = 0;
            this.btnTestConnection.Design = SmartCreator.RJControls.ButtonDesign.Normal;
            this.btnTestConnection.FlatAppearance.BorderSize = 0;
            this.btnTestConnection.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(115)))), ((int)(((byte)(97)))), ((int)(((byte)(223)))));
            this.btnTestConnection.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(108)))), ((int)(((byte)(91)))), ((int)(((byte)(209)))));
            this.btnTestConnection.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnTestConnection.Font = new System.Drawing.Font("Segoe UI", 10F);
            this.btnTestConnection.ForeColor = System.Drawing.Color.White;
            this.btnTestConnection.IconChar = FontAwesome.Sharp.IconChar.None;
            this.btnTestConnection.IconColor = System.Drawing.Color.White;
            this.btnTestConnection.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btnTestConnection.IconSize = 24;
            this.btnTestConnection.Location = new System.Drawing.Point(10, 10);
            this.btnTestConnection.Name = "btnTestConnection";
            this.btnTestConnection.Size = new System.Drawing.Size(120, 35);
            this.btnTestConnection.Style = SmartCreator.RJControls.ControlStyle.Solid;
            this.btnTestConnection.TabIndex = 2;
            this.btnTestConnection.Text = "🔗 اختبار الاتصال";
            this.btnTestConnection.UseVisualStyleBackColor = false;
            // 
            // btnReset
            // 
            this.btnReset.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btnReset.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btnReset.BorderRadius = 5;
            this.btnReset.BorderSize = 0;
            this.btnReset.Design = SmartCreator.RJControls.ButtonDesign.Normal;
            this.btnReset.FlatAppearance.BorderSize = 0;
            this.btnReset.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(115)))), ((int)(((byte)(97)))), ((int)(((byte)(223)))));
            this.btnReset.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(108)))), ((int)(((byte)(91)))), ((int)(((byte)(209)))));
            this.btnReset.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnReset.Font = new System.Drawing.Font("Segoe UI", 10F);
            this.btnReset.ForeColor = System.Drawing.Color.White;
            this.btnReset.IconChar = FontAwesome.Sharp.IconChar.None;
            this.btnReset.IconColor = System.Drawing.Color.White;
            this.btnReset.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btnReset.IconSize = 24;
            this.btnReset.Location = new System.Drawing.Point(980, 10);
            this.btnReset.Name = "btnReset";
            this.btnReset.Size = new System.Drawing.Size(90, 35);
            this.btnReset.Style = SmartCreator.RJControls.ControlStyle.Solid;
            this.btnReset.TabIndex = 1;
            this.btnReset.Text = "🔄 إعادة تعيين";
            this.btnReset.UseVisualStyleBackColor = false;
            // 
            // btnSave
            // 
            this.btnSave.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btnSave.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btnSave.BorderRadius = 5;
            this.btnSave.BorderSize = 0;
            this.btnSave.Design = SmartCreator.RJControls.ButtonDesign.Normal;
            this.btnSave.FlatAppearance.BorderSize = 0;
            this.btnSave.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(115)))), ((int)(((byte)(97)))), ((int)(((byte)(223)))));
            this.btnSave.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(108)))), ((int)(((byte)(91)))), ((int)(((byte)(209)))));
            this.btnSave.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnSave.Font = new System.Drawing.Font("Segoe UI", 10F, System.Drawing.FontStyle.Bold);
            this.btnSave.ForeColor = System.Drawing.Color.White;
            this.btnSave.IconChar = FontAwesome.Sharp.IconChar.None;
            this.btnSave.IconColor = System.Drawing.Color.White;
            this.btnSave.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btnSave.IconSize = 24;
            this.btnSave.Location = new System.Drawing.Point(1080, 10);
            this.btnSave.Name = "btnSave";
            this.btnSave.Size = new System.Drawing.Size(90, 35);
            this.btnSave.Style = SmartCreator.RJControls.ControlStyle.Solid;
            this.btnSave.TabIndex = 0;
            this.btnSave.Text = "💾 حفظ";
            this.btnSave.UseVisualStyleBackColor = false;
            // 
            // Frm_SecuritySettings
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 15F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(111)))), ((int)(((byte)(106)))), ((int)(((byte)(143)))));
            this.BorderSize = 5;
            this.Caption = "إعدادات الأمان";
            this.ClientSize = new System.Drawing.Size(1000, 750);
            this.Location = new System.Drawing.Point(0, 0);
            this.Name = "Frm_SecuritySettings";
            this.Padding = new System.Windows.Forms.Padding(5);
            this.Text = "إعدادات الأمان";
            this.pnlClientArea.ResumeLayout(false);
            this.pnlMain.ResumeLayout(false);
            this.tabControl1.ResumeLayout(false);
            this.tabPassword.ResumeLayout(false);
            this.pnlPassword.ResumeLayout(false);
            this.pnlPassword.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numPasswordHistory)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numPasswordExpiry)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMinPasswordLength)).EndInit();
            this.tabSession.ResumeLayout(false);
            this.pnlSession.ResumeLayout(false);
            this.pnlSession.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numAutoLockTime)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numSessionTimeout)).EndInit();
            this.tabLogin.ResumeLayout(false);
            this.pnlLogin.ResumeLayout(false);
            this.pnlLogin.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numLockoutDuration)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMaxFailedAttempts)).EndInit();
            this.tabAudit.ResumeLayout(false);
            this.pnlAudit.ResumeLayout(false);
            this.pnlAudit.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numAuditRetention)).EndInit();
            this.tabBackup.ResumeLayout(false);
            this.pnlBackup.ResumeLayout(false);
            this.pnlBackup.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numBackupRetention)).EndInit();
            this.pnlButtons.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.Panel pnlMain;
        private System.Windows.Forms.TabControl tabControl1;
        private System.Windows.Forms.TabPage tabPassword;
        private SmartCreator.RJControls.RJPanel pnlPassword;
        private System.Windows.Forms.NumericUpDown numPasswordHistory;
        private System.Windows.Forms.CheckBox chkPasswordHistory;
        private System.Windows.Forms.NumericUpDown numPasswordExpiry;
        private SmartCreator.RJControls.RJLabel lblPasswordExpiry;
        private System.Windows.Forms.NumericUpDown numMinPasswordLength;
        private SmartCreator.RJControls.RJLabel lblMinPasswordLength;
        private System.Windows.Forms.CheckBox chkPasswordComplexity;
        private SmartCreator.RJControls.RJLabel lblPasswordTitle;
        private System.Windows.Forms.TabPage tabSession;
        private SmartCreator.RJControls.RJPanel pnlSession;
        private System.Windows.Forms.NumericUpDown numAutoLockTime;
        private System.Windows.Forms.CheckBox chkAutoLock;
        private System.Windows.Forms.NumericUpDown numSessionTimeout;
        private SmartCreator.RJControls.RJLabel lblSessionTimeout;
        private SmartCreator.RJControls.RJLabel lblSessionTitle;
        private System.Windows.Forms.TabPage tabLogin;
        private SmartCreator.RJControls.RJPanel pnlLogin;
        private System.Windows.Forms.NumericUpDown numLockoutDuration;
        private SmartCreator.RJControls.RJLabel lblLockoutDuration;
        private System.Windows.Forms.NumericUpDown numMaxFailedAttempts;
        private SmartCreator.RJControls.RJLabel lblMaxFailedAttempts;
        private System.Windows.Forms.CheckBox chkLockAfterFailedAttempts;
        private SmartCreator.RJControls.RJLabel lblLoginTitle;
        private System.Windows.Forms.TabPage tabAudit;
        private SmartCreator.RJControls.RJPanel pnlAudit;
        private System.Windows.Forms.NumericUpDown numAuditRetention;
        private SmartCreator.RJControls.RJLabel lblAuditRetention;
        private System.Windows.Forms.CheckBox chkAuditDataChanges;
        private System.Windows.Forms.CheckBox chkAuditPermissions;
        private System.Windows.Forms.CheckBox chkAuditLogin;
        private SmartCreator.RJControls.RJLabel lblAuditTitle;
        private System.Windows.Forms.TabPage tabBackup;
        private SmartCreator.RJControls.RJPanel pnlBackup;
        private SmartCreator.RJControls.RJButton btnBackupNow;
        private System.Windows.Forms.NumericUpDown numBackupRetention;
        private SmartCreator.RJControls.RJLabel lblBackupRetention;
        private SmartCreator.RJControls.RJComboBox cmbBackupFrequency;
        private SmartCreator.RJControls.RJLabel lblBackupFrequency;
        private System.Windows.Forms.CheckBox chkAutoBackup;
        private SmartCreator.RJControls.RJLabel lblBackupTitle;
        private System.Windows.Forms.Panel pnlButtons;
        private SmartCreator.RJControls.RJButton btnImportSettings;
        private SmartCreator.RJControls.RJButton btnExportSettings;
        private SmartCreator.RJControls.RJButton btnTestConnection;
        private SmartCreator.RJControls.RJButton btnReset;
        private SmartCreator.RJControls.RJButton btnSave;
    }
}
