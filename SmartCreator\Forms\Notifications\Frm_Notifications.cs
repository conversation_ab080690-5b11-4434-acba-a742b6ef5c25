using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using SmartCreator.Entities;
using SmartCreator.RJControls;
using SmartCreator.RJForms;
using SmartCreator.Services;
using SmartCreator.Settings;
using FontAwesome.Sharp;

namespace SmartCreator.Forms.Notifications
{
    /// <summary>
    /// نموذج عرض الإشعارات
    /// </summary>
    public partial class Frm_Notifications : RJChildForm
    {
        #region Fields
        private readonly NotificationService _notificationService;
        private List<Notification> _notifications;
        private bool _showOnlyUnread = false;
        #endregion

        #region Constructor
        public Frm_Notifications()
        {
            InitializeComponent();
            _notificationService = new NotificationService();
            _notifications = new List<Notification>();

            this.FormIcon = FontAwesome.Sharp.IconChar.Bell;

            SetupForm();
            SetupEvents();
            LoadNotificationsAsync();
        }
        #endregion

        #region Setup Methods
        /// <summary>
        /// إعداد النموذج
        /// </summary>
        private void SetupForm()
        {
            try
            {
                // إعداد الخصائص الأساسية
                this.Size = new Size(900, 600);
                this.MinimumSize = new Size(700, 400);

                // إعداد DataGridView
                SetupDataGridView();

                // إعداد الألوان والخطوط
                ApplyTheme();
            }
            catch (Exception ex)
            {
                RJMessageBox.Show($"خطأ في إعداد النموذج: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// إعداد DataGridView
        /// </summary>
        private void SetupDataGridView()
        {
            // إعداد الخصائص الأساسية
            dgvNotifications.AutoGenerateColumns = false;
            dgvNotifications.AllowUserToAddRows = false;
            dgvNotifications.AllowUserToDeleteRows = false;
            dgvNotifications.ReadOnly = true;
            dgvNotifications.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dgvNotifications.MultiSelect = false;

            // إعداد الأعمدة
            SetupColumns();

            // إعداد التنسيق
            dgvNotifications.RowHeadersVisible = false;
            dgvNotifications.BackgroundColor = Color.White;
            dgvNotifications.BorderStyle = BorderStyle.None;
            dgvNotifications.CellBorderStyle = DataGridViewCellBorderStyle.SingleHorizontal;
            dgvNotifications.DefaultCellStyle.SelectionBackColor = Color.FromArgb(235, 245, 255);
            dgvNotifications.DefaultCellStyle.SelectionForeColor = Color.Black;
            dgvNotifications.RowTemplate.Height = 60;

            // إعداد RTL إذا كان مطلوباً
            if (UIAppearance.DGV_RTL == false)
            {
                dgvNotifications.RightToLeft = RightToLeft.No;
            }
        }

        /// <summary>
        /// إعداد أعمدة DataGridView
        /// </summary>
        private void SetupColumns()
        {
            dgvNotifications.Columns.Clear();

            // عمود الأيقونة
            var iconColumn = new DataGridViewTextBoxColumn
            {
                Name = "TypeIcon",
                HeaderText = "",
                DataPropertyName = "TypeIcon",
                Width = 40,
                ReadOnly = true,
                DefaultCellStyle = new DataGridViewCellStyle
                {
                    Alignment = DataGridViewContentAlignment.MiddleCenter,
                    Font = new Font("Segoe UI Emoji", 16)
                }
            };
            dgvNotifications.Columns.Add(iconColumn);

            // عمود العنوان
            var titleColumn = new DataGridViewTextBoxColumn
            {
                Name = "Title",
                HeaderText = "العنوان",
                DataPropertyName = "Title",
                Width = 200,
                ReadOnly = true,
                DefaultCellStyle = new DataGridViewCellStyle
                {
                    Font = new Font("Tahoma", 10, FontStyle.Bold),
                    WrapMode = DataGridViewTriState.True
                }
            };
            dgvNotifications.Columns.Add(titleColumn);

            // عمود المحتوى
            var messageColumn = new DataGridViewTextBoxColumn
            {
                Name = "Message",
                HeaderText = "المحتوى",
                DataPropertyName = "Message",
                Width = 300,
                ReadOnly = true,
                DefaultCellStyle = new DataGridViewCellStyle
                {
                    WrapMode = DataGridViewTriState.True
                }
            };
            dgvNotifications.Columns.Add(messageColumn);

            // عمود النوع
            var typeColumn = new DataGridViewTextBoxColumn
            {
                Name = "Type",
                HeaderText = "النوع",
                DataPropertyName = "Type",
                Width = 80,
                ReadOnly = true
            };
            dgvNotifications.Columns.Add(typeColumn);

            // عمود الأهمية
            var priorityColumn = new DataGridViewTextBoxColumn
            {
                Name = "Priority",
                HeaderText = "الأهمية",
                DataPropertyName = "Priority",
                Width = 80,
                ReadOnly = true
            };
            dgvNotifications.Columns.Add(priorityColumn);

            // عمود الحالة
            var statusColumn = new DataGridViewTextBoxColumn
            {
                Name = "ReadStatus",
                HeaderText = "الحالة",
                DataPropertyName = "ReadStatus",
                Width = 80,
                ReadOnly = true
            };
            dgvNotifications.Columns.Add(statusColumn);

            // عمود التاريخ
            var timeColumn = new DataGridViewTextBoxColumn
            {
                Name = "TimeAgo",
                HeaderText = "التاريخ",
                DataPropertyName = "TimeAgo",
                Width = 100,
                ReadOnly = true
            };
            dgvNotifications.Columns.Add(timeColumn);

            // عمود مخفي للـ ID
            var idColumn = new DataGridViewTextBoxColumn
            {
                Name = "Id",
                DataPropertyName = "Id",
                Visible = false
            };
            dgvNotifications.Columns.Add(idColumn);
        }

        /// <summary>
        /// إعداد الأحداث
        /// </summary>
        private void SetupEvents()
        {
            try
            {
                // أحداث الأزرار
                btnRefresh.Click += BtnRefresh_Click;
                btnMarkAllRead.Click += BtnMarkAllRead_Click;
                btnDeleteSelected.Click += BtnDeleteSelected_Click;
                btnShowUnreadOnly.Click += BtnShowUnreadOnly_Click;

                // أحداث DataGridView
                dgvNotifications.CellDoubleClick += DgvNotifications_CellDoubleClick;
                dgvNotifications.SelectionChanged += DgvNotifications_SelectionChanged;
                dgvNotifications.CellFormatting += DgvNotifications_CellFormatting;

                // أحداث البحث
                txtSearch.TextChanged += TxtSearch_TextChanged;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إعداد الأحداث: {ex.Message}");
            }
        }

        /// <summary>
        /// تطبيق السمة
        /// </summary>
        private void ApplyTheme()
        {
            try
            {
                // تطبيق الخطوط
                var arabicFont = Program.GetCustomFont(Properties.Resources.DroidSansArabic, 9, FontStyle.Regular);

                foreach (Control control in this.Controls)
                {
                    if (control is RJLabel || control is RJButton || control is RJTextBox)
                    {
                        control.Font = arabicFont;
                    }
                }

                dgvNotifications.Font = arabicFont;
                dgvNotifications.ColumnHeadersDefaultCellStyle.Font =
                    Program.GetCustomFont(Properties.Resources.DroidKufi_Bold, 9, FontStyle.Bold);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تطبيق السمة: {ex.Message}");
            }
        }
        #endregion

        #region Data Methods
        /// <summary>
        /// تحميل الإشعارات
        /// </summary>
        private async void LoadNotificationsAsync()
        {
            try
            {
                lblStatus.Text = "جاري تحميل الإشعارات...";

                _notifications = await _notificationService.GetAllNotificationsAsync(
                    includeRead: !_showOnlyUnread);

                UpdateDataGridView();
                UpdateStatusLabel();
            }
            catch (Exception ex)
            {
                RJMessageBox.Show($"خطأ في تحميل الإشعارات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                lblStatus.Text = "خطأ في تحميل البيانات";
            }
        }

        /// <summary>
        /// تحديث DataGridView
        /// </summary>
        private void UpdateDataGridView()
        {
            try
            {
                var filteredNotifications = _notifications;

                // تطبيق فلتر البحث
                if (!string.IsNullOrWhiteSpace(txtSearch.Text))
                {
                    var searchTerm = txtSearch.Text.Trim().ToLower();
                    filteredNotifications = _notifications.Where(n =>
                        n.Title.ToLower().Contains(searchTerm) ||
                        n.Message.ToLower().Contains(searchTerm)).ToList();
                }

                dgvNotifications.DataSource = filteredNotifications;

                // تحديث عدد الإشعارات غير المقروءة
                UpdateUnreadCount();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديث DataGridView: {ex.Message}");
            }
        }

        /// <summary>
        /// تحديث عدد الإشعارات غير المقروءة
        /// </summary>
        private async void UpdateUnreadCount()
        {
            try
            {
                var unreadCount = await _notificationService.GetUnreadCountAsync();
                lblUnreadCount.Text = $"غير مقروء: {unreadCount}";
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديث عدد الإشعارات غير المقروءة: {ex.Message}");
            }
        }

        /// <summary>
        /// تحديث تسمية الحالة
        /// </summary>
        private void UpdateStatusLabel()
        {
            var totalCount = _notifications.Count;
            var unreadCount = _notifications.Count(n => !n.IsRead);

            lblStatus.Text = $"إجمالي: {totalCount} | غير مقروء: {unreadCount}";
        }
        #endregion

        #region Event Handlers
        /// <summary>
        /// حدث النقر على زر التحديث
        /// </summary>
        private void BtnRefresh_Click(object sender, EventArgs e)
        {
            LoadNotificationsAsync();
        }

        /// <summary>
        /// حدث النقر على زر تحديد الكل كمقروء
        /// </summary>
        private async void BtnMarkAllRead_Click(object sender, EventArgs e)
        {
            try
            {
                var result = RJMessageBox.Show(
                    "هل تريد تحديد جميع الإشعارات كمقروءة؟",
                    "تأكيد",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    var success = await _notificationService.MarkAllAsReadAsync();
                    if (success)
                    {
                        RJMessageBox.Show("تم تحديد جميع الإشعارات كمقروءة بنجاح", "نجح",
                            MessageBoxButtons.OK, MessageBoxIcon.Information);
                        LoadNotificationsAsync();
                    }
                    else
                    {
                        RJMessageBox.Show("فشل في تحديد الإشعارات", "خطأ",
                            MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                RJMessageBox.Show($"خطأ: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// حدث النقر على زر حذف المحدد
        /// </summary>
        private async void BtnDeleteSelected_Click(object sender, EventArgs e)
        {
            try
            {
                if (dgvNotifications.SelectedRows.Count == 0)
                {
                    RJMessageBox.Show("يرجى تحديد إشعار للحذف", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                var result = RJMessageBox.Show(
                    "هل تريد حذف الإشعار المحدد؟",
                    "تأكيد الحذف",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    var selectedNotification = (Notification)dgvNotifications.SelectedRows[0].DataBoundItem;
                    var success = await _notificationService.DeleteNotificationAsync(selectedNotification.Id);

                    if (success)
                    {
                        RJMessageBox.Show("تم حذف الإشعار بنجاح", "نجح",
                            MessageBoxButtons.OK, MessageBoxIcon.Information);
                        LoadNotificationsAsync();
                    }
                    else
                    {
                        RJMessageBox.Show("فشل في حذف الإشعار", "خطأ",
                            MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                RJMessageBox.Show($"خطأ: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// حدث النقر على زر عرض غير المقروء فقط
        /// </summary>
        private void BtnShowUnreadOnly_Click(object sender, EventArgs e)
        {
            _showOnlyUnread = !_showOnlyUnread;
            btnShowUnreadOnly.Text = _showOnlyUnread ? "عرض الكل" : "غير مقروء فقط";
            LoadNotificationsAsync();
        }

        /// <summary>
        /// حدث النقر المزدوج على الإشعار
        /// </summary>
        private async void DgvNotifications_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            try
            {
                if (e.RowIndex >= 0)
                {
                    var notification = (Notification)dgvNotifications.Rows[e.RowIndex].DataBoundItem;

                    // تحديد الإشعار كمقروء إذا لم يكن مقروءاً
                    if (!notification.IsRead)
                    {
                        await _notificationService.MarkAsReadAsync(notification.Id);
                        notification.MarkAsRead();
                        dgvNotifications.Refresh();
                        UpdateUnreadCount();
                    }

                    // عرض تفاصيل الإشعار
                    ShowNotificationDetails(notification);
                }
            }
            catch (Exception ex)
            {
                RJMessageBox.Show($"خطأ: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// حدث تغيير التحديد في DataGridView
        /// </summary>
        private void DgvNotifications_SelectionChanged(object sender, EventArgs e)
        {
            btnDeleteSelected.Enabled = dgvNotifications.SelectedRows.Count > 0;
        }

        /// <summary>
        /// حدث تنسيق الخلايا
        /// </summary>
        private void DgvNotifications_CellFormatting(object sender, DataGridViewCellFormattingEventArgs e)
        {
            try
            {
                if (e.RowIndex >= 0 && e.RowIndex < dgvNotifications.Rows.Count)
                {
                    var notification = (Notification)dgvNotifications.Rows[e.RowIndex].DataBoundItem;

                    if (notification != null)
                    {
                        // تلوين الصفوف غير المقروءة
                        if (!notification.IsRead)
                        {
                            dgvNotifications.Rows[e.RowIndex].DefaultCellStyle.BackColor = Color.FromArgb(255, 248, 220);
                            dgvNotifications.Rows[e.RowIndex].DefaultCellStyle.Font =
                                new Font(dgvNotifications.Font, FontStyle.Bold);
                        }

                        // تلوين حسب الأهمية
                        if (dgvNotifications.Columns[e.ColumnIndex].Name == "Priority")
                        {
                            switch (notification.Priority?.ToLower())
                            {
                                case "high":
                                    e.CellStyle.ForeColor = Color.Red;
                                    break;
                                case "medium":
                                    e.CellStyle.ForeColor = Color.Orange;
                                    break;
                                case "low":
                                    e.CellStyle.ForeColor = Color.Green;
                                    break;
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تنسيق الخلايا: {ex.Message}");
            }
        }

        /// <summary>
        /// حدث تغيير نص البحث
        /// </summary>
        private void TxtSearch_TextChanged(object sender, EventArgs e)
        {
            UpdateDataGridView();
        }

        /// <summary>
        /// عرض تفاصيل الإشعار
        /// </summary>
        private void ShowNotificationDetails(Notification notification)
        {
            try
            {
                var details = $"العنوان: {notification.Title}\n\n" +
                             $"المحتوى: {notification.Message}\n\n" +
                             $"النوع: {notification.Type}\n" +
                             $"الأهمية: {notification.Priority}\n" +
                             $"التاريخ: {notification.CreatedDate:dd/MM/yyyy HH:mm}\n" +
                             $"الحالة: {notification.ReadStatus}";

                if (!string.IsNullOrEmpty(notification.Module))
                    details += $"\nالوحدة: {notification.Module}";

                RJMessageBox.Show(details, "تفاصيل الإشعار",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                RJMessageBox.Show($"خطأ في عرض التفاصيل: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        #endregion
    }
}