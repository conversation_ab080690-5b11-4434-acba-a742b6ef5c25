﻿"DeployProject"
{
"VSVersion" = "3:800"
"ProjectType" = "8:{978C614F-708E-4E1A-B201-565925725DBA}"
"IsWebType" = "8:FALSE"
"ProjectName" = "8:InstallerSmartCreator"
"LanguageId" = "3:1033"
"CodePage" = "3:1252"
"UILanguageId" = "3:1033"
"SccProjectName" = "8:"
"SccLocalPath" = "8:"
"SccAuxPath" = "8:"
"SccProvider" = "8:"
    "Hierarchy"
    {
        "Entry"
        {
        "MsmKey" = "8:_0D8504E9105EDC56063815CDE9AFC167"
        "OwnerKey" = "8:_5FC3513C5E89413E835FAC3E7D6D72C8"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_0D8504E9105EDC56063815CDE9AFC167"
        "OwnerKey" = "8:_9EB140E07C6DC5956D2F3EE09B609CCC"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_17A4DDBBBEFC21EB59DACE6B3A484E50"
        "OwnerKey" = "8:_5FC3513C5E89413E835FAC3E7D6D72C8"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_20FA8E0E6C62AFB8F176E19E1ED42C8D"
        "OwnerKey" = "8:_C08BA0F533E19E3BE0B968F870E20EC2"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_20FA8E0E6C62AFB8F176E19E1ED42C8D"
        "OwnerKey" = "8:_5FC3513C5E89413E835FAC3E7D6D72C8"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_225ABF9853014E0118F5F54F942FE2BD"
        "OwnerKey" = "8:_5FC3513C5E89413E835FAC3E7D6D72C8"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_2D553D2504FAD0D71EA6CE36B6C68A60"
        "OwnerKey" = "8:_5FC3513C5E89413E835FAC3E7D6D72C8"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_390A5FCA79CA773EA7FF1CDE6BDA9D0E"
        "OwnerKey" = "8:_5FC3513C5E89413E835FAC3E7D6D72C8"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_3C5E6AA003469959E26E6329681B7822"
        "OwnerKey" = "8:_20FA8E0E6C62AFB8F176E19E1ED42C8D"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_3C5E6AA003469959E26E6329681B7822"
        "OwnerKey" = "8:_5FC3513C5E89413E835FAC3E7D6D72C8"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_3C5E6AA003469959E26E6329681B7822"
        "OwnerKey" = "8:_C08BA0F533E19E3BE0B968F870E20EC2"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_4002B498490010FE4F9B0F008C62B8C5"
        "OwnerKey" = "8:_77ABF236239AFAB4ABCDB566BF472F3F"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_4002B498490010FE4F9B0F008C62B8C5"
        "OwnerKey" = "8:_5FC3513C5E89413E835FAC3E7D6D72C8"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_4002B498490010FE4F9B0F008C62B8C5"
        "OwnerKey" = "8:_C08BA0F533E19E3BE0B968F870E20EC2"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_45410A779B79851B1379D3E44EE5FFAA"
        "OwnerKey" = "8:_4B26DB340A8ADC2C691C0E6D34928574"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_45410A779B79851B1379D3E44EE5FFAA"
        "OwnerKey" = "8:_9EB140E07C6DC5956D2F3EE09B609CCC"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_45410A779B79851B1379D3E44EE5FFAA"
        "OwnerKey" = "8:_5FC3513C5E89413E835FAC3E7D6D72C8"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_45410A779B79851B1379D3E44EE5FFAA"
        "OwnerKey" = "8:_0D8504E9105EDC56063815CDE9AFC167"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_4AE8356319B14CFBF844BE817FB1F771"
        "OwnerKey" = "8:_B3223D4A1C6D5AC87E1E611439AD070C"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_4B26DB340A8ADC2C691C0E6D34928574"
        "OwnerKey" = "8:_5FC3513C5E89413E835FAC3E7D6D72C8"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_4B26DB340A8ADC2C691C0E6D34928574"
        "OwnerKey" = "8:_0D8504E9105EDC56063815CDE9AFC167"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_51A53DC5CEA8B9979CBAD5B38D6F68DF"
        "OwnerKey" = "8:_5FC3513C5E89413E835FAC3E7D6D72C8"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_562AE7B4090058F4E75C0A48DDD5030A"
        "OwnerKey" = "8:_5FC3513C5E89413E835FAC3E7D6D72C8"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_5B199C267A1651C2A76A498B4650B75F"
        "OwnerKey" = "8:_5FC3513C5E89413E835FAC3E7D6D72C8"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_5FC3513C5E89413E835FAC3E7D6D72C8"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_693E3F9077C67F9B8CE276E58B958222"
        "OwnerKey" = "8:_B3223D4A1C6D5AC87E1E611439AD070C"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_693E3F9077C67F9B8CE276E58B958222"
        "OwnerKey" = "8:_5FC3513C5E89413E835FAC3E7D6D72C8"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_6D0054054371507F5F694DA9828C72A0"
        "OwnerKey" = "8:_C08BA0F533E19E3BE0B968F870E20EC2"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_6D0054054371507F5F694DA9828C72A0"
        "OwnerKey" = "8:_5FC3513C5E89413E835FAC3E7D6D72C8"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_6D0054054371507F5F694DA9828C72A0"
        "OwnerKey" = "8:_FC9EE6DCDB7381B3A330AAD560F43681"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_6D0054054371507F5F694DA9828C72A0"
        "OwnerKey" = "8:_20FA8E0E6C62AFB8F176E19E1ED42C8D"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_77ABF236239AFAB4ABCDB566BF472F3F"
        "OwnerKey" = "8:_5FC3513C5E89413E835FAC3E7D6D72C8"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_77ABF236239AFAB4ABCDB566BF472F3F"
        "OwnerKey" = "8:_C08BA0F533E19E3BE0B968F870E20EC2"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_8CB4A546439204AD1BC636868F4D452D"
        "OwnerKey" = "8:_5FC3513C5E89413E835FAC3E7D6D72C8"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_97C110229451566C7FB791B82AD85EAD"
        "OwnerKey" = "8:_8CB4A546439204AD1BC636868F4D452D"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_97C110229451566C7FB791B82AD85EAD"
        "OwnerKey" = "8:_5FC3513C5E89413E835FAC3E7D6D72C8"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_97F0F019CADEF05558206C601E2F22A9"
        "OwnerKey" = "8:_5FC3513C5E89413E835FAC3E7D6D72C8"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_97F0F019CADEF05558206C601E2F22A9"
        "OwnerKey" = "8:_C08BA0F533E19E3BE0B968F870E20EC2"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_97F0F019CADEF05558206C601E2F22A9"
        "OwnerKey" = "8:_9F17476D7CF2BD9D0014C9018FB2D56F"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_9AC1F377F5A04BBCC8D6766D7BE393E2"
        "OwnerKey" = "8:_4002B498490010FE4F9B0F008C62B8C5"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_9AC1F377F5A04BBCC8D6766D7BE393E2"
        "OwnerKey" = "8:_5FC3513C5E89413E835FAC3E7D6D72C8"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_9AC1F377F5A04BBCC8D6766D7BE393E2"
        "OwnerKey" = "8:_FC9EE6DCDB7381B3A330AAD560F43681"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_9EB140E07C6DC5956D2F3EE09B609CCC"
        "OwnerKey" = "8:_5FC3513C5E89413E835FAC3E7D6D72C8"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_9F17476D7CF2BD9D0014C9018FB2D56F"
        "OwnerKey" = "8:_5FC3513C5E89413E835FAC3E7D6D72C8"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_A2AB456FF65B95B4E74DF1E01A7F9E13"
        "OwnerKey" = "8:_FC9EE6DCDB7381B3A330AAD560F43681"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_A2AB456FF65B95B4E74DF1E01A7F9E13"
        "OwnerKey" = "8:_5FC3513C5E89413E835FAC3E7D6D72C8"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_A2AB456FF65B95B4E74DF1E01A7F9E13"
        "OwnerKey" = "8:_C08BA0F533E19E3BE0B968F870E20EC2"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_B3223D4A1C6D5AC87E1E611439AD070C"
        "OwnerKey" = "8:_5FC3513C5E89413E835FAC3E7D6D72C8"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_B3223D4A1C6D5AC87E1E611439AD070C"
        "OwnerKey" = "8:_51A53DC5CEA8B9979CBAD5B38D6F68DF"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_C08BA0F533E19E3BE0B968F870E20EC2"
        "OwnerKey" = "8:_5FC3513C5E89413E835FAC3E7D6D72C8"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_C2894A88FF656C1934D11A8A61F910C6"
        "OwnerKey" = "8:_693E3F9077C67F9B8CE276E58B958222"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_C2894A88FF656C1934D11A8A61F910C6"
        "OwnerKey" = "8:_B3223D4A1C6D5AC87E1E611439AD070C"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_C9735AD5424D589A53DBA151D648C0DA"
        "OwnerKey" = "8:_5FC3513C5E89413E835FAC3E7D6D72C8"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_D3A42DAA3254F01C367334FF2E915F66"
        "OwnerKey" = "8:_B3223D4A1C6D5AC87E1E611439AD070C"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_D3A42DAA3254F01C367334FF2E915F66"
        "OwnerKey" = "8:_C9735AD5424D589A53DBA151D648C0DA"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_D75846083061B711A2C3070A7D97A014"
        "OwnerKey" = "8:_5FC3513C5E89413E835FAC3E7D6D72C8"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_FC9EE6DCDB7381B3A330AAD560F43681"
        "OwnerKey" = "8:_20FA8E0E6C62AFB8F176E19E1ED42C8D"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_FC9EE6DCDB7381B3A330AAD560F43681"
        "OwnerKey" = "8:_5FC3513C5E89413E835FAC3E7D6D72C8"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_FC9EE6DCDB7381B3A330AAD560F43681"
        "OwnerKey" = "8:_C08BA0F533E19E3BE0B968F870E20EC2"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_5FC3513C5E89413E835FAC3E7D6D72C8"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_562AE7B4090058F4E75C0A48DDD5030A"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_C08BA0F533E19E3BE0B968F870E20EC2"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_20FA8E0E6C62AFB8F176E19E1ED42C8D"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_3C5E6AA003469959E26E6329681B7822"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_FC9EE6DCDB7381B3A330AAD560F43681"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_A2AB456FF65B95B4E74DF1E01A7F9E13"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_6D0054054371507F5F694DA9828C72A0"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_D75846083061B711A2C3070A7D97A014"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_77ABF236239AFAB4ABCDB566BF472F3F"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_4002B498490010FE4F9B0F008C62B8C5"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_9AC1F377F5A04BBCC8D6766D7BE393E2"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_9F17476D7CF2BD9D0014C9018FB2D56F"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_C9735AD5424D589A53DBA151D648C0DA"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_390A5FCA79CA773EA7FF1CDE6BDA9D0E"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_17A4DDBBBEFC21EB59DACE6B3A484E50"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_8CB4A546439204AD1BC636868F4D452D"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_97C110229451566C7FB791B82AD85EAD"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_9EB140E07C6DC5956D2F3EE09B609CCC"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_0D8504E9105EDC56063815CDE9AFC167"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_4B26DB340A8ADC2C691C0E6D34928574"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_45410A779B79851B1379D3E44EE5FFAA"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_97F0F019CADEF05558206C601E2F22A9"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_5B199C267A1651C2A76A498B4650B75F"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_225ABF9853014E0118F5F54F942FE2BD"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_B3223D4A1C6D5AC87E1E611439AD070C"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_D3A42DAA3254F01C367334FF2E915F66"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_4AE8356319B14CFBF844BE817FB1F771"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_693E3F9077C67F9B8CE276E58B958222"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_C2894A88FF656C1934D11A8A61F910C6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_2D553D2504FAD0D71EA6CE36B6C68A60"
        "MsmSig" = "8:_UNDEFINED"
        }
    }
    "Configurations"
    {
        "Debug"
        {
        "DisplayName" = "8:Debug"
        "IsDebugOnly" = "11:TRUE"
        "IsReleaseOnly" = "11:FALSE"
        "OutputFilename" = "8:Debug\\InstallerSmartCreator.msi"
        "PackageFilesAs" = "3:2"
        "PackageFileSize" = "3:-2147483648"
        "CabType" = "3:1"
        "Compression" = "3:2"
        "SignOutput" = "11:FALSE"
        "CertificateFile" = "8:"
        "PrivateKeyFile" = "8:"
        "TimeStampServer" = "8:"
        "InstallerBootstrapper" = "3:2"
            "BootstrapperCfg:{63ACBE69-63AA-4F98-B2B6-99F9E24495F2}"
            {
            "Enabled" = "11:TRUE"
            "PromptEnabled" = "11:TRUE"
            "PrerequisitesLocation" = "2:1"
            "Url" = "8:"
            "ComponentsUrl" = "8:"
            }
        }
        "Release"
        {
        "DisplayName" = "8:Release"
        "IsDebugOnly" = "11:FALSE"
        "IsReleaseOnly" = "11:TRUE"
        "OutputFilename" = "8:Release\\InstallerSmartCreator.msi"
        "PackageFilesAs" = "3:2"
        "PackageFileSize" = "3:-2147483648"
        "CabType" = "3:1"
        "Compression" = "3:3"
        "SignOutput" = "11:FALSE"
        "CertificateFile" = "8:"
        "PrivateKeyFile" = "8:"
        "TimeStampServer" = "8:"
        "InstallerBootstrapper" = "3:2"
            "BootstrapperCfg:{63ACBE69-63AA-4F98-B2B6-99F9E24495F2}"
            {
            "Enabled" = "11:TRUE"
            "PromptEnabled" = "11:TRUE"
            "PrerequisitesLocation" = "2:1"
            "Url" = "8:"
            "ComponentsUrl" = "8:"
                "Items"
                {
                    "{EDC2488A-8267-493A-A98E-7D9C3B36CDF3}:.NETFramework,Version=v4.7.2"
                    {
                    "Name" = "8:Microsoft .NET Framework 4.7.2 (x86 and x64)"
                    "ProductCode" = "8:.NETFramework,Version=v4.7.2"
                    }
                }
            }
        }
    }
    "Deployable"
    {
        "CustomAction"
        {
        }
        "DefaultFeature"
        {
        "Name" = "8:DefaultFeature"
        "Title" = "8:"
        "Description" = "8:"
        }
        "ExternalPersistence"
        {
            "LaunchCondition"
            {
                "{A06ECF26-33A3-4562-8140-9B0E340D4F24}:_D3A2CDC2DEC948A2945FA268EDE91665"
                {
                "Name" = "8:.NET Framework"
                "Message" = "8:[VSDNETMSG]"
                "FrameworkVersion" = "8:.NETFramework,Version=v4.7.2"
                "AllowLaterVersions" = "11:FALSE"
                "InstallUrl" = "8:http://go.microsoft.com/fwlink/?LinkId=863262"
                }
            }
        }
        "File"
        {
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_0D8504E9105EDC56063815CDE9AFC167"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:CefSharp.Core, Version=***********, Culture=neutral, PublicKeyToken=40c4b6fc221f4138, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_0D8504E9105EDC56063815CDE9AFC167"
                    {
                    "Name" = "8:CefSharp.Core.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:CefSharp.Core.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_BCAEB2EE9CF74F6C8D69B48CCD79B25B"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_17A4DDBBBEFC21EB59DACE6B3A484E50"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:Dapper, Version=2.0.0.0, Culture=neutral, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_17A4DDBBBEFC21EB59DACE6B3A484E50"
                    {
                    "Name" = "8:Dapper.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Dapper.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_BCAEB2EE9CF74F6C8D69B48CCD79B25B"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_20FA8E0E6C62AFB8F176E19E1ED42C8D"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Formats.Asn1, Version=9.0.0.1, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_20FA8E0E6C62AFB8F176E19E1ED42C8D"
                    {
                    "Name" = "8:System.Formats.Asn1.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Formats.Asn1.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_BCAEB2EE9CF74F6C8D69B48CCD79B25B"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_225ABF9853014E0118F5F54F942FE2BD"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.IO.Compression, Version=4.2.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089"
                "ScatterAssemblies"
                {
                    "_225ABF9853014E0118F5F54F942FE2BD"
                    {
                    "Name" = "8:System.IO.Compression.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.IO.Compression.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_BCAEB2EE9CF74F6C8D69B48CCD79B25B"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_2D553D2504FAD0D71EA6CE36B6C68A60"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:DevComponents.DotNetBar2, Version=12.9.0.0, Culture=neutral, PublicKeyToken=c39c3242a43eee2b, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_2D553D2504FAD0D71EA6CE36B6C68A60"
                    {
                    "Name" = "8:DevComponents.DotNetBar2.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:DevComponents.DotNetBar2.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_BCAEB2EE9CF74F6C8D69B48CCD79B25B"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_390A5FCA79CA773EA7FF1CDE6BDA9D0E"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:FontAwesome.Sharp, Version=6.6.0.0, Culture=neutral, PublicKeyToken=d16d1e4e568ec10f, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_390A5FCA79CA773EA7FF1CDE6BDA9D0E"
                    {
                    "Name" = "8:FontAwesome.Sharp.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:FontAwesome.Sharp.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_BCAEB2EE9CF74F6C8D69B48CCD79B25B"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_3C5E6AA003469959E26E6329681B7822"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.ValueTuple, Version=4.0.3.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_3C5E6AA003469959E26E6329681B7822"
                    {
                    "Name" = "8:System.ValueTuple.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.ValueTuple.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_BCAEB2EE9CF74F6C8D69B48CCD79B25B"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_4002B498490010FE4F9B0F008C62B8C5"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Threading.Tasks.Extensions, Version=4.2.1.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_4002B498490010FE4F9B0F008C62B8C5"
                    {
                    "Name" = "8:System.Threading.Tasks.Extensions.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Threading.Tasks.Extensions.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_BCAEB2EE9CF74F6C8D69B48CCD79B25B"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_45410A779B79851B1379D3E44EE5FFAA"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:CefSharp, Version=***********, Culture=neutral, PublicKeyToken=40c4b6fc221f4138, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_45410A779B79851B1379D3E44EE5FFAA"
                    {
                    "Name" = "8:CefSharp.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:CefSharp.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_BCAEB2EE9CF74F6C8D69B48CCD79B25B"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_4AE8356319B14CFBF844BE817FB1F771"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:System.Diagnostics.Tracing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_4AE8356319B14CFBF844BE817FB1F771"
                    {
                    "Name" = "8:System.Diagnostics.Tracing.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Diagnostics.Tracing.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_BCAEB2EE9CF74F6C8D69B48CCD79B25B"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_4B26DB340A8ADC2C691C0E6D34928574"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:CefSharp.Core.Runtime, Version=***********, Culture=neutral, PublicKeyToken=40c4b6fc221f4138, processorArchitecture=x86"
                "ScatterAssemblies"
                {
                    "_4B26DB340A8ADC2C691C0E6D34928574"
                    {
                    "Name" = "8:CefSharp.Core.Runtime.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:CefSharp.Core.Runtime.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_BCAEB2EE9CF74F6C8D69B48CCD79B25B"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_51A53DC5CEA8B9979CBAD5B38D6F68DF"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:tik4net, Version=4.0.0.0, Culture=neutral, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_51A53DC5CEA8B9979CBAD5B38D6F68DF"
                    {
                    "Name" = "8:tik4net.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:tik4net.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_BCAEB2EE9CF74F6C8D69B48CCD79B25B"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_562AE7B4090058F4E75C0A48DDD5030A"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Data.SQLite, Version=1.0.96.0, Culture=neutral, PublicKeyToken=db937bc2d44ff139, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_562AE7B4090058F4E75C0A48DDD5030A"
                    {
                    "Name" = "8:System.Data.SQLite.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Data.SQLite.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_BCAEB2EE9CF74F6C8D69B48CCD79B25B"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_5B199C267A1651C2A76A498B4650B75F"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Net.Http, Version=4.2.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_5B199C267A1651C2A76A498B4650B75F"
                    {
                    "Name" = "8:System.Net.Http.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Net.Http.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_BCAEB2EE9CF74F6C8D69B48CCD79B25B"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_693E3F9077C67F9B8CE276E58B958222"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.IO.Compression.FileSystem, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089"
                "ScatterAssemblies"
                {
                    "_693E3F9077C67F9B8CE276E58B958222"
                    {
                    "Name" = "8:System.IO.Compression.FileSystem.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.IO.Compression.FileSystem.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_BCAEB2EE9CF74F6C8D69B48CCD79B25B"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_6D0054054371507F5F694DA9828C72A0"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Buffers, Version=4.0.4.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_6D0054054371507F5F694DA9828C72A0"
                    {
                    "Name" = "8:System.Buffers.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Buffers.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_BCAEB2EE9CF74F6C8D69B48CCD79B25B"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_77ABF236239AFAB4ABCDB566BF472F3F"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:Microsoft.Bcl.AsyncInterfaces, Version=9.0.0.1, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_77ABF236239AFAB4ABCDB566BF472F3F"
                    {
                    "Name" = "8:Microsoft.Bcl.AsyncInterfaces.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Microsoft.Bcl.AsyncInterfaces.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_BCAEB2EE9CF74F6C8D69B48CCD79B25B"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_8CB4A546439204AD1BC636868F4D452D"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:CircularProgressBar, Version=2.8.0.16, Culture=neutral, PublicKeyToken=310fd07b25df79b3, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_8CB4A546439204AD1BC636868F4D452D"
                    {
                    "Name" = "8:CircularProgressBar.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:CircularProgressBar.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_BCAEB2EE9CF74F6C8D69B48CCD79B25B"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_97C110229451566C7FB791B82AD85EAD"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:WinFormAnimation, Version=1.6.0.4, Culture=neutral, PublicKeyToken=310fd07b25df79b3, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_97C110229451566C7FB791B82AD85EAD"
                    {
                    "Name" = "8:WinFormAnimation.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:WinFormAnimation.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_BCAEB2EE9CF74F6C8D69B48CCD79B25B"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_97F0F019CADEF05558206C601E2F22A9"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:BouncyCastle.Cryptography, Version=2.0.0.0, Culture=neutral, PublicKeyToken=072edcf4a5328938, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_97F0F019CADEF05558206C601E2F22A9"
                    {
                    "Name" = "8:BouncyCastle.Cryptography.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:BouncyCastle.Cryptography.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_BCAEB2EE9CF74F6C8D69B48CCD79B25B"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_9AC1F377F5A04BBCC8D6766D7BE393E2"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Runtime.CompilerServices.Unsafe, Version=6.0.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_9AC1F377F5A04BBCC8D6766D7BE393E2"
                    {
                    "Name" = "8:System.Runtime.CompilerServices.Unsafe.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Runtime.CompilerServices.Unsafe.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_BCAEB2EE9CF74F6C8D69B48CCD79B25B"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_9EB140E07C6DC5956D2F3EE09B609CCC"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:CefSharp.WinForms, Version=***********, Culture=neutral, PublicKeyToken=40c4b6fc221f4138, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_9EB140E07C6DC5956D2F3EE09B609CCC"
                    {
                    "Name" = "8:CefSharp.WinForms.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:CefSharp.WinForms.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_BCAEB2EE9CF74F6C8D69B48CCD79B25B"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_9F17476D7CF2BD9D0014C9018FB2D56F"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:itextsharp, Version=5.5.13.4, Culture=neutral, PublicKeyToken=8354ae6d2174ddca, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_9F17476D7CF2BD9D0014C9018FB2D56F"
                    {
                    "Name" = "8:itextsharp.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:itextsharp.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_BCAEB2EE9CF74F6C8D69B48CCD79B25B"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_A2AB456FF65B95B4E74DF1E01A7F9E13"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Numerics.Vectors, Version=4.1.5.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_A2AB456FF65B95B4E74DF1E01A7F9E13"
                    {
                    "Name" = "8:System.Numerics.Vectors.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Numerics.Vectors.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_BCAEB2EE9CF74F6C8D69B48CCD79B25B"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_B3223D4A1C6D5AC87E1E611439AD070C"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:netstandard, Version=2.0.0.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51"
                "ScatterAssemblies"
                {
                    "_B3223D4A1C6D5AC87E1E611439AD070C"
                    {
                    "Name" = "8:netstandard.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:netstandard.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_BCAEB2EE9CF74F6C8D69B48CCD79B25B"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_C08BA0F533E19E3BE0B968F870E20EC2"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:Renci.SshNet, Version=2024.2.0.1, Culture=neutral, PublicKeyToken=1cee9f8bde3db106, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_C08BA0F533E19E3BE0B968F870E20EC2"
                    {
                    "Name" = "8:Renci.SshNet.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Renci.SshNet.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_BCAEB2EE9CF74F6C8D69B48CCD79B25B"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_C2894A88FF656C1934D11A8A61F910C6"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:System.IO.Compression, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_C2894A88FF656C1934D11A8A61F910C6"
                    {
                    "Name" = "8:System.IO.Compression.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.IO.Compression.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_BCAEB2EE9CF74F6C8D69B48CCD79B25B"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_C9735AD5424D589A53DBA151D648C0DA"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:HtmlAgilityPack, Version=1.11.72.0, Culture=neutral, PublicKeyToken=bd319b19eaf3b43a, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_C9735AD5424D589A53DBA151D648C0DA"
                    {
                    "Name" = "8:HtmlAgilityPack.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:HtmlAgilityPack.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_BCAEB2EE9CF74F6C8D69B48CCD79B25B"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_D3A42DAA3254F01C367334FF2E915F66"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:System.Net.Http, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_D3A42DAA3254F01C367334FF2E915F66"
                    {
                    "Name" = "8:System.Net.Http.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Net.Http.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_BCAEB2EE9CF74F6C8D69B48CCD79B25B"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_D75846083061B711A2C3070A7D97A014"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:Newtonsoft.Json, Version=13.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_D75846083061B711A2C3070A7D97A014"
                    {
                    "Name" = "8:Newtonsoft.Json.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Newtonsoft.Json.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_BCAEB2EE9CF74F6C8D69B48CCD79B25B"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_FC9EE6DCDB7381B3A330AAD560F43681"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Memory, Version=4.0.2.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_FC9EE6DCDB7381B3A330AAD560F43681"
                    {
                    "Name" = "8:System.Memory.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Memory.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_BCAEB2EE9CF74F6C8D69B48CCD79B25B"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
        }
        "FileType"
        {
        }
        "Folder"
        {
            "{1525181F-901A-416C-8A58-119130FE478E}:_235F8E4229B04ECD8D298913D4547C99"
            {
            "Name" = "8:#1916"
            "AlwaysCreate" = "11:FALSE"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Property" = "8:DesktopFolder"
                "Folders"
                {
                }
            }
            "{3C67513D-01DD-4637-8A68-80971EB9504F}:_BCAEB2EE9CF74F6C8D69B48CCD79B25B"
            {
            "DefaultLocation" = "8:[ProgramFilesFolder][Manufacturer]\\[ProductName]"
            "Name" = "8:#1925"
            "AlwaysCreate" = "11:FALSE"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Property" = "8:TARGETDIR"
                "Folders"
                {
                }
            }
            "{1525181F-901A-416C-8A58-119130FE478E}:_FDFE2EC99E7348ED91280CE531337079"
            {
            "Name" = "8:#1919"
            "AlwaysCreate" = "11:FALSE"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Property" = "8:ProgramMenuFolder"
                "Folders"
                {
                }
            }
        }
        "LaunchCondition"
        {
        }
        "Locator"
        {
        }
        "MsiBootstrapper"
        {
        "LangId" = "3:1033"
        "RequiresElevation" = "11:FALSE"
        }
        "Product"
        {
        "Name" = "8:Microsoft Visual Studio"
        "ProductName" = "8:InstallerSmartCreator"
        "ProductCode" = "8:{78B90725-5136-4828-83C7-85BB9B44CF09}"
        "PackageCode" = "8:{ECD74E64-0278-44F0-8B4D-93404C417D73}"
        "UpgradeCode" = "8:{ABC56F87-759F-4672-A0F7-72ACE94A1099}"
        "AspNetVersion" = "8:4.0.30319.0"
        "RestartWWWService" = "11:FALSE"
        "RemovePreviousVersions" = "11:FALSE"
        "DetectNewerInstalledVersion" = "11:TRUE"
        "InstallAllUsers" = "11:FALSE"
        "ProductVersion" = "8:1.0.0"
        "Manufacturer" = "8:<EMAIL>"
        "ARPHELPTELEPHONE" = "8:"
        "ARPHELPLINK" = "8:"
        "Title" = "8:InstallerSmartCreator"
        "Subject" = "8:"
        "ARPCONTACT" = "8:<EMAIL>"
        "Keywords" = "8:"
        "ARPCOMMENTS" = "8:"
        "ARPURLINFOABOUT" = "8:"
        "ARPPRODUCTICON" = "8:"
        "ARPIconIndex" = "3:0"
        "SearchPath" = "8:"
        "UseSystemSearchPath" = "11:TRUE"
        "TargetPlatform" = "3:0"
        "PreBuildEvent" = "8:"
        "PostBuildEvent" = "8:"
        "RunPostBuildEvent" = "3:0"
        }
        "Registry"
        {
            "HKLM"
            {
                "Keys"
                {
                    "{60EA8692-D2D5-43EB-80DC-7906BF13D6EF}:_31B8AE9BCD7C4585B4B6B8B4599F30AE"
                    {
                    "Name" = "8:Software"
                    "Condition" = "8:"
                    "AlwaysCreate" = "11:FALSE"
                    "DeleteAtUninstall" = "11:FALSE"
                    "Transitive" = "11:FALSE"
                        "Keys"
                        {
                            "{60EA8692-D2D5-43EB-80DC-7906BF13D6EF}:_319C0EC2976A4AA69461E7C04C71204F"
                            {
                            "Name" = "8:[Manufacturer]"
                            "Condition" = "8:"
                            "AlwaysCreate" = "11:FALSE"
                            "DeleteAtUninstall" = "11:FALSE"
                            "Transitive" = "11:FALSE"
                                "Keys"
                                {
                                }
                                "Values"
                                {
                                }
                            }
                        }
                        "Values"
                        {
                        }
                    }
                }
            }
            "HKCU"
            {
                "Keys"
                {
                    "{60EA8692-D2D5-43EB-80DC-7906BF13D6EF}:_84FA0EECC2EF4851B4CAEBE71BE675FC"
                    {
                    "Name" = "8:Software"
                    "Condition" = "8:"
                    "AlwaysCreate" = "11:FALSE"
                    "DeleteAtUninstall" = "11:FALSE"
                    "Transitive" = "11:FALSE"
                        "Keys"
                        {
                            "{60EA8692-D2D5-43EB-80DC-7906BF13D6EF}:_5B1484795050432E88A7AD1D5D4B800C"
                            {
                            "Name" = "8:[Manufacturer]"
                            "Condition" = "8:"
                            "AlwaysCreate" = "11:FALSE"
                            "DeleteAtUninstall" = "11:FALSE"
                            "Transitive" = "11:FALSE"
                                "Keys"
                                {
                                }
                                "Values"
                                {
                                }
                            }
                        }
                        "Values"
                        {
                        }
                    }
                }
            }
            "HKCR"
            {
                "Keys"
                {
                }
            }
            "HKU"
            {
                "Keys"
                {
                }
            }
            "HKPU"
            {
                "Keys"
                {
                }
            }
        }
        "Sequences"
        {
        }
        "Shortcut"
        {
        }
        "UserInterface"
        {
            "{DF760B10-853B-4699-99F2-AFF7185B4A62}:_21311BC4B1D840B8919703E750074BEF"
            {
            "Name" = "8:#1901"
            "Sequence" = "3:2"
            "Attributes" = "3:2"
                "Dialogs"
                {
                    "{688940B3-5CA9-4162-8DEE-2993FA9D8CBC}:_7631F3C3A13F4769A0EE06572E42C27A"
                    {
                    "Sequence" = "3:100"
                    "DisplayName" = "8:Progress"
                    "UseDynamicProperties" = "11:TRUE"
                    "IsDependency" = "11:FALSE"
                    "SourcePath" = "8:<VsdDialogDir>\\VsdAdminProgressDlg.wid"
                        "Properties"
                        {
                            "BannerBitmap"
                            {
                            "Name" = "8:BannerBitmap"
                            "DisplayName" = "8:#1001"
                            "Description" = "8:#1101"
                            "Type" = "3:8"
                            "ContextData" = "8:Bitmap"
                            "Attributes" = "3:4"
                            "Setting" = "3:1"
                            "UsePlugInResources" = "11:TRUE"
                            }
                            "ShowProgress"
                            {
                            "Name" = "8:ShowProgress"
                            "DisplayName" = "8:#1009"
                            "Description" = "8:#1109"
                            "Type" = "3:5"
                            "ContextData" = "8:1;True=1;False=0"
                            "Attributes" = "3:0"
                            "Setting" = "3:0"
                            "Value" = "3:1"
                            "DefaultValue" = "3:1"
                            "UsePlugInResources" = "11:TRUE"
                            }
                        }
                    }
                }
            }
            "{DF760B10-853B-4699-99F2-AFF7185B4A62}:_4BBD56E2B1D24015B0EB126D522BA46A"
            {
            "Name" = "8:#1900"
            "Sequence" = "3:2"
            "Attributes" = "3:1"
                "Dialogs"
                {
                    "{688940B3-5CA9-4162-8DEE-2993FA9D8CBC}:_ECB291DE04FC45DAB8478F87B35AEA99"
                    {
                    "Sequence" = "3:200"
                    "DisplayName" = "8:Installation Folder"
                    "UseDynamicProperties" = "11:TRUE"
                    "IsDependency" = "11:FALSE"
                    "SourcePath" = "8:<VsdDialogDir>\\VsdAdminFolderDlg.wid"
                        "Properties"
                        {
                            "BannerBitmap"
                            {
                            "Name" = "8:BannerBitmap"
                            "DisplayName" = "8:#1001"
                            "Description" = "8:#1101"
                            "Type" = "3:8"
                            "ContextData" = "8:Bitmap"
                            "Attributes" = "3:4"
                            "Setting" = "3:1"
                            "UsePlugInResources" = "11:TRUE"
                            }
                        }
                    }
                    "{688940B3-5CA9-4162-8DEE-2993FA9D8CBC}:_F225AC07FC3D4B30BC77347A4712E965"
                    {
                    "Sequence" = "3:300"
                    "DisplayName" = "8:Confirm Installation"
                    "UseDynamicProperties" = "11:TRUE"
                    "IsDependency" = "11:FALSE"
                    "SourcePath" = "8:<VsdDialogDir>\\VsdAdminConfirmDlg.wid"
                        "Properties"
                        {
                            "BannerBitmap"
                            {
                            "Name" = "8:BannerBitmap"
                            "DisplayName" = "8:#1001"
                            "Description" = "8:#1101"
                            "Type" = "3:8"
                            "ContextData" = "8:Bitmap"
                            "Attributes" = "3:4"
                            "Setting" = "3:1"
                            "UsePlugInResources" = "11:TRUE"
                            }
                        }
                    }
                    "{688940B3-5CA9-4162-8DEE-2993FA9D8CBC}:_FD07F72C638E4454BE14C148DC0E8CBF"
                    {
                    "Sequence" = "3:100"
                    "DisplayName" = "8:Welcome"
                    "UseDynamicProperties" = "11:TRUE"
                    "IsDependency" = "11:FALSE"
                    "SourcePath" = "8:<VsdDialogDir>\\VsdAdminWelcomeDlg.wid"
                        "Properties"
                        {
                            "BannerBitmap"
                            {
                            "Name" = "8:BannerBitmap"
                            "DisplayName" = "8:#1001"
                            "Description" = "8:#1101"
                            "Type" = "3:8"
                            "ContextData" = "8:Bitmap"
                            "Attributes" = "3:4"
                            "Setting" = "3:1"
                            "UsePlugInResources" = "11:TRUE"
                            }
                            "CopyrightWarning"
                            {
                            "Name" = "8:CopyrightWarning"
                            "DisplayName" = "8:#1002"
                            "Description" = "8:#1102"
                            "Type" = "3:3"
                            "ContextData" = "8:"
                            "Attributes" = "3:0"
                            "Setting" = "3:1"
                            "Value" = "8:#1202"
                            "DefaultValue" = "8:#1202"
                            "UsePlugInResources" = "11:TRUE"
                            }
                            "Welcome"
                            {
                            "Name" = "8:Welcome"
                            "DisplayName" = "8:#1003"
                            "Description" = "8:#1103"
                            "Type" = "3:3"
                            "ContextData" = "8:"
                            "Attributes" = "3:0"
                            "Setting" = "3:1"
                            "Value" = "8:#1203"
                            "DefaultValue" = "8:#1203"
                            "UsePlugInResources" = "11:TRUE"
                            }
                        }
                    }
                }
            }
            "{2479F3F5-0309-486D-8047-8187E2CE5BA0}:_56BA62B6AF3143D2828347881A859864"
            {
            "UseDynamicProperties" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "SourcePath" = "8:<VsdDialogDir>\\VsdUserInterface.wim"
            }
            "{DF760B10-853B-4699-99F2-AFF7185B4A62}:_C641F132833E482E8F81ACD65C6E3291"
            {
            "Name" = "8:#1901"
            "Sequence" = "3:1"
            "Attributes" = "3:2"
                "Dialogs"
                {
                    "{688940B3-5CA9-4162-8DEE-2993FA9D8CBC}:_79EAB881D5064485A5F596E38B4CAA78"
                    {
                    "Sequence" = "3:100"
                    "DisplayName" = "8:Progress"
                    "UseDynamicProperties" = "11:TRUE"
                    "IsDependency" = "11:FALSE"
                    "SourcePath" = "8:<VsdDialogDir>\\VsdProgressDlg.wid"
                        "Properties"
                        {
                            "BannerBitmap"
                            {
                            "Name" = "8:BannerBitmap"
                            "DisplayName" = "8:#1001"
                            "Description" = "8:#1101"
                            "Type" = "3:8"
                            "ContextData" = "8:Bitmap"
                            "Attributes" = "3:4"
                            "Setting" = "3:1"
                            "UsePlugInResources" = "11:TRUE"
                            }
                            "ShowProgress"
                            {
                            "Name" = "8:ShowProgress"
                            "DisplayName" = "8:#1009"
                            "Description" = "8:#1109"
                            "Type" = "3:5"
                            "ContextData" = "8:1;True=1;False=0"
                            "Attributes" = "3:0"
                            "Setting" = "3:0"
                            "Value" = "3:1"
                            "DefaultValue" = "3:1"
                            "UsePlugInResources" = "11:TRUE"
                            }
                        }
                    }
                }
            }
            "{2479F3F5-0309-486D-8047-8187E2CE5BA0}:_C64E5FCA310E487C8DF32B6DC6E427C7"
            {
            "UseDynamicProperties" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "SourcePath" = "8:<VsdDialogDir>\\VsdBasicDialogs.wim"
            }
            "{DF760B10-853B-4699-99F2-AFF7185B4A62}:_CD6659911432478A80EE079D60089BFF"
            {
            "Name" = "8:#1902"
            "Sequence" = "3:2"
            "Attributes" = "3:3"
                "Dialogs"
                {
                    "{688940B3-5CA9-4162-8DEE-2993FA9D8CBC}:_EB7CF9AC0C7D4638B79B27F5A17D5D3C"
                    {
                    "Sequence" = "3:100"
                    "DisplayName" = "8:Finished"
                    "UseDynamicProperties" = "11:TRUE"
                    "IsDependency" = "11:FALSE"
                    "SourcePath" = "8:<VsdDialogDir>\\VsdAdminFinishedDlg.wid"
                        "Properties"
                        {
                            "BannerBitmap"
                            {
                            "Name" = "8:BannerBitmap"
                            "DisplayName" = "8:#1001"
                            "Description" = "8:#1101"
                            "Type" = "3:8"
                            "ContextData" = "8:Bitmap"
                            "Attributes" = "3:4"
                            "Setting" = "3:1"
                            "UsePlugInResources" = "11:TRUE"
                            }
                        }
                    }
                }
            }
            "{DF760B10-853B-4699-99F2-AFF7185B4A62}:_D9117C0932E24FE599A001CDFB64060B"
            {
            "Name" = "8:#1902"
            "Sequence" = "3:1"
            "Attributes" = "3:3"
                "Dialogs"
                {
                    "{688940B3-5CA9-4162-8DEE-2993FA9D8CBC}:_09A62D816C5B4410814DBF4AD83CE979"
                    {
                    "Sequence" = "3:100"
                    "DisplayName" = "8:Finished"
                    "UseDynamicProperties" = "11:TRUE"
                    "IsDependency" = "11:FALSE"
                    "SourcePath" = "8:<VsdDialogDir>\\VsdFinishedDlg.wid"
                        "Properties"
                        {
                            "BannerBitmap"
                            {
                            "Name" = "8:BannerBitmap"
                            "DisplayName" = "8:#1001"
                            "Description" = "8:#1101"
                            "Type" = "3:8"
                            "ContextData" = "8:Bitmap"
                            "Attributes" = "3:4"
                            "Setting" = "3:1"
                            "UsePlugInResources" = "11:TRUE"
                            }
                            "UpdateText"
                            {
                            "Name" = "8:UpdateText"
                            "DisplayName" = "8:#1058"
                            "Description" = "8:#1158"
                            "Type" = "3:15"
                            "ContextData" = "8:"
                            "Attributes" = "3:0"
                            "Setting" = "3:1"
                            "Value" = "8:#1258"
                            "DefaultValue" = "8:#1258"
                            "UsePlugInResources" = "11:TRUE"
                            }
                        }
                    }
                }
            }
            "{DF760B10-853B-4699-99F2-AFF7185B4A62}:_E9529BB392814DF799632AA41E506194"
            {
            "Name" = "8:#1900"
            "Sequence" = "3:1"
            "Attributes" = "3:1"
                "Dialogs"
                {
                    "{688940B3-5CA9-4162-8DEE-2993FA9D8CBC}:_1B25F122E1524DF08F6E84A6166CF202"
                    {
                    "Sequence" = "3:100"
                    "DisplayName" = "8:Welcome"
                    "UseDynamicProperties" = "11:TRUE"
                    "IsDependency" = "11:FALSE"
                    "SourcePath" = "8:<VsdDialogDir>\\VsdWelcomeDlg.wid"
                        "Properties"
                        {
                            "BannerBitmap"
                            {
                            "Name" = "8:BannerBitmap"
                            "DisplayName" = "8:#1001"
                            "Description" = "8:#1101"
                            "Type" = "3:8"
                            "ContextData" = "8:Bitmap"
                            "Attributes" = "3:4"
                            "Setting" = "3:1"
                            "UsePlugInResources" = "11:TRUE"
                            }
                            "CopyrightWarning"
                            {
                            "Name" = "8:CopyrightWarning"
                            "DisplayName" = "8:#1002"
                            "Description" = "8:#1102"
                            "Type" = "3:3"
                            "ContextData" = "8:"
                            "Attributes" = "3:0"
                            "Setting" = "3:1"
                            "Value" = "8:#1202"
                            "DefaultValue" = "8:#1202"
                            "UsePlugInResources" = "11:TRUE"
                            }
                            "Welcome"
                            {
                            "Name" = "8:Welcome"
                            "DisplayName" = "8:#1003"
                            "Description" = "8:#1103"
                            "Type" = "3:3"
                            "ContextData" = "8:"
                            "Attributes" = "3:0"
                            "Setting" = "3:1"
                            "Value" = "8:#1203"
                            "DefaultValue" = "8:#1203"
                            "UsePlugInResources" = "11:TRUE"
                            }
                        }
                    }
                    "{688940B3-5CA9-4162-8DEE-2993FA9D8CBC}:_AD2CC333F9344D21A055E59CA4A51357"
                    {
                    "Sequence" = "3:200"
                    "DisplayName" = "8:Installation Folder"
                    "UseDynamicProperties" = "11:TRUE"
                    "IsDependency" = "11:FALSE"
                    "SourcePath" = "8:<VsdDialogDir>\\VsdFolderDlg.wid"
                        "Properties"
                        {
                            "BannerBitmap"
                            {
                            "Name" = "8:BannerBitmap"
                            "DisplayName" = "8:#1001"
                            "Description" = "8:#1101"
                            "Type" = "3:8"
                            "ContextData" = "8:Bitmap"
                            "Attributes" = "3:4"
                            "Setting" = "3:1"
                            "UsePlugInResources" = "11:TRUE"
                            }
                            "InstallAllUsersVisible"
                            {
                            "Name" = "8:InstallAllUsersVisible"
                            "DisplayName" = "8:#1059"
                            "Description" = "8:#1159"
                            "Type" = "3:5"
                            "ContextData" = "8:1;True=1;False=0"
                            "Attributes" = "3:0"
                            "Setting" = "3:0"
                            "Value" = "3:1"
                            "DefaultValue" = "3:1"
                            "UsePlugInResources" = "11:TRUE"
                            }
                        }
                    }
                    "{688940B3-5CA9-4162-8DEE-2993FA9D8CBC}:_C5B57D34FE6342AAA72BA60F6C5729F2"
                    {
                    "Sequence" = "3:300"
                    "DisplayName" = "8:Confirm Installation"
                    "UseDynamicProperties" = "11:TRUE"
                    "IsDependency" = "11:FALSE"
                    "SourcePath" = "8:<VsdDialogDir>\\VsdConfirmDlg.wid"
                        "Properties"
                        {
                            "BannerBitmap"
                            {
                            "Name" = "8:BannerBitmap"
                            "DisplayName" = "8:#1001"
                            "Description" = "8:#1101"
                            "Type" = "3:8"
                            "ContextData" = "8:Bitmap"
                            "Attributes" = "3:4"
                            "Setting" = "3:1"
                            "UsePlugInResources" = "11:TRUE"
                            }
                        }
                    }
                }
            }
        }
        "MergeModule"
        {
        }
        "ProjectOutput"
        {
            "{5259A561-127C-4D43-A0A1-72F10C7B3BF8}:_5FC3513C5E89413E835FAC3E7D6D72C8"
            {
            "SourcePath" = "8:..\\SmartCreator\\obj\\Release\\SmartCreator.exe"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_BCAEB2EE9CF74F6C8D69B48CCD79B25B"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            "ProjectOutputGroupRegister" = "3:1"
            "OutputConfiguration" = "8:"
            "OutputGroupCanonicalName" = "8:Built"
            "OutputProjectGuid" = "8:{6BF6386B-B046-428A-BB35-B78A0198BD3D}"
            "ShowKeyOutput" = "11:TRUE"
                "ExcludeFilters"
                {
                }
            }
        }
    }
}
