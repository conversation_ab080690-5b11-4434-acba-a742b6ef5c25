using SmartCreator.Entities;
using SmartCreator.Models;
using SmartCreator.Services.Security;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace SmartCreator.Helpers
{
    /// <summary>
    /// فئة مساعدة لإدارة الأمان والصلاحيات
    /// </summary>
    public static class SecurityHelper
    {
        private static UserActivityService _activityService;
        private static PermissionService _permissionService;

        /// <summary>
        /// تهيئة خدمات الأمان
        /// </summary>
        public static void Initialize(UserActivityService activityService, PermissionService permissionService)
        {
            _activityService = activityService;
            _permissionService = permissionService;
        }

        /// <summary>
        /// التحقق من وجود صلاحية للمستخدم الحالي
        /// </summary>
        public static bool HasPermission(string permissionCode)
        {
            if (Global_Variable.CurrentUser == null)
                return false;

            // مدير النظام له جميع الصلاحيات
            if (Global_Variable.CurrentUser.Permissions?.Contains(PermissionCodes.SYSTEM_ADMIN) == true)
                return true;

            return Global_Variable.CurrentUser.Permissions?.Contains(permissionCode) == true;
        }

        /// <summary>
        /// التحقق من وجود أي من الصلاحيات المحددة
        /// </summary>
        public static bool HasAnyPermission(params string[] permissionCodes)
        {
            if (Global_Variable.CurrentUser == null)
                return false;

            // مدير النظام له جميع الصلاحيات
            if (Global_Variable.CurrentUser.Permissions?.Contains(PermissionCodes.SYSTEM_ADMIN) == true)
                return true;

            return permissionCodes.Any(code => Global_Variable.CurrentUser.Permissions?.Contains(code) == true);
        }

        /// <summary>
        /// التحقق من وجود جميع الصلاحيات المحددة
        /// </summary>
        public static bool HasAllPermissions(params string[] permissionCodes)
        {
            if (Global_Variable.CurrentUser == null)
                return false;

            // مدير النظام له جميع الصلاحيات
            if (Global_Variable.CurrentUser.Permissions?.Contains(PermissionCodes.SYSTEM_ADMIN) == true)
                return true;

            return permissionCodes.All(code => Global_Variable.CurrentUser.Permissions?.Contains(code) == true);
        }

        /// <summary>
        /// التحقق من الصلاحية مع عرض رسالة خطأ
        /// </summary>
        public static bool CheckPermissionWithMessage(string permissionCode, string actionName = null)
        {
            if (HasPermission(permissionCode))
                return true;

            var message = string.IsNullOrEmpty(actionName)
                ? "ليس لديك صلاحية للوصول إلى هذه الميزة"
                : $"ليس لديك صلاحية لـ {actionName}";

            MessageBox.Show(message, "غير مصرح", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            return false;
        }

        /// <summary>
        /// تسجيل نشاط المستخدم
        /// </summary>
        public static async Task LogActivityAsync(string action, string module, string description = null,
            string severity = "Info", bool isSuccessful = true)
        {
            if (_activityService == null || Global_Variable.CurrentUser == null)
                return;

            try
            {
                await _activityService.LogActivityAsync(
                    Global_Variable.CurrentUser.Id,
                    //Global_Variable.CurrentUser?.Username ?? "غير محدد",
                    action,
                    module,
                    description
                    //isSuccessful,
                    //severity
                    );
            }
            catch (Exception ex)
            {
                // تسجيل الخطأ في ملف منفصل
                System.Diagnostics.Debug.WriteLine($"خطأ في تسجيل النشاط: {ex.Message}");
            }
        }

        /// <summary>
        /// تطبيق الصلاحيات على عنصر تحكم
        /// </summary>
        public static void ApplyPermissionToControl(Control control, string permissionCode, bool hideIfNoPermission = true)
        {
            var hasPermission = HasPermission(permissionCode);

            if (hideIfNoPermission)
            {
                control.Visible = hasPermission;
            }
            else
            {
                control.Enabled = hasPermission;
                if (!hasPermission && control is Button button)
                {
                    button.BackColor = System.Drawing.Color.Gray;
                    button.ForeColor = System.Drawing.Color.DarkGray;
                }
            }
        }

        /// <summary>
        /// تطبيق الصلاحيات على مجموعة من عناصر التحكم
        /// </summary>
        public static void ApplyPermissionsToControls(Dictionary<Control, string> controlPermissions, bool hideIfNoPermission = true)
        {
            foreach (var item in controlPermissions)
            {
                ApplyPermissionToControl(item.Key, item.Value, hideIfNoPermission);
            }
        }

        /// <summary>
        /// تطبيق الصلاحيات على عناصر القائمة
        /// </summary>
        public static void ApplyPermissionToMenuItem(ToolStripMenuItem menuItem, string permissionCode)
        {
            menuItem.Visible = HasPermission(permissionCode);
        }

        /// <summary>
        /// إنشاء الصلاحيات الافتراضية للنظام
        /// </summary>
        public static async Task<bool> CreateDefaultPermissionsAsync()
        {
            if (_permissionService == null)
                return false;

            try
            {
                return await _permissionService.CreateDefaultPermissionsAsync();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إنشاء الصلاحيات الافتراضية: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// التحقق من قوة كلمة المرور
        /// </summary>
        public static PasswordStrength CheckPasswordStrength(string password)
        {
            if (string.IsNullOrEmpty(password))
                return PasswordStrength.VeryWeak;

            int score = 0;

            // الطول
            if (password.Length >= 8) score++;
            if (password.Length >= 12) score++;

            // الأحرف الكبيرة
            if (password.Any(char.IsUpper)) score++;

            // الأحرف الصغيرة
            if (password.Any(char.IsLower)) score++;

            // الأرقام
            if (password.Any(char.IsDigit)) score++;

            // الرموز الخاصة
            if (password.Any(c => !char.IsLetterOrDigit(c))) score++;

            return score switch
            {
                0 or 1 => PasswordStrength.VeryWeak,
                2 => PasswordStrength.Weak,
                3 or 4 => PasswordStrength.Medium,
                5 => PasswordStrength.Strong,
                6 => PasswordStrength.VeryStrong,
                _ => PasswordStrength.VeryWeak
            };
        }

        /// <summary>
        /// الحصول على وصف قوة كلمة المرور
        /// </summary>
        public static string GetPasswordStrengthDescription(PasswordStrength strength)
        {
            return strength switch
            {
                PasswordStrength.VeryWeak => "ضعيفة جداً",
                PasswordStrength.Weak => "ضعيفة",
                PasswordStrength.Medium => "متوسطة",
                PasswordStrength.Strong => "قوية",
                PasswordStrength.VeryStrong => "قوية جداً",
                _ => "غير محددة"
            };
        }

        /// <summary>
        /// الحصول على لون قوة كلمة المرور
        /// </summary>
        public static System.Drawing.Color GetPasswordStrengthColor(PasswordStrength strength)
        {
            return strength switch
            {
                PasswordStrength.VeryWeak => System.Drawing.Color.DarkRed,
                PasswordStrength.Weak => System.Drawing.Color.Red,
                PasswordStrength.Medium => System.Drawing.Color.Orange,
                PasswordStrength.Strong => System.Drawing.Color.YellowGreen,
                PasswordStrength.VeryStrong => System.Drawing.Color.Green,
                _ => System.Drawing.Color.Gray
            };
        }

        /// <summary>
        /// تشفير النص
        /// </summary>
        public static string EncryptText(string plainText, string key = null)
        {
            if (string.IsNullOrEmpty(plainText))
                return string.Empty;

            try
            {
                // تنفيذ بسيط للتشفير (يمكن استخدام AES للتشفير الأقوى)
                var bytes = System.Text.Encoding.UTF8.GetBytes(plainText);
                return Convert.ToBase64String(bytes);
            }
            catch
            {
                return plainText;
            }
        }

        /// <summary>
        /// فك تشفير النص
        /// </summary>
        public static string DecryptText(string encryptedText, string key = null)
        {
            if (string.IsNullOrEmpty(encryptedText))
                return string.Empty;

            try
            {
                var bytes = Convert.FromBase64String(encryptedText);
                return System.Text.Encoding.UTF8.GetString(bytes);
            }
            catch
            {
                return encryptedText;
            }
        }

        /// <summary>
        /// التحقق من انتهاء صلاحية الجلسة
        /// </summary>
        public static bool IsSessionExpired()
        {
            if (Global_Variable.CurrentUser == null)
                return true;

            // التحقق من انتهاء صلاحية كلمة المرور
            if (Global_Variable.CurrentUser.IsPasswordExpired)
                return true;

            // التحقق من قفل الحساب
            if (Global_Variable.CurrentUser.IsAccountLocked)
                return true;

            // يمكن إضافة المزيد من التحققات هنا
            return false;
        }

        /// <summary>
        /// تسجيل خروج المستخدم
        /// </summary>
        public static async Task LogoutUserAsync()
        {
            if (Global_Variable.CurrentUser != null)
            {
                await LogActivityAsync("تسجيل خروج", "المصادقة",
                    $"تسجيل خروج المستخدم {Global_Variable.CurrentUser.Username}");

                Global_Variable.CurrentUser = null;
            }
        }

        /// <summary>
        /// التحقق من الحد الأقصى لمحاولات الدخول الفاشلة
        /// </summary>
        public static bool IsAccountLockedDueToFailedAttempts(User user)
        {
            return user.FailedLoginAttempts >= 5;
        }

        /// <summary>
        /// الحصول على قائمة الصلاحيات المطلوبة لوحدة معينة
        /// </summary>
        public static List<string> GetRequiredPermissionsForModule(string module)
        {
            return module switch
            {
                "المحاسبة" => new List<string> { PermissionCodes.ACCOUNTING_VIEW, PermissionCodes.ACCOUNTS_VIEW },
                "الحسابات" => new List<string> { PermissionCodes.ACCOUNTS_VIEW },
                "التقارير" => new List<string> { PermissionCodes.REPORTS_VIEW },
                "إدارة المستخدمين" => new List<string> { PermissionCodes.MANAGE_USERS },
                "الصلاحيات" => new List<string> { PermissionCodes.MANAGE_PERMISSIONS },
                _ => new List<string>()
            };
        }
    }

    /// <summary>
    /// مستويات قوة كلمة المرور
    /// </summary>
    public enum PasswordStrength
    {
        VeryWeak,
        Weak,
        Medium,
        Strong,
        VeryStrong
    }
}
