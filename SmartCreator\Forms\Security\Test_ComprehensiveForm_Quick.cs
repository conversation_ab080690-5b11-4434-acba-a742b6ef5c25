sing System;
using System.Windows.Forms;
using SmartCreator.Forms.Security;
using SmartCreator.Settings;
using SmartCreator.RJControls;

namespace SmartCreator.Forms.Security
{
    /// <summary>
    /// اختبار سريع لنموذج الاختبار الشامل
    /// </summary>
    public static class Test_ComprehensiveForm_Quick
    {
        /// <summary>
        /// اختبار سريع لعرض النموذج
        /// </summary>
        public static void QuickTest()
        {
            try
            {
                Console.WriteLine("🧪 اختبار سريع لنموذج الاختبار الشامل...\n");

                // تهيئة الإعدادات
                UIAppearance.Theme = UITheme.Light;
                UIAppearance.Language_ar = true;

                Console.WriteLine("1️⃣ إنشاء النموذج...");
                var form = new Frm_ComprehensiveTest();
                
                Console.WriteLine("2️⃣ عرض النموذج...");
                form.Show();
                
                Console.WriteLine("✅ تم عرض النموذج بنجاح!");
                Console.WriteLine("📝 تحقق من ظهور المكونات:");
                Console.WriteLine("   • الهيدر الأزرق مع العنوان");
                Console.WriteLine("   • 8 أزرار للاختبارات");
                Console.WriteLine("   • منطقة النتائج");
                Console.WriteLine("   • شريط التقدم");

                RJMessageBox.Show(
                    "🧪 تم عرض نموذج الاختبار الشامل!\n\n" +
                    "تحقق من ظهور المكونات التالية:\n" +
                    "✅ هيدر أزرق مع العنوان\n" +
                    "✅ 8 أزرار للاختبارات\n" +
                    "✅ منطقة النتائج السوداء\n" +
                    "✅ النص الترحيبي\n\n" +
                    "إذا ظهرت جميع المكونات، فالنموذج يعمل بشكل مثالي!",
                    "اختبار النموذج",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Information);

                // لا نغلق النموذج هنا ليتمكن المستخدم من رؤيته
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في الاختبار: {ex.Message}");
                RJMessageBox.Show(
                    $"❌ خطأ في عرض النموذج:\n\n{ex.Message}\n\n" +
                    "تفاصيل الخطأ:\n{ex.StackTrace}",
                    "خطأ في الاختبار",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// اختبار إنشاء وإغلاق النموذج
        /// </summary>
        public static void TestCreateAndClose()
        {
            try
            {
                Console.WriteLine("🔄 اختبار إنشاء وإغلاق النموذج...\n");

                for (int i = 1; i <= 3; i++)
                {
                    Console.WriteLine($"المحاولة {i}:");
                    
                    var form = new Frm_ComprehensiveTest();
                    Console.WriteLine($"   ✅ تم إنشاء النموذج");
                    
                    Console.WriteLine($"   📏 الحجم: {form.Size.Width}x{form.Size.Height}");
                    Console.WriteLine($"   📝 العنوان: {form.Text}");
                    
                    form.Dispose();
                    Console.WriteLine($"   ✅ تم إغلاق النموذج\n");
                }

                Console.WriteLine("🎉 جميع الاختبارات نجحت!");
                
                RJMessageBox.Show(
                    "✅ نجح اختبار إنشاء وإغلاق النموذج!\n\n" +
                    "تم إنشاء النموذج 3 مرات بنجاح\n" +
                    "النموذج مستقر ويعمل بشكل مثالي\n\n" +
                    "🎯 النموذج جاهز للاستخدام!",
                    "نجح الاختبار",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في الاختبار: {ex.Message}");
                RJMessageBox.Show(
                    $"❌ خطأ في اختبار النموذج:\n\n{ex.Message}",
                    "خطأ في الاختبار",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// اختبار شامل للنموذج
        /// </summary>
        public static void FullTest()
        {
            try
            {
                var result = RJMessageBox.Show(
                    "🧪 اختبار نموذج الاختبار الشامل\n\n" +
                    "هذا الاختبار سيقوم بـ:\n" +
                    "✅ إنشاء النموذج وعرضه\n" +
                    "✅ التحقق من ظهور جميع المكونات\n" +
                    "✅ اختبار الاستقرار\n\n" +
                    "هل تريد المتابعة؟",
                    "اختبار النموذج الشامل",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    // اختبار الإنشاء والإغلاق
                    TestCreateAndClose();
                    
                    // اختبار العرض
                    QuickTest();
                    
                    RJMessageBox.Show(
                        "🎉 اكتمل الاختبار الشامل بنجاح!\n\n" +
                        "✅ النموذج يعمل بشكل مثالي\n" +
                        "✅ جميع المكونات تظهر بشكل صحيح\n" +
                        "✅ النموذج مستقر وجاهز للاستخدام\n\n" +
                        "🚀 يمكنك الآن استخدام النموذج من MainForm!",
                        "اكتمل الاختبار",
                        MessageBoxButtons.OK,
                        MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                RJMessageBox.Show(
                    $"❌ خطأ في الاختبار الشامل:\n\n{ex.Message}",
                    "خطأ",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// نقطة الدخول للاختبار
        /// </summary>
        [STAThread]
        public static void Main()
        {
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);
            
            FullTest();
        }
    }
}
