using System;
using System.Threading.Tasks;
using System.Windows.Forms;
using SmartCreator.Data;
using SmartCreator.RJForms;

namespace SmartCreator.Services
{
    /// <summary>
    /// خدمة اختبار قاعدة البيانات
    /// </summary>
    public static class DatabaseTestService
    {
        /// <summary>
        /// اختبار سريع لإنشاء جدول الإشعارات
        /// </summary>
        public static async Task TestNotificationsTableAsync()
        {
            try
            {
                var dataAccess = new Smart_DataAccess();

                // إنشاء جدول الإشعارات
                var createTableSql = @"
                    CREATE TABLE IF NOT EXISTS Notifications (
                        Id INTEGER PRIMARY KEY AUTOINCREMENT,
                        Title NVARCHAR(200) NOT NULL,
                        Message NVARCHAR(1000) NOT NULL,
                        Type NVARCHAR(50) NOT NULL DEFAULT 'Info',
                        Priority NVARCHAR(20) NOT NULL DEFAULT 'Normal',
                        IsRead BOOLEAN NOT NULL DEFAULT 0,
                        CreatedDate DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                        ReadDate DATETIME,
                        UserId INTEGER,
                        Username NVARCHAR(100),
                        Module NVARCHAR(100),
                        EntityId INTEGER,
                        EntityType NVARCHAR(100),
                        ActionUrl NVARCHAR(500),
                        AdditionalData NTEXT,
                        ExpiryDate DATETIME,
                        IsActive BOOLEAN NOT NULL DEFAULT 1,
                        Rb NVARCHAR(50)
                    )";

                await Task.Run(() => dataAccess.Execute(createTableSql));

                // إنشاء فهرس للإشعارات
                var indexSql = @"
                    CREATE INDEX IF NOT EXISTS IX_Notifications_IsRead ON Notifications(IsRead);
                    CREATE INDEX IF NOT EXISTS IX_Notifications_CreatedDate ON Notifications(CreatedDate);
                    CREATE INDEX IF NOT EXISTS IX_Notifications_Type ON Notifications(Type);
                    CREATE INDEX IF NOT EXISTS IX_Notifications_IsActive ON Notifications(IsActive);";

                await Task.Run(() => dataAccess.Execute(indexSql));

                // اختبار إدراج إشعار
                var insertSql = @"
                    INSERT INTO Notifications (Title, Message, Type, Priority, CreatedDate, IsActive)
                    VALUES ('اختبار النظام', 'تم إنشاء جدول الإشعارات بنجاح', 'SUCCESS', 'NORMAL', CURRENT_TIMESTAMP, 1)";

                await Task.Run(() => dataAccess.Execute(insertSql));

                // اختبار عد الإشعارات
                var count = await Task.Run(() => dataAccess.Get_int_FromDB("SELECT COUNT(*) FROM Notifications"));

                RJMessageBox.Show(
                    $"✅ تم إنشاء جدول الإشعارات بنجاح!\n\n" +
                    $"📊 النتائج:\n" +
                    $"• تم إنشاء الجدول والفهارس\n" +
                    $"• تم إدراج إشعار اختبار\n" +
                    $"• عدد الإشعارات الحالي: {count}\n\n" +
                    $"🎉 النظام جاهز للاستخدام!",
                    "نجح إنشاء جدول الإشعارات",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Information
                );
            }
            catch (Exception ex)
            {
                RJMessageBox.Show(
                    $"❌ خطأ في إنشاء جدول الإشعارات:\n\n{ex.Message}",
                    "خطأ في قاعدة البيانات",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error
                );
            }
        }

        /// <summary>
        /// فحص وجود جدول الإشعارات
        /// </summary>
        public static async Task<bool> CheckNotificationsTableExistsAsync()
        {
            try
            {
                var dataAccess = new Smart_DataAccess();
                var sql = "SELECT name FROM sqlite_master WHERE type='table' AND name='Notifications'";
                var result = await Task.Run(() => dataAccess.GetAnyDB<string>(sql));
                return !string.IsNullOrEmpty(result);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في فحص جدول الإشعارات: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// اختبار شامل لقاعدة البيانات
        /// </summary>
        public static async Task RunComprehensiveDatabaseTestAsync()
        {
            try
            {
                // فحص وجود الجدول
                var tableExists = await CheckNotificationsTableExistsAsync();

                if (!tableExists)
                {
                    // إنشاء الجدول إذا لم يكن موجوداً
                    await TestNotificationsTableAsync();
                }
                else
                {
                    // اختبار الخدمات إذا كان الجدول موجوداً
                    await NotificationServiceTest.RunQuickTestAsync();
                }
            }
            catch (Exception ex)
            {
                RJMessageBox.Show(
                    $"❌ خطأ في الاختبار الشامل:\n\n{ex.Message}",
                    "خطأ في الاختبار",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error
                );
            }
        }
    }
}
