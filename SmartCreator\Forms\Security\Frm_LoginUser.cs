﻿using FontAwesome.Sharp;
using SmartCreator.Data;
using SmartCreator.Entities;
using SmartCreator.Models;
using SmartCreator.RJControls;
using SmartCreator.Services;
using SmartCreator.Services.Security;
using SmartCreator.Settings;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Diagnostics;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace SmartCreator.Forms.Security
{
    public partial class Frm_LoginUser : RJForms.RJBaseForm
    {
        #region Fields

        private UserManagementService _userService;
        private UserActivityService _activityService;
        private bool _isLoading = false;
        private int _loginAttempts = 0;
        private const int MAX_LOGIN_ATTEMPTS = 3;

        #endregion
        #region -> Constructor

        public Frm_LoginUser()
        {
            InitializeComponent();
            InitializeServices();
            this.StartPosition = FormStartPosition.Manual;
            this.Top = (Screen.PrimaryScreen.Bounds.Height - this.Height) / 2;
            this.Left = (Screen.PrimaryScreen.Bounds.Width - this.Width) / 2;


            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.None;

            AddControlBox();
            ApplyAppearanceSettings();
            this.Text = "تسجيل الدخول - Smart Creator";

            //Add line
            var line = new Control();
            line.Size = new Size(lblDescription.Width - 10, 1);
            line.BackColor = Color.LightGray;
            line.Location = new Point(lblDescription.Left + 5, lblTitle.Bottom + 15);
            icoBanner.Controls.Add(line);
        }
        #endregion

        #region Initialization

        /// <summary>
        /// تهيئة الخدمات
        /// </summary>
        private void InitializeServices()
        {
            try
            {
                _userService = new UserManagementService();
                _activityService = new UserActivityService(new Smart_DataAccess());
            }
            catch (Exception ex)
            {
                RJMessageBox.Show($"خطأ في تهيئة الخدمات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        #endregion

        #region -> Private methods

        private void AddControlBox()
        {//Add control buttons (Maximize, close and minimize)
		
            this.Controls.Add(this.btnClose);
            this.Controls.Add(this.btnMinimize);
            this.btnClose.Height = 20;
            this.btnClose.Location = new Point(this.Width - btnClose.Width, 0);

            this.btnMinimize.Height = 20;
            this.btnMinimize.Location = new Point(this.btnClose.Location.X - btnMinimize.Width, 0);
        }
        private void ApplyAppearanceSettings()
        {// Apply the appearance properties of the configuration.

             this.PrimaryForm = true; // Set as primary form.
             this.Resizable = false; // Set that the form cannot be resized from the border.
             this.BorderSize = 0; // Remove the border.
             this.BackColor = UIAppearance.BackgroundColor; // Set the back color.

            if (UIAppearance.Theme == UITheme.Light)//if the theme is LIGHT, set the maximize, minimize and close buttons to black.
            {
                this.btnClose.Image = Properties.Resources.CloseDark;
                this.btnMaximize.Image = Properties.Resources.MaximizeDark;
                this.btnMinimize.Image = Properties.Resources.MinimizeDark;
            }

        }

        /// <summary>
        /// تحميل بيانات المستخدم المحفوظة
        /// </summary>
        private void LoadSavedCredentials()
        {
            try
            {
                // محاولة تحميل البيانات المحفوظة
                string savedUsername = Properties.Settings.Default.SavedUsername ?? "";
                bool rememberMe = Properties.Settings.Default.RememberUsername;

                if (rememberMe && !string.IsNullOrEmpty(savedUsername))
                {
                    txtUser.Text = savedUsername;
                    chkRememberMe.Checked = true;

                    // التركيز على حقل كلمة المرور إذا كان اسم المستخدم محفوظ
                    txtPassword.Focus();

                    Console.WriteLine($"📥 تم تحميل اسم المستخدم المحفوظ: {savedUsername}");

                    // عرض رسالة ترحيب
                    lblWelcome.Text = $"مرحباً بعودتك {savedUsername}!";
                }
                else
                {
                    // التركيز على حقل اسم المستخدم
                    txtUser.Focus();
                    Console.WriteLine("📥 لا توجد بيانات محفوظة");
                }

                // إضافة أسماء مستخدمين للاختبار السريع
                //AddTestUsernames();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في تحميل البيانات المحفوظة: {ex.Message}");
                txtUser.Focus();
            }
        }
        /// <summary>
        /// حفظ بيانات المستخدم
        /// </summary>
        private void SaveCredentials()
        {
            try
            {
                if (chkRememberMe.Checked)
                {
                    // حفظ اسم المستخدم
                    Properties.Settings.Default.SavedUsername = txtUser.Text.Trim();
                    Properties.Settings.Default.SavedPassword = txtPassword.Text.Trim();
                    Properties.Settings.Default.RememberUsername = true;

                    Console.WriteLine($"💾 تم حفظ اسم المستخدم: {txtUser.Text.Trim()}");
                }
                else
                {
                    // مسح البيانات المحفوظة
                    Properties.Settings.Default.SavedUsername = "";
                    Properties.Settings.Default.SavedPassword = "";
                    Properties.Settings.Default.RememberUsername = false;

                    Console.WriteLine("💾 تم مسح البيانات المحفوظة");
                }

                // حفظ الإعدادات
                Properties.Settings.Default.Save();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في حفظ البيانات: {ex.Message}");
            }
        }


        /// <summary>
        /// تنفيذ عملية تسجيل الدخول بشكل غير متزامن
        /// </summary>
        [Obsolete]
        private async Task PerformLoginAsync()
        {
            if (_isLoading) return;

            try
            {
                _isLoading = true;
                ShowLoadingState(true);

                // التحقق من صحة البيانات
                if (!ValidateLoginData())
                {
                    ShowLoadingState(false);
                    _isLoading = false;
                    return;
                }

                string username = txtUser.Text.Trim();
                string password = txtPassword.Text;

                // عرض معلومات تسجيل الدخول للمطور
                //ShowLoginAttemptInfo(username, password);

                // محاولة تسجيل الدخول من قاعدة البيانات
                User authenticatedUser = await AttemptLogin(username, password);

                if (authenticatedUser != null)
                {
                    // نجح تسجيل الدخول
                    await HandleSuccessfulLogin(authenticatedUser);
                }
                else
                {
                    // فشل تسجيل الدخول
                    await HandleFailedLogin(username);
                }
            }
            catch (Exception ex)
            {
                RJMessageBox.Show($"خطأ في تسجيل الدخول: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                ShowLoadingState(false);
                _isLoading = false;
            }
        }
        /// <summary>
        /// عرض حالة التحميل
        /// </summary>
        private void ShowLoadingState(bool loading)
        {
            btnLogin.Enabled = !loading;
            btnLogin.Text = loading ? "جاري تسجيل الدخول..." : "تسجيل الدخول";

            if (loading)
            {
                btnLogin.IconChar = IconChar.Spinner;
                // يمكن إضافة تأثير دوران للأيقونة
            }
            else
            {
                btnLogin.IconChar = IconChar.SignInAlt;
            }
        }

        /// <summary>
        /// التحقق من صحة بيانات تسجيل الدخول
        /// </summary>
        private bool ValidateLoginData()
        {
            if (string.IsNullOrWhiteSpace(txtUser.Text))
            {
                RJMessageBox.Show("يرجى إدخال اسم المستخدم", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtUser.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(txtPassword.Text))
            {
                RJMessageBox.Show("يرجى إدخال كلمة المرور", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtPassword.Focus();
                return false;
            }

            return true;
        }


        /// <summary>
        /// محاولة تسجيل الدخول مع التحقق من قاعدة البيانات
        /// </summary>
        private async Task<User> AttemptLogin(string username, string password)
        {
            try
            {
                //Console.WriteLine($"🔍 بدء التحقق من المستخدم: {username}");

                // التحقق من وجود الجداول المطلوبة
                var tablesExist = await CheckRequiredTablesExistAsync();

                if (!tablesExist)
                {
                    //Console.WriteLine("❌ الجداول المطلوبة غير موجودة");

                    var result = RJMessageBox.Show(
                        "🗄️ قاعدة البيانات غير مكتملة\n\n" +
                        "الجداول المطلوبة لنظام الأمان غير موجودة.\n\n" +
                        "هل تريد إنشاء الجداول الآن؟\n\n" +
                        "سيتم إنشاء:\n" +
                        "• جدول المستخدمين\n" +
                        "• جدول الأنشطة\n" +
                        "• جدول الصلاحيات\n" +
                        "• مستخدمين افتراضيين للاختبار",
                        "إنشاء قاعدة البيانات",
                        MessageBoxButtons.YesNo,
                        MessageBoxIcon.Question);

                    if (result == DialogResult.Yes)
                    {
                        //Console.WriteLine("🔧 إنشاء الجداول المطلوبة...");
                        //await CreateRequiredTablesAsync();

                        RJMessageBox.Show(
                            "✅ تم إنشاء قاعدة البيانات بنجاح!\n\n" +
                            "المستخدمون المُنشأون:\n" +
                            "• admin / admin123 (مدير النظام)\n" +
                            "• test / test123 (مستخدم تجريبي)\n\n" +
                            "يمكنك الآن تسجيل الدخول!",
                            "نجح إنشاء قاعدة البيانات",
                            MessageBoxButtons.OK,
                            MessageBoxIcon.Information);
                    }
                    else
                    {
                        return null;
                    }
                }

                // استخدام UserService للمصادقة من قاعدة البيانات
                var dataAccess = new Smart_DataAccess();
                var permissionService = new PermissionService(dataAccess, _activityService);
                var userService = new Services.Security.UserService(dataAccess, _activityService, permissionService);
                var authenticatedUser = await userService.AuthenticateAsync(username, password);

                if (authenticatedUser != null)
                {
                    Console.WriteLine($"✅ نجح التحقق من المستخدم: {authenticatedUser.Username}");
                    Console.WriteLine($"📧 البريد الإلكتروني: {authenticatedUser.Email}");
                    Console.WriteLine($"👤 الاسم الكامل: {authenticatedUser.DisplayName}");
                    Console.WriteLine($"🏢 القسم: {authenticatedUser.Department}");
                    Console.WriteLine($"💼 المنصب: {authenticatedUser.Position}");

                    return authenticatedUser;
                }
                else
                {
                    Console.WriteLine($"❌ فشل التحقق من المستخدم: {username}");
                    return null;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في محاولة تسجيل الدخول: {ex.Message}");

                // تسجيل محاولة دخول فاشلة بسبب خطأ تقني
                try
                {
                    await _activityService?.LogLoginAttemptAsync(null, username, false,
                        $"خطأ تقني: {ex.Message}");
                }
                catch (Exception logEx)
                {
                    Console.WriteLine($"⚠️ خطأ في تسجيل النشاط: {logEx.Message}");
                }

                return null;
            }
        }

        /// <summary>
        /// معالجة تسجيل الدخول الفاشل
        /// </summary>
        private async Task HandleFailedLogin(string username)
        {
            try
            {
                _loginAttempts++;

                Console.WriteLine($"❌ فشل تسجيل الدخول للمستخدم: {username}");
                Console.WriteLine($"🔢 المحاولة رقم: {_loginAttempts} من {MAX_LOGIN_ATTEMPTS}");

                // تسجيل محاولة الدخول الفاشلة - تم بالفعل في UserService.AuthenticateAsync
                Console.WriteLine($"📝 تم تسجيل محاولة الدخول الفاشلة في قاعدة البيانات");

                if (_loginAttempts >= MAX_LOGIN_ATTEMPTS)
                {
                    Console.WriteLine($"🚨 تم تجاوز الحد الأقصى للمحاولات!");

                    // تسجيل تجاوز الحد الأقصى
                    try
                    {
                        await _activityService?.LogActivityAsync(0, "تجاوز الحد الأقصى للمحاولات",
                            "Authentication", $"تم تجاوز الحد الأقصى لمحاولات تسجيل الدخول للمستخدم: {username}",
                            severity: "Critical", isSuccessful: false);
                    }
                    catch (Exception logEx)
                    {
                        Console.WriteLine($"⚠️ خطأ في تسجيل تجاوز الحد الأقصى: {logEx.Message}");
                    }

                    RJMessageBox.Show($"🚨 تحذير أمني!\n\n" +
                        $"تم تجاوز الحد الأقصى لمحاولات تسجيل الدخول ({MAX_LOGIN_ATTEMPTS})\n\n" +
                        "سيتم إغلاق التطبيق لأسباب أمنية.\n" +
                        "يرجى التواصل مع مدير النظام إذا كنت تواجه مشاكل في تسجيل الدخول.",
                        "تحذير أمني", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    Application.Exit();
                }
                else
                {
                    int remainingAttempts = MAX_LOGIN_ATTEMPTS - _loginAttempts;
                    RJMessageBox.Show($"اسم المستخدم أو كلمة المرور غير صحيحة\nالمحاولات المتبقية: {remainingAttempts}",
                        "خطأ في تسجيل الدخول", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }

                // مسح كلمة المرور
                txtPassword.Clear();
                txtUser.Focus();
            }
            catch (Exception ex)
            {
                RJMessageBox.Show($"خطأ في معالجة فشل تسجيل الدخول: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تبديل رؤية كلمة المرور
        /// </summary>
        private void TogglePasswordVisibility()
        {
            try
            {
                // تبديل حالة إظهار كلمة المرور
                txtPassword.PasswordChar = !txtPassword.PasswordChar;

                // تحديث الأيقونة
                btnShowPassword.IconChar = txtPassword.PasswordChar ? IconChar.Eye : IconChar.EyeSlash;

                // تحديث لون الأيقونة
                btnShowPassword.IconColor = txtPassword.PasswordChar ?
                    Color.FromArgb(160, 174, 192) : RJColors.PrimaryColor;

                // تحديث tooltip
                toolTip1.SetToolTip(btnShowPassword, txtPassword.PasswordChar ?
                    "إظهار كلمة المرور" : "إخفاء كلمة المرور");

                // عرض معلومات للمطور
                Console.WriteLine($"🔍 تم {(txtPassword.PasswordChar ? "إخفاء" : "إظهار")} كلمة المرور");

                // إعادة التركيز على حقل كلمة المرور
                txtPassword.Focus();

                // وضع المؤشر في نهاية النص
                SetTextBoxCursorToEnd(txtPassword);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في تبديل رؤية كلمة المرور: {ex.Message}");
                RJMessageBox.Show($"خطأ في تبديل رؤية كلمة المرور: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        /// <summary>
        /// وضع المؤشر في نهاية النص لـ RJTextBox
        /// </summary>
        private void SetTextBoxCursorToEnd(RJTextBox rjTextBox)
        {
            try
            {
                if (rjTextBox?.Text != null)
                {
                    // محاولة الوصول للـ TextBox الداخلي في RJTextBox
                    var textBoxField = rjTextBox.GetType().GetField("textBox1",
                        System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

                    if (textBoxField != null)
                    {
                        var innerTextBox = textBoxField.GetValue(rjTextBox) as TextBox;
                        if (innerTextBox != null)
                        {
                            innerTextBox.SelectionStart = innerTextBox.Text.Length;
                            innerTextBox.SelectionLength = 0;
                            Console.WriteLine($"🎯 تم وضع المؤشر في نهاية النص (الموضع: {innerTextBox.SelectionStart})");
                            return;
                        }
                    }

                    // محاولة أخرى بأسماء مختلفة للحقل
                    var possibleNames = new[] { "textBox", "txtBox", "innerTextBox", "baseTextBox" };

                    foreach (var fieldName in possibleNames)
                    {
                        var field = rjTextBox.GetType().GetField(fieldName,
                            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

                        if (field != null)
                        {
                            var innerTextBox = field.GetValue(rjTextBox) as TextBox;
                            if (innerTextBox != null)
                            {
                                innerTextBox.SelectionStart = innerTextBox.Text.Length;
                                innerTextBox.SelectionLength = 0;
                                Console.WriteLine($"🎯 تم وضع المؤشر باستخدام الحقل: {fieldName}");
                                return;
                            }
                        }
                    }

                    Console.WriteLine("⚠️ لم يتم العثور على TextBox الداخلي في RJTextBox");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"تحذير: لم يتم تحديد موضع المؤشر - {ex.Message}");
            }
        }


        /// <summary>
        /// التحقق من وجود الجداول المطلوبة
        /// </summary>
        private async Task<bool> CheckRequiredTablesExistAsync()
        {
            try
            {
                var dataAccess = new Smart_DataAccess();
                var tables = new[] { "Users", "UserActivities", "Permissions", "UserPermissions" };

                foreach (var table in tables)
                {
                    var sql = "SELECT name FROM sqlite_master WHERE type='table' AND name=@TableName";
                    var result = await dataAccess.ExecuteScalarAsync<string>(sql, new { TableName = table });

                    if (string.IsNullOrEmpty(result))
                    {
                        Console.WriteLine($"❌ جدول {table} غير موجود");
                        return false;
                    }
                }

                Console.WriteLine("✅ جميع الجداول موجودة");
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في فحص الجداول: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// معالجة تسجيل الدخول الناجح
        /// </summary>
        [Obsolete]
        private async Task HandleSuccessfulLogin(User user)
        {
            try
            {
                Console.WriteLine($"✅ نجح تسجيل الدخول للمستخدم: {user.Username}");
                Console.WriteLine($"📋 معلومات المستخدم:");
                Console.WriteLine($"   🆔 المعرف: {user.Id}");
                Console.WriteLine($"   👤 الاسم: {user.DisplayName}");
                Console.WriteLine($"   📧 البريد: {user.Email}");
                Console.WriteLine($"   🏢 القسم: {user.Department ?? "غير محدد"}");
                Console.WriteLine($"   💼 المنصب: {user.Position ?? "غير محدد"}");
                Console.WriteLine($"   📱 الهاتف: {user.Phone ?? "غير محدد"}");
                Console.WriteLine($"   🔐 الصلاحيات: {user.Permissions?.Count ?? 0} صلاحية");

                // تسجيل النشاط - تم بالفعل في UserService.AuthenticateAsync
                Console.WriteLine($"📝 تم تسجيل نشاط تسجيل الدخول في قاعدة البيانات");

                // حفظ بيانات المستخدم الحالي
                Global_Variable.CurrentUser = user;

                // حفظ بيانات المستخدم إذا كان مطلوباً
                if (chkRememberMe.Checked)
                {
                    SaveCredentials();
                }

                // عرض رسالة نجاح
                //ShowSuccessMessage();

                //// عرض معلومات المستخدم الكاملة من قاعدة البيانات
                //var welcomeMessage = $"🎉 مرحباً بك {user.DisplayName}!\n\n" +
                //    $"✅ تم تسجيل الدخول بنجاح من قاعدة البيانات\n\n" +
                //    $"📋 معلومات حسابك:\n" +
                //    $"👤 اسم المستخدم: {user.Username}\n" +
                //    $"📧 البريد الإلكتروني: {user.Email}\n" +
                //    $"🏢 القسم: {user.Department ?? "غير محدد"}\n" +
                //    $"💼 المنصب: {user.Position ?? "غير محدد"}\n" +
                //    $"📱 الهاتف: {user.Phone ?? "غير محدد"}\n" +
                //    $"🔐 عدد الصلاحيات: {user.Permissions?.Count ?? 0}\n" +
                //    $"⏰ وقت الدخول: {DateTime.Now:yyyy-MM-dd HH:mm:ss}\n" +
                //    $"🌐 آخر دخول: {user.LastLoginDate?.ToString("yyyy-MM-dd HH:mm") ?? "أول مرة"}\n\n" +
                //    "سيتم الآن فتح التطبيق الرئيسي...";

                //RJMessageBox.Show(welcomeMessage, "نجح تسجيل الدخول",
                //    MessageBoxButtons.OK, MessageBoxIcon.Information);

                //// تأخير قصير للتأثير البصري
                //await Task.Delay(500);

                LoginForm loginForm = new LoginForm();
                loginForm.Show();

                // إغلاق النموذج بنتيجة إيجابية
                this.DialogResult = DialogResult.OK;
                this.Hide();
                //this.Close();
            }
            catch (Exception ex)
            {
                //Console.WriteLine($"❌ خطأ في معالجة تسجيل الدخول: {ex.Message}");
                RJMessageBox.Show($"خطأ في معالجة تسجيل الدخول: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        /// <summary>
        /// عرض رسالة النجاح
        /// </summary>
        private void ShowSuccessMessage()
        {
            // يمكن عرض رسالة نجاح أو تأثير بصري
            //lblSubtitle.Text = "تم تسجيل الدخول بنجاح!";
            //lblSubtitle.ForeColor = Color.Green;
        }

        private void Login()
        {
            //Validate fields
            if (string.IsNullOrWhiteSpace(txtUser.Text))
            {
                lblMessage.Text = "*ادخل اسم المستخدم";
                lblMessage.Visible = true;
                return;
            }
            if (string.IsNullOrWhiteSpace(txtPassword.Text))
            {
                lblMessage.Text = "*ادخل كلمة المرور";
                lblMessage.Visible = true;
                return;
            }

            ////Login
            //var user = new Models.User().Login(txtUser.Text, txtPassword.Text);

            //if (user != null)
            //{
            //    var mainForm = new MainForm(user);               
            //    mainForm.Show();
            //    this.Hide();

            //    //Redisplay the login form and clear fields if the main form is closed
            //    mainForm.FormClosed += new FormClosedEventHandler(MainForm_Logout);
            //}
            //else
            //{
            //    lblMessage.Text = "*Incorrect username or password";
            //    lblMessage.Visible = true;
            //}
        }
        private void Logout()
        {
            txtPassword.Clear();
            txtUser.Clear();           
            lblMessage.Visible = false;
            lblCaption.Select();            
            this.Show();
        }


        #endregion

        #region -> Overrides

        protected override void CloseWindow()
        {//Override the method (To remove the message box if you want to exit the app) and simply exit the application, This is optional.
            Application.Exit();
        }
        #endregion

        #region -> Event Methods
        private void LoginForm_Load(object sender, EventArgs e)
        {
            try
            {
                //// تطبيق تأثير الظهور التدريجي
                //await AnimateFormAppearance();

                // تحميل بيانات المستخدم المحفوظة
                LoadSavedCredentials();

                // تركيز على حقل اسم المستخدم
                txtUser.Focus();

                // عرض رسالة ترحيب
                //ShowWelcomeMessage();
            }
            catch (Exception ex)
            {
                RJMessageBox.Show($"خطأ في تحميل النموذج: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        [Obsolete]
        private async void btnLogin_Click(object sender, EventArgs e)
        {
            //Login();
            await PerformLoginAsync();
        }

        private void txtPassword_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
                Login();
        }

        private void MainForm_Logout(object sender, FormClosedEventArgs e)
        {
            Logout();//Log out
        }
        private void biYoutube_Click(object sender, EventArgs e)
        {
            Process.Start("https://youtube.com/rjcodeadvanceen");
        }
        private void biWebPage_Click(object sender, EventArgs e)
        {
            Process.Start("https://rjcodeadvance.com/");
        }
        private void biGitHub_Click(object sender, EventArgs e)
        {
            Process.Start("https://github.com/rjcodeadvance");
        }
        private void biFacebook_Click(object sender, EventArgs e)
        {
            Process.Start("https://www.facebook.com/RJCodeAdvance");
        }
        #endregion

       
    }
}
