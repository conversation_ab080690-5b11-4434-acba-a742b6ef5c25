﻿using Dapper;

using SmartCreator.Entities;
using SmartCreator.Entities.UserManager;
using SmartCreator.Models;
using SmartCreator.RJControls;
using SmartCreator.Utils;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SQLite;
using System.IO;
using System.Linq;
using System.Linq.Expressions;
using System.Threading.Tasks;
using System.Windows.Forms;
//using System.Windows.Forms.DataVisualization.Charting;

namespace SmartCreator.Data
{
    public class Smart_DataAccess
    {
      public  static readonly object Lock_object = new object();

        public static int _dbType = 0;//sqlite
        //public  string cur = AppContext.BaseDirectory;
        //public static string connection_string = $"Data Source={Directory.GetParent(AppContext.BaseDirectory)}\\dbs\\Smart.db;";
        public static string connection_string = "Data Source="+ AppContext.BaseDirectory + "\\db\\Smart.db;";
        public static string db_connection_string_path = AppContext.BaseDirectory + "\\db\\Smart.db";
        //public static string connection_string = @"Data Source=Smart.db";
        //Smart_DataAccess Smart_DB;
        Sql_DataAccess Local_DB;
        public static string username_db = "";
        public static string password_db = "";
        //public OrmLiteConnectionFactory dbFactory = null;

        public Smart_DataAccess()
        {
            //Smart_DB = new Smart_DataAccess();
            Local_DB = new Sql_DataAccess();

            create_default_db();
            //connection_string = $"Data Source={Directory.GetParent(AppContext.BaseDirectory)}\\dbs\\Smart.db;";

            //var parent = Directory.GetParent(AppContext.BaseDirectory);
            //if (parent != null)
            //{
            //    connection_string = $"Data Source={parent}\\dbs\\Smart.db;";
            //}
        }
        public static IDbConnection GetConnSmart()
        {
            return new SQLiteConnection(utils.Get_SmartDB_ConnectionString());
            //return new SQLiteConnection(connection_string);
        }
        public static IDbConnection GetConnSmart(string _connection_string)
        {
            return new SQLiteConnection(_connection_string);
        }
        public static Connections_Db Get_default_Connections_Db(int id = -1)
        {
            lock (Lock_object)
            {
                string rb = Global_Variable.Mk_resources.RB_code;
                string rb_sn = Global_Variable.Mk_resources.RB_SN;
                string query = $"SELECT * from Connections_Db WHERE [Default]=1 and ( Mk_code='{rb}' or Mk_code='{rb_sn}' or  Mk_sn='{rb_sn}' ) ;";

                if (id != -1)
                    query = $"SELECT * from Connections_Db WHERE [Id]={id} and ( Mk_code='{rb}' or Mk_code='{rb_sn}' or  Mk_sn='{rb_sn}' ) ;";
                try
                {
                    using (var cnn = GetConnSmart())
                    {
                        return cnn.QuerySingleOrDefault<Connections_Db>(query);
                    }
                }
                catch (Exception ex) { RJMessageBox.Show(ex.Message); return null; }
            }
        }

        public static SourceSaveStateFormsVariable Get_SourceSaveStateFormsVariable(string FormName)
        {
            lock (Smart_DataAccess.Lock_object)
            {
                SourceSaveStateFormsVariable formState = null;
                try
                {
                    string rb = Global_Variable.Mk_resources.RB_code;
                    string rb_sn = Global_Variable.Mk_resources.RB_SN;
                    using (var cnn = GetConnSmart())
                    {
                        if (FormName == "FormLogin")
                            formState = cnn.QueryFirstOrDefault<SourceSaveStateFormsVariable>("select * from Setting_SaveState_Forms_Variables where name='" + FormName + "' ;");
                        else
                            formState = cnn.QueryFirstOrDefault<SourceSaveStateFormsVariable>("select * from Setting_SaveState_Forms_Variables where name='" + FormName + $"' and (rb='{rb}' or rb='{rb_sn}') ;");

                    }
                }
                catch (Exception ex) { RJMessageBox.Show(ex.Message); return null; }
                return formState;
            }
        }


        public static void Setting_SaveState_Forms_Variables(string name, string type, string value,string rb_sn = "")
        {
            lock (Smart_DataAccess.Lock_object)
            {
                SourceSaveStateFormsVariable form_stave = Get_SourceSaveStateFormsVariable(name);
                try
                {
                    string rb = "";

                    if (rb_sn == "")// اي من استعادة نسخه قبل تشغيل البرنامج
                    {
                         rb = Global_Variable.Mk_resources.RB_code;
                         rb_sn = Global_Variable.Mk_resources.RB_SN;
                    }

                    using (var cnn = GetConnSmart())
                    {
                        if (form_stave != null)
                        {
                            if (name == "FormLogin")
                            {
                                var affectedRows = cnn.Execute("UPDATE Setting_SaveState_Forms_Variables SET [values] ='" + value + "' WHERE [name] ='" + name + "' ;");
                            }
                            else
                            {
                                var affectedRows = cnn.Execute("UPDATE Setting_SaveState_Forms_Variables SET [values] ='" + value + "' WHERE [name] ='" + name + $"' and (rb='{rb}' or rb='{rb_sn}'  ) ;");
                                if (affectedRows > 0)
                                    return;
                            }
                        }
                        else
                        {
                            var affectedRows = cnn.Execute("insert into Setting_SaveState_Forms_Variables ([name],[type],[values],[rb]) values (@name,@type,@values,@rb)", new { name = name, type = type, values = value, rb = rb_sn });
                        }
                    }
                }
                catch (Exception ex) { /*RJMessageBox.Show(ex.Message);*/ }
            }
        }

        public List<T> GetListAnyDB<T>()
        {
            //string tableName = typeof(T).FullName;
            lock (Lock_object)
            {
                try
                {
                    using (var cnn = GetConnSmart())
                    {
                        return cnn.Query<T>($"select * from {typeof(T).FullName}", new DynamicParameters()).ToList();
                    }
                }
                catch (Exception ex) { RJMessageBox.Show(ex.Message); return default; }
            }
        }
        public List<T> GetListAnyDB<T>(string query)
        {
            lock (Lock_object)
            {
                try
                {
                    using (var cnn = GetConnSmart())
                    {
                        return cnn.Query<T>(query, new DynamicParameters()).ToList();
                    }
                }
                catch (Exception ex) { RJMessageBox.Show(ex.Message); return default; }
            }
        }
        public long Get_int_FromDB(string query)
        {
            lock (Lock_object)
            {
                try
                {
                    using (var cnn = GetConnSmart())
                    {
                        return cnn.ExecuteScalar<int>(query);
                    }
                }
                catch (Exception ex) { RJMessageBox.Show("Get_int_FromDB\n"+ex.Message); return default; }
            }
        }
        public T GetAnyDB<T>(string query)
        {
            lock (Lock_object)
            {
                try
                {
                    using (var cnn = GetConnSmart())
                    {
                        return cnn.QuerySingleOrDefault<T>(query, new DynamicParameters());
                    }
                }
                catch (Exception ex) { RJMessageBox.Show(ex.Message); return default; }
            }
        }
        public bool Set_DeletFromServer_As_disable(string table)
        {
            lock (Lock_object)
            {
                string rb = Global_Variable.Mk_resources.RB_SN;
                bool status = false;
                string query = "UPDATE  " + table + " SET DeleteFromServer = 1  WHERE DeleteFromServer = 0 and rb='"+rb+"';";
                using (var con = GetConnSmart())
                {
                    try
                    {
                        con.Open();
                        var sqLiteTransaction = con.BeginTransaction();
                       int effectrow= con.Execute(query, sqLiteTransaction);
                        sqLiteTransaction.Commit();
                        status = true;
                    }
                    catch (Exception ex) { RJMessageBox.Show(ex.Message); status = false; }
                }
                return status;
            }
        }


        public List<T> Load<T>(string query)
        {
            lock (Lock_object)
            {
                try
                {
                    using (var con = GetConnSmart())
                    {
                        return con.Query<T>(query, new DynamicParameters()).ToList();
                    }
                }
                catch (Exception ex) { RJMessageBox.Show(ex.Message); return default; }
            }
        }
        public List<T> Load<T>()
        {
            lock (Lock_object)
            {
                try
                {
                    using (var con = GetConnSmart())
                    {
                        return con.Query<T>($"select * from {typeof(T).Name}", new DynamicParameters()).ToList();
                    }
                }
                catch (Exception ex) { RJMessageBox.Show(ex.Message); return default; }
            }
        }

        //public List<T> Load<T>(string tableName)
        //{
        //    lock (Lock_object)
        //    {
        //        try
        //        {
        //            using (var con = GetConnSmart())
        //            {
        //                return con.Query<T>($"select * from {tableName}", new DynamicParameters()).ToList();
        //            }
        //        }
        //        catch (Exception ex) { RJMessageBox.Show(ex.Message); return default; }
        //    }
        //}

        public List<T> Load<T>(Expression<Func<T, bool>> predicate)
        {
            return default;
            lock (Lock_object)
            {
                try
                {
                    //string expBody = ((LambdaExpression)expression).Body.ToString();
                    //// Gives: ((x.Id > 5) AndAlso (x.Warranty != False))

                    //var paramName = expression.Parameters[0].Name;
                    //var paramTypeName = expression.Parameters[0].Type.Name;

                    //// You could easily add "OrElse" and others...
                    //expBody = expBody.Replace(paramName + ".", paramTypeName + ".").Replace("AndAlso", "&&");


                    ////Console.WriteLine(expBody);

                    ////Func<T, bool> func = expression.Compile();
                    ////Predicate<T> predicate = func.Invoke;

                    //using (var con = GetConnSmart())
                    //{
                    //    return con.Query<T>($"select * from {typeof(T).Name} where {expBody}", new DynamicParameters()).ToList();
                    //}
                }
                catch (Exception ex) { RJMessageBox.Show(ex.Message); return default; }
            }
        }



        //public T LoadSingle<T>()
        //{
        //    lock (Lock_object)
        //    {
        //        try
        //        {
        //            using (var con = GetConnSmart())
        //            {
        //                return con.QueryFirstOrDefault<T>($"select * from {typeof(T).Name} where ", new DynamicParameters());
        //            }
        //        }
        //        catch (Exception ex) { RJMessageBox.Show(ex.Message); return default; }
        //    }
        //}

        public T LoadSingleById<T>(string id)
        {
            lock (Lock_object)
            {
                try
                {
                    using (var con = GetConnSmart())
                    {
                        return con.QueryFirstOrDefault<T>($"select * from {typeof(T).Name} where Id={id}", new DynamicParameters());
                    }
                }
                catch (Exception ex) { RJMessageBox.Show(ex.Message); return default; }
            }
        }

         public T LoadSingleById<T>(string id,string tableName)
        {
            lock (Lock_object)
            {
                try
                {
                    using (var con = GetConnSmart())
                    {
                        return con.QueryFirstOrDefault<T>($"select * from {tableName} where Id={id}", new DynamicParameters());
                    }
                }
                catch (Exception ex) { RJMessageBox.Show(ex.Message); return default; }
            }
        }



        public T LoadSingleByNullId<T>(int? id)
        {
            if (id==null) return default;
            lock (Lock_object)
            {
                try
                {
                    using (var con = GetConnSmart())
                    {
                        return con.QueryFirstOrDefault<T>($"select * from {typeof(T).Name} where Id={id}", new DynamicParameters());
                    }
                }
                catch (Exception ex) { MessageBox.Show("LoadSingleByNullId\n"+ex.Message); return default; }
            }
        }

        public T LoadSingleByNullId<T>(int? id,string tableName)
        {
            if (id == null) return default;
            lock (Lock_object)
            {
                try
                {
                    using (var con = GetConnSmart())
                    {
                        return con.QueryFirstOrDefault<T>($"select * from {tableName} where Id={id}", new DynamicParameters());
                    }
                }
                catch (Exception ex) { MessageBox.Show("LoadSingleByNullId\n" + ex.Message); return default; }
            }
        }

        public T LoadSingle<T>(string Query)
        {
            lock (Lock_object)
            {
                try
                {
                    using (var con = GetConnSmart())
                    {
                        return con.QuerySingle<T>(Query, new DynamicParameters());
                    }
                }
                catch (Exception ex) { RJMessageBox.Show(ex.Message); return default; }
            }
        }

        public int Execute(string Query)
        {
            lock (Lock_object)
            {
                int affectedRows = 0;
                using (var con = GetConnSmart())
                {
                    try
                    {
                        con.Open();
                        var sqLiteTransaction = con.BeginTransaction();
                        affectedRows = con.Execute(Query, sqLiteTransaction);
                        sqLiteTransaction.Commit();
                    }
                    catch (Exception ex) { MessageBox.Show("Execute\n" + ex.Message); }
                }
                return affectedRows;
            }
        }

        public bool DeleteById<T>(string id,string tableName)
        {
            lock (Lock_object)
            {
                bool status = false;

                //string tableName = GetType().Name;
                //string id = (string)table.GetId();
                //string iid = (string)table.ToId();
                //string iid = (string)table.id();

                string query = "DELETE FROM " + tableName + " where Id=" + (id) + ";  ";
                using (var con = GetConnSmart())
                {
                    try
                    {
                        //var affectedRows = con.Execute(query);
                        //if (affectedRows > 0)
                        //    return true;

                        con.Open();
                        var sqLiteTransaction = con.BeginTransaction();
                        var affectedRows = con.Execute(query, sqLiteTransaction);
                        sqLiteTransaction.Commit();
                        status = true;
                    }
                    catch (Exception ex) { RJMessageBox.Show(ex.Message); status = false; }
                }
                return status;
            }
        }
        public bool Delete<T>(string Query)
        {
            lock (Lock_object)
            {
                bool status = false;
                string tableName = GetType().Name;
                //string query = "DELETE FROM " + tableName + " where Id=" + (id) + ";  ";
                using (var con = GetConnSmart())
                {
                    try
                    {
                        //var affectedRows = con.Execute(query);
                        //if (affectedRows > 0)
                        //    return true;

                        con.Open();
                        var sqLiteTransaction = con.BeginTransaction();
                        var affectedRows = con.Execute(Query, sqLiteTransaction);
                        sqLiteTransaction.Commit();
                        status = true;
                    }
                    catch (Exception ex) { RJMessageBox.Show(ex.Message); status = false; }
                }
                return status;
            }
        }
        public int InsertTable<T>(List<string> Fields, T data,string table)
        {
            int effectRows = 0;
            lock (Lock_object)
            {
                try
                {
                    string inserSql = UtilsSql.GetInsertSql<T>(Fields, table);
                    using (var con = GetConnSmart())
                    {

                        effectRows = con.Execute(inserSql, data);
                    }
                }
                catch (Exception ex) { MessageBox.Show(ex.Message); }
            }
            return effectRows;
        }
        public int InsertTable<T>(T data)
        {
            int effectRows = 0;
            try
            {
                lock (Lock_object)
                {

                    string inserSql = UtilsSql.GetInsertSql<T>();
                    using (var con = GetConnSmart())
                    {

                        //var rwos = con.Execute(inserSql, data);

                        con.Open();
                        var sqLiteTransaction = con.BeginTransaction();
                        effectRows = con.Execute(inserSql, data, sqLiteTransaction);
                        sqLiteTransaction.Commit();
                    }
                }
            }
            catch(Exception ex) { MessageBox.Show("InsertTable\n"+ex.Message); }
            return effectRows;
        }


        public int UpateTable<T>(T data,string Query)
        {
            int effectRows = 0;
            lock (Lock_object)
            {
                using (var con = GetConnSmart())
                {
                    try
                    {
                        //var affectedRows = con.Execute(query);
                        //if (affectedRows > 0)
                        //    return true;

                        con.Open();
                        var sqLiteTransaction = con.BeginTransaction();
                        effectRows = con.Execute(Query,data, sqLiteTransaction);
                        sqLiteTransaction.Commit();

                    }
                    catch (Exception ex) { MessageBox.Show("UpateTable"+ex.Message); }
                }
            }
            return effectRows;
        }
        //public T Get_Any_byId<T>(int id)
        //{
        //    string tableName = typeof(T).Name;
        //    lock (Lock_object)
        //    {
        //        try
        //        {
        //            using (var cnn = GetConnSmart())
        //            {
        //                return cnn.QuerySingleOrDefault<T>(query, new DynamicParameters());
        //            }
        //        }
        //        catch (Exception ex) { RJMessageBox.Show(ex.Message); return default; }
        //    }


        //    //lock (Lock_object)
        //    //{
        //    //    using var db = dbFactory.Open();
        //    //    T table = db.SingleById<T>(id);
        //    //    return table;
        //    //}
        //}
        private string GenerateInsertCommand<T>(T t)
        {
            string cmd = "";

            //string columns = "";
            //// Add column list on the basis of students properties
            //foreach (var property in UmUser)
            //{
            //    columns += "property.ID,"; // It should be studentID
            //}

            return cmd;
        }
        public T GetSingleById<T>(int id)
        {
            string tableName = typeof(T).Name;
            lock (Lock_object)
            {
                try
                {
                    using (var cnn = GetConnSmart())
                    {
                        return cnn.QuerySingleOrDefault<T>($"select * from {tableName} where Id={id}", new DynamicParameters());
                    }
                }
                catch (Exception ex) { MessageBox.Show("GetSingleById\n"+ex.Message); return default; }
            }
        }




        public int Execute<T>(string Query, T data)
        {
            lock (Lock_object)
            {
                int affectedRows = 0;
                using (var con = GetConnSmart())
                {
                    try
                    {
                        con.Open();
                        var sqLiteTransaction = con.BeginTransaction();
                        affectedRows = con.Execute(Query, data, sqLiteTransaction);
                        sqLiteTransaction.Commit();
                    }
                    catch (Exception ex) { MessageBox.Show("Execute\n"+ex.Message); }
                }
                return affectedRows;
            }
        }

        public int Execute<T>(string Query, HashSet<T> data)
        {
            lock (Lock_object)
            {
                int affectedRows = 0;
                using (var con = GetConnSmart())
                {
                    try
                    {
                        con.Open();
                        var sqLiteTransaction = con.BeginTransaction();
                        affectedRows = con.Execute(Query, data, sqLiteTransaction);
                        sqLiteTransaction.Commit();
                    }
                    catch (Exception ex) { MessageBox.Show("Execute\n"+ex.Message); }
                }
                return affectedRows;
            }
        }




        public List<SellingPoint> Get_SellingPoints()
        {
            lock (Lock_object)
            {
                return Load<SellingPoint>($"select * from SellingPoint where  Rb='{Global_Variable.Mk_resources.RB_code}' or Rb='{Global_Variable.Mk_resources.RB_SN}'");
                //return Smart_DB.Load<SellingPoint>(x => x.Rb == Global_Variable.Mk_resources.RB_code);

                //using var db = dbFactory.Open();
                //var lst = db.Select<SellingPoint>(x => x.Rb == Global_Variable.Mk_resources.RB_code);
                //return lst;
            }
        }

        public SellingPoint Get_SellingPoint_byId(int id)
        {
            lock (Lock_object)
            {
                //using var db = dbFactory.Open();
                SellingPoint sp = LoadSingle<SellingPoint>($"select * from SellingPoint where Id={id} ");
                return sp;
            }
        }
        public SellingPoint Get_SellingPoint_Code(string code)
        {
            lock (Lock_object)
            {
                SellingPoint sp = null;
                try
                {
                     sp = LoadSingle<SellingPoint>($"select * from SellingPoint where ( Rb='{Global_Variable.Mk_resources.RB_code}' or Rb='{Global_Variable.Mk_resources.RB_SN}' ) and Code='{code}' ");

                    //using var db = dbFactory.Open();
                    //SellingPoint sp = db.Select<SellingPoint>(x => x.Rb == Global_Variable.Mk_resources.RB_code  && x.Code==code).FirstOrDefault();
                }
                catch(Exception ex) { MessageBox.Show("Get_SellingPoint_Code\n"+ex.Message); }
                return sp;

            }
        }
        public RJComboBox Get_ComboBox_SellingPoint()
        {
            RJComboBox combo = new RJComboBox();

            try
            {
                //List<SellingPoint> sp = Smart_DataAccess;
                List<SellingPoint> sp = Get_SellingPoints();
                Dictionary<string, string> comboSource = new Dictionary<string, string>();
                comboSource.Add("", "");
                foreach (SellingPoint s in sp)
                    comboSource.Add(s.Code, s.UserName);

                combo.DataSource = new BindingSource(comboSource, null);
                combo.DisplayMember = "Value";
                combo.ValueMember = "Key";
                //combo.SelectedIndex = 0;
                //combo.Text = "";
            }
            catch { }
            return combo;
        }
        public BindingSource Get_BindingSource_SellingPoint()
        {

            BindingSource bindingSource=   new BindingSource();
            try
            {
                List<SellingPoint> sp = Get_SellingPoints();
                Dictionary<string, string> comboSource = new Dictionary<string, string>();
                comboSource.Add("", "");
                foreach (SellingPoint s in sp)
                    comboSource.Add(s.Code, s.UserName);

                bindingSource = new BindingSource(comboSource, null);
                //combo.DisplayMember = "Value";
                //combo.ValueMember = "Key";
                //combo.SelectedIndex = 0;
                //combo.Text = "";
            }
            catch { }
            return bindingSource;
        }
        public BindingSource Get_BindingSource_Cards_Batch(int server =0)
        {
            BindingSource bindingSource = null;
            try
            {
                string Rb = Global_Variable.Mk_resources.RB_code;
                string Rb_sn = Global_Variable.Mk_resources.RB_SN;
                string Qury = "SELECT *  FROM BatchCard where Server=" + server + " and (Rb='" + Rb + "' or Rb='" + Rb_sn + "'  ) ORDER BY AddedDate DESC;";
                List<BatchCard> sp = Load<BatchCard>(Qury);


                var res = sp.Where(p => p.Rb != null).GroupBy(p => p.BatchNumber).Select(grp => grp.FirstOrDefault());

                Dictionary<string, string> comboSource = new Dictionary<string, string>();
                comboSource.Add("", "");
                foreach (BatchCard s in res)
                    comboSource.Add(s.Str_Name.ToString(), s.BatchNumber.ToString());
                    //comboSource.Add(s.Str_Name, s.Id.ToString());

                bindingSource = new BindingSource(comboSource, null);
            }
            catch { }
            return bindingSource;
        }
        public BindingSource Get_BindingSource_Number_Print(int server = 0,string BatchNumber="0")
        {
            BindingSource bindingSource = null;
            try
            {
                string Rb = Global_Variable.Mk_resources.RB_code;
                string Rb_Sn = Global_Variable.Mk_resources.RB_SN;
                string Qury = $"SELECT *  FROM NumberPrintCard where BatchNumber={BatchNumber} and Server={server}  and (Rb='{Rb}' or Rb='{Rb_Sn}' ) ORDER BY AddedDate DESC;";
                List<NumberPrintCard> sp = Load<NumberPrintCard>(Qury);


                var res = sp.Where(p => p.Rb != null).GroupBy(p => p.NumberPrint).Select(grp => grp.FirstOrDefault());

                Dictionary<string, string> comboSource = new Dictionary<string, string>();
                comboSource.Add("", "");
                foreach (NumberPrintCard s in res)
                    comboSource.Add(s.NumberPrint.ToString(), s.NumberPrint.ToString());
                //comboSource.Add(s.Str_Name, s.Id.ToString());

                bindingSource = new BindingSource(comboSource, null);
            }
            catch { }
            return bindingSource;
        }

        public List<SourceSessionUserManager_FromDB> Get_Nas_Port()
        {
            lock (Lock_object)
            {
                string Qury = "SELECT DISTINCT  NasPortId   FROM UmSession ORDER BY NasPortId ;";
                try
                {
                    using (var cnn = Sql_DataAccess.GetConnection())
                    {
                        return cnn.Query<SourceSessionUserManager_FromDB>(Qury, new DynamicParameters()).ToList();
                    }

                }
                catch (Exception ex) { MessageBox.Show("Get_Nas_Port\n"+ex.Message); return default; }
            }
        }

        public List<SourceSessionUserManager_FromDB> Get_Radius()
        {
            lock (Lock_object)
            {
                string Qury = "SELECT DISTINCT  IpRouter   FROM UmSession ORDER BY IpRouter ;";
                try
                {
                    using (var cnn = Sql_DataAccess.GetConnection())
                    {
                        return cnn.Query<SourceSessionUserManager_FromDB>(Qury, new DynamicParameters()).ToList();
                    }

                }
                catch (Exception ex) { MessageBox.Show("Get_Nas_Port\n" + ex.Message); return default; }
            }
        }

        //public List<BatchCard> Get_Batch_byBatchNumber(int id)
        //{
        //    lock (Lock_object)
        //    {
        //        using var db = dbFactory.Open();
        //        var table = db.Select<BatchCard>(a => a.BatchNumber == id);

        //        return table;
        //    }
        //}

        public List<BatchCard> Get_Batch_byBatchNumber_And_Server(int id, int server = 0)
        {
            lock (Lock_object)
            {

                string Qury = $"SELECT * FROM BatchCard where BatchNumber={id} and Server={server} and (Rb='{Global_Variable.Mk_resources.RB_SN}' or Rb='{Global_Variable.Mk_resources.RB_code}');";
                try
                {
                    using (var cnn = GetConnSmart())
                    {
                        return cnn.Query<BatchCard>(Qury, new DynamicParameters()).ToList();
                    }

                }
                catch (Exception ex) { MessageBox.Show("Get_Batch_byBatchNumber_And_Server\n" + ex.Message); return default; }

                //using var db = dbFactory.Open();
                //var table = db.Select<BatchCard>(a => a.BatchNumber == id && a.Server == server);

                //return table;
            }
        }
        public void Add_Batch_Cards(BatchCard data, int server = 0, bool add_to_Last = false,string Name = "BatchCards" )
        {
            if (Global_Variable.Mk_resources.RB_SN != null)
                data.Rb = Global_Variable.Mk_resources.RB_SN;

            //data.Rb = Global_Variable.Mk_resources.RB_SN;
            lock (Lock_object)
            {
                try
                {
                    if (add_to_Last == false)
                    {
                        string QUpdate = "update My_Sequence set  Seq=@Seq where Name=@Name and Rb=@Rb ; ";
                        using (var con = GetConnSmart())
                        {
                            int effectRows = con.Execute(QUpdate, new My_Sequence { Seq = data.BatchNumber, Name = Name, Rb = data.Rb });
                        }

                        string InsertQuery = "insert into BatchCard ( " +
                             "[BatchNumber]" +
                             ",[AddedDate]" +
                             ",[Count]" +
                             ",[Sn_from]" +
                             ",[Sn_to]" +
                             ",[ProfileName]" +
                             ",[SpCode]" +
                             ",[SpName]" +
                             ",[Server]" +
                             ",[Rb]" +
                             ",[BatchType]" +
                             "" +
                             " ) values(" +
                             "@BatchNumber" +
                             ",@AddedDate" +
                             ",@Count" +
                             ",@Sn_from" +
                             ",@Sn_to" +
                             ",@ProfileName" +
                             ",@SpCode" +
                             ",@SpName" +
                             ",@Server" +
                             ",@Rb" +
                             ",@BatchType" +
                             "); ";

                        using (var con = GetConnSmart())
                        {

                            var rwos = con.Execute(InsertQuery, data);
                        }
                    }
                }
                catch (Exception ex) {/* MessageBox.Show("error add batch Batch to db \n" + ex.Message);*/ }

                //try
                //{
                //    data.Rb = Global_Variable.Mk_resources.RB_code;
                //    using var db = dbFactory.Open();

                //    if (add_to_Last == false)
                //        db.UpdateOnly(() => new My_Sequence { Seq = data.BatchNumber }, where: p => p.Name == Name && p.Rb == data.Rb);

                //    var effect = db.Insert(data);
                //}
                //catch (Exception ex) { RJMessageBox.Show("error add batch number to db \n" + ex.Message); }
            }
        }

        public void Add_NumberPrint_Cards(NumberPrintCard data, int server = 0, bool add_to_Last = false, string Name = "NumberPrint")
        {
            if(Global_Variable.Mk_resources.RB_SN != null)
                data.Rb = Global_Variable.Mk_resources.RB_SN;

            lock (Lock_object)
            {
                try
                {
                    //if (add_to_Last == false)
                    //{
                        string QUpdate = "update My_Sequence set  Seq=@Seq where Name=@Name and Rb=@Rb ; ";
                        using (var con = GetConnSmart())
                        {
                            int effectRows = con.Execute(QUpdate, new My_Sequence { Seq = data.NumberPrint, Name = Name, Rb = data.Rb });
                        }
                    //}
                    string InsertQuery = "insert into NumberPrintCard ( " +
                         "[BatchNumber]" +
                         ",[NumberPrint]" +
                         ",[AddedDate]" +
                         ",[Count]" +
                         ",[Sn_from]" +
                         ",[Sn_to]" +
                         ",[ProfileName]" +
                         ",[SpCode]" +
                         ",[SpName]" +
                         ",[Server]" +
                         ",[Rb]" +
                         ",[BatchType]" +
                         "" +
                         " ) values(" +
                         "@BatchNumber" +
                         ",@NumberPrint" +
                         ",@AddedDate" +
                         ",@Count" +
                         ",@Sn_from" +
                         ",@Sn_to" +
                         ",@ProfileName" +
                         ",@SpCode" +
                         ",@SpName" +
                         ",@Server" +
                         ",@Rb" +
                         ",@BatchType" +
                         "); ";

                    using (var con = GetConnSmart())
                    {

                        var rwos = con.Execute(InsertQuery, data);
                    }
                    //var effect = db.Insert(data);
                }
                catch (Exception ex) { MessageBox.Show("error add NumberPrint number to db \n" + ex.Message); }

                //try
                //{
                //    data.Rb = Global_Variable.Mk_resources.RB_code;
                //    using var db = dbFactory.Open();

                //    if (add_to_Last == false)
                //        db.UpdateOnly(() => new My_Sequence { Seq = data.BatchNumber }, where: p => p.Name == Name && p.Rb == data.Rb);

                //    var effect = db.Insert(data);
                //}
                //catch (Exception ex) { RJMessageBox.Show("error add batch number to db \n" + ex.Message); }
            }
        }

        public List<NumberPrintCard> Get_NumberPrintCard_byNumberPrint_And_Server(int id, int server = 0)
        {
            lock (Lock_object)
            {

                string Qury = $"SELECT * FROM NumberPrintCard where NumberPrint={id} and Server={server} and (Rb='{Global_Variable.Mk_resources.RB_code}' or Rb='{Global_Variable.Mk_resources.RB_SN}') ;";
                try
                {
                    using (var cnn = GetConnSmart())
                    {
                        return cnn.Query<NumberPrintCard>(Qury, new DynamicParameters()).ToList();
                    }

                }
                catch (Exception ex) { MessageBox.Show("Get_NumberPrintCard_byNumberPrint_And_Server\n" + ex.Message); return default; }

                //using var db = dbFactory.Open();
                //var table = db.Select<BatchCard>(a => a.BatchNumber == id && a.Server == server);

                //return table;
            }
        }

        public long Get_BatchCards_My_Sequence(string name = "BatchCards")
        {
            lock (Lock_object)
            {

                long seq = 0;
                try
                {
                    //var lst = Load<My_Sequence>($"select * from My_Sequence where Name='{name}' and (Rb='{Global_Variable.Mk_resources.RB_code}' or Rb='{Global_Variable.Mk_resources.RB_SN}') ");
                    var lst = Load<My_Sequence>($"select * from My_Sequence where Name='{name}' and ( Rb='{Global_Variable.Mk_resources.RB_SN}') ");
                    //var lst = LoadSingle<My_Sequence>($"select * from My_Sequence where Name='{name}' and (Rb='{Global_Variable.Mk_resources.RB_code}' or Rb='{Global_Variable.Mk_resources.RB_SN}') ");
                    if (lst != null && lst.Count!=0)
                    {
                        return lst.Last().Seq;
                    }
                    else
                    {

                        string Query = "insert into My_Sequence ( [Name],[Seq],[Rb] ) values(@Name,@Seq,@Rb); ";

                        using (var conn = GetConnSmart())
                        {
                            var rwo_seq = conn.ExecuteScalar<My_Sequence>(Query, new My_Sequence { Name = name, Seq = 0, Rb = Global_Variable.Mk_resources.RB_SN });
                            //var update_last_sn = conn.ExecuteScalar<My_Sequence>($"update My_Sequence set  Rb=@Rb where Rb='{Global_Variable.Mk_resources.RB_code}' ;", new My_Sequence {  Rb = Global_Variable.Mk_resources.RB_SN });
                        }
                    }

                }
                catch(Exception ex) { MessageBox.Show($"Get_Sequence {name}\n"+ex.Message); }

                //using var db = dbFactory.Open();
                //var lst = db.Select<My_Sequence>().Where(a => a.Name == name && a.Rb == Global_Variable.Mk_resources.RB_code);
                //if (lst != null && lst.Count() != 0)
                //    return lst.First().Seq;
                //else
                //{
                //    db.Insert(new My_Sequence { Name = name, Seq = 0, Rb = Global_Variable.Mk_resources.RB_code });
                //}
                return seq;
            }
        }
        public long Get_MySequence(string name = "BatchCards")
        {
            //BatchCards  -  CardsArtchive
            lock (Lock_object)
            {
                string rb = Global_Variable.Mk_resources.RB_code;
                string rb_sn = Global_Variable.Mk_resources.RB_SN;
                long seq = 0;
                string Query = "select Seq from My_Sequence where name='" + name + "'  and  (Rb='" + rb + "' or Rb='" + rb_sn + "') ;";
                try
                {
                    using (var conn = GetConnSmart())
                    {
                        seq = conn.QuerySingleOrDefault<int>(Query, new DynamicParameters());
                    }
                }
                catch (Exception ex) { MessageBox.Show(ex.Message); return default; }
                if (seq == 0)
                {

                    Query = "insert into My_Sequence ( [Name],[Seq],[Rb] ) values(@Name,@Seq,@Rb); ";

                    using (var conn = GetConnSmart())
                    {
                        var rwo_seq = conn.QuerySingleOrDefault<My_Sequence>(Query, new My_Sequence { Name = name, Seq = 0, Rb = rb_sn });
                    }
                }
                return seq;
            }
        }

        public int Update_MySequence(string name ,long Seq) //BatchCards  -  CardsArtchive
        {
            int effectRows = 0;
            lock (Lock_object)
            {
                try
                {
                    string rb = Global_Variable.Mk_resources.RB_SN;
                    string Query = "update My_Sequence set  Seq=@Seq where Name=@Name and Rb=@Rb ; ";
                    using (var con = GetConnSmart())
                    {
                        effectRows = con.Execute(Query, new My_Sequence { Name = name, Seq = Seq, Rb = rb });
                    }
                }
                catch(Exception ex) { MessageBox.Show(ex.Message); }
                return effectRows;
            }
        }

        public bool Syn_UmProfile(List<UmProfile> profile, bool is_insert = true, bool check_byID = true)
        {
            lock (Lock_object)
            {
                string rb=Global_Variable.Mk_resources.RB_SN;
                string sql = ""; bool status = false;
                if (is_insert)
                {
                    sql = "INSERT OR IGNORE into  UmProfile (" +
                              "[IdHX]" +
                              ",[Sn]" +
                              ",[Sn_Name]" +
                              ",[Name]" +
                              ",[Validity]" +
                              ",[Price]" +
                              ",[Price_Disply]" +
                              ",[Is_percentage]" +
                              ",[Percentage]" +
                              ",[PercentageType]" +
                              ",[SharedUsers]" +
                              ",[NameForUser]" +
                              ",[DeleteFromServer]" +
                              ",[Rb]" +
                              ") " +
                              "values (" +
                              " @IdHX" +
                              ",@Sn" +
                              ",@Sn_Name" +
                              ",@Name" +
                              ",@Validity" +
                              ",@Price" +
                              ",@Price_Disply" +
                              ",@Is_percentage" +
                              ",@Percentage" +
                              ",@PercentageType" +
                              ",@SharedUsers" +
                              ",@NameForUser" +
                              ",@DeleteFromServer" +
                              ",@Rb" +
                              ")";
                }
                else
                {
                    string _update_check = " WHERE Id = @Id;";
                    if (!check_byID)
                        _update_check = "  WHERE Sn_Name = @Sn_Name and Rb='" + rb + "' ;";

                 sql =
                  "update UmProfile set "
                  + "[Validity]=@Validity "
                  + ",[Price]=@Price "
                  //+ ",[Price_Disply]=@Price_Disply "
                  //+ ",[Is_percentage]=@Is_percentage"
                  //+ ",[Percentage]=@Percentage"
                  //+ ",[PercentageType]=@PercentageType"
                  + ",[SharedUsers]=@SharedUsers"
                  + ",[NameForUser]=@NameForUser"
                  + ",[DeleteFromServer]=@DeleteFromServer"

                  + _update_check;


                }
                //using var db = dbFactory.Open();
                //create_default_db();
                //var effectRows = db.ExecuteSql(sql,profile);
                //db.SaveAll(profile);
                try
                {
                     using (var cnn = GetConnSmart())
                    {
                        cnn.Open();
                        var sqLiteTransaction = cnn.BeginTransaction();
                        var rowsEfect = cnn.Execute(sql, profile, sqLiteTransaction);
                        sqLiteTransaction.Commit();

                        status = true;
                    }
                }
                catch (Exception ex) { MessageBox.Show(ex.Message); status = false; }
                return status;
            }
        }
        public bool Syn_UmLimit(List<UmLimitation> profile, bool is_insert = true, bool check_byID = true)
        {
            lock (Lock_object)
            {
                string rb = Global_Variable.Mk_resources.RB_SN;
                string sql = "";
                bool status = false;
                if (is_insert)
                {
                    sql = "INSERT OR IGNORE into  UmLimitation (" +
                              "[IdHX]" +
                              ",[Sn]" +
                              ",[Name]" +
                              ",[Sn_Name]" +
                              ",[TransferLimit]" +
                              ",[UptimeLimit]" +
                              ",[Download_tx]" +
                              ",[Upload_rx]" +
                              ",[GroupName]" +
                              ",[Rb]" +
                              ",[DeleteFromServer]" +
                              ") " +
                              "values (" +
                              " @IdHX" +
                              ",@Sn" +
                              ",@Name" +
                              ",@Sn_Name" +
                              ",@TransferLimit" +
                              ",@UptimeLimit" +
                              ",@Download_tx" +
                              ",@Upload_rx" +
                              ",@GroupName" +
                              ",@Rb" +
                              ",@DeleteFromServer" +
                              ")";
                }
                else
                {
                    string _update_check = " WHERE Id = @Id;";
                    if (!check_byID)
                        _update_check = "  WHERE Sn_Name = @Sn_Name and Rb='" + rb + "' ;";

                    sql =
                     "update UmLimitation set "
                     + " [TransferLimit]=@TransferLimit "
                     + ",[UptimeLimit]=@UptimeLimit "
                     + ",[Download_tx]=@Download_tx "
                     + ",[Upload_rx]=@Upload_rx"
                     + ",[GroupName]=@GroupName"
                     + ",[DeleteFromServer]=@DeleteFromServer"

                     + _update_check;


                }
                //using var db = dbFactory.Open();
                //create_default_db();
                try
                {
                     using (var cnn = GetConnSmart())
                    {
                        cnn.Open();
                        var sqLiteTransaction = cnn.BeginTransaction();
                        var rowsEfect = cnn.Execute(sql, profile, sqLiteTransaction);
                        sqLiteTransaction.Commit();

                        status = true;
                    }
                }
                catch (Exception ex) { MessageBox.Show(ex.Message+ "\nSyn_UmLimit"); status = false; }
                return status;
            }
        }
        public bool Syn_UmProfile_Limtition(List<UmProfile_Limtition> profile, bool is_insert = true, bool check_byID = true)
        {
            lock (Lock_object)
            {
                string rb = Global_Variable.Mk_resources.RB_SN;
                string sql = ""; bool status = false;
                if (is_insert)
                {
                    sql = "INSERT OR IGNORE into  UmProfile_Limtition (" +
                              "[IdHX]" +
                              ",[Sn]" +
                              ",[Sn_Profile_Limitation]" +
                              ",[Profile]" +
                              ",[Limitation]" +
                              ",[From_time]" +
                              ",[Till_time]" +
                              ",[Weekdays]" +
                              ",[Rb]" +
                              ",[DeleteFromServer]" +
                              ") " +
                              "values (" +
                              " @IdHX" +
                              ",@Sn" +
                              ",@Sn_Profile_Limitation" +
                              ",@Profile" +
                              ",@Limitation" +
                              ",@From_time" +
                              ",@Till_time" +
                              ",@Weekdays" +
                              ",@Rb" +
                              ",@DeleteFromServer" +
                              ")";
                }
                else
                {
                    string _update_check = " WHERE Id = @Id;";
                    if (!check_byID)
                        _update_check = "  WHERE Sn_Profile_Limitation = @Sn_Profile_Limitation and Rb='" + rb + "' ;";

                    sql =
                     "update UmProfile_Limtition set "
                     + " [From_time]=@From_time "
                     + ",[Till_time]=@Till_time "
                     + ",[Weekdays]=@Weekdays "
                     + ",[DeleteFromServer]=@DeleteFromServer"

                     + _update_check;


                }
                //using var db = dbFactory.Open();
                //create_default_db();
                try
                {
                    using (var cnn = GetConnSmart())
                    {
                        cnn.Open();
                        var sqLiteTransaction = cnn.BeginTransaction();
                        var rowsEfect = cnn.Execute(sql, profile, sqLiteTransaction);
                        sqLiteTransaction.Commit();

                        status = true;
                    }
                }
                catch (Exception ex) { RJMessageBox.Show(ex.Message + "\n Syn_UmProfile_Limtition"); status = false; }
                return status;
            }
        }

        public bool RunSqlScript(string sql)
        {
            lock (Lock_object)
            {
                bool status = false;
                try
                {
                    using (var cnn = GetConnSmart())
                    {
                        cnn.Open();
                        var sqLiteTransaction = cnn.BeginTransaction();
                        var rowsEfect = cnn.ExecuteScalar(sql, sqLiteTransaction);
                        sqLiteTransaction.Commit();
                        if (rowsEfect != null)
                        {
                            RJMessageBox.Show(rowsEfect.ToString());
                            status = true;
                        }
                    }
                }
                catch (Exception ex) { RJMessageBox.Show(ex.Message); status = false; }
                return status;
            }
        }
        public bool create_default_db()
        {
            //lock (Lock_object)
            //{
                bool status = false;
            //    try
            //    {
            //        //IOrmLiteDialectProvider provider = Global_Variable.ProviderList[conn.Type];
            //        IOrmLiteDialectProvider provider = SqliteDialect.Provider;
            //        //======= Create File database ===================
            //        if (true)
            //        {
            //            if (!File.Exists(db_connection_string_path))
            //            {
            //                SQLiteConnection.CreateFile(db_connection_string_path);
            //            }
            //            OrmLiteConfig.OnModelDefinitionInit = modelDef =>
            //            {
            //                modelDef.FieldDefinitions
            //                    .Where(x => x.ColumnType == typeof(long) || x.ColumnType == typeof(double) || x.ColumnType == typeof(DateTime) || x.ColumnType == typeof(string) || x.Name == "PercentageType" || x.Name == "Server")
            //                    .Each(x =>
            //                    {
            //                        if (x.ColumnType == typeof(long) || x.ColumnType == typeof(double))
            //                        {
            //                            x.CustomFieldDefinition = "INTEGER";
            //                        }
            //                        if (x.ColumnType == typeof(DateTime) || x.ColumnType == typeof(string))
            //                        {
            //                            x.CustomFieldDefinition = "TEXT";
            //                        }
            //                        //if (x.Name == "PercentageType")
            //                        //{
            //                        //    x.DefaultValue = "'ByPercentage'";
            //                        //}
            //                        //if (x.Name == "Server")
            //                        //{
            //                        //    x.DefaultValue = "'UserManager'";
            //                        //}

            //                    });
            //            };
            //        }
            //        provider.GetStringConverter().UseUnicode = true;
            //        provider.GetStringConverter().StringLength = 255;

            //        var dbFactory = new OrmLiteConnectionFactory(connection_string, provider);
            //        using (var db = dbFactory.Open())
            //        {
            //            db.CreateTableIfNotExists<UmProfile>();
            //            db.CreateTableIfNotExists<UmLimitation>();
            //            db.CreateTableIfNotExists<SellingPoint>();
            //            db.CreateTableIfNotExists<UmProfile_Limtition>();
            //            db.CreateTableIfNotExists<My_Sequence>();
            //            db.CreateTableIfNotExists<BatchCard>();


            //            status = true;
            //        }
            //    }
            //    catch (Exception ex) { RJMessageBox.Show(ex.Message); }
                return status;
            //}
        }

        public bool Change_Selling_Point(HashSet<UmUser> Users,string SpCode,string SpName,bool is_remove=false,bool is_changePrice=false)
        {
            lock (Lock_object)
            {
                string rb = Global_Variable.Mk_resources.RB_code;
                string sql = ""; bool status = false;

                {
                    string _update_check = " WHERE Sn_Name = @Sn_Name;";
                    if (is_remove == false)
                    {
                        if (is_changePrice == false)
                        {
                            sql =
                             "update UmUser set "
                             + " [SpCode]='" + SpCode + "' "
                             + ",[SpName]='" + SpName + "' "

                             + _update_check;
                        }
                        else
                        {
                            sql =
                            "update UmUser set "
                            + " [SpCode]='" + SpCode + "' "
                            + ",[SpName]='" + SpName + "' "
                            + ",[TotalPrice]=@TotalPrice"
                            + ",[PercentageType]=@PercentageType"
                            + ",[Percentage]=@Percentage"

                            + _update_check;
                        }

                    }
                    else
                        sql =
                        "update UmUser set "
                     + " [SpCode]=@SpCode "
                     + ",[SpName]=@SpName"

                     + _update_check;
                }
                try
                {
                    using (IDbConnection cnn = new SQLiteConnection(Sql_DataAccess.connection_string))
                    {
                        cnn.Open();
                        var sqLiteTransaction = cnn.BeginTransaction();
                        var rowsEfect = cnn.Execute(sql, Users, sqLiteTransaction);
                        sqLiteTransaction.Commit();

                        status = true;
                    }
                }
                catch (Exception ex) { RJMessageBox.Show(ex.Message + "\n Change_Selling_Point"); status = false; }
                return status;
            }
        }
        public bool Change_Selling_Point<T>(string tableName,HashSet<T> Users, string SpCode, string SpName, bool is_remove = false, bool is_changePrice = false)
        {
            //string tableName = typeof(T).Name;
            lock (Lock_object)
            {
                string rb = Global_Variable.Mk_resources.RB_SN;
                string sql = ""; bool status = false;

                {
                    string _update_check = " WHERE Sn_Name = @Sn_Name;";
                    if (is_remove == false)
                    {
                        if (is_changePrice == false)
                        {
                            sql =
                             $"update {tableName} set "
                             + " [SpCode]='" + SpCode + "' "
                             + ",[SpName]='" + SpName + "' "

                             + _update_check;
                        }
                        else
                        {
                            sql =
                            $"update {tableName} set "
                            + " [SpCode]='" + SpCode + "' "
                            + ",[SpName]='" + SpName + "' "
                            + ",[TotalPrice]=@TotalPrice"
                            + ",[PercentageType]=@PercentageType"
                            + ",[Percentage]=@Percentage"

                            + _update_check;
                        }

                    }
                    else
                        sql =
                        $"update {tableName} set "
                     + " [SpCode]=@SpCode "
                     + ",[SpName]=@SpName"

                     + _update_check;
                }
                try
                {
                    using (IDbConnection cnn = new SQLiteConnection(Sql_DataAccess.connection_string))
                    {
                        cnn.Open();
                        var sqLiteTransaction = cnn.BeginTransaction();
                        var rowsEfect = cnn.Execute(sql, Users, sqLiteTransaction);
                        sqLiteTransaction.Commit();

                        status = true;
                    }
                }
                catch (Exception ex) { RJMessageBox.Show(ex.Message + "\n Change_Selling_Point"); status = false; }
                return status;
            }
        }

        #region Async Methods for UserManagementService

        /// <summary>
        /// تنفيذ استعلام وإرجاع قائمة من البيانات بشكل غير متزامن
        /// </summary>
        public async Task<List<T>> GetDataAsync<T>(string query, object parameters = null)
        {
            return await Task.Run(() =>
            {
                lock (Lock_object)
                {
                    try
                    {
                        using (var cnn = GetConnSmart())
                        {
                            return cnn.Query<T>(query, parameters).ToList();
                        }
                    }
                    catch (Exception ex)
                    {
                        RJMessageBox.Show($"خطأ في GetDataAsync: {ex.Message}");
                        return new List<T>();
                    }
                }
            });
        }

        /// <summary>
        /// تنفيذ استعلام تحديث/إدراج/حذف بشكل غير متزامن
        /// </summary>
        public async Task<int> ExecuteAsync(string query, object parameters = null)
        {
            return await Task.Run(() =>
            {
                lock (Lock_object)
                {
                    int affectedRows = 0;
                    using (var con = GetConnSmart())
                    {
                        try
                        {
                            con.Open();
                            var sqLiteTransaction = con.BeginTransaction();
                            affectedRows = con.Execute(query, parameters, sqLiteTransaction);
                            sqLiteTransaction.Commit();
                            Console.WriteLine($" Counts  Rows: {affectedRows}");
                            //MessageBox.Show($" Counts  Rows: {affectedRows}");
                        }
                        catch (Exception ex)
                        {
                            RJMessageBox.Show($"خطأ في ExecuteAsync: {ex.Message}");
                        }
                    }
                    return affectedRows;
                }
            });
        }

        /// <summary>
        /// تنفيذ استعلام وإرجاع قيمة واحدة بشكل غير متزامن
        /// </summary>
        public async Task<T> ExecuteScalarAsync<T>(string query, object parameters = null)
        {
            return await Task.Run(() =>
            {
                lock (Lock_object)
                {
                    try
                    {
                        using (var cnn = GetConnSmart())
                        {
                            return cnn.ExecuteScalar<T>(query, parameters);
                        }
                    }
                    catch (Exception ex)
                    {
                        RJMessageBox.Show($"خطأ في ExecuteScalarAsync: {ex.Message}");
                        return default(T);
                    }
                }
            });
        }

        /// <summary>
        /// الحصول على سجل واحد بشكل غير متزامن
        /// </summary>
        public async Task<T> QuerySingleOrDefaultAsync<T>(string query, object parameters = null)
        {
            return await Task.Run(() =>
            {
                lock (Lock_object)
                {
                    try
                    {
                        using (var cnn = GetConnSmart())
                        {
                            return cnn.QuerySingleOrDefault<T>(query, parameters);
                        }
                    }
                    catch (Exception ex)
                    {
                        RJMessageBox.Show($"خطأ في QuerySingleOrDefaultAsync: {ex.Message}");
                        return default(T);
                    }
                }
            });
        }

        #endregion

    }
}
