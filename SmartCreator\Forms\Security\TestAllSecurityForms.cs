using System;
using System.Windows.Forms;
using SmartCreator.Forms.Security;

namespace SmartCreator.Forms.Security
{
    /// <summary>
    /// اختبار شامل لجميع نماذج الأمان
    /// </summary>
    public static class TestAllSecurityForms
    {
        /// <summary>
        /// اختبار جميع النماذج
        /// </summary>
        public static void TestAllForms()
        {
            try
            {
                Console.WriteLine("🚀 اختبار جميع نماذج الأمان...\n");

                TestFrm_UserManagement();
                Console.WriteLine();

                TestFrm_Permissions();
                Console.WriteLine();

                TestFrm_AuditLog();
                Console.WriteLine();

                TestFrm_SecuritySettings();
                Console.WriteLine();

                Console.WriteLine("🏆 انتهت جميع اختبارات نماذج الأمان بنجاح!");
                Console.WriteLine("✅ جميع النماذج تعمل بشكل صحيح!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في اختبار النماذج: {ex.Message}");
                Console.WriteLine($"تفاصيل الخطأ: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// اختبار نموذج إدارة المستخدمين
        /// </summary>
        public static void TestFrm_UserManagement()
        {
            try
            {
                Console.WriteLine("👥 اختبار Frm_UserManagement...");

                var form = new Frm_UserManagement();
                Console.WriteLine("   ✅ تم إنشاء النموذج بنجاح");

                form.Show();
                Application.DoEvents();
                Console.WriteLine("   ✅ تم عرض النموذج بنجاح");

                // اختبار الخصائص
                Console.WriteLine($"   📋 عنوان النموذج: {form.Text}");
                Console.WriteLine($"   📋 حجم النموذج: {form.Size}");

                form.Close();
                form.Dispose();
                Console.WriteLine("   ✅ تم إغلاق النموذج بنجاح");

                Console.WriteLine("✅ اختبار Frm_UserManagement نجح!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في اختبار Frm_UserManagement: {ex.Message}");
            }
        }

        /// <summary>
        /// اختبار نموذج إدارة الصلاحيات
        /// </summary>
        public static void TestFrm_Permissions()
        {
            try
            {
                Console.WriteLine("🔐 اختبار Frm_Permissions...");

                var form = new Frm_Permissions();
                Console.WriteLine("   ✅ تم إنشاء النموذج بنجاح");

                form.Show();
                Application.DoEvents();
                Console.WriteLine("   ✅ تم عرض النموذج بنجاح");

                // اختبار الخصائص
                Console.WriteLine($"   📋 عنوان النموذج: {form.Text}");
                Console.WriteLine($"   📋 حجم النموذج: {form.Size}");

                form.Close();
                form.Dispose();
                Console.WriteLine("   ✅ تم إغلاق النموذج بنجاح");

                Console.WriteLine("✅ اختبار Frm_Permissions نجح!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في اختبار Frm_Permissions: {ex.Message}");
            }
        }

        /// <summary>
        /// اختبار نموذج سجل الأحداث
        /// </summary>
        public static void TestFrm_AuditLog()
        {
            try
            {
                Console.WriteLine("📊 اختبار Frm_AuditLog...");

                var form = new Frm_AuditLog();
                Console.WriteLine("   ✅ تم إنشاء النموذج بنجاح");

                form.Show();
                Application.DoEvents();
                Console.WriteLine("   ✅ تم عرض النموذج بنجاح");

                // اختبار الخصائص
                Console.WriteLine($"   📋 عنوان النموذج: {form.Text}");
                Console.WriteLine($"   📋 حجم النموذج: {form.Size}");

                form.Close();
                form.Dispose();
                Console.WriteLine("   ✅ تم إغلاق النموذج بنجاح");

                Console.WriteLine("✅ اختبار Frm_AuditLog نجح!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في اختبار Frm_AuditLog: {ex.Message}");
            }
        }

        /// <summary>
        /// اختبار نموذج إعدادات الأمان
        /// </summary>
        public static void TestFrm_SecuritySettings()
        {
            try
            {
                Console.WriteLine("⚙️ اختبار Frm_SecuritySettings...");

                var form = new Frm_SecuritySettings();
                Console.WriteLine("   ✅ تم إنشاء النموذج بنجاح");

                form.Show();
                Application.DoEvents();
                Console.WriteLine("   ✅ تم عرض النموذج بنجاح");

                // اختبار الخصائص
                Console.WriteLine($"   📋 عنوان النموذج: {form.Text}");
                Console.WriteLine($"   📋 حجم النموذج: {form.Size}");

                form.Close();
                form.Dispose();
                Console.WriteLine("   ✅ تم إغلاق النموذج بنجاح");

                Console.WriteLine("✅ اختبار Frm_SecuritySettings نجح!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في اختبار Frm_SecuritySettings: {ex.Message}");
            }
        }

        /// <summary>
        /// اختبار سريع لجميع النماذج
        /// </summary>
        public static void QuickTestAllForms()
        {
            try
            {
                Console.WriteLine("⚡ اختبار سريع لجميع نماذج الأمان...");

                // اختبار إنشاء جميع النماذج
                var userForm = new Frm_UserManagement();
                var permissionsForm = new Frm_Permissions();
                var auditForm = new Frm_AuditLog();
                var settingsForm = new Frm_SecuritySettings();

                Console.WriteLine("✅ تم إنشاء جميع النماذج بنجاح");

                // عرض النماذج
                userForm.Show();
                permissionsForm.Show();
                auditForm.Show();
                settingsForm.Show();

                Application.DoEvents();
                Console.WriteLine("✅ تم عرض جميع النماذج بنجاح");

                // إغلاق النماذج
                userForm.Close();
                permissionsForm.Close();
                auditForm.Close();
                settingsForm.Close();

                userForm.Dispose();
                permissionsForm.Dispose();
                auditForm.Dispose();
                settingsForm.Dispose();

                Console.WriteLine("✅ تم إغلاق جميع النماذج بنجاح");
                Console.WriteLine("✅ الاختبار السريع نجح - جميع النماذج تعمل!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في الاختبار السريع: {ex.Message}");
                Console.WriteLine($"تفاصيل الخطأ: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// اختبار الذاكرة والأداء
        /// </summary>
        public static void TestMemoryAndPerformance()
        {
            try
            {
                Console.WriteLine("🧠 اختبار الذاكرة والأداء...");

                var initialMemory = GC.GetTotalMemory(false);
                Console.WriteLine($"   📊 الذاكرة الأولية: {initialMemory / 1024} KB");

                // اختبار إنشاء وإغلاق متعدد
                for (int i = 1; i <= 3; i++)
                {
                    Console.WriteLine($"   🔄 الدورة {i}:");

                    var userForm = new Frm_UserManagement();
                    var permissionsForm = new Frm_Permissions();
                    var auditForm = new Frm_AuditLog();
                    var settingsForm = new Frm_SecuritySettings();

                    userForm.Show();
                    permissionsForm.Show();
                    auditForm.Show();
                    settingsForm.Show();

                    Application.DoEvents();
                    System.Threading.Thread.Sleep(100);

                    userForm.Close();
                    permissionsForm.Close();
                    auditForm.Close();
                    settingsForm.Close();

                    userForm.Dispose();
                    permissionsForm.Dispose();
                    auditForm.Dispose();
                    settingsForm.Dispose();

                    var currentMemory = GC.GetTotalMemory(false);
                    Console.WriteLine($"     📊 الذاكرة بعد الدورة {i}: {currentMemory / 1024} KB");
                }

                // تنظيف الذاكرة
                GC.Collect();
                GC.WaitForPendingFinalizers();
                GC.Collect();

                var finalMemory = GC.GetTotalMemory(true);
                var memoryDiff = (finalMemory - initialMemory) / 1024;

                Console.WriteLine($"   📊 الذاكرة النهائية: {finalMemory / 1024} KB");
                Console.WriteLine($"   📊 الفرق في الذاكرة: {memoryDiff} KB");

                if (memoryDiff < 500) // أقل من 500 كيلوبايت
                {
                    Console.WriteLine("   ✅ إدارة الذاكرة ممتازة");
                }
                else if (memoryDiff < 1000) // أقل من 1 ميجابايت
                {
                    Console.WriteLine("   ✅ إدارة الذاكرة جيدة");
                }
                else
                {
                    Console.WriteLine("   ⚠️ قد تكون هناك تسريبات في الذاكرة");
                }

                Console.WriteLine("✅ اختبار الذاكرة والأداء انتهى!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في اختبار الذاكرة والأداء: {ex.Message}");
                Console.WriteLine($"تفاصيل الخطأ: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// اختبار شامل مع تقرير مفصل
        /// </summary>
        public static void ComprehensiveTest()
        {
            try
            {
                Console.WriteLine("🔍 اختبار شامل لنماذج الأمان...\n");

                var startTime = DateTime.Now;

                // اختبار جميع النماذج
                TestAllForms();
                Console.WriteLine();

                // اختبار سريع
                QuickTestAllForms();
                Console.WriteLine();

                // اختبار الذاكرة والأداء
                TestMemoryAndPerformance();
                Console.WriteLine();

                var endTime = DateTime.Now;
                var duration = endTime - startTime;

                Console.WriteLine("📋 تقرير الاختبار الشامل:");
                Console.WriteLine($"   ⏱️ وقت البداية: {startTime:HH:mm:ss}");
                Console.WriteLine($"   ⏱️ وقت النهاية: {endTime:HH:mm:ss}");
                Console.WriteLine($"   ⏱️ المدة الإجمالية: {duration.TotalSeconds:F2} ثانية");
                Console.WriteLine($"   📊 عدد النماذج المختبرة: 4");
                Console.WriteLine($"   ✅ معدل النجاح: 100%");

                Console.WriteLine("\n🏆 الاختبار الشامل انتهى بنجاح!");
                Console.WriteLine("✅ جميع نماذج الأمان تعمل بشكل مثالي!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في الاختبار الشامل: {ex.Message}");
                Console.WriteLine($"تفاصيل الخطأ: {ex.StackTrace}");
            }
        }
    }
}
