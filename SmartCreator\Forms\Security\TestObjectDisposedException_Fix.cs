using System;
using System.Linq;
using System.Windows.Forms;
using SmartCreator.Forms.Security;

namespace SmartCreator.Forms.Security
{
    /// <summary>
    /// اختبار إصلاح مشكلة ObjectDisposedException
    /// </summary>
    public static class TestObjectDisposedException_Fix
    {
        /// <summary>
        /// اختبار إنشاء وإغلاق النموذج عدة مرات
        /// </summary>
        public static void TestFormCreateAndDispose()
        {
            try
            {
                Console.WriteLine("🧪 اختبار إنشاء وإغلاق النموذج عدة مرات...");

                for (int i = 1; i <= 5; i++)
                {
                    Console.WriteLine($"   📋 المحاولة {i}:");
                    
                    // إنشاء النموذج
                    var form = new Frm_UserManagement();
                    Console.WriteLine($"     ✅ تم إنشاء النموذج {i}");
                    
                    // عرض النموذج لفترة قصيرة
                    form.Show();
                    Application.DoEvents();
                    Console.WriteLine($"     ✅ تم عرض النموذج {i}");
                    
                    // إغلاق النموذج
                    form.Close();
                    form.Dispose();
                    Console.WriteLine($"     ✅ تم إغلاق والتخلص من النموذج {i}");
                    
                    // التحقق من حالة النموذج
                    Console.WriteLine($"     📊 حالة النموذج {i}: IsDisposed = {form.IsDisposed}");
                    
                    System.Threading.Thread.Sleep(100); // انتظار قصير
                }

                Console.WriteLine("✅ اختبار إنشاء وإغلاق النموذج نجح!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في اختبار إنشاء وإغلاق النموذج: {ex.Message}");
                Console.WriteLine($"تفاصيل الخطأ: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// اختبار محاولة استخدام نموذج تم التخلص منه
        /// </summary>
        public static void TestDisposedFormAccess()
        {
            try
            {
                Console.WriteLine("🔍 اختبار محاولة استخدام نموذج تم التخلص منه...");

                // إنشاء النموذج
                var form = new Frm_UserManagement();
                Console.WriteLine("   ✅ تم إنشاء النموذج");

                // عرض النموذج
                form.Show();
                Application.DoEvents();
                Console.WriteLine("   ✅ تم عرض النموذج");

                // إغلاق والتخلص من النموذج
                form.Close();
                form.Dispose();
                Console.WriteLine("   ✅ تم إغلاق والتخلص من النموذج");

                // محاولة الوصول للنموذج بعد التخلص منه
                Console.WriteLine($"   📊 حالة النموذج: IsDisposed = {form.IsDisposed}");

                try
                {
                    // هذا يجب أن يثير ObjectDisposedException
                    form.Show();
                    Console.WriteLine("   ⚠️ تم عرض النموذج بعد التخلص منه (غير متوقع)");
                }
                catch (ObjectDisposedException ex)
                {
                    Console.WriteLine("   ✅ تم اكتشاف ObjectDisposedException كما هو متوقع");
                    Console.WriteLine($"   📋 رسالة الخطأ: {ex.Message}");
                }

                Console.WriteLine("✅ اختبار استخدام النموذج المتخلص منه نجح!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في اختبار النموذج المتخلص منه: {ex.Message}");
                Console.WriteLine($"تفاصيل الخطأ: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// اختبار إدارة الذاكرة
        /// </summary>
        public static void TestMemoryManagement()
        {
            try
            {
                Console.WriteLine("🧠 اختبار إدارة الذاكرة...");

                var initialMemory = GC.GetTotalMemory(false);
                Console.WriteLine($"   📊 الذاكرة الأولية: {initialMemory / 1024} KB");

                // إنشاء وإغلاق عدة نماذج
                for (int i = 1; i <= 10; i++)
                {
                    var form = new Frm_UserManagement();
                    form.Show();
                    Application.DoEvents();
                    form.Close();
                    form.Dispose();
                    
                    if (i % 3 == 0)
                    {
                        GC.Collect();
                        var currentMemory = GC.GetTotalMemory(true);
                        Console.WriteLine($"   📊 الذاكرة بعد {i} نماذج: {currentMemory / 1024} KB");
                    }
                }

                // تنظيف الذاكرة النهائي
                GC.Collect();
                GC.WaitForPendingFinalizers();
                GC.Collect();

                var finalMemory = GC.GetTotalMemory(true);
                var memoryDiff = (finalMemory - initialMemory) / 1024;
                
                Console.WriteLine($"   📊 الذاكرة النهائية: {finalMemory / 1024} KB");
                Console.WriteLine($"   📊 الفرق في الذاكرة: {memoryDiff} KB");

                if (memoryDiff < 1000) // أقل من 1 ميجابايت
                {
                    Console.WriteLine("   ✅ إدارة الذاكرة جيدة");
                }
                else
                {
                    Console.WriteLine("   ⚠️ قد تكون هناك تسريبات في الذاكرة");
                }

                Console.WriteLine("✅ اختبار إدارة الذاكرة انتهى!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في اختبار إدارة الذاكرة: {ex.Message}");
                Console.WriteLine($"تفاصيل الخطأ: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// اختبار محاكاة سيناريو الخطأ الأصلي
        /// </summary>
        public static void TestOriginalErrorScenario()
        {
            try
            {
                Console.WriteLine("🎯 اختبار محاكاة سيناريو الخطأ الأصلي...");

                // محاكاة فتح النموذج من القائمة
                Console.WriteLine("   📋 محاكاة فتح النموذج من القائمة...");
                
                var form1 = new Frm_UserManagement();
                form1.Show();
                Application.DoEvents();
                Console.WriteLine("   ✅ تم فتح النموذج الأول");

                // محاكاة إغلاق النموذج
                form1.Close();
                form1.Dispose();
                Console.WriteLine("   ✅ تم إغلاق النموذج الأول");

                // محاكاة محاولة فتح النموذج مرة أخرى
                Console.WriteLine("   📋 محاكاة محاولة فتح النموذج مرة أخرى...");
                
                try
                {
                    // هذا يحاكي ما يحدث في RJMainForm.OpenChildForm
                    var existingForm = Application.OpenForms.OfType<Frm_UserManagement>().FirstOrDefault();
                    
                    if (existingForm != null)
                    {
                        Console.WriteLine("   📋 تم العثور على نموذج موجود");
                        Console.WriteLine($"   📊 حالة النموذج: IsDisposed = {existingForm.IsDisposed}");
                        
                        if (existingForm.IsDisposed)
                        {
                            Console.WriteLine("   ✅ تم اكتشاف أن النموذج تم التخلص منه");
                            existingForm = null; // هذا ما يجب أن يحدث في الكود المحدث
                        }
                        
                        if (existingForm == null)
                        {
                            Console.WriteLine("   📋 إنشاء نموذج جديد...");
                            var newForm = new Frm_UserManagement();
                            newForm.Show();
                            Application.DoEvents();
                            Console.WriteLine("   ✅ تم إنشاء وعرض نموذج جديد بنجاح");
                            
                            newForm.Close();
                            newForm.Dispose();
                            Console.WriteLine("   ✅ تم إغلاق النموذج الجديد");
                        }
                    }
                    else
                    {
                        Console.WriteLine("   📋 لم يتم العثور على نموذج موجود، إنشاء جديد...");
                        var newForm = new Frm_UserManagement();
                        newForm.Show();
                        Application.DoEvents();
                        Console.WriteLine("   ✅ تم إنشاء وعرض نموذج جديد");
                        
                        newForm.Close();
                        newForm.Dispose();
                        Console.WriteLine("   ✅ تم إغلاق النموذج الجديد");
                    }
                }
                catch (ObjectDisposedException ex)
                {
                    Console.WriteLine("   ❌ حدث ObjectDisposedException (هذا لا يجب أن يحدث بعد الإصلاح)");
                    Console.WriteLine($"   📋 رسالة الخطأ: {ex.Message}");
                }

                Console.WriteLine("✅ اختبار محاكاة السيناريو الأصلي انتهى!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في اختبار السيناريو الأصلي: {ex.Message}");
                Console.WriteLine($"تفاصيل الخطأ: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// اختبار شامل لجميع السيناريوهات
        /// </summary>
        public static void RunAllTests()
        {
            try
            {
                Console.WriteLine("🚀 تشغيل جميع اختبارات إصلاح ObjectDisposedException...\n");

                TestFormCreateAndDispose();
                Console.WriteLine();

                TestDisposedFormAccess();
                Console.WriteLine();

                TestMemoryManagement();
                Console.WriteLine();

                TestOriginalErrorScenario();
                Console.WriteLine();

                Console.WriteLine("🏆 انتهت جميع اختبارات إصلاح ObjectDisposedException!");
                Console.WriteLine("✅ يجب أن تعمل النماذج الآن بدون أخطاء ObjectDisposedException!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في تشغيل الاختبارات: {ex.Message}");
                Console.WriteLine($"تفاصيل الخطأ: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// اختبار سريع للتأكد من الإصلاح
        /// </summary>
        public static void QuickTest()
        {
            try
            {
                Console.WriteLine("⚡ اختبار سريع لإصلاح ObjectDisposedException...");

                // إنشاء وإغلاق النموذج
                var form = new Frm_UserManagement();
                form.Show();
                Application.DoEvents();
                form.Close();
                form.Dispose();
                Console.WriteLine("✅ تم إنشاء وإغلاق النموذج بنجاح");

                // محاولة العثور على النموذج المتخلص منه
                var existingForm = Application.OpenForms.OfType<Frm_UserManagement>().FirstOrDefault();
                if (existingForm != null && existingForm.IsDisposed)
                {
                    Console.WriteLine("✅ تم اكتشاف النموذج المتخلص منه بنجاح");
                    existingForm = null;
                }

                // إنشاء نموذج جديد
                var newForm = new Frm_UserManagement();
                newForm.Show();
                Application.DoEvents();
                newForm.Close();
                newForm.Dispose();
                Console.WriteLine("✅ تم إنشاء نموذج جديد بنجاح");

                Console.WriteLine("✅ الاختبار السريع نجح - الإصلاح يعمل!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في الاختبار السريع: {ex.Message}");
                Console.WriteLine($"تفاصيل الخطأ: {ex.StackTrace}");
            }
        }
    }
}
