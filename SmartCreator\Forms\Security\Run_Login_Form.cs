using System;
using System.Windows.Forms;
using SmartCreator.Forms.Security;
using SmartCreator.Settings;
using SmartCreator.RJControls;
using SmartCreator.Models;

namespace SmartCreator.Forms.Security
{
    /// <summary>
    /// ملف تشغيل سريع لنموذج تسجيل الدخول
    /// </summary>
    public static class Run_Login_Form
    {
        /// <summary>
        /// تشغيل نموذج تسجيل الدخول مباشرة
        /// </summary>
        [STAThread]
        public static void Main()
        {
            try
            {
                // تهيئة التطبيق
                Application.EnableVisualStyles();
                Application.SetCompatibleTextRenderingDefault(false);

                // تطبيق الإعدادات الافتراضية
                SetupDefaultSettings();

                // عرض رسالة ترحيب
                ShowWelcomeMessage();

                // تشغيل نموذج تسجيل الدخول
                RunLoginForm();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تشغيل التطبيق: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// إعداد الإعدادات الافتراضية
        /// </summary>
        private static void SetupDefaultSettings()
        {
            // تطبيق السمة الافتراضية
            UIAppearance.Theme = UITheme.Light;
            UIAppearance.Language_ar = true;
            UIAppearance.TextSize = 10F;

            // تهيئة المتغيرات العامة
            Global_Variable.CurrentUser = null;
        }

        /// <summary>
        /// عرض رسالة ترحيب
        /// </summary>
        private static void ShowWelcomeMessage()
        {
            var result = RJMessageBox.Show(
                "🔐 مرحباً بك في نموذج تسجيل الدخول المتقدم!\n\n" +
                "✅ تم إنشاء النموذج باستخدام Custom Controls:\n" +
                "• RJBaseForm - النموذج الأساسي المتقدم\n" +
                "• RJPanel - لوحات مخصصة مع حواف مدورة\n" +
                "• RJTextBox - حقول نص متقدمة\n" +
                "• RJButton - أزرار مخصصة مع أيقونات\n" +
                "• RJLabel - تسميات مخصصة\n" +
                "• RJCheckBox - مربع اختيار مخصص\n\n" +
                "✅ الميزات المتاحة:\n" +
                "• تسجيل دخول آمن مع تشفير\n" +
                "• واجهة عربية احترافية\n" +
                "• دعم السمات الفاتحة والمظلمة\n" +
                "• تأثيرات بصرية متقدمة\n" +
                "• حماية من محاولات الاختراق\n" +
                "• تسجيل الأنشطة التلقائي\n\n" +
                "🚀 هل تريد فتح نموذج تسجيل الدخول؟",
                "نموذج تسجيل الدخول المتقدم",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Question);

            if (result == DialogResult.No)
            {
                Environment.Exit(0);
            }
        }

        /// <summary>
        /// تشغيل نموذج تسجيل الدخول
        /// </summary>
        private static void RunLoginForm()
        {
            try
            {
                // إنشاء وعرض نموذج تسجيل الدخول
                using (var loginForm = new Frm_Login())
                {
                    var result = loginForm.ShowDialog();

                    if (result == DialogResult.OK)
                    {
                        // تم تسجيل الدخول بنجاح
                        ShowSuccessMessage();
                    }
                    else
                    {
                        // تم إلغاء تسجيل الدخول
                        ShowCancelMessage();
                    }
                }
            }
            catch (Exception ex)
            {
                RJMessageBox.Show(
                    $"خطأ في تشغيل نموذج تسجيل الدخول:\n\n{ex.Message}",
                    "خطأ",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// عرض رسالة النجاح
        /// </summary>
        private static void ShowSuccessMessage()
        {
            var user = Global_Variable.CurrentUser;
            
            RJMessageBox.Show(
                $"🎉 تم تسجيل الدخول بنجاح!\n\n" +
                $"مرحباً {user?.DisplayName ?? "المستخدم"}\n\n" +
                "✅ تم تسجيل الدخول بأمان\n" +
                "✅ تم حفظ بيانات الجلسة\n" +
                "✅ تم تسجيل النشاط\n\n" +
                "يمكنك الآن استخدام النظام بكامل الصلاحيات.",
                "نجح تسجيل الدخول",
                MessageBoxButtons.OK,
                MessageBoxIcon.Information);
        }

        /// <summary>
        /// عرض رسالة الإلغاء
        /// </summary>
        private static void ShowCancelMessage()
        {
            RJMessageBox.Show(
                "تم إلغاء تسجيل الدخول.\n\n" +
                "شكراً لاستخدام Smart Creator!",
                "تم الإلغاء",
                MessageBoxButtons.OK,
                MessageBoxIcon.Information);
        }

        /// <summary>
        /// تشغيل اختبار شامل للنموذج
        /// </summary>
        public static async void RunComprehensiveTest()
        {
            try
            {
                var result = RJMessageBox.Show(
                    "🧪 هل تريد تشغيل اختبار شامل لنموذج تسجيل الدخول؟\n\n" +
                    "هذا الاختبار سيفحص:\n" +
                    "✅ إنشاء النموذج بدون أخطاء\n" +
                    "✅ عمل جميع Custom Controls\n" +
                    "✅ الوظائف والتفاعل\n" +
                    "✅ السمات المختلفة\n" +
                    "✅ الاستقرار والأداء\n\n" +
                    "⏱️ قد يستغرق الاختبار بضع ثوانٍ...",
                    "اختبار شامل",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    await Frm_Login_Test.RunLoginFormTest();
                }
            }
            catch (Exception ex)
            {
                RJMessageBox.Show(
                    $"خطأ في تشغيل الاختبار:\n\n{ex.Message}",
                    "خطأ",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تشغيل اختبار سريع
        /// </summary>
        public static void RunQuickTest()
        {
            try
            {
                Frm_Login_Test.QuickLoginFormTest();
            }
            catch (Exception ex)
            {
                RJMessageBox.Show(
                    $"خطأ في الاختبار السريع:\n\n{ex.Message}",
                    "خطأ",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// عرض معلومات النموذج
        /// </summary>
        public static void ShowFormInfo()
        {
            RJMessageBox.Show(
                "📋 معلومات نموذج تسجيل الدخول\n\n" +
                "📁 الملفات المُنشأة:\n" +
                "• Frm_Login.cs - الكود الرئيسي\n" +
                "• Frm_Login.Designer.cs - ملف التصميم\n" +
                "• Frm_Login.resx - ملف الموارد\n" +
                "• Frm_Login_Test.cs - ملف الاختبارات\n" +
                "• Frm_Login_Guide.md - دليل الاستخدام\n" +
                "• Run_Login_Form.cs - ملف التشغيل\n\n" +
                "🎨 Custom Controls المستخدمة:\n" +
                "• من RJForms: RJBaseForm\n" +
                "• من RJControls: RJPanel, RJTextBox, RJButton, RJLabel, RJCheckBox\n" +
                "• من Settings: UIAppearance, UITheme, RJColors\n" +
                "• من Utils: utils, TCResize\n\n" +
                "✨ الميزات:\n" +
                "• أمان متقدم مع تشفير\n" +
                "• واجهة عربية احترافية\n" +
                "• دعم السمات المتعددة\n" +
                "• تأثيرات بصرية متقدمة\n" +
                "• حماية من الاختراق\n" +
                "• تسجيل الأنشطة\n\n" +
                "🚀 النموذج جاهز للاستخدام!",
                "معلومات النموذج",
                MessageBoxButtons.OK,
                MessageBoxIcon.Information);
        }

        /// <summary>
        /// تبديل السمة وإعادة تشغيل النموذج
        /// </summary>
        public static void ToggleThemeAndRestart()
        {
            try
            {
                // تبديل السمة
                UIAppearance.Theme = UIAppearance.Theme == UITheme.Light ? 
                    UITheme.Dark : UITheme.Light;

                var themeName = UIAppearance.Theme == UITheme.Dark ? "المظلمة" : "الفاتحة";

                RJMessageBox.Show(
                    $"تم تبديل السمة إلى {themeName}\n\n" +
                    "سيتم فتح نموذج تسجيل الدخول بالسمة الجديدة...",
                    "تبديل السمة",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Information);

                // تشغيل النموذج بالسمة الجديدة
                RunLoginForm();
            }
            catch (Exception ex)
            {
                RJMessageBox.Show(
                    $"خطأ في تبديل السمة:\n\n{ex.Message}",
                    "خطأ",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error);
            }
        }
    }
}
