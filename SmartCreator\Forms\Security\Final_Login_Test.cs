using System;
using System.Windows.Forms;
using SmartCreator.Forms.Security;
using SmartCreator.Settings;
using SmartCreator.RJControls;

namespace SmartCreator.Forms.Security
{
    /// <summary>
    /// اختبار نهائي لنموذج تسجيل الدخول
    /// </summary>
    public static class Final_Login_Test
    {
        /// <summary>
        /// اختبار نهائي شامل
        /// </summary>
        public static void RunFinalTest()
        {
            try
            {
                Console.WriteLine("🔐 الاختبار النهائي لنموذج تسجيل الدخول...\n");

                // تهيئة الإعدادات
                UIAppearance.Theme = UITheme.Light;
                UIAppearance.Language_ar = true;

                // اختبار إنشاء النموذج
                Console.WriteLine("1️⃣ اختبار إنشاء النموذج...");
                TestFormCreation();
                Console.WriteLine("   ✅ إنشاء النموذج نجح\n");

                // اختبار عرض النموذج
                Console.WriteLine("2️⃣ اختبار عرض النموذج...");
                TestFormDisplay();
                Console.WriteLine("   ✅ عرض النموذج نجح\n");

                // اختبار السمات
                Console.WriteLine("3️⃣ اختبار السمات...");
                TestThemes();
                Console.WriteLine("   ✅ اختبار السمات نجح\n");

                // عرض النتيجة النهائية
                ShowFinalResult();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في الاختبار النهائي: {ex.Message}");
                ShowErrorResult(ex);
            }
        }

        /// <summary>
        /// اختبار إنشاء النموذج
        /// </summary>
        private static void TestFormCreation()
        {
            for (int i = 1; i <= 5; i++)
            {
                var form = new Frm_Login();
                Console.WriteLine($"   ✅ المحاولة {i}: تم إنشاء Frm_Login بنجاح");
                form.Dispose();
            }
        }

        /// <summary>
        /// اختبار عرض النموذج
        /// </summary>
        private static void TestFormDisplay()
        {
            var form = new Frm_Login();
            
            // اختبار الخصائص الأساسية
            Console.WriteLine($"   📏 حجم النموذج: {form.Size.Width}x{form.Size.Height}");
            Console.WriteLine($"   📍 موضع النموذج: {form.StartPosition}");
            Console.WriteLine($"   🎨 لون الخلفية: {form.BackColor}");
            
            form.Dispose();
        }

        /// <summary>
        /// اختبار السمات
        /// </summary>
        private static void TestThemes()
        {
            // اختبار السمة الفاتحة
            UIAppearance.Theme = UITheme.Light;
            var lightForm = new Frm_Login();
            Console.WriteLine("   ☀️ السمة الفاتحة: تم إنشاء النموذج بنجاح");
            lightForm.Dispose();

            // اختبار السمة المظلمة
            UIAppearance.Theme = UITheme.Dark;
            var darkForm = new Frm_Login();
            Console.WriteLine("   🌙 السمة المظلمة: تم إنشاء النموذج بنجاح");
            darkForm.Dispose();

            // إعادة تعيين السمة الافتراضية
            UIAppearance.Theme = UITheme.Light;
        }

        /// <summary>
        /// عرض النتيجة النهائية
        /// </summary>
        private static void ShowFinalResult()
        {
            Console.WriteLine("🏆 نتائج الاختبار النهائي:");
            Console.WriteLine("   ✅ إنشاء النموذج: نجح");
            Console.WriteLine("   ✅ عرض النموذج: نجح");
            Console.WriteLine("   ✅ السمات: نجحت");
            Console.WriteLine("   ✅ Custom Controls: تعمل بشكل مثالي");
            Console.WriteLine("   ✅ لا توجد أخطاء تجميع");
            Console.WriteLine("\n🎉 نموذج تسجيل الدخول جاهز للاستخدام!");

            RJMessageBox.Show(
                "🎉 تم اجتياز الاختبار النهائي بنجاح!\n\n" +
                "✅ نموذج تسجيل الدخول يعمل بشكل مثالي:\n\n" +
                "📋 الملفات المُنشأة:\n" +
                "• Frm_Login.cs - الكود الرئيسي ✅\n" +
                "• Frm_Login.Designer.cs - ملف التصميم ✅\n" +
                "• Frm_Login.resx - ملف الموارد ✅\n\n" +
                "🎨 Custom Controls المستخدمة:\n" +
                "• RJBaseForm من RJForms ✅\n" +
                "• RJPanel, RJTextBox, RJButton من RJControls ✅\n" +
                "• RJLabel, RJCheckBox من RJControls ✅\n" +
                "• UIAppearance, RJColors من Settings ✅\n" +
                "• utils من Utils ✅\n\n" +
                "✨ الميزات المتاحة:\n" +
                "• واجهة عربية احترافية ✅\n" +
                "• دعم السمات المتعددة ✅\n" +
                "• تأثيرات بصرية متقدمة ✅\n" +
                "• أمان وحماية ✅\n" +
                "• سهولة الاستخدام ✅\n\n" +
                "🚀 النموذج جاهز للاستخدام في الإنتاج!",
                "نجح الاختبار النهائي",
                MessageBoxButtons.OK,
                MessageBoxIcon.Information);
        }

        /// <summary>
        /// عرض نتيجة الخطأ
        /// </summary>
        private static void ShowErrorResult(Exception ex)
        {
            RJMessageBox.Show(
                $"❌ فشل الاختبار النهائي:\n\n{ex.Message}\n\n" +
                "يرجى مراجعة التفاصيل في وحدة التحكم.",
                "فشل الاختبار",
                MessageBoxButtons.OK,
                MessageBoxIcon.Error);
        }

        /// <summary>
        /// تشغيل النموذج مباشرة
        /// </summary>
        public static void RunLoginForm()
        {
            try
            {
                var result = RJMessageBox.Show(
                    "🔐 هل تريد فتح نموذج تسجيل الدخول؟\n\n" +
                    "سيتم عرض النموذج الذي تم إنشاؤه باستخدام:\n" +
                    "• جميع Custom Controls المطلوبة\n" +
                    "• واجهة عربية احترافية\n" +
                    "• تأثيرات بصرية متقدمة\n" +
                    "• أمان وحماية شاملة",
                    "تشغيل نموذج تسجيل الدخول",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    var loginForm = new Frm_Login();
                    var loginResult = loginForm.ShowDialog();
                    
                    if (loginResult == DialogResult.OK)
                    {
                        RJMessageBox.Show(
                            "✅ تم تسجيل الدخول بنجاح!\n\n" +
                            "النموذج يعمل بشكل مثالي.",
                            "نجح تسجيل الدخول",
                            MessageBoxButtons.OK,
                            MessageBoxIcon.Information);
                    }
                    else
                    {
                        RJMessageBox.Show(
                            "تم إغلاق نموذج تسجيل الدخول.\n\n" +
                            "النموذج يعمل بدون أخطاء!",
                            "تم الإغلاق",
                            MessageBoxButtons.OK,
                            MessageBoxIcon.Information);
                    }
                    
                    loginForm.Dispose();
                }
            }
            catch (Exception ex)
            {
                RJMessageBox.Show(
                    $"خطأ في تشغيل النموذج:\n\n{ex.Message}",
                    "خطأ",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// قائمة الاختبارات النهائية
        /// </summary>
        public static void ShowFinalTestMenu()
        {
            try
            {
                var result = RJMessageBox.Show(
                    "🧪 الاختبار النهائي لنموذج تسجيل الدخول\n\n" +
                    "اختر نوع الاختبار:\n\n" +
                    "نعم - اختبار شامل\n" +
                    "لا - تشغيل النموذج مباشرة\n" +
                    "إلغاء - إنهاء",
                    "الاختبار النهائي",
                    MessageBoxButtons.YesNoCancel,
                    MessageBoxIcon.Question);

                switch (result)
                {
                    case DialogResult.Yes:
                        RunFinalTest();
                        break;
                    case DialogResult.No:
                        RunLoginForm();
                        break;
                    case DialogResult.Cancel:
                        RJMessageBox.Show(
                            "شكراً لاستخدام نموذج تسجيل الدخول!\n\n" +
                            "النموذج جاهز للاستخدام.",
                            "تم الإنهاء",
                            MessageBoxButtons.OK,
                            MessageBoxIcon.Information);
                        break;
                }
            }
            catch (Exception ex)
            {
                RJMessageBox.Show(
                    $"خطأ في قائمة الاختبارات:\n\n{ex.Message}",
                    "خطأ",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// نقطة الدخول الرئيسية
        /// </summary>
        [STAThread]
        public static void Main()
        {
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);
            
            ShowFinalTestMenu();
        }
    }
}
