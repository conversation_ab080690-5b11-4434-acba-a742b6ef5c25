namespace SmartCreator.Forms._Accounting.Product
{
    partial class Frm_ProductAdd
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.pnlForm = new SmartCreator.RJControls.RJPanel();
            this.chkActive = new SmartCreator.RJControls.RJCheckBox();
            this.txtLowStockThreshold = new SmartCreator.RJControls.RJTextBox();
            this.lblLowStockThreshold = new SmartCreator.RJControls.RJLabel();
            this.txtStock = new SmartCreator.RJControls.RJTextBox();
            this.lblStock = new SmartCreator.RJControls.RJLabel();
            this.txtPrice = new SmartCreator.RJControls.RJTextBox();
            this.lblPrice = new SmartCreator.RJControls.RJLabel();
            this.txtDescription = new SmartCreator.RJControls.RJTextBox();
            this.lblDescription = new SmartCreator.RJControls.RJLabel();
            this.txtName = new SmartCreator.RJControls.RJTextBox();
            this.lblName = new SmartCreator.RJControls.RJLabel();
            this.txtCode = new SmartCreator.RJControls.RJTextBox();
            this.lblCode = new SmartCreator.RJControls.RJLabel();
            this.pnlButtons = new SmartCreator.RJControls.RJPanel();
            this.btnClear = new SmartCreator.RJControls.RJButton();
            this.btnCancel = new SmartCreator.RJControls.RJButton();
            this.btnSave = new SmartCreator.RJControls.RJButton();
            this.pnlClientArea.SuspendLayout();
            this.pnlForm.SuspendLayout();
            this.pnlButtons.SuspendLayout();
            this.SuspendLayout();
            // 
            // pnlClientArea
            // 
            this.pnlClientArea.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.pnlClientArea.Controls.Add(this.pnlForm);
            this.pnlClientArea.Controls.Add(this.pnlButtons);
            this.pnlClientArea.Location = new System.Drawing.Point(5, 45);
            this.pnlClientArea.Size = new System.Drawing.Size(990, 550);
            // 
            // lblCaption
            // 
            this.lblCaption.Size = new System.Drawing.Size(92, 17);
            this.lblCaption.Text = "إضافة منتج جديد";
            // 
            // pnlForm
            // 
            this.pnlForm.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.pnlForm.BorderRadius = 10;
            this.pnlForm.Controls.Add(this.chkActive);
            this.pnlForm.Controls.Add(this.txtLowStockThreshold);
            this.pnlForm.Controls.Add(this.lblLowStockThreshold);
            this.pnlForm.Controls.Add(this.txtStock);
            this.pnlForm.Controls.Add(this.lblStock);
            this.pnlForm.Controls.Add(this.txtPrice);
            this.pnlForm.Controls.Add(this.lblPrice);
            this.pnlForm.Controls.Add(this.txtDescription);
            this.pnlForm.Controls.Add(this.lblDescription);
            this.pnlForm.Controls.Add(this.txtName);
            this.pnlForm.Controls.Add(this.lblName);
            this.pnlForm.Controls.Add(this.txtCode);
            this.pnlForm.Controls.Add(this.lblCode);
            this.pnlForm.Customizable = false;
            this.pnlForm.Dock = System.Windows.Forms.DockStyle.Fill;
            this.pnlForm.ForeColor = System.Drawing.Color.White;
            this.pnlForm.GradientAngle = 90F;
            this.pnlForm.GradientBottomColor = System.Drawing.Color.FromArgb(((int)(((byte)(30)))), ((int)(((byte)(30)))), ((int)(((byte)(30)))));
            this.pnlForm.GradientTopColor = System.Drawing.Color.FromArgb(((int)(((byte)(30)))), ((int)(((byte)(30)))), ((int)(((byte)(30)))));
            this.pnlForm.Location = new System.Drawing.Point(0, 0);
            this.pnlForm.Name = "pnlForm";
            this.pnlForm.Size = new System.Drawing.Size(990, 470);
            this.pnlForm.TabIndex = 0;
            // 
            // chkActive
            // 
            this.chkActive.AutoSize = true;
            this.chkActive.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.chkActive.BorderSize = 1;
            this.chkActive.Check = true;
            this.chkActive.Checked = true;
            this.chkActive.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkActive.Cursor = System.Windows.Forms.Cursors.Hand;
            this.chkActive.Customizable = false;
            this.chkActive.Font = new System.Drawing.Font("Droid Arabic Kufi", 12F, System.Drawing.FontStyle.Bold);
            this.chkActive.ForeColor = System.Drawing.Color.White;
            this.chkActive.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.chkActive.Location = new System.Drawing.Point(600, 330);
            this.chkActive.MinimumSize = new System.Drawing.Size(0, 21);
            this.chkActive.Name = "chkActive";
            this.chkActive.Padding = new System.Windows.Forms.Padding(8, 0, 0, 0);
            this.chkActive.Size = new System.Drawing.Size(85, 35);
            this.chkActive.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.chkActive.TabIndex = 12;
            this.chkActive.Text = "مفعل";
            this.chkActive.UseVisualStyleBackColor = true;
            // 
            // txtLowStockThreshold
            // 
            this.txtLowStockThreshold._Customizable = false;
            this.txtLowStockThreshold.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txtLowStockThreshold.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txtLowStockThreshold.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txtLowStockThreshold.BorderRadius = 8;
            this.txtLowStockThreshold.BorderSize = 2;
            this.txtLowStockThreshold.Font = new System.Drawing.Font("Droid Arabic Kufi", 10F);
            this.txtLowStockThreshold.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txtLowStockThreshold.Location = new System.Drawing.Point(300, 280);
            this.txtLowStockThreshold.Margin = new System.Windows.Forms.Padding(4);
            this.txtLowStockThreshold.MultiLine = false;
            this.txtLowStockThreshold.Name = "txtLowStockThreshold";
            this.txtLowStockThreshold.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txtLowStockThreshold.PasswordChar = false;
            this.txtLowStockThreshold.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txtLowStockThreshold.PlaceHolderText = "10";
            this.txtLowStockThreshold.ReadOnly = false;
            this.txtLowStockThreshold.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txtLowStockThreshold.Size = new System.Drawing.Size(180, 38);
            this.txtLowStockThreshold.Style = SmartCreator.RJControls.TextBoxStyle.MatteBorder;
            this.txtLowStockThreshold.TabIndex = 11;
            this.txtLowStockThreshold.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            this.txtLowStockThreshold.Texts = "";
            this.txtLowStockThreshold.UnderlinedStyle = false;
            // 
            // lblLowStockThreshold
            // 
            this.lblLowStockThreshold.AutoSize = true;
            this.lblLowStockThreshold.Font = new System.Drawing.Font("Droid Arabic Kufi", 12F, System.Drawing.FontStyle.Bold);
            this.lblLowStockThreshold.ForeColor = System.Drawing.Color.White;
            this.lblLowStockThreshold.LinkLabel = false;
            this.lblLowStockThreshold.Location = new System.Drawing.Point(500, 280);
            this.lblLowStockThreshold.Name = "lblLowStockThreshold";
            this.lblLowStockThreshold.Size = new System.Drawing.Size(174, 31);
            this.lblLowStockThreshold.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.lblLowStockThreshold.TabIndex = 10;
            this.lblLowStockThreshold.Text = "الحد الأدنى للمخزون:";
            // 
            // txtStock
            // 
            this.txtStock._Customizable = false;
            this.txtStock.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txtStock.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txtStock.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txtStock.BorderRadius = 8;
            this.txtStock.BorderSize = 2;
            this.txtStock.Font = new System.Drawing.Font("Droid Arabic Kufi", 10F);
            this.txtStock.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txtStock.Location = new System.Drawing.Point(150, 230);
            this.txtStock.Margin = new System.Windows.Forms.Padding(4);
            this.txtStock.MultiLine = false;
            this.txtStock.Name = "txtStock";
            this.txtStock.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txtStock.PasswordChar = false;
            this.txtStock.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txtStock.PlaceHolderText = "0";
            this.txtStock.ReadOnly = false;
            this.txtStock.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txtStock.Size = new System.Drawing.Size(180, 38);
            this.txtStock.Style = SmartCreator.RJControls.TextBoxStyle.MatteBorder;
            this.txtStock.TabIndex = 9;
            this.txtStock.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            this.txtStock.Texts = "";
            this.txtStock.UnderlinedStyle = false;
            // 
            // lblStock
            // 
            this.lblStock.AutoSize = true;
            this.lblStock.Font = new System.Drawing.Font("Droid Arabic Kufi", 12F, System.Drawing.FontStyle.Bold);
            this.lblStock.ForeColor = System.Drawing.Color.White;
            this.lblStock.LinkLabel = false;
            this.lblStock.Location = new System.Drawing.Point(350, 230);
            this.lblStock.Name = "lblStock";
            this.lblStock.Size = new System.Drawing.Size(83, 31);
            this.lblStock.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.lblStock.TabIndex = 8;
            this.lblStock.Text = "المخزون:";
            // 
            // txtPrice
            // 
            this.txtPrice._Customizable = false;
            this.txtPrice.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txtPrice.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txtPrice.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txtPrice.BorderRadius = 8;
            this.txtPrice.BorderSize = 2;
            this.txtPrice.Font = new System.Drawing.Font("Droid Arabic Kufi", 10F);
            this.txtPrice.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txtPrice.Location = new System.Drawing.Point(450, 230);
            this.txtPrice.Margin = new System.Windows.Forms.Padding(4);
            this.txtPrice.MultiLine = false;
            this.txtPrice.Name = "txtPrice";
            this.txtPrice.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txtPrice.PasswordChar = false;
            this.txtPrice.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txtPrice.PlaceHolderText = "0.00";
            this.txtPrice.ReadOnly = false;
            this.txtPrice.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txtPrice.Size = new System.Drawing.Size(180, 38);
            this.txtPrice.Style = SmartCreator.RJControls.TextBoxStyle.MatteBorder;
            this.txtPrice.TabIndex = 7;
            this.txtPrice.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            this.txtPrice.Texts = "";
            this.txtPrice.UnderlinedStyle = false;
            // 
            // lblPrice
            // 
            this.lblPrice.AutoSize = true;
            this.lblPrice.Font = new System.Drawing.Font("Droid Arabic Kufi", 12F, System.Drawing.FontStyle.Bold);
            this.lblPrice.ForeColor = System.Drawing.Color.White;
            this.lblPrice.LinkLabel = false;
            this.lblPrice.Location = new System.Drawing.Point(650, 230);
            this.lblPrice.Name = "lblPrice";
            this.lblPrice.Size = new System.Drawing.Size(68, 31);
            this.lblPrice.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.lblPrice.TabIndex = 6;
            this.lblPrice.Text = "السعر:";
            // 
            // txtDescription
            // 
            this.txtDescription._Customizable = false;
            this.txtDescription.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txtDescription.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txtDescription.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txtDescription.BorderRadius = 8;
            this.txtDescription.BorderSize = 2;
            this.txtDescription.Font = new System.Drawing.Font("Droid Arabic Kufi", 10F);
            this.txtDescription.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txtDescription.Location = new System.Drawing.Point(50, 130);
            this.txtDescription.Margin = new System.Windows.Forms.Padding(4);
            this.txtDescription.MultiLine = true;
            this.txtDescription.Name = "txtDescription";
            this.txtDescription.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txtDescription.PasswordChar = false;
            this.txtDescription.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txtDescription.PlaceHolderText = "ادخل وصف المنتج";
            this.txtDescription.ReadOnly = false;
            this.txtDescription.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txtDescription.Size = new System.Drawing.Size(580, 80);
            this.txtDescription.Style = SmartCreator.RJControls.TextBoxStyle.MatteBorder;
            this.txtDescription.TabIndex = 5;
            this.txtDescription.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            this.txtDescription.Texts = "";
            this.txtDescription.UnderlinedStyle = false;
            // 
            // lblDescription
            // 
            this.lblDescription.AutoSize = true;
            this.lblDescription.Font = new System.Drawing.Font("Droid Arabic Kufi", 12F, System.Drawing.FontStyle.Bold);
            this.lblDescription.ForeColor = System.Drawing.Color.White;
            this.lblDescription.LinkLabel = false;
            this.lblDescription.Location = new System.Drawing.Point(650, 130);
            this.lblDescription.Name = "lblDescription";
            this.lblDescription.Size = new System.Drawing.Size(76, 31);
            this.lblDescription.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.lblDescription.TabIndex = 4;
            this.lblDescription.Text = "الوصف:";
            // 
            // txtName
            // 
            this.txtName._Customizable = false;
            this.txtName.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txtName.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txtName.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txtName.BorderRadius = 8;
            this.txtName.BorderSize = 2;
            this.txtName.Font = new System.Drawing.Font("Droid Arabic Kufi", 10F);
            this.txtName.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txtName.Location = new System.Drawing.Point(350, 80);
            this.txtName.Margin = new System.Windows.Forms.Padding(4);
            this.txtName.MultiLine = false;
            this.txtName.Name = "txtName";
            this.txtName.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txtName.PasswordChar = false;
            this.txtName.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txtName.PlaceHolderText = "ادخل اسم المنتج";
            this.txtName.ReadOnly = false;
            this.txtName.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txtName.Size = new System.Drawing.Size(280, 38);
            this.txtName.Style = SmartCreator.RJControls.TextBoxStyle.MatteBorder;
            this.txtName.TabIndex = 3;
            this.txtName.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            this.txtName.Texts = "";
            this.txtName.UnderlinedStyle = false;
            // 
            // lblName
            // 
            this.lblName.AutoSize = true;
            this.lblName.Font = new System.Drawing.Font("Droid Arabic Kufi", 12F, System.Drawing.FontStyle.Bold);
            this.lblName.ForeColor = System.Drawing.Color.White;
            this.lblName.LinkLabel = false;
            this.lblName.Location = new System.Drawing.Point(650, 80);
            this.lblName.Name = "lblName";
            this.lblName.Size = new System.Drawing.Size(105, 31);
            this.lblName.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.lblName.TabIndex = 2;
            this.lblName.Text = "اسم المنتج:";
            // 
            // txtCode
            // 
            this.txtCode._Customizable = false;
            this.txtCode.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txtCode.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txtCode.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txtCode.BorderRadius = 8;
            this.txtCode.BorderSize = 2;
            this.txtCode.Font = new System.Drawing.Font("Droid Arabic Kufi", 10F);
            this.txtCode.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txtCode.Location = new System.Drawing.Point(350, 30);
            this.txtCode.Margin = new System.Windows.Forms.Padding(4);
            this.txtCode.MultiLine = false;
            this.txtCode.Name = "txtCode";
            this.txtCode.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txtCode.PasswordChar = false;
            this.txtCode.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txtCode.PlaceHolderText = "ادخل كود المنتج";
            this.txtCode.ReadOnly = false;
            this.txtCode.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txtCode.Size = new System.Drawing.Size(280, 38);
            this.txtCode.Style = SmartCreator.RJControls.TextBoxStyle.MatteBorder;
            this.txtCode.TabIndex = 1;
            this.txtCode.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            this.txtCode.Texts = "";
            this.txtCode.UnderlinedStyle = false;
            // 
            // lblCode
            // 
            this.lblCode.AutoSize = true;
            this.lblCode.Font = new System.Drawing.Font("Droid Arabic Kufi", 12F, System.Drawing.FontStyle.Bold);
            this.lblCode.ForeColor = System.Drawing.Color.White;
            this.lblCode.LinkLabel = false;
            this.lblCode.Location = new System.Drawing.Point(650, 30);
            this.lblCode.Name = "lblCode";
            this.lblCode.Size = new System.Drawing.Size(102, 31);
            this.lblCode.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.lblCode.TabIndex = 0;
            this.lblCode.Text = "كود المنتج:";
            // 
            // pnlButtons
            // 
            this.pnlButtons.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.pnlButtons.BorderRadius = 10;
            this.pnlButtons.Controls.Add(this.btnClear);
            this.pnlButtons.Controls.Add(this.btnCancel);
            this.pnlButtons.Controls.Add(this.btnSave);
            this.pnlButtons.Customizable = false;
            this.pnlButtons.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.pnlButtons.ForeColor = System.Drawing.Color.White;
            this.pnlButtons.GradientAngle = 90F;
            this.pnlButtons.GradientBottomColor = System.Drawing.Color.FromArgb(((int)(((byte)(45)))), ((int)(((byte)(45)))), ((int)(((byte)(48)))));
            this.pnlButtons.GradientTopColor = System.Drawing.Color.FromArgb(((int)(((byte)(45)))), ((int)(((byte)(45)))), ((int)(((byte)(48)))));
            this.pnlButtons.Location = new System.Drawing.Point(0, 470);
            this.pnlButtons.Name = "pnlButtons";
            this.pnlButtons.Size = new System.Drawing.Size(990, 80);
            this.pnlButtons.TabIndex = 1;
            // 
            // btnClear
            // 
            this.btnClear.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(96)))), ((int)(((byte)(125)))), ((int)(((byte)(139)))));
            this.btnClear.BackgroundColor = System.Drawing.Color.FromArgb(((int)(((byte)(96)))), ((int)(((byte)(125)))), ((int)(((byte)(139)))));
            this.btnClear.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btnClear.BorderRadius = 8;
            this.btnClear.BorderSize = 0;
            this.btnClear.Design = SmartCreator.RJControls.ButtonDesign.Normal;
            this.btnClear.FlatAppearance.BorderSize = 0;
            this.btnClear.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(90)))), ((int)(((byte)(117)))), ((int)(((byte)(130)))));
            this.btnClear.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(84)))), ((int)(((byte)(110)))), ((int)(((byte)(122)))));
            this.btnClear.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnClear.Font = new System.Drawing.Font("Droid Arabic Kufi", 12F, System.Drawing.FontStyle.Bold);
            this.btnClear.ForeColor = System.Drawing.Color.White;
            this.btnClear.IconChar = FontAwesome.Sharp.IconChar.None;
            this.btnClear.IconColor = System.Drawing.Color.White;
            this.btnClear.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btnClear.IconSize = 24;
            this.btnClear.Location = new System.Drawing.Point(370, 15);
            this.btnClear.Name = "btnClear";
            this.btnClear.Size = new System.Drawing.Size(120, 50);
            this.btnClear.Style = SmartCreator.RJControls.ControlStyle.Solid;
            this.btnClear.TabIndex = 2;
            this.btnClear.Text = "مسح";
            this.btnClear.TextColor = System.Drawing.Color.White;
            this.btnClear.UseVisualStyleBackColor = false;
            // 
            // btnCancel
            // 
            this.btnCancel.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(244)))), ((int)(((byte)(67)))), ((int)(((byte)(54)))));
            this.btnCancel.BackgroundColor = System.Drawing.Color.FromArgb(((int)(((byte)(244)))), ((int)(((byte)(67)))), ((int)(((byte)(54)))));
            this.btnCancel.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btnCancel.BorderRadius = 8;
            this.btnCancel.BorderSize = 0;
            this.btnCancel.Design = SmartCreator.RJControls.ButtonDesign.Normal;
            this.btnCancel.FlatAppearance.BorderSize = 0;
            this.btnCancel.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(229)))), ((int)(((byte)(62)))), ((int)(((byte)(50)))));
            this.btnCancel.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(214)))), ((int)(((byte)(58)))), ((int)(((byte)(47)))));
            this.btnCancel.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnCancel.Font = new System.Drawing.Font("Droid Arabic Kufi", 12F, System.Drawing.FontStyle.Bold);
            this.btnCancel.ForeColor = System.Drawing.Color.White;
            this.btnCancel.IconChar = FontAwesome.Sharp.IconChar.None;
            this.btnCancel.IconColor = System.Drawing.Color.White;
            this.btnCancel.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btnCancel.IconSize = 24;
            this.btnCancel.Location = new System.Drawing.Point(510, 15);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(120, 50);
            this.btnCancel.Style = SmartCreator.RJControls.ControlStyle.Solid;
            this.btnCancel.TabIndex = 1;
            this.btnCancel.Text = "إلغاء";
            this.btnCancel.TextColor = System.Drawing.Color.White;
            this.btnCancel.UseVisualStyleBackColor = false;
            // 
            // btnSave
            // 
            this.btnSave.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(76)))), ((int)(((byte)(175)))), ((int)(((byte)(80)))));
            this.btnSave.BackgroundColor = System.Drawing.Color.FromArgb(((int)(((byte)(76)))), ((int)(((byte)(175)))), ((int)(((byte)(80)))));
            this.btnSave.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btnSave.BorderRadius = 8;
            this.btnSave.BorderSize = 0;
            this.btnSave.Design = SmartCreator.RJControls.ButtonDesign.Normal;
            this.btnSave.FlatAppearance.BorderSize = 0;
            this.btnSave.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(71)))), ((int)(((byte)(164)))), ((int)(((byte)(75)))));
            this.btnSave.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(66)))), ((int)(((byte)(154)))), ((int)(((byte)(70)))));
            this.btnSave.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnSave.Font = new System.Drawing.Font("Droid Arabic Kufi", 12F, System.Drawing.FontStyle.Bold);
            this.btnSave.ForeColor = System.Drawing.Color.White;
            this.btnSave.IconChar = FontAwesome.Sharp.IconChar.None;
            this.btnSave.IconColor = System.Drawing.Color.White;
            this.btnSave.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btnSave.IconSize = 24;
            this.btnSave.Location = new System.Drawing.Point(650, 15);
            this.btnSave.Name = "btnSave";
            this.btnSave.Size = new System.Drawing.Size(120, 50);
            this.btnSave.Style = SmartCreator.RJControls.ControlStyle.Solid;
            this.btnSave.TabIndex = 0;
            this.btnSave.Text = "حفظ";
            this.btnSave.TextColor = System.Drawing.Color.White;
            this.btnSave.UseVisualStyleBackColor = false;
            // 
            // Frm_ProductAdd
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 15F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(111)))), ((int)(((byte)(106)))), ((int)(((byte)(143)))));
            this.BorderSize = 5;
            this.Caption = "إضافة منتج جديد";
            this.ClientSize = new System.Drawing.Size(1000, 600);
            this.Location = new System.Drawing.Point(0, 0);
            this.Name = "Frm_ProductAdd";
            this.Padding = new System.Windows.Forms.Padding(5);
            this.Text = "إضافة منتج جديد";
            this.pnlClientArea.ResumeLayout(false);
            this.pnlForm.ResumeLayout(false);
            this.pnlForm.PerformLayout();
            this.pnlButtons.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private RJControls.RJPanel pnlForm;
        private RJControls.RJLabel lblCode;
        private RJControls.RJTextBox txtCode;
        private RJControls.RJLabel lblName;
        private RJControls.RJTextBox txtName;
        private RJControls.RJLabel lblDescription;
        private RJControls.RJTextBox txtDescription;
        private RJControls.RJLabel lblPrice;
        private RJControls.RJTextBox txtPrice;
        private RJControls.RJLabel lblStock;
        private RJControls.RJTextBox txtStock;
        private RJControls.RJLabel lblLowStockThreshold;
        private RJControls.RJTextBox txtLowStockThreshold;
        private RJControls.RJCheckBox chkActive;
        private RJControls.RJPanel pnlButtons;
        private RJControls.RJButton btnSave;
        private RJControls.RJButton btnCancel;
        private RJControls.RJButton btnClear;
    }
}
