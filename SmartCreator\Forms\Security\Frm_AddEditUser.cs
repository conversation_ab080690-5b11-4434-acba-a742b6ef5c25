using SmartCreator.Entities;
using SmartCreator.Services;
using SmartCreator.Models;
using SmartCreator.RJControls;
using SmartCreator.Helpers;
using System;
using System.Drawing;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Windows.Forms;
using SmartCreator.RJForms;

namespace SmartCreator.Forms.Security
{
    /// <summary>
    /// واجهة إضافة/تعديل المستخدمين - نسخة مبسطة
    /// </summary>
    public partial class Frm_AddEditUser : RJChildForm
    {
        private readonly UserManagementService _userService;
        private readonly User _editingUser;
        private readonly bool _isEditMode;

        // عناصر التحكم المفقودة
        private Label lblPasswordStrength;
        private Label lblPasswordMatch;
        private Label lblEmailValidation;
        private Label lblUsernameValidation;

        public Frm_AddEditUser(UserManagementService userService, User editingUser = null)
        {
            InitializeComponent();
            _userService = userService;
            _editingUser = editingUser;
            _isEditMode = editingUser != null;

            InitializeForm();
        }

        /// <summary>
        /// تهيئة النموذج
        /// </summary>
        private void InitializeForm()
        {
            //return;
            // إنشاء العناصر المفقودة
            CreateValidationLabels();

            // تحديد عنوان النموذج
            if (_isEditMode)
            {
                this.Text = "تعديل المستخدم";
                lblTitle.Text = "تعديل بيانات المستخدم";
                btnChangePassword.Visible = true;
                pnlPassword.Visible = false;
            }
            else
            {
                this.Text = "إضافة مستخدم جديد";
                lblTitle.Text = "إضافة مستخدم جديد";
                btnChangePassword.Visible = false;
                pnlPassword.Visible = true;
            }

            // ربط الأحداث
            btnSave.Click += BtnSave_Click;
            btnCancel.Click += BtnCancel_Click;
            btnChangePassword.Click += BtnChangePassword_Click;

            // تحميل البيانات في حالة التعديل
            if (_isEditMode)
            {
                LoadUserData();
            }

            SetupValidation();
        }

        /// <summary>
        /// إنشاء عناصر التحقق المفقودة
        /// </summary>
        private void CreateValidationLabels()
        {
            // مؤشر قوة كلمة المرور
            lblPasswordStrength = new Label
            {
                Location = new Point(20, 200),
                Size = new Size(350, 20),
                ForeColor = Color.Yellow,
                Visible = false,
                Font = new Font("Segoe UI", 9)
            };

            // مؤشر تطابق كلمة المرور
            lblPasswordMatch = new Label
            {
                Location = new Point(20, 280),
                Size = new Size(350, 20),
                ForeColor = Color.Yellow,
                Visible = false,
                Font = new Font("Segoe UI", 9)
            };

            // مؤشر صحة البريد الإلكتروني
            lblEmailValidation = new Label
            {
                Location = new Point(20, 360),
                Size = new Size(350, 20),
                ForeColor = Color.Yellow,
                Visible = false,
                Font = new Font("Segoe UI", 9)
            };

            // مؤشر توفر اسم المستخدم
            lblUsernameValidation = new Label
            {
                Location = new Point(20, 120),
                Size = new Size(350, 20),
                ForeColor = Color.Yellow,
                Visible = false,
                Font = new Font("Segoe UI", 9)
            };

            // إضافة العناصر للنموذج
            this.Controls.AddRange(new Control[]
            {
                lblPasswordStrength,
                lblPasswordMatch,
                lblEmailValidation,
                lblUsernameValidation
            });
        }

        /// <summary>
        /// تحميل بيانات المستخدم للتعديل
        /// </summary>
        private void LoadUserData()
        {
            if (_editingUser == null) return;

            try
            {
                txtUsername.Text = _editingUser.Username;
                txtFirstName.Text = _editingUser.FirstName;
                txtLastName.Text = _editingUser.LastName;
                txtEmail.Text = _editingUser.Email;
                txtPhone.Text = _editingUser.Phone;
                txtDepartment.Text = _editingUser.Department;
                txtPosition.Text = _editingUser.Position;
                txtNotes.Text = _editingUser.Notes;
                chkIsActive.Checked = _editingUser.IsActive;
                chkIsLocked.Checked = _editingUser.IsLocked;

                // تعطيل تعديل اسم المستخدم في وضع التعديل
                txtUsername.Enabled = false;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل بيانات المستخدم: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// إعداد التحقق من صحة البيانات
        /// </summary>
        private void SetupValidation()
        {
            // إعداد التحقق من قوة كلمة المرور
            if (!_isEditMode)
            {
                txtPassword.TextChanged += TxtPassword_TextChanged;
                txtConfirmPassword.TextChanged += TxtConfirmPassword_TextChanged;
            }

            // إعداد التحقق من البريد الإلكتروني
            txtEmail.Leave += TxtEmail_Leave;
            txtUsername.Leave += TxtUsername_Leave;
        }

        /// <summary>
        /// التحقق من قوة كلمة المرور
        /// </summary>
        private void TxtPassword_TextChanged(object sender, EventArgs e)
        {
            if (!_isEditMode && txtPassword.Text.Length > 0)
            {
                var strength = SecurityHelper.CheckPasswordStrength(txtPassword.Text);
                var description = SecurityHelper.GetPasswordStrengthDescription(strength);
                var color = SecurityHelper.GetPasswordStrengthColor(strength);

                lblPasswordStrength.Text = $"قوة كلمة المرور: {description}";
                lblPasswordStrength.ForeColor = color;
                lblPasswordStrength.Visible = true;
            }
            else
            {
                lblPasswordStrength.Visible = false;
            }
        }

        /// <summary>
        /// التحقق من تطابق كلمة المرور
        /// </summary>
        private void TxtConfirmPassword_TextChanged(object sender, EventArgs e)
        {

        }

        /// <summary>
        /// التحقق من صحة البريد الإلكتروني
        /// </summary>
        private void TxtEmail_Leave(object sender, EventArgs e)
        {
            if (!string.IsNullOrEmpty(txtEmail.Text))
            {
                if (IsValidEmail(txtEmail.Text))
                {
                    lblEmailValidation.Text = "✅ البريد الإلكتروني صحيح";
                    lblEmailValidation.ForeColor = Color.Green;
                }
                else
                {
                    lblEmailValidation.Text = "❌ البريد الإلكتروني غير صحيح";
                    lblEmailValidation.ForeColor = Color.Red;
                }
                lblEmailValidation.Visible = true;
            }
            else
            {
                lblEmailValidation.Visible = false;
            }
        }

        /// <summary>
        /// التحقق من توفر اسم المستخدم
        /// </summary>
        private async void TxtUsername_Leave(object sender, EventArgs e)
        {
            if (!string.IsNullOrEmpty(txtUsername.Text) && !_isEditMode)
            {
                try
                {
                    var existingUser = await _userService.GetUserByUsernameAsync(txtUsername.Text);
                    if (existingUser != null)
                    {
                        lblUsernameValidation.Text = "❌ اسم المستخدم موجود مسبقاً";
                        lblUsernameValidation.ForeColor = Color.Red;
                    }
                    else
                    {
                        lblUsernameValidation.Text = "✅ اسم المستخدم متاح";
                        lblUsernameValidation.ForeColor = Color.Green;
                    }
                    lblUsernameValidation.Visible = true;
                }
                catch
                {
                    lblUsernameValidation.Visible = false;
                }
            }
            else
            {
                lblUsernameValidation.Visible = false;
            }
        }

        /// <summary>
        /// حفظ المستخدم
        /// </summary>
        private async void BtnSave_Click(object sender, EventArgs e)
        {
            if (!ValidateForm()) return;

            try
            {
                btnSave.Enabled = false;
                btnSave.Text = "جاري الحفظ...";

                if (_isEditMode)
                {
                    // تعديل مستخدم موجود
                    await UpdateUserAsync();
                }
                else
                {
                    // إضافة مستخدم جديد
                    await CreateUserAsync();
                }

                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ المستخدم: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                btnSave.Enabled = true;
                btnSave.Text = "💾 حفظ";
            }
        }

        /// <summary>
        /// إنشاء مستخدم جديد
        /// </summary>
        private async Task CreateUserAsync()
        {
            var user = new User
            {
                Username = txtUsername.Text.Trim(),
                FirstName = txtFirstName.Text.Trim(),
                FullName = txtFirstName.Text.Trim(),
                //LastName = txtLastName.Text.Trim(),
                Email = txtEmail.Text.Trim(),
                Phone = txtPhone.Text.Trim(),
                //Department = txtDepartment.Text.Trim(),
                //Position = txtPosition.Text.Trim(),
                Notes = txtNotes.Text.Trim(),
                IsActive = chkIsActive.Checked,
                IsLocked = false
            };

            var password = txtPassword.Text;
            var createdBy = Global_Variable.CurrentUser?.Id ?? 1;

            var createdUser = await _userService.CreateUserAsync(user, password, createdBy);

            // تسجيل النشاط
            await SecurityHelper.LogActivityAsync(
                "إنشاء مستخدم",
                "إدارة المستخدمين",
                $"تم إنشاء مستخدم جديد: {user.Username}");

            MessageBox.Show($"تم إنشاء المستخدم '{user.Username}' بنجاح", "نجح الإنشاء",
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        /// <summary>
        /// تحديث مستخدم موجود
        /// </summary>
        private async Task UpdateUserAsync()
        {
            _editingUser.Username = txtUsername.Text.Trim();
            _editingUser.FirstName = txtFirstName.Text.Trim();
            //_editingUser.LastName = txtFirstName.Text.Trim();
            _editingUser.FullName = txtFirstName.Text.Trim();
            _editingUser.Email = txtEmail.Text.Trim();
            _editingUser.Phone = txtPhone.Text.Trim();
            //_editingUser.Department = txtDepartment.Text.Trim();
            //_editingUser.Position = txtPosition.Text.Trim();
            _editingUser.Notes = txtNotes.Text.Trim();
            _editingUser.IsActive = chkIsActive.Checked;
            _editingUser.IsLocked = chkIsLocked.Checked;

            var updatedBy = Global_Variable.CurrentUser?.Id ?? 1;
            var success = await _userService.UpdateUserAsync(_editingUser, updatedBy);

            if (success)
            {
                // تسجيل النشاط
                await SecurityHelper.LogActivityAsync(
                    "تعديل مستخدم",
                    "إدارة المستخدمين",
                    $"تم تعديل بيانات المستخدم: {_editingUser.Username}");

                MessageBox.Show($"تم تحديث بيانات المستخدم '{_editingUser.Username}' بنجاح", "نجح التحديث",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            else
            {
                throw new Exception("فشل في تحديث بيانات المستخدم");
            }
        }

        /// <summary>
        /// إلغاء العملية
        /// </summary>
        private void BtnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        /// <summary>
        /// تغيير كلمة المرور
        /// </summary>
        private async void BtnChangePassword_Click(object sender, EventArgs e)
        {
            if (_editingUser == null) return;

            try
            {
                var changePasswordForm = new Frm_ChangePassword(_userService, _editingUser);
                if (changePasswordForm.ShowDialog() == DialogResult.OK)
                {
                    MessageBox.Show("تم تغيير كلمة المرور بنجاح", "نجح التغيير",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);

                    // تسجيل النشاط
                    await SecurityHelper.LogActivityAsync(
                        "تغيير كلمة المرور",
                        "إدارة المستخدمين",
                        $"تم تغيير كلمة مرور المستخدم: {_editingUser.Username}");
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تغيير كلمة المرور: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// التحقق من صحة النموذج
        /// </summary>
        private bool ValidateForm()
        {
            // التحقق الأساسي
            if (string.IsNullOrWhiteSpace(txtUsername.Text))
            {
                MessageBox.Show("اسم المستخدم مطلوب", "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtUsername.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(txtFirstName.Text))
            {
                MessageBox.Show("ادخل الاسم الكامل", "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtFirstName.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(txtPhone.Text))
            {
                MessageBox.Show("ادخل رقم الهاتف", "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtLastName.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(txtEmail.Text))
            {
                MessageBox.Show("البريد الإلكتروني مطلوب", "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtEmail.Focus();
                return false;
            }

            if (!IsValidEmail(txtEmail.Text))
            {
                MessageBox.Show("البريد الإلكتروني غير صحيح", "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtEmail.Focus();
                return false;
            }

            // التحقق من كلمة المرور في حالة الإضافة
            if (!_isEditMode)
            {
                if (string.IsNullOrWhiteSpace(txtPassword.Text))
                {
                    MessageBox.Show("كلمة المرور مطلوبة", "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtPassword.Focus();
                    return false;
                }

                if (txtPassword.Text.Length < 6)
                {
                    MessageBox.Show("كلمة المرور يجب أن تكون 6 أحرف على الأقل", "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtPassword.Focus();
                    return false;
                }

                if (txtPassword.Text != txtConfirmPassword.Text)
                {
                    MessageBox.Show("كلمة المرور غير متطابقة", "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtConfirmPassword.Focus();
                    return false;
                }
            }

            return true;
        }

        /// <summary>
        /// التحقق من صحة البريد الإلكتروني
        /// </summary>
        private bool IsValidEmail(string email)
        {
            try
            {
                var emailRegex = new Regex(@"^[^@\s]+@[^@\s]+\.[^@\s]+$");
                return emailRegex.IsMatch(email);
            }
            catch
            {
                return false;
            }
        }
    }
}
