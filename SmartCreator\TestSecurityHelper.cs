using SmartCreator.Helpers;
using SmartCreator.Entities;
using SmartCreator.Models;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace SmartCreator
{
    /// <summary>
    /// ملف اختبار للتأكد من عمل SecurityHelper
    /// </summary>
    public class TestSecurityHelper
    {
        /// <summary>
        /// اختبار شامل لـ SecurityHelper
        /// </summary>
        public static async Task TestAsync()
        {
            try
            {
                Console.WriteLine("🚀 بدء اختبار SecurityHelper...\n");

                // 1. اختبار بدون مستخدم حالي
                TestWithoutCurrentUser();
                Console.WriteLine();

                // 2. اختبار مع مستخدم عادي
                await TestWithNormalUser();
                Console.WriteLine();

                // 3. اختبار مع مدير النظام
                await TestWithSystemAdmin();
                Console.WriteLine();

                // 4. اختبار وظائف كلمة المرور
                TestPasswordFunctions();
                Console.WriteLine();

                // 5. اختبار وظائف التشفير
                TestEncryptionFunctions();
                Console.WriteLine();

                Console.WriteLine("🎉 انتهت جميع اختبارات SecurityHelper بنجاح!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في اختبار SecurityHelper: {ex.Message}");
                Console.WriteLine($"تفاصيل الخطأ: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// اختبار بدون مستخدم حالي
        /// </summary>
        private static void TestWithoutCurrentUser()
        {
            Console.WriteLine("📋 اختبار بدون مستخدم حالي:");

            // التأكد من عدم وجود مستخدم
            Global_Variable.CurrentUser = null;

            // اختبار الصلاحيات
            var hasPermission = SecurityHelper.HasPermission(PermissionCodes.SYSTEM_ADMIN);
            Console.WriteLine($"   HasPermission (بدون مستخدم): {hasPermission} ✅");

            var hasAnyPermission = SecurityHelper.HasAnyPermission(PermissionCodes.ACCOUNTING_VIEW, PermissionCodes.REPORTS_VIEW);
            Console.WriteLine($"   HasAnyPermission (بدون مستخدم): {hasAnyPermission} ✅");

            var isSessionExpired = SecurityHelper.IsSessionExpired();
            Console.WriteLine($"   IsSessionExpired (بدون مستخدم): {isSessionExpired} ✅");

            Console.WriteLine("✅ اختبار بدون مستخدم نجح!");
        }

        /// <summary>
        /// اختبار مع مستخدم عادي
        /// </summary>
        private static async Task TestWithNormalUser()
        {
            Console.WriteLine("👤 اختبار مع مستخدم عادي:");

            // إنشاء مستخدم عادي
            Global_Variable.CurrentUser = new User
            {
                Id = 1,
                Username = "test_user",
                FullName = "مستخدم تجريبي",
                Email = "<EMAIL>",
                IsActive = true,
                IsLocked = false,
                FailedLoginAttempts = 0,
                Permissions = new List<string>
                {
                    PermissionCodes.ACCOUNTING_VIEW,
                    PermissionCodes.REPORTS_VIEW
                }
            };

            // اختبار الصلاحيات
            var hasAccountingView = SecurityHelper.HasPermission(PermissionCodes.ACCOUNTING_VIEW);
            Console.WriteLine($"   HasPermission (ACCOUNTING_VIEW): {hasAccountingView} ✅");

            var hasSystemAdmin = SecurityHelper.HasPermission(PermissionCodes.SYSTEM_ADMIN);
            Console.WriteLine($"   HasPermission (SYSTEM_ADMIN): {hasSystemAdmin} ✅");

            var hasAnyPermission = SecurityHelper.HasAnyPermission(PermissionCodes.ACCOUNTING_VIEW, PermissionCodes.SYSTEM_ADMIN);
            Console.WriteLine($"   HasAnyPermission: {hasAnyPermission} ✅");

            var hasAllPermissions = SecurityHelper.HasAllPermissions(PermissionCodes.ACCOUNTING_VIEW, PermissionCodes.REPORTS_VIEW);
            Console.WriteLine($"   HasAllPermissions: {hasAllPermissions} ✅");

            var isSessionExpired = SecurityHelper.IsSessionExpired();
            Console.WriteLine($"   IsSessionExpired: {isSessionExpired} ✅");

            // اختبار تسجيل النشاط
            await SecurityHelper.LogActivityAsync("اختبار النشاط", "النظام", "اختبار تسجيل نشاط المستخدم العادي");
            Console.WriteLine($"   LogActivityAsync: تم تسجيل النشاط ✅");

            Console.WriteLine("✅ اختبار المستخدم العادي نجح!");
        }

        /// <summary>
        /// اختبار مع مدير النظام
        /// </summary>
        private static async Task TestWithSystemAdmin()
        {
            Console.WriteLine("👑 اختبار مع مدير النظام:");

            // إنشاء مدير النظام
            Global_Variable.CurrentUser = new User
            {
                Id = 2,
                Username = "admin",
                FullName = "مدير النظام",
                Email = "<EMAIL>",
                IsActive = true,
                IsLocked = false,
                FailedLoginAttempts = 0,
                Permissions = new List<string>
                {
                    PermissionCodes.SYSTEM_ADMIN
                }
            };

            // اختبار الصلاحيات (مدير النظام له جميع الصلاحيات)
            var hasAccountingView = SecurityHelper.HasPermission(PermissionCodes.ACCOUNTING_VIEW);
            Console.WriteLine($"   HasPermission (ACCOUNTING_VIEW): {hasAccountingView} ✅");

            var hasSystemAdmin = SecurityHelper.HasPermission(PermissionCodes.SYSTEM_ADMIN);
            Console.WriteLine($"   HasPermission (SYSTEM_ADMIN): {hasSystemAdmin} ✅");

            var hasAnyPermission = SecurityHelper.HasAnyPermission(PermissionCodes.ACCOUNTS_DELETE, PermissionCodes.ENTRIES_DELETE);
            Console.WriteLine($"   HasAnyPermission: {hasAnyPermission} ✅");

            var hasAllPermissions = SecurityHelper.HasAllPermissions(PermissionCodes.MANAGE_USERS, PermissionCodes.MANAGE_PERMISSIONS);
            Console.WriteLine($"   HasAllPermissions: {hasAllPermissions} ✅");

            // اختبار تسجيل النشاط
            await SecurityHelper.LogActivityAsync("اختبار النشاط", "النظام", "اختبار تسجيل نشاط مدير النظام");
            Console.WriteLine($"   LogActivityAsync: تم تسجيل النشاط ✅");

            // اختبار تسجيل الخروج
            await SecurityHelper.LogoutUserAsync();
            Console.WriteLine($"   LogoutUserAsync: تم تسجيل الخروج ✅");

            Console.WriteLine("✅ اختبار مدير النظام نجح!");
        }

        /// <summary>
        /// اختبار وظائف كلمة المرور
        /// </summary>
        private static void TestPasswordFunctions()
        {
            Console.WriteLine("🔐 اختبار وظائف كلمة المرور:");

            // اختبار كلمات مرور مختلفة
            var passwords = new[]
            {
                "123",
                "password",
                "Password123",
                "MyStr0ng@Password!",
                "VeryComplexP@ssw0rd123!"
            };

            foreach (var password in passwords)
            {
                var strength = SecurityHelper.CheckPasswordStrength(password);
                var description = SecurityHelper.GetPasswordStrengthDescription(strength);
                var color = SecurityHelper.GetPasswordStrengthColor(strength);
                
                Console.WriteLine($"   كلمة المرور: '{password}' - القوة: {description} ({strength}) ✅");
            }

            Console.WriteLine("✅ اختبار وظائف كلمة المرور نجح!");
        }

        /// <summary>
        /// اختبار وظائف التشفير
        /// </summary>
        private static void TestEncryptionFunctions()
        {
            Console.WriteLine("🔒 اختبار وظائف التشفير:");

            var originalText = "هذا نص تجريبي للتشفير";
            
            // تشفير النص
            var encryptedText = SecurityHelper.EncryptText(originalText);
            Console.WriteLine($"   النص الأصلي: {originalText}");
            Console.WriteLine($"   النص المشفر: {encryptedText}");

            // فك التشفير
            var decryptedText = SecurityHelper.DecryptText(encryptedText);
            Console.WriteLine($"   النص بعد فك التشفير: {decryptedText}");

            // التحقق من صحة التشفير وفك التشفير
            var isCorrect = originalText == decryptedText;
            Console.WriteLine($"   صحة التشفير وفك التشفير: {isCorrect} ✅");

            Console.WriteLine("✅ اختبار وظائف التشفير نجح!");
        }

        /// <summary>
        /// اختبار وظائف إضافية
        /// </summary>
        public static void TestAdditionalFunctions()
        {
            Console.WriteLine("🔧 اختبار وظائف إضافية:");

            // اختبار الحصول على الصلاحيات المطلوبة للوحدات
            var modules = new[] { "المحاسبة", "الحسابات", "التقارير", "إدارة المستخدمين", "الصلاحيات" };
            
            foreach (var module in modules)
            {
                var requiredPermissions = SecurityHelper.GetRequiredPermissionsForModule(module);
                Console.WriteLine($"   الوحدة: {module} - الصلاحيات المطلوبة: {string.Join(", ", requiredPermissions)} ✅");
            }

            // اختبار قفل الحساب بسبب محاولات الدخول الفاشلة
            var lockedUser = new User { FailedLoginAttempts = 6 };
            var isLocked = SecurityHelper.IsAccountLockedDueToFailedAttempts(lockedUser);
            Console.WriteLine($"   قفل الحساب (6 محاولات فاشلة): {isLocked} ✅");

            var normalUser = new User { FailedLoginAttempts = 2 };
            var isNotLocked = SecurityHelper.IsAccountLockedDueToFailedAttempts(normalUser);
            Console.WriteLine($"   قفل الحساب (2 محاولات فاشلة): {isNotLocked} ✅");

            Console.WriteLine("✅ اختبار الوظائف الإضافية نجح!");
        }

        /// <summary>
        /// تشغيل جميع الاختبارات
        /// </summary>
        public static async Task RunAllTestsAsync()
        {
            Console.WriteLine("🚀 بدء اختبار SecurityHelper الشامل...\n");

            await TestAsync();
            Console.WriteLine();

            TestAdditionalFunctions();
            Console.WriteLine();

            Console.WriteLine("✅ انتهت جميع اختبارات SecurityHelper!");
        }
    }
}
