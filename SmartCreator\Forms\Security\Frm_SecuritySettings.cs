using SmartCreator.RJForms;
using SmartCreator.RJControls;
using System;
using System.Drawing;
using System.Windows.Forms;

namespace SmartCreator.Forms.Security
{
    /// <summary>
    /// واجهة إعدادات الأمان
    /// </summary>
    public partial class Frm_SecuritySettings : RJChildForm
    {
        public Frm_SecuritySettings()
        {
            try
            {
                InitializeComponent();
                InitializeForm();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إنشاء نموذج إعدادات الأمان: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تهيئة النموذج
        /// </summary>
        private void InitializeForm()
        {
            try
            {
                // إعداد خصائص النموذج
                this.Text = "إعدادات الأمان";
                this.FormIcon = FontAwesome.Sharp.IconChar.Shield;
                this.Caption = "إعدادات الأمان";

                // تحميل الإعدادات الحالية
                LoadCurrentSettings();

                // إعداد الأحداث
                SetupEvents();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تهيئة نموذج إعدادات الأمان: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تحميل الإعدادات الحالية
        /// </summary>
        private void LoadCurrentSettings()
        {
            try
            {
                // إعدادات كلمة المرور
                chkPasswordComplexity.Checked = true;
                numMinPasswordLength.Value = 8;
                numPasswordExpiry.Value = 90;
                chkPasswordHistory.Checked = true;
                numPasswordHistory.Value = 5;

                // إعدادات الجلسة
                numSessionTimeout.Value = 30;
                chkAutoLock.Checked = true;
                numAutoLockTime.Value = 15;

                // إعدادات تسجيل الدخول (إذا كانت العناصر موجودة)
                if (chkLockAfterFailedAttempts != null)
                {
                    chkLockAfterFailedAttempts.Checked = true;
                    numMaxFailedAttempts.Value = 3;
                    numLockoutDuration.Value = 15;
                }

                // إعدادات التدقيق (إذا كانت العناصر موجودة)
                if (chkAuditLogin != null)
                {
                    chkAuditLogin.Checked = true;
                    chkAuditPermissions.Checked = true;
                    chkAuditDataChanges.Checked = true;
                    numAuditRetention.Value = 365;
                }

                // إعدادات النسخ الاحتياطي
                chkAutoBackup.Checked = true;
                if (cmbBackupFrequency.Items.Count == 0)
                {
                    cmbBackupFrequency.Items.AddRange(new string[] { "يومي", "أسبوعي", "شهري" });
                }
                cmbBackupFrequency.SelectedIndex = 0; // يومي
                numBackupRetention.Value = 30;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الإعدادات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// إعداد الأحداث
        /// </summary>
        private void SetupEvents()
        {
            try
            {
                btnSave.Click += BtnSave_Click;
                btnReset.Click += BtnReset_Click;
                btnTestConnection.Click += BtnTestConnection_Click;
                btnBackupNow.Click += BtnBackupNow_Click;
                btnExportSettings.Click += BtnExportSettings_Click;
                btnImportSettings.Click += BtnImportSettings_Click;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إعداد الأحداث: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #region أحداث الأزرار

        private void BtnSave_Click(object sender, EventArgs e)
        {
            try
            {
                // حفظ الإعدادات
                SaveSettings();
                MessageBox.Show("تم حفظ الإعدادات بنجاح", "نجح",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ الإعدادات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnReset_Click(object sender, EventArgs e)
        {
            var result = MessageBox.Show("هل أنت متأكد من إعادة تعيين جميع الإعدادات إلى القيم الافتراضية؟",
                "تأكيد إعادة التعيين", MessageBoxButtons.YesNo, MessageBoxIcon.Warning);

            if (result == DialogResult.Yes)
            {
                try
                {
                    ResetToDefaults();
                    MessageBox.Show("تم إعادة تعيين الإعدادات إلى القيم الافتراضية", "نجح",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في إعادة تعيين الإعدادات: {ex.Message}", "خطأ",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        private void BtnTestConnection_Click(object sender, EventArgs e)
        {
            try
            {
                // اختبار الاتصال بقاعدة البيانات
                MessageBox.Show("تم اختبار الاتصال بنجاح", "نجح الاختبار",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"فشل في اختبار الاتصال: {ex.Message}", "خطأ في الاختبار",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnBackupNow_Click(object sender, EventArgs e)
        {
            try
            {
                // إنشاء نسخة احتياطية فورية
                MessageBox.Show("تم إنشاء النسخة الاحتياطية بنجاح", "نجح النسخ الاحتياطي",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إنشاء النسخة الاحتياطية: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnExportSettings_Click(object sender, EventArgs e)
        {
            try
            {
                var saveDialog = new SaveFileDialog
                {
                    Filter = "ملفات الإعدادات (*.json)|*.json",
                    Title = "تصدير إعدادات الأمان"
                };

                if (saveDialog.ShowDialog() == DialogResult.OK)
                {
                    // تصدير الإعدادات
                    MessageBox.Show("تم تصدير الإعدادات بنجاح", "نجح التصدير",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تصدير الإعدادات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnImportSettings_Click(object sender, EventArgs e)
        {
            try
            {
                var openDialog = new OpenFileDialog
                {
                    Filter = "ملفات الإعدادات (*.json)|*.json",
                    Title = "استيراد إعدادات الأمان"
                };

                if (openDialog.ShowDialog() == DialogResult.OK)
                {
                    // استيراد الإعدادات
                    MessageBox.Show("تم استيراد الإعدادات بنجاح", "نجح الاستيراد",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في استيراد الإعدادات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region طرق مساعدة

        /// <summary>
        /// حفظ الإعدادات
        /// </summary>
        private void SaveSettings()
        {
            try
            {
                // حفظ إعدادات كلمة المرور
                var passwordSettings = new
                {
                    RequireComplexity = chkPasswordComplexity.Checked,
                    MinLength = (int)numMinPasswordLength.Value,
                    ExpiryDays = (int)numPasswordExpiry.Value,
                    RememberHistory = chkPasswordHistory.Checked,
                    HistoryCount = (int)numPasswordHistory.Value
                };

                // حفظ إعدادات الجلسة
                var sessionSettings = new
                {
                    TimeoutMinutes = (int)numSessionTimeout.Value,
                    AutoLock = chkAutoLock.Checked,
                    AutoLockMinutes = (int)numAutoLockTime.Value
                };

                // حفظ إعدادات تسجيل الدخول (إذا كانت العناصر موجودة)
                object loginSettings = null;
                if (chkLockAfterFailedAttempts != null)
                {
                    loginSettings = new
                    {
                        LockAfterFailedAttempts = chkLockAfterFailedAttempts.Checked,
                        MaxFailedAttempts = (int)numMaxFailedAttempts.Value,
                        LockoutDurationMinutes = (int)numLockoutDuration.Value
                    };
                }

                // حفظ إعدادات التدقيق (إذا كانت العناصر موجودة)
                object auditSettings = null;
                if (chkAuditLogin != null)
                {
                    auditSettings = new
                    {
                        AuditLogin = chkAuditLogin.Checked,
                        AuditPermissions = chkAuditPermissions.Checked,
                        AuditDataChanges = chkAuditDataChanges.Checked,
                        RetentionDays = (int)numAuditRetention.Value
                    };
                }

                // حفظ إعدادات النسخ الاحتياطي
                var backupSettings = new
                {
                    AutoBackup = chkAutoBackup.Checked,
                    Frequency = cmbBackupFrequency.SelectedItem?.ToString(),
                    RetentionDays = (int)numBackupRetention.Value
                };

                // هنا يتم حفظ الإعدادات في قاعدة البيانات أو ملف الإعدادات
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في حفظ الإعدادات: {ex.Message}");
            }
        }

        /// <summary>
        /// إعادة تعيين الإعدادات إلى القيم الافتراضية
        /// </summary>
        private void ResetToDefaults()
        {
            try
            {
                // إعدادات كلمة المرور الافتراضية
                chkPasswordComplexity.Checked = true;
                numMinPasswordLength.Value = 8;
                numPasswordExpiry.Value = 90;
                chkPasswordHistory.Checked = true;
                numPasswordHistory.Value = 5;

                // إعدادات الجلسة الافتراضية
                numSessionTimeout.Value = 30;
                chkAutoLock.Checked = true;
                numAutoLockTime.Value = 15;

                // إعدادات تسجيل الدخول الافتراضية (إذا كانت العناصر موجودة)
                if (chkLockAfterFailedAttempts != null)
                {
                    chkLockAfterFailedAttempts.Checked = true;
                    numMaxFailedAttempts.Value = 3;
                    numLockoutDuration.Value = 15;
                }

                // إعدادات التدقيق الافتراضية (إذا كانت العناصر موجودة)
                if (chkAuditLogin != null)
                {
                    chkAuditLogin.Checked = true;
                    chkAuditPermissions.Checked = true;
                    chkAuditDataChanges.Checked = true;
                    numAuditRetention.Value = 365;
                }

                // إعدادات النسخ الاحتياطي الافتراضية
                chkAutoBackup.Checked = true;
                cmbBackupFrequency.SelectedIndex = 0; // يومي
                numBackupRetention.Value = 30;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في إعادة تعيين الإعدادات: {ex.Message}");
            }
        }

        #endregion
    }
}
