using SmartCreator.Services.Security;
using SmartCreator.Entities;
using System;
using System.Threading.Tasks;

namespace SmartCreator
{
    /// <summary>
    /// ملف اختبار للتأكد من عمل ActivityService
    /// </summary>
    public class TestActivityService
    {
        public static async Task TestAsync()
        {
            try
            {
                // إنشاء خدمة الأنشطة
                var activityService = new ActivityService();

                Console.WriteLine("✅ تم إنشاء ActivityService بنجاح");

                // تسجيل نشاط تجريبي
                await activityService.LogActivityAsync(
                    username: "admin",
                    action: "اختبار النظام",
                    module: "النظام",
                    description: "اختبار عمل خدمة الأنشطة",
                    isSuccessful: true,
                    severity: "Info"
                );

                Console.WriteLine("✅ تم تسجيل النشاط بنجاح");

                // الحصول على الأنشطة
                var (activities, totalCount) = await activityService.GetActivitiesAsync(
                    page: 1,
                    pageSize: 10
                );

                Console.WriteLine($"✅ تم الحصول على {activities.Count} نشاط من أصل {totalCount}");

                // الحصول على الإحصائيات
                var stats = await activityService.GetActivityStatisticsAsync();

                Console.WriteLine($"✅ إحصائيات الأنشطة:");
                foreach (var stat in stats)
                {
                    Console.WriteLine($"   {stat.Key}: {stat.Value}");
                }

                // اختبار الطرق الأخرى
                var modules = activityService.GetAvailableModules();
                Console.WriteLine($"✅ الوحدات المتاحة: {string.Join(", ", modules)}");

                var severityLevels = activityService.GetSeverityLevels();
                Console.WriteLine($"✅ مستويات الخطورة: {string.Join(", ", severityLevels)}");

                Console.WriteLine("🎉 جميع الاختبارات نجحت!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في الاختبار: {ex.Message}");
                Console.WriteLine($"تفاصيل الخطأ: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// اختبار UserActivity model
        /// </summary>
        public static void TestUserActivityModel()
        {
            try
            {
                // إنشاء UserActivity
                var activity = new UserActivity
                {
                    Id = 1,
                    UserId = 1,
                    Username = "test_user",
                    Action = "اختبار النموذج",
                    Module = "النظام",
                    Description = "اختبار نموذج UserActivity",
                    IsSuccessful = true,
                    Severity = "Info",
                    IpAddress = "127.0.0.1",
                    ActivityType = "اختبار",
                    Timestamp = DateTime.Now
                };

                Console.WriteLine("✅ تم إنشاء UserActivity بنجاح");
                Console.WriteLine($"   المستخدم: {activity.Username}");
                Console.WriteLine($"   الإجراء: {activity.Action}");
                Console.WriteLine($"   الوحدة: {activity.Module}");
                Console.WriteLine($"   الوصف: {activity.Description}");
                Console.WriteLine($"   نجح: {activity.IsSuccessful}");
                Console.WriteLine($"   المستوى: {activity.Severity}");
                Console.WriteLine($"   IP: {activity.IpAddress}");
                Console.WriteLine($"   التاريخ: {activity.Timestamp:yyyy-MM-dd HH:mm:ss}");

                Console.WriteLine("🎉 اختبار UserActivity نجح!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في اختبار UserActivity: {ex.Message}");
            }
        }

        /// <summary>
        /// اختبار الثوابت
        /// </summary>
        public static void TestConstants()
        {
            try
            {
                Console.WriteLine("✅ اختبار الثوابت:");

                Console.WriteLine($"   أنواع الأنشطة:");
                Console.WriteLine($"     LOGIN: {ActivityTypes.LOGIN}");
                Console.WriteLine($"     CREATE: {ActivityTypes.CREATE}");
                Console.WriteLine($"     UPDATE: {ActivityTypes.UPDATE}");
                Console.WriteLine($"     DELETE: {ActivityTypes.DELETE}");

                Console.WriteLine($"   الوحدات:");
                Console.WriteLine($"     AUTHENTICATION: {ActivityModules.AUTHENTICATION}");
                Console.WriteLine($"     USER_MANAGEMENT: {ActivityModules.USER_MANAGEMENT}");
                Console.WriteLine($"     SYSTEM: {ActivityModules.SYSTEM}");

                Console.WriteLine($"   مستويات الخطورة:");
                Console.WriteLine($"     INFO: {SeverityLevels.INFO}");
                Console.WriteLine($"     WARNING: {SeverityLevels.WARNING}");
                Console.WriteLine($"     ERROR: {SeverityLevels.ERROR}");
                Console.WriteLine($"     CRITICAL: {SeverityLevels.CRITICAL}");

                Console.WriteLine("🎉 اختبار الثوابت نجح!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في اختبار الثوابت: {ex.Message}");
            }
        }

        /// <summary>
        /// تشغيل جميع الاختبارات
        /// </summary>
        public static async Task RunAllTestsAsync()
        {
            Console.WriteLine("🚀 بدء اختبار ActivityService...\n");

            TestConstants();
            Console.WriteLine();

            TestUserActivityModel();
            Console.WriteLine();

            await TestAsync();
            Console.WriteLine();

            Console.WriteLine("✅ انتهت جميع الاختبارات!");
        }
    }
}
