using System;
using System.Windows.Forms;
using SmartCreator.Forms.Security;
using SmartCreator.Settings;
using SmartCreator.RJControls;

namespace SmartCreator.Forms.Security
{
    /// <summary>
    /// اختبار بسيط لنموذج تسجيل الدخول
    /// </summary>
    public static class Simple_Login_Test
    {
        /// <summary>
        /// تشغيل نموذج تسجيل الدخول بشكل مباشر
        /// </summary>
        public static void RunSimpleTest()
        {
            try
            {
                // تهيئة الإعدادات الأساسية
                UIAppearance.Theme = UITheme.Light;
                UIAppearance.Language_ar = true;

                // عرض رسالة ترحيب
                var result = RJMessageBox.Show(
                    "🔐 مرحباً بك في نموذج تسجيل الدخول!\n\n" +
                    "✅ تم إنشاء النموذج باستخدام Custom Controls:\n" +
                    "• RJBaseForm - النموذج الأساسي\n" +
                    "• RJPanel - اللوحات المخصصة\n" +
                    "• RJTextBox - حقول النص المتقدمة\n" +
                    "• RJButton - الأزرار المخصصة\n" +
                    "• RJLabel - التسميات المخصصة\n" +
                    "• RJCheckBox - مربع الاختيار\n\n" +
                    "هل تريد فتح نموذج تسجيل الدخول؟",
                    "نموذج تسجيل الدخول",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    // إنشاء وعرض النموذج
                    using (var loginForm = new Frm_Login())
                    {
                        var loginResult = loginForm.ShowDialog();
                        
                        if (loginResult == DialogResult.OK)
                        {
                            RJMessageBox.Show(
                                "✅ تم تسجيل الدخول بنجاح!\n\n" +
                                "النموذج يعمل بشكل مثالي مع جميع Custom Controls.",
                                "نجح تسجيل الدخول",
                                MessageBoxButtons.OK,
                                MessageBoxIcon.Information);
                        }
                        else
                        {
                            RJMessageBox.Show(
                                "تم إلغاء تسجيل الدخول.\n\n" +
                                "النموذج يعمل بدون أخطاء!",
                                "تم الإلغاء",
                                MessageBoxButtons.OK,
                                MessageBoxIcon.Information);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                RJMessageBox.Show(
                    $"خطأ في تشغيل النموذج:\n\n{ex.Message}",
                    "خطأ",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// اختبار سريع للنموذج
        /// </summary>
        public static void QuickTest()
        {
            try
            {
                Console.WriteLine("🔐 اختبار سريع لنموذج تسجيل الدخول...");

                // محاولة إنشاء النموذج 3 مرات
                for (int i = 1; i <= 3; i++)
                {
                    var form = new Frm_Login();
                    Console.WriteLine($"✅ المحاولة {i}: إنشاء Frm_Login نجح");
                    form.Dispose();
                }

                RJMessageBox.Show(
                    "✅ اختبار سريع نجح!\n\n" +
                    "تم إنشاء نموذج تسجيل الدخول 3 مرات بدون أخطاء.\n" +
                    "جميع Custom Controls تعمل بشكل مثالي!",
                    "نجح الاختبار السريع",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                RJMessageBox.Show(
                    $"❌ فشل الاختبار السريع:\n\n{ex.Message}",
                    "فشل الاختبار",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// اختبار السمات
        /// </summary>
        public static void TestThemes()
        {
            try
            {
                var result = RJMessageBox.Show(
                    "🎨 اختبار السمات\n\n" +
                    "هل تريد اختبار السمة المظلمة؟\n" +
                    "اختر نعم للسمة المظلمة أو لا للسمة الفاتحة",
                    "اختبار السمات",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    UIAppearance.Theme = UITheme.Dark;
                    RJMessageBox.Show("تم تطبيق السمة المظلمة", "السمة المظلمة", 
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    UIAppearance.Theme = UITheme.Light;
                    RJMessageBox.Show("تم تطبيق السمة الفاتحة", "السمة الفاتحة", 
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }

                // عرض النموذج بالسمة الجديدة
                using (var loginForm = new Frm_Login())
                {
                    loginForm.ShowDialog();
                }
            }
            catch (Exception ex)
            {
                RJMessageBox.Show(
                    $"خطأ في اختبار السمات:\n\n{ex.Message}",
                    "خطأ",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// عرض معلومات النموذج
        /// </summary>
        public static void ShowInfo()
        {
            RJMessageBox.Show(
                "📋 معلومات نموذج تسجيل الدخول\n\n" +
                "📁 الملفات المُنشأة:\n" +
                "• Frm_Login.cs - الكود الرئيسي\n" +
                "• Frm_Login.Designer.cs - ملف التصميم\n" +
                "• Frm_Login.resx - ملف الموارد\n" +
                "• Simple_Login_Test.cs - اختبار بسيط\n\n" +
                "🎨 Custom Controls المستخدمة:\n" +
                "• RJBaseForm من RJForms\n" +
                "• RJPanel, RJTextBox, RJButton من RJControls\n" +
                "• RJLabel, RJCheckBox من RJControls\n" +
                "• UIAppearance, RJColors من Settings\n" +
                "• utils من Utils\n\n" +
                "✨ الميزات:\n" +
                "• واجهة عربية احترافية\n" +
                "• دعم السمات المتعددة\n" +
                "• تأثيرات بصرية متقدمة\n" +
                "• أمان وحماية\n" +
                "• سهولة الاستخدام\n\n" +
                "🚀 النموذج جاهز للاستخدام!",
                "معلومات النموذج",
                MessageBoxButtons.OK,
                MessageBoxIcon.Information);
        }

        /// <summary>
        /// قائمة الاختبارات
        /// </summary>
        public static void ShowTestMenu()
        {
            try
            {
                var result = RJMessageBox.Show(
                    "🧪 قائمة اختبارات نموذج تسجيل الدخول\n\n" +
                    "اختر نوع الاختبار:\n\n" +
                    "نعم - تشغيل النموذج مباشرة\n" +
                    "لا - اختبار سريع\n" +
                    "إلغاء - عرض المعلومات",
                    "قائمة الاختبارات",
                    MessageBoxButtons.YesNoCancel,
                    MessageBoxIcon.Question);

                switch (result)
                {
                    case DialogResult.Yes:
                        RunSimpleTest();
                        break;
                    case DialogResult.No:
                        QuickTest();
                        break;
                    case DialogResult.Cancel:
                        ShowInfo();
                        break;
                }
            }
            catch (Exception ex)
            {
                RJMessageBox.Show(
                    $"خطأ في قائمة الاختبارات:\n\n{ex.Message}",
                    "خطأ",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// نقطة الدخول الرئيسية
        /// </summary>
        [STAThread]
        public static void Main()
        {
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);
            
            ShowTestMenu();
        }
    }
}
