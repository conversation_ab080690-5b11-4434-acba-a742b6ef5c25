using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using SmartCreator.Entities.Accounting;
using SmartCreator.Data;
using SmartCreator.Services.Security;
using SmartCreator.Models;

namespace SmartCreator.Services.Accounting
{
    /// <summary>
    /// خدمة إدارة القيود المحاسبية
    /// </summary>
    public class JournalEntryService
    {
        private readonly Smart_DataAccess _dataAccess;
        private readonly UserActivityService _activityService;

        public JournalEntryService(Smart_DataAccess dataAccess, UserActivityService activityService)
        {
            _dataAccess = dataAccess;
            _activityService = activityService;
        }

        // Constructor بدون معاملات للاختبار
        public JournalEntryService()
        {
            _dataAccess = null;
            _activityService = null;
        }

        #region القيود المحاسبية

        /// <summary>
        /// الحصول على جميع القيود
        /// </summary>
        public async Task<List<JournalEntry>> GetAllJournalEntriesAsync()
        {
            try
            {
                // محاكاة البيانات - يجب استبدالها بقاعدة البيانات الفعلية
                await Task.Delay(100);

                var entries = new List<JournalEntry>
                {
                    new JournalEntry
                    {
                        Id = 1,
                        EntryNumber = "JE-2024-001",
                        Date = DateTime.Today.AddDays(-5),
                        Description = "قيد افتتاحي للنقدية",
                        Type = JournalEntryType.Opening,
                        Status = JournalEntryStatus.Posted,
                        TotalDebit = 50000,
                        TotalCredit = 50000,
                        CreatedBy = "admin",
                        CreatedDate = DateTime.Now.AddDays(-5)
                    },
                    new JournalEntry
                    {
                        Id = 2,
                        EntryNumber = "JE-2024-002",
                        Date = DateTime.Today.AddDays(-3),
                        Description = "قيد مبيعات نقدية",
                        Type = JournalEntryType.Sales,
                        Status = JournalEntryStatus.Approved,
                        TotalDebit = 15000,
                        TotalCredit = 15000,
                        CreatedBy = "user1",
                        CreatedDate = DateTime.Now.AddDays(-3)
                    },
                    new JournalEntry
                    {
                        Id = 3,
                        EntryNumber = "JE-2024-003",
                        Date = DateTime.Today.AddDays(-1),
                        Description = "قيد مشتريات آجلة",
                        Type = JournalEntryType.Purchase,
                        Status = JournalEntryStatus.Draft,
                        TotalDebit = 8000,
                        TotalCredit = 8000,
                        CreatedBy = "user2",
                        CreatedDate = DateTime.Now.AddDays(-1)
                    }
                };

                return entries;
            }
            catch (Exception ex)
            {
                await _activityService.LogActivityAsync(
                    Global_Variable.CurrentUser?.Id ?? 0,
                    "GET_ALL_ENTRIES",
                    "JOURNAL_ENTRIES",
                    $"خطأ في جلب القيود: {ex.Message}",
                    severity: "Error",
                    isSuccessful: false,
                    errorMessage: ex.Message
                );
                throw;
            }
        }

        /// <summary>
        /// الحصول على قيد بالمعرف
        /// </summary>
        public async Task<JournalEntry?> GetJournalEntryByIdAsync(int id)
        {
            try
            {
                var entries = await GetAllJournalEntriesAsync();
                var entry = entries.FirstOrDefault(e => e.Id == id);

                if (entry != null)
                {
                    // تحميل التفاصيل
                    entry.Details = await GetJournalEntryDetailsAsync(id);
                }

                return entry;
            }
            catch (Exception ex)
            {
                await _activityService.LogActivityAsync(
                    Global_Variable.CurrentUser?.Id ?? 0,
                    "GET_ENTRY_BY_ID",
                    "JOURNAL_ENTRIES",
                    $"خطأ في جلب القيد {id}: {ex.Message}",
                    severity: "Error",
                    isSuccessful: false,
                    errorMessage: ex.Message
                );
                throw;
            }
        }

        /// <summary>
        /// الحصول على تفاصيل القيد
        /// </summary>
        public async Task<List<JournalEntryDetail>> GetJournalEntryDetailsAsync(int journalEntryId)
        {
            try
            {
                await Task.Delay(50);

                // محاكاة البيانات
                var details = new List<JournalEntryDetail>();

                if (journalEntryId == 1) // قيد افتتاحي
                {
                    details.AddRange(new[]
                    {
                        new JournalEntryDetail
                        {
                            Id = 1,
                            JournalEntryId = 1,
                            AccountId = 1,
                            DebitAmount = 50000,
                            CreditAmount = 0,
                            Description = "رصيد افتتاحي للنقدية",
                            LineNumber = 1
                        },
                        new JournalEntryDetail
                        {
                            Id = 2,
                            JournalEntryId = 1,
                            AccountId = 2,
                            DebitAmount = 0,
                            CreditAmount = 50000,
                            Description = "رأس المال",
                            LineNumber = 2
                        }
                    });
                }
                else if (journalEntryId == 2) // قيد مبيعات
                {
                    details.AddRange(new[]
                    {
                        new JournalEntryDetail
                        {
                            Id = 3,
                            JournalEntryId = 2,
                            AccountId = 1,
                            DebitAmount = 15000,
                            CreditAmount = 0,
                            Description = "نقدية من المبيعات",
                            LineNumber = 1
                        },
                        new JournalEntryDetail
                        {
                            Id = 4,
                            JournalEntryId = 2,
                            AccountId = 3,
                            DebitAmount = 0,
                            CreditAmount = 15000,
                            Description = "إيرادات المبيعات",
                            LineNumber = 2
                        }
                    });
                }

                return details;
            }
            catch (Exception ex)
            {
                await _activityService.LogActivityAsync(
                    Global_Variable.CurrentUser?.Id ?? 0,
                    "GET_ENTRY_DETAILS",
                    "JOURNAL_ENTRIES",
                    $"خطأ في جلب تفاصيل القيد {journalEntryId}: {ex.Message}",
                    severity: "Error",
                    isSuccessful: false,
                    errorMessage: ex.Message
                );
                throw;
            }
        }

        /// <summary>
        /// إضافة قيد جديد
        /// </summary>
        public async Task<int> AddJournalEntryAsync(JournalEntry entry)
        {
            try
            {
                // التحقق من صحة البيانات
                ValidateJournalEntry(entry);

                // محاكاة الحفظ
                await Task.Delay(200);

                // توليد رقم القيد
                entry.EntryNumber = await GenerateEntryNumberAsync(entry.Type);
                entry.Id = new Random().Next(1000, 9999);
                entry.CreatedBy = Global_Variable.CurrentUser?.Username ?? "system";
                entry.CreatedDate = DateTime.Now;

                await _activityService.LogActivityAsync(
                    Global_Variable.CurrentUser?.Id ?? 0,
                    "ADD_ENTRY",
                    "JOURNAL_ENTRIES",
                    $"تم إضافة قيد جديد: {entry.EntryNumber}",
                    severity: "Info",
                    isSuccessful: true
                );

                return entry.Id;
            }
            catch (Exception ex)
            {
                await _activityService.LogActivityAsync(
                    Global_Variable.CurrentUser?.Id ?? 0,
                    "ADD_ENTRY",
                    "JOURNAL_ENTRIES",
                    $"خطأ في إضافة القيد: {ex.Message}",
                    severity: "Error",
                    isSuccessful: false,
                    errorMessage: ex.Message
                );
                throw;
            }
        }

        /// <summary>
        /// تحديث قيد
        /// </summary>
        public async Task<bool> UpdateJournalEntryAsync(JournalEntry entry)
        {
            try
            {
                // التحقق من صحة البيانات
                ValidateJournalEntry(entry);

                // التحقق من إمكانية التعديل
                if (entry.Status == JournalEntryStatus.Posted || entry.Status == JournalEntryStatus.Approved)
                {
                    throw new InvalidOperationException("لا يمكن تعديل قيد مرحل أو معتمد");
                }

                // محاكاة التحديث
                await Task.Delay(150);

                entry.UpdatedBy = Global_Variable.CurrentUser?.Username ?? "system";
                entry.UpdatedDate = DateTime.Now;

                await _activityService.LogActivityAsync(
                    Global_Variable.CurrentUser?.Id ?? 0,
                    "UPDATE_ENTRY",
                    "JOURNAL_ENTRIES",
                    $"تم تحديث القيد: {entry.EntryNumber}",
                    severity: "Info",
                    isSuccessful: true
                );

                return true;
            }
            catch (Exception ex)
            {
                await _activityService.LogActivityAsync(
                    Global_Variable.CurrentUser?.Id ?? 0,
                    "UPDATE_ENTRY",
                    "JOURNAL_ENTRIES",
                    $"خطأ في تحديث القيد {entry.EntryNumber}: {ex.Message}",
                    severity: "Error",
                    isSuccessful: false,
                    errorMessage: ex.Message
                );
                throw;
            }
        }

        /// <summary>
        /// حذف قيد
        /// </summary>
        public async Task<bool> DeleteJournalEntryAsync(int id)
        {
            try
            {
                var entry = await GetJournalEntryByIdAsync(id);
                if (entry == null)
                {
                    throw new ArgumentException("القيد غير موجود");
                }

                // التحقق من إمكانية الحذف
                if (entry.Status == JournalEntryStatus.Posted || entry.Status == JournalEntryStatus.Approved)
                {
                    throw new InvalidOperationException("لا يمكن حذف قيد مرحل أو معتمد");
                }

                // محاكاة الحذف
                await Task.Delay(100);

                await _activityService.LogActivityAsync(
                    Global_Variable.CurrentUser?.Id ?? 0,
                    "DELETE_ENTRY",
                    "JOURNAL_ENTRIES",
                    $"تم حذف القيد: {entry.EntryNumber}",
                    severity: "Warning",
                    isSuccessful: true
                );

                return true;
            }
            catch (Exception ex)
            {
                await _activityService.LogActivityAsync(
                    Global_Variable.CurrentUser?.Id ?? 0,
                    "DELETE_ENTRY",
                    "JOURNAL_ENTRIES",
                    $"خطأ في حذف القيد {id}: {ex.Message}",
                    severity: "Error",
                    isSuccessful: false,
                    errorMessage: ex.Message
                );
                throw;
            }
        }

        #endregion

        #region طرق متزامنة للتوافق مع النماذج

        /// <summary>
        /// الحصول على جميع القيود (متزامن)
        /// </summary>
        public List<JournalEntry> GetAllJournalEntries()
        {
            return GetAllJournalEntriesAsync().Result;
        }

        /// <summary>
        /// الحصول على تفاصيل القيد (متزامن)
        /// </summary>
        public List<JournalEntryDetail> GetJournalEntryDetails(int journalEntryId)
        {
            return GetJournalEntryDetailsAsync(journalEntryId).Result;
        }

        /// <summary>
        /// حذف قيد (متزامن)
        /// </summary>
        public bool DeleteJournalEntry(int id)
        {
            return DeleteJournalEntryAsync(id).Result;
        }

        #endregion

        #region وظائف مساعدة

        /// <summary>
        /// التحقق من صحة القيد
        /// </summary>
        private void ValidateJournalEntry(JournalEntry entry)
        {
            if (string.IsNullOrWhiteSpace(entry.Description))
                throw new ArgumentException("وصف القيد مطلوب");

            if (entry.Details == null || !entry.Details.Any())
                throw new ArgumentException("يجب إضافة تفاصيل للقيد");

            if (entry.Details.Count < 2)
                throw new ArgumentException("يجب أن يحتوي القيد على حسابين على الأقل");

            entry.RecalculateTotals();

            if (!entry.IsBalanced)
                throw new ArgumentException("القيد غير متوازن - مجموع المدين يجب أن يساوي مجموع الدائن");
        }

        /// <summary>
        /// توليد رقم القيد
        /// </summary>
        private async Task<string> GenerateEntryNumberAsync(JournalEntryType type)
        {
            await Task.Delay(10);

            var prefix = JournalEntryTypeInfo.GetPrefix(type);
            var year = DateTime.Now.Year;
            var sequence = new Random().Next(1, 999);

            return $"{prefix}-{year}-{sequence:D3}";
        }

        #endregion
    }
}
