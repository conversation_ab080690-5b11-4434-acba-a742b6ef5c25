using SmartCreator.Data;
using SmartCreator.Data.CustomORM;
using System;
using System.ComponentModel;
using RequiredAttribute = SmartCreator.Data.RequiredAttribute;
//using System.ComponentModel.DataAnnotations;

namespace SmartCreator.Entities
{
    /// <summary>
    /// فئة الإشعارات
    /// </summary>
    public class Notification
    {
        /// <summary>
        /// معرف الإشعار
        /// </summary>
        [PrimaryKey, AutoIncrement, Required, Unique]
        public int Id { get; set; }

        /// <summary>
        /// عنوان الإشعار
        /// </summary>
        [Required, MaxLength(200)]
        [DisplayName("العنوان")]
        public string Title { get; set; }

        /// <summary>
        /// محتوى الإشعار
        /// </summary>
        [Required, MaxLength(1000)]
        [DisplayName("المحتوى")]
        public string Message { get; set; }

        /// <summary>
        /// نوع الإشعار
        /// </summary>
        [Required, MaxLength(50)]
        [DisplayName("النوع")]
        public string Type { get; set; }

        /// <summary>
        /// مستوى الأهمية
        /// </summary>
        [Required, MaxLength(20)]
        [DisplayName("الأهمية")]
        public string Priority { get; set; }

        /// <summary>
        /// هل تم قراءة الإشعار
        /// </summary>
        [DisplayName("مقروء")]
        public bool IsRead { get; set; } = false;

        /// <summary>
        /// تاريخ إنشاء الإشعار
        /// </summary>
        [Required]
        [DisplayName("تاريخ الإنشاء")]
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        /// <summary>
        /// تاريخ قراءة الإشعار
        /// </summary>
        [DisplayName("تاريخ القراءة")]
        public DateTime? ReadDate { get; set; }

        /// <summary>
        /// معرف المستخدم المرسل إليه الإشعار
        /// </summary>
        [DisplayName("معرف المستخدم")]
        public int? UserId { get; set; }

        /// <summary>
        /// اسم المستخدم المرسل إليه الإشعار
        /// </summary>
        [MaxLength(100)]
        [DisplayName("اسم المستخدم")]
        public string Username { get; set; }

        /// <summary>
        /// الوحدة المرتبطة بالإشعار
        /// </summary>
        [MaxLength(100)]
        [DisplayName("الوحدة")]
        public string Module { get; set; }

        /// <summary>
        /// معرف الكيان المرتبط بالإشعار
        /// </summary>
        [DisplayName("معرف الكيان")]
        public int? EntityId { get; set; }

        /// <summary>
        /// نوع الكيان المرتبط بالإشعار
        /// </summary>
        [MaxLength(100)]
        [DisplayName("نوع الكيان")]
        public string EntityType { get; set; }

        /// <summary>
        /// رابط الإجراء (إذا كان الإشعار قابل للنقر)
        /// </summary>
        [MaxLength(500)]
        [DisplayName("رابط الإجراء")]
        public string ActionUrl { get; set; }

        /// <summary>
        /// بيانات إضافية بصيغة JSON
        /// </summary>
        [DisplayName("بيانات إضافية")]
        public string AdditionalData { get; set; }

        /// <summary>
        /// تاريخ انتهاء صلاحية الإشعار
        /// </summary>
        [DisplayName("تاريخ الانتهاء")]
        public DateTime? ExpiryDate { get; set; }

        /// <summary>
        /// هل الإشعار نشط
        /// </summary>
        [DisplayName("نشط")]
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// رمز الشركة/المؤسسة
        /// </summary>
        [MaxLength(50)]
        [DisplayName("رمز الشركة")]
        public string Rb { get; set; }

        /// <summary>
        /// خاصية محسوبة لعرض حالة القراءة
        /// </summary>
        [Computed]
        [DisplayName("الحالة")]
        public string ReadStatus => IsRead ? "مقروء" : "غير مقروء";

        /// <summary>
        /// خاصية محسوبة لعرض الوقت المنقضي
        /// </summary>
        [Computed]
        [DisplayName("منذ")]
        public string TimeAgo
        {
            get
            {
                var timeSpan = DateTime.Now - CreatedDate;
                if (timeSpan.TotalMinutes < 1)
                    return "الآن";
                else if (timeSpan.TotalMinutes < 60)
                    return $"منذ {(int)timeSpan.TotalMinutes} دقيقة";
                else if (timeSpan.TotalHours < 24)
                    return $"منذ {(int)timeSpan.TotalHours} ساعة";
                else if (timeSpan.TotalDays < 30)
                    return $"منذ {(int)timeSpan.TotalDays} يوم";
                else
                    return CreatedDate.ToString("dd/MM/yyyy");
            }
        }

        /// <summary>
        /// خاصية محسوبة لعرض أيقونة حسب النوع
        /// </summary>
        [Computed]
        [DisplayName("الأيقونة")]
        public string TypeIcon
        {
            get
            {
                return Type?.ToLower() switch
                {
                    "info" => "ℹ️",
                    "warning" => "⚠️",
                    "error" => "❌",
                    "success" => "✅",
                    "security" => "🔒",
                    "system" => "⚙️",
                    "user" => "👤",
                    "update" => "🔄",
                    _ => "📢"
                };
            }
        }

        /// <summary>
        /// خاصية محسوبة لعرض لون الأهمية
        /// </summary>
        [Computed]
        [Browsable(false)]
        public string PriorityColor
        {
            get
            {
                return Priority?.ToLower() switch
                {
                    "high" => "#FF4444",
                    "medium" => "#FF8800",
                    "low" => "#4CAF50",
                    _ => "#2196F3"
                };
            }
        }

        /// <summary>
        /// تحديد الإشعار كمقروء
        /// </summary>
        public void MarkAsRead()
        {
            IsRead = true;
            ReadDate = DateTime.Now;
        }

        /// <summary>
        /// تحديد الإشعار كغير مقروء
        /// </summary>
        public void MarkAsUnread()
        {
            IsRead = false;
            ReadDate = null;
        }

        /// <summary>
        /// التحقق من انتهاء صلاحية الإشعار
        /// </summary>
        public bool IsExpired => ExpiryDate.HasValue && DateTime.Now > ExpiryDate.Value;

        /// <summary>
        /// التحقق من أن الإشعار صالح للعرض
        /// </summary>
        public bool IsValidForDisplay => IsActive && !IsExpired;
    }

    /// <summary>
    /// ثوابت أنواع الإشعارات
    /// </summary>
    public static class NotificationTypes
    {
        public const string INFO = "Info";
        public const string WARNING = "Warning";
        public const string ERROR = "Error";
        public const string SUCCESS = "Success";
        public const string SECURITY = "Security";
        public const string SYSTEM = "System";
        public const string USER = "User";
        public const string UPDATE = "Update";
    }

    /// <summary>
    /// ثوابت مستويات الأهمية
    /// </summary>
    public static class NotificationPriorities
    {
        public const string HIGH = "High";
        public const string MEDIUM = "Medium";
        public const string LOW = "Low";
        public const string NORMAL = "Normal";
    }
}
