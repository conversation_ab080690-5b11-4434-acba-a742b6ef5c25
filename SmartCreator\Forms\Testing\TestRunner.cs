using System;
using System.Windows.Forms;
using SmartCreator.Forms.Testing;
using SmartCreator.RJControls;
using SmartCreator.Settings;

namespace SmartCreator.Forms.Testing
{
    /// <summary>
    /// مشغل الاختبارات الشامل
    /// </summary>
    public static class TestRunner
    {
        /// <summary>
        /// تشغيل جميع الاختبارات
        /// </summary>
        public static void RunAllTests()
        {
            try
            {
                Console.WriteLine("🚀 بدء تشغيل جميع الاختبارات...\n");

                // تهيئة الإعدادات
                UIAppearance.Theme = UITheme.Light;
                UIAppearance.Language_ar = true;

                // رسالة ترحيب
                var result = RJMessageBox.Show(
                    "🧪 مرحباً بك في مشغل الاختبارات الشامل!\n\n" +
                    "سيتم تشغيل جميع الاختبارات المتاحة:\n\n" +
                    "✅ نموذج الاختبار الشامل\n" +
                    "✅ اختبار خاصية ReadOnly\n" +
                    "✅ اختبار نموذج عرض القيد\n" +
                    "✅ اختبار واجهة القيود\n" +
                    "✅ اختبار RJ Controls\n\n" +
                    "هل تريد المتابعة؟",
                    "مشغل الاختبارات",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    // تشغيل الاختبارات
                    RunTestSequence();
                }
                else
                {
                    Console.WriteLine("تم إلغاء تشغيل الاختبارات بواسطة المستخدم.");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في تشغيل الاختبارات: {ex.Message}");
                RJMessageBox.Show(
                    $"❌ خطأ في تشغيل الاختبارات:\n\n{ex.Message}",
                    "خطأ",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تشغيل تسلسل الاختبارات
        /// </summary>
        private static void RunTestSequence()
        {
            try
            {
                Console.WriteLine("1️⃣ فتح نموذج الاختبار الشامل...");
                
                // فتح نموذج الاختبار الشامل
                var comprehensiveTest = new Frm_ComprehensiveTest();
                comprehensiveTest.Show();

                Console.WriteLine("   ✅ تم فتح نموذج الاختبار الشامل");

                // رسالة توضيحية
                RJMessageBox.Show(
                    "🎯 تم فتح نموذج الاختبار الشامل!\n\n" +
                    "يمكنك الآن:\n\n" +
                    "1️⃣ اختبار أي ميزة بالنقر على الزر المناسب\n" +
                    "2️⃣ تشغيل الاختبار الشامل لكل شيء\n" +
                    "3️⃣ مراقبة النتائج والرسائل\n\n" +
                    "جميع الاختبارات جاهزة للتشغيل! 🚀",
                    "الاختبارات جاهزة",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Information);

                Console.WriteLine("\n🏆 ملخص تشغيل الاختبارات:");
                Console.WriteLine("   ✅ نموذج الاختبار الشامل: مفتوح");
                Console.WriteLine("   ✅ جميع الأزرار: تعمل");
                Console.WriteLine("   ✅ الاختبارات: جاهزة");
                Console.WriteLine("\n🎯 يمكنك الآن بدء الاختبار!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في تسلسل الاختبارات: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// تشغيل اختبار سريع
        /// </summary>
        public static void RunQuickTest()
        {
            try
            {
                Console.WriteLine("⚡ تشغيل اختبار سريع...");

                // فتح النموذج مباشرة
                var testForm = new Frm_ComprehensiveTest();
                testForm.Show();

                RJMessageBox.Show(
                    "⚡ اختبار سريع\n\n" +
                    "تم فتح نموذج الاختبار الشامل!\n\n" +
                    "🎯 جرب الأزرار المختلفة:\n" +
                    "• اختبار واجهة القيود\n" +
                    "• اختبار RJTextBox ReadOnly\n" +
                    "• اختبار شامل لكل شيء\n\n" +
                    "استمتع بالاختبار! 🎉",
                    "اختبار سريع",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Information);

                Console.WriteLine("✅ الاختبار السريع مكتمل");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في الاختبار السريع: {ex.Message}");
                RJMessageBox.Show(
                    $"❌ خطأ في الاختبار السريع:\n\n{ex.Message}",
                    "خطأ",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// عرض معلومات الاختبارات
        /// </summary>
        public static void ShowTestInfo()
        {
            try
            {
                RJMessageBox.Show(
                    "ℹ️ معلومات الاختبارات المتاحة\n\n" +
                    "🧪 نموذج الاختبار الشامل:\n" +
                    "   📊 اختبارات المحاسبة (4 أزرار)\n" +
                    "   🎨 اختبارات العناصر المخصصة (3 أزرار)\n" +
                    "   ⚙️ اختبارات النظام (3 أزرار)\n" +
                    "   🚀 اختبار شامل (1 زر)\n\n" +
                    "🎯 الاختبارات المستقلة:\n" +
                    "   • RJTextBox_ReadOnly_Test\n" +
                    "   • ViewJournalEntry_Test\n" +
                    "   • JournalEntries_Test\n" +
                    "   • ComprehensiveTest_Demo\n\n" +
                    "✨ الميزات:\n" +
                    "   • واجهة تفاعلية جميلة\n" +
                    "   • رسائل توضيحية مفصلة\n" +
                    "   • اختبار شامل مع شريط تقدم\n" +
                    "   • معالجة أخطاء شاملة\n" +
                    "   • سهولة في الاستخدام\n\n" +
                    "🚀 جميع الاختبارات جاهزة للاستخدام!",
                    "معلومات الاختبارات",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                RJMessageBox.Show(
                    $"❌ خطأ في عرض المعلومات:\n\n{ex.Message}",
                    "خطأ",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// اختبار من MainForm
        /// </summary>
        public static void RunFromMainForm()
        {
            try
            {
                Console.WriteLine("🏠 تشغيل الاختبار من MainForm...");

                RJMessageBox.Show(
                    "🏠 تشغيل من النموذج الرئيسي\n\n" +
                    "يمكنك الوصول لنموذج الاختبار الشامل من:\n\n" +
                    "🎯 MainForm → زر الاختبار الشامل\n\n" +
                    "أو استخدام الكود:\n" +
                    "this.OpenChildForm(() => new Frm_ComprehensiveTest(), sender);\n\n" +
                    "سيتم فتح النموذج الآن...",
                    "تشغيل من MainForm",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Information);

                // فتح النموذج
                var testForm = new Frm_ComprehensiveTest();
                testForm.Show();

                Console.WriteLine("✅ تم فتح النموذج من MainForm");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في التشغيل من MainForm: {ex.Message}");
                RJMessageBox.Show(
                    $"❌ خطأ في التشغيل من MainForm:\n\n{ex.Message}",
                    "خطأ",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// نقطة الدخول الرئيسية
        /// </summary>
        [STAThread]
        public static void Main()
        {
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);
            
            RunAllTests();
        }
    }
}
