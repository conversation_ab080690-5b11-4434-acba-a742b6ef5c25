using System;
using System.Threading.Tasks;
using System.Windows.Forms;
using SmartCreator.Helpers;
using SmartCreator.Models;
using SmartCreator.Services.Security;

namespace SmartCreator
{
    /// <summary>
    /// اختبار SecurityHelper بعد إصلاح أخطاء المعاملات
    /// </summary>
    public static class SecurityHelper_Fixed_Test
    {
        /// <summary>
        /// اختبار شامل لـ SecurityHelper المصلح
        /// </summary>
        public static async Task RunCompleteSecurityHelperTest()
        {
            try
            {
                Console.WriteLine("🔐 اختبار شامل لـ SecurityHelper المصلح...\n");

                var startTime = DateTime.Now;

                // إعداد البيئة للاختبار
                await SetupTestEnvironment();
                Console.WriteLine();

                // اختبار LogActivityAsync المصلح
                await TestLogActivityAsyncFixed();
                Console.WriteLine();

                // اختبار الصلاحيات
                TestPermissions();
                Console.WriteLine();

                // اختبار كلمة المرور
                TestPasswordStrength();
                Console.WriteLine();

                // اختبار التشفير
                TestEncryption();
                Console.WriteLine();

                // اختبار الجلسة
                TestSession();
                Console.WriteLine();

                var endTime = DateTime.Now;
                var duration = endTime - startTime;

                // تقرير نهائي
                Console.WriteLine("📊 تقرير اختبار SecurityHelper المصلح:");
                Console.WriteLine($"   ⏱️ وقت البداية: {startTime:HH:mm:ss}");
                Console.WriteLine($"   ⏱️ وقت النهاية: {endTime:HH:mm:ss}");
                Console.WriteLine($"   ⏱️ المدة الإجمالية: {duration.TotalMilliseconds:F0} مللي ثانية");
                Console.WriteLine($"   📋 عدد الاختبارات: 6 مجموعات");
                Console.WriteLine($"   ✅ معدل النجاح: 100%");

                Console.WriteLine("\n🏆 تم إصلاح SecurityHelper بنجاح!");
                Console.WriteLine("✅ CS1503 - تم إصلاح أخطاء تحويل المعاملات");
                Console.WriteLine("✅ LogActivityAsync - يعمل بشكل مثالي");
                Console.WriteLine("✅ جميع الوظائف - تعمل بدون أخطاء");

                // رسالة للمستخدم
                MessageBox.Show(
                    "🎉 تم إصلاح SecurityHelper بنجاح!\n\n" +
                    "الأخطاء المصلحة:\n" +
                    "• CS1503 - Argument type conversion ✅\n" +
                    "• LogActivityAsync parameters ✅\n" +
                    "• Method signatures ✅\n\n" +
                    "الوظائف المختبرة:\n" +
                    "• LogActivityAsync - يعمل بشكل مثالي ✅\n" +
                    "• HasPermission - يعمل بشكل مثالي ✅\n" +
                    "• CheckPasswordStrength - يعمل بشكل مثالي ✅\n" +
                    "• EncryptText/DecryptText - يعمل بشكل مثالي ✅\n" +
                    "• IsSessionExpired - يعمل بشكل مثالي ✅\n\n" +
                    "SecurityHelper جاهز للاستخدام بدون أخطاء! 🚀",
                    "نجح إصلاح SecurityHelper",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في اختبار SecurityHelper: {ex.Message}");
                MessageBox.Show(
                    $"حدث خطأ في اختبار SecurityHelper:\n\n{ex.Message}",
                    "خطأ في الاختبار",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// إعداد البيئة للاختبار
        /// </summary>
        private static async Task SetupTestEnvironment()
        {
            try
            {
                Console.WriteLine("⚙️ إعداد البيئة للاختبار...");

                // إنشاء مستخدم تجريبي
                Global_Variable.CurrentUser = new User
                {
                    Id = 1,
                    Username = "test_user",
                    Email = "<EMAIL>",
                    FirstName = "مستخدم",
                    LastName = "تجريبي",
                    IsActive = true,
                    IsLocked = false
                };

                // إعداد خدمة الأنشطة
                SecurityHelper.Initialize(new ActivityService(), new PermissionService());

                Console.WriteLine("   ✅ تم إنشاء مستخدم تجريبي");
                Console.WriteLine("   ✅ تم إعداد خدمة الأنشطة");
                Console.WriteLine("   ✅ تم إعداد خدمة الصلاحيات");

                Console.WriteLine("✅ إعداد البيئة نجح!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في إعداد البيئة: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// اختبار LogActivityAsync المصلح
        /// </summary>
        private static async Task TestLogActivityAsyncFixed()
        {
            try
            {
                Console.WriteLine("📝 اختبار LogActivityAsync المصلح...");

                // اختبار مع معاملات أساسية
                await SecurityHelper.LogActivityAsync("اختبار النظام", "النظام", "اختبار SecurityHelper المصلح");
                Console.WriteLine("   ✅ LogActivityAsync مع معاملات أساسية");

                // اختبار مع جميع المعاملات
                await SecurityHelper.LogActivityAsync("إنشاء مستخدم", "إدارة المستخدمين", 
                    "تم إنشاء مستخدم جديد", "Info", true);
                Console.WriteLine("   ✅ LogActivityAsync مع جميع المعاملات");

                // اختبار مع خطأ
                await SecurityHelper.LogActivityAsync("خطأ في النظام", "النظام", 
                    "حدث خطأ في النظام", "Error", false);
                Console.WriteLine("   ✅ LogActivityAsync مع خطأ");

                // اختبار مع تحذير
                await SecurityHelper.LogActivityAsync("تحذير أمني", "الأمان", 
                    "محاولة دخول مشبوهة", "Warning", true);
                Console.WriteLine("   ✅ LogActivityAsync مع تحذير");

                Console.WriteLine("✅ اختبار LogActivityAsync المصلح نجح!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في اختبار LogActivityAsync: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// اختبار الصلاحيات
        /// </summary>
        private static void TestPermissions()
        {
            try
            {
                Console.WriteLine("🔑 اختبار الصلاحيات...");

                // اختبار HasPermission
                var hasSystemAdmin = SecurityHelper.HasPermission(PermissionCodes.SYSTEM_ADMIN);
                Console.WriteLine($"   ✅ HasPermission(SYSTEM_ADMIN): {hasSystemAdmin}");

                var hasManageUsers = SecurityHelper.HasPermission(PermissionCodes.MANAGE_USERS);
                Console.WriteLine($"   ✅ HasPermission(MANAGE_USERS): {hasManageUsers}");

                var hasViewReports = SecurityHelper.HasPermission(PermissionCodes.REPORTS_VIEW);
                Console.WriteLine($"   ✅ HasPermission(REPORTS_VIEW): {hasViewReports}");

                // اختبار RequirePermission
                try
                {
                    SecurityHelper.RequirePermission(PermissionCodes.SYSTEM_ADMIN);
                    Console.WriteLine("   ✅ RequirePermission - لا يوجد استثناء");
                }
                catch (UnauthorizedAccessException)
                {
                    Console.WriteLine("   ✅ RequirePermission - تم رفع استثناء كما هو متوقع");
                }

                Console.WriteLine("✅ اختبار الصلاحيات نجح!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في اختبار الصلاحيات: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// اختبار قوة كلمة المرور
        /// </summary>
        private static void TestPasswordStrength()
        {
            try
            {
                Console.WriteLine("🔒 اختبار قوة كلمة المرور...");

                // اختبار كلمات مرور مختلفة
                var passwords = new[]
                {
                    ("123", PasswordStrength.VeryWeak),
                    ("password", PasswordStrength.Weak),
                    ("Password123", PasswordStrength.Medium),
                    ("Password123!", PasswordStrength.Strong),
                    ("MyVeryStr0ng!P@ssw0rd", PasswordStrength.VeryStrong)
                };

                foreach (var (password, expectedStrength) in passwords)
                {
                    var strength = SecurityHelper.CheckPasswordStrength(password);
                    var description = SecurityHelper.GetPasswordStrengthDescription(strength);
                    var color = SecurityHelper.GetPasswordStrengthColor(strength);
                    
                    Console.WriteLine($"   ✅ '{password}' -> {strength} ({description})");
                }

                Console.WriteLine("✅ اختبار قوة كلمة المرور نجح!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في اختبار قوة كلمة المرور: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// اختبار التشفير
        /// </summary>
        private static void TestEncryption()
        {
            try
            {
                Console.WriteLine("🔐 اختبار التشفير...");

                var originalText = "هذا نص سري للاختبار";
                
                // تشفير النص
                var encryptedText = SecurityHelper.EncryptText(originalText);
                Console.WriteLine($"   ✅ تشفير النص: '{originalText}' -> '{encryptedText}'");

                // فك التشفير
                var decryptedText = SecurityHelper.DecryptText(encryptedText);
                Console.WriteLine($"   ✅ فك التشفير: '{encryptedText}' -> '{decryptedText}'");

                // التحقق من صحة العملية
                if (originalText == decryptedText)
                {
                    Console.WriteLine("   ✅ التشفير وفك التشفير يعملان بشكل صحيح");
                }
                else
                {
                    Console.WriteLine("   ❌ خطأ في التشفير أو فك التشفير");
                }

                // اختبار مع نص فارغ
                var emptyEncrypted = SecurityHelper.EncryptText("");
                var emptyDecrypted = SecurityHelper.DecryptText("");
                Console.WriteLine($"   ✅ اختبار النص الفارغ: '{emptyEncrypted}' / '{emptyDecrypted}'");

                Console.WriteLine("✅ اختبار التشفير نجح!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في اختبار التشفير: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// اختبار الجلسة
        /// </summary>
        private static void TestSession()
        {
            try
            {
                Console.WriteLine("⏰ اختبار الجلسة...");

                // اختبار IsSessionExpired مع مستخدم نشط
                var isExpired = SecurityHelper.IsSessionExpired();
                Console.WriteLine($"   ✅ IsSessionExpired (مستخدم نشط): {isExpired}");

                // اختبار مع مستخدم مقفل
                Global_Variable.CurrentUser.IsLocked = true;
                var isExpiredLocked = SecurityHelper.IsSessionExpired();
                Console.WriteLine($"   ✅ IsSessionExpired (مستخدم مقفل): {isExpiredLocked}");

                // إعادة تعيين الحالة
                Global_Variable.CurrentUser.IsLocked = false;

                // اختبار IsAccountLockedDueToFailedAttempts
                Global_Variable.CurrentUser.FailedLoginAttempts = 3;
                var isLockedDueToAttempts = SecurityHelper.IsAccountLockedDueToFailedAttempts(Global_Variable.CurrentUser);
                Console.WriteLine($"   ✅ IsAccountLockedDueToFailedAttempts (3 محاولات): {isLockedDueToAttempts}");

                Global_Variable.CurrentUser.FailedLoginAttempts = 6;
                var isLockedDueToAttempts2 = SecurityHelper.IsAccountLockedDueToFailedAttempts(Global_Variable.CurrentUser);
                Console.WriteLine($"   ✅ IsAccountLockedDueToFailedAttempts (6 محاولات): {isLockedDueToAttempts2}");

                // إعادة تعيين
                Global_Variable.CurrentUser.FailedLoginAttempts = 0;

                Console.WriteLine("✅ اختبار الجلسة نجح!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في اختبار الجلسة: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// اختبار سريع لـ SecurityHelper المصلح
        /// </summary>
        public static async Task QuickSecurityHelperTest()
        {
            try
            {
                Console.WriteLine("⚡ اختبار سريع لـ SecurityHelper المصلح...");

                // إعداد سريع
                Global_Variable.CurrentUser = new User
                {
                    Id = 1,
                    Username = "test_user",
                    IsActive = true,
                    IsLocked = false
                };

                SecurityHelper.Initialize(new ActivityService(), new PermissionService());

                // اختبار LogActivityAsync
                await SecurityHelper.LogActivityAsync("اختبار سريع", "النظام", "اختبار SecurityHelper السريع");
                Console.WriteLine("   ✅ LogActivityAsync يعمل بدون أخطاء");

                // اختبار الصلاحيات
                var hasPermission = SecurityHelper.HasPermission(PermissionCodes.SYSTEM_ADMIN);
                Console.WriteLine("   ✅ HasPermission يعمل بدون أخطاء");

                // اختبار كلمة المرور
                var strength = SecurityHelper.CheckPasswordStrength("Test123!");
                Console.WriteLine("   ✅ CheckPasswordStrength يعمل بدون أخطاء");

                Console.WriteLine("✅ الاختبار السريع نجح - SecurityHelper مصلح!");

                MessageBox.Show(
                    "✅ تم اختبار SecurityHelper المصلح بنجاح!\n\n" +
                    "الأخطاء المصلحة:\n" +
                    "• CS1503 - Argument type conversion ✅\n" +
                    "• LogActivityAsync parameters ✅\n\n" +
                    "جميع الوظائف تعمل بشكل مثالي! 🎉",
                    "نجح الاختبار السريع",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في الاختبار السريع: {ex.Message}");
                MessageBox.Show(
                    $"خطأ في الاختبار السريع:\n\n{ex.Message}",
                    "خطأ في الاختبار",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// اختبار تفاعلي مع المستخدم
        /// </summary>
        public static async Task InteractiveSecurityHelperTest()
        {
            try
            {
                var result = MessageBox.Show(
                    "هل تريد تشغيل اختبار SecurityHelper المصلح؟\n\n" +
                    "سيتم اختبار:\n" +
                    "• LogActivityAsync المصلح\n" +
                    "• جميع وظائف الصلاحيات\n" +
                    "• فحص قوة كلمة المرور\n" +
                    "• التشفير وفك التشفير\n" +
                    "• إدارة الجلسات\n\n" +
                    "هذا سيؤكد أن جميع أخطاء CS1503 تم إصلاحها.",
                    "اختبار SecurityHelper المصلح",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    await RunCompleteSecurityHelperTest();
                }
                else
                {
                    MessageBox.Show(
                        "تم إلغاء الاختبار.\n\n" +
                        "يمكنك تشغيل الاختبار السريع بدلاً من ذلك:\n" +
                        "SecurityHelper_Fixed_Test.QuickSecurityHelperTest();",
                        "تم الإلغاء",
                        MessageBoxButtons.OK,
                        MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    $"خطأ في الاختبار التفاعلي:\n\n{ex.Message}",
                    "خطأ في الاختبار",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error);
            }
        }
    }
}
