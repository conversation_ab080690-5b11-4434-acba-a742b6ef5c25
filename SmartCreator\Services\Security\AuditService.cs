﻿using Dapper;
using System.Text.Json;
using SmartCreator.Data;
using SmartCreator.Entities;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
 
namespace SmartCreator.Service
{

    /// <summary>
    /// خدمة تسجيل الأحداث والمراقبة
    /// </summary>
    public class AuditService
    {
        private readonly DatabaseHelper _dbHelper;

        public AuditService(DatabaseHelper dbHelper)
        {
            _dbHelper = dbHelper;
        }

        /// <summary>
        /// تسجيل حدث
        /// </summary>
        public async Task LogAsync(
            string action,
            string entityType,
            int? entityId = null,
            string? entityName = null,
            object? oldValues = null,
            object? newValues = null,
            int? userId = null,
            string module = "System",
            AuditSeverity severity = AuditSeverity.Information,
            bool success = true,
            string? errorMessage = null,
            string? sessionId = null,
            string? ipAddress = null,
            string? userAgent = null,
            long? duration = null,
            object? additionalData = null)
        {
            try
            {
                var auditLog = new AuditLog
                {
                    UserId = userId,
                    Action = action,
                    EntityType = entityType,
                    EntityId = entityId,
                    EntityName = entityName,
                    Module = module,
                    Severity = severity,
                    Success = success,
                    ErrorMessage = errorMessage,
                    SessionId = sessionId,
                    IPAddress = ipAddress,
                    UserAgent = userAgent,
                    Duration = duration,
                    Timestamp = DateTime.Now
                };

                // تحويل القيم إلى JSON
                if (oldValues != null)
                    auditLog.OldValues = System.Text.Json.JsonSerializer.Serialize(oldValues, new JsonSerializerOptions { WriteIndented = true });

                if (newValues != null)
                    auditLog.NewValues = System.Text.Json.JsonSerializer.Serialize(newValues, new JsonSerializerOptions { WriteIndented = true });

                if (additionalData != null)
                    auditLog.AdditionalData = System.Text.Json.JsonSerializer.Serialize(additionalData, new JsonSerializerOptions { WriteIndented = true });

                // حساب التغييرات
                if (oldValues != null && newValues != null)
                    auditLog.Changes = CalculateChanges(oldValues, newValues);

                // الحصول على اسم المستخدم إذا لم يكن متوفراً
                if (userId.HasValue && string.IsNullOrEmpty(auditLog.Username))
                {
                    auditLog.Username = await GetUsernameAsync(userId.Value);
                }

                await SaveAuditLogAsync(auditLog);
            }
            catch (Exception ex)
            {
                // تسجيل خطأ في تسجيل الأحداث (يجب أن يكون هذا نادراً)
                Debug.WriteLine($"Error logging audit: {ex.Message}");
                // يمكن إضافة تسجيل في ملف أو نظام تسجيل آخر هنا
            }
        }

        /// <summary>
        /// تسجيل حدث مع قياس الوقت
        /// </summary>
        public async Task<T> LogWithTimingAsync<T>(
            Func<Task<T>> operation,
            string action,
            string entityType,
            int? entityId = null,
            string? entityName = null,
            int? userId = null,
            string module = "System",
            string? sessionId = null)
        {
            var stopwatch = Stopwatch.StartNew();
            Exception? exception = null;
            T? result = default;

            try
            {
                result = await operation();
                return result;
            }
            catch (Exception ex)
            {
                exception = ex;
                throw;
            }
            finally
            {
                stopwatch.Stop();

                await LogAsync(
                    action: action,
                    entityType: entityType,
                    entityId: entityId,
                    entityName: entityName,
                    userId: userId,
                    module: module,
                    severity: exception != null ? AuditSeverity.Error : AuditSeverity.Information,
                    success: exception == null,
                    errorMessage: exception?.Message,
                    sessionId: sessionId,
                    duration: stopwatch.ElapsedMilliseconds,
                    newValues: result
                );
            }
        }

        /// <summary>
        /// جلب جميع سجلات الأحداث
        /// </summary>
        public async Task<List<AuditLog>> GetAllAuditLogsAsync()
        {
            using var connection = _dbHelper.GetConnection();
            var sql = @"
                SELECT al.*, COALESCE(al.Username, u.Username) as Username
                FROM AuditLogs al
                LEFT JOIN Users u ON al.UserId = u.Id
                ORDER BY al.Timestamp DESC";

            var logs = await connection.QueryAsync<AuditLog>(sql);
            return logs.ToList();
        }

        /// <summary>
        /// جلب سجلات الأحداث
        /// </summary>
        public async Task<List<AuditLog>> GetAuditLogsAsync(
            int? userId = null,
            string? action = null,
            string? entityType = null,
            DateTime? fromDate = null,
            DateTime? toDate = null,
            AuditSeverity? severity = null,
            bool? success = null,
            int pageNumber = 1,
            int pageSize = 50)
        {
            using var connection = _dbHelper.GetConnection();

            var whereConditions = new List<string>();
            var parameters = new DynamicParameters();

            if (userId.HasValue)
            {
                whereConditions.Add("UserId = @UserId");
                parameters.Add("UserId", userId.Value);
            }

            if (!string.IsNullOrEmpty(action))
            {
                whereConditions.Add("Action = @Action");
                parameters.Add("Action", action);
            }

            if (!string.IsNullOrEmpty(entityType))
            {
                whereConditions.Add("EntityType = @EntityType");
                parameters.Add("EntityType", entityType);
            }

            if (fromDate.HasValue)
            {
                whereConditions.Add("Timestamp >= @FromDate");
                parameters.Add("FromDate", fromDate.Value);
            }

            if (toDate.HasValue)
            {
                whereConditions.Add("Timestamp <= @ToDate");
                parameters.Add("ToDate", toDate.Value);
            }

            if (severity.HasValue)
            {
                whereConditions.Add("Severity = @Severity");
                parameters.Add("Severity", (int)severity.Value);
            }

            if (success.HasValue)
            {
                whereConditions.Add("Success = @Success");
                parameters.Add("Success", success.Value);
            }

            var whereClause = whereConditions.Count > 0 ? "WHERE " + string.Join(" AND ", whereConditions) : "";

            var sql = $@"
                SELECT * FROM AuditLogs
                {whereClause}
                ORDER BY Timestamp DESC
                LIMIT @PageSize OFFSET @Offset";

            parameters.Add("PageSize", pageSize);
            parameters.Add("Offset", (pageNumber - 1) * pageSize);

            var logs = await connection.QueryAsync<AuditLog>(sql, parameters);
            return logs.ToList();
        }

        /// <summary>
        /// جلب إحصائيات الأحداث
        /// </summary>
        public async Task<AuditStatistics> GetAuditStatisticsAsync(DateTime? fromDate = null, DateTime? toDate = null)
        {
            using var connection = _dbHelper.GetConnection();

            var whereClause = "";
            var parameters = new DynamicParameters();

            if (fromDate.HasValue || toDate.HasValue)
            {
                var conditions = new List<string>();
                if (fromDate.HasValue)
                {
                    conditions.Add("Timestamp >= @FromDate");
                    parameters.Add("FromDate", fromDate.Value);
                }
                if (toDate.HasValue)
                {
                    conditions.Add("Timestamp <= @ToDate");
                    parameters.Add("ToDate", toDate.Value);
                }
                whereClause = "WHERE " + string.Join(" AND ", conditions);
            }

            var sql = $@"
                SELECT
                    COUNT(*) as TotalEvents,
                    COUNT(CASE WHEN Success = 1 THEN 1 END) as SuccessfulEvents,
                    COUNT(CASE WHEN Success = 0 THEN 1 END) as FailedEvents,
                    COUNT(CASE WHEN Severity = 4 THEN 1 END) as CriticalEvents,
                    COUNT(CASE WHEN Severity = 3 THEN 1 END) as ErrorEvents,
                    COUNT(CASE WHEN Severity = 2 THEN 1 END) as WarningEvents,
                    COUNT(CASE WHEN Action = 'Login' THEN 1 END) as LoginEvents,
                    COUNT(CASE WHEN Action = 'LoginFailed' THEN 1 END) as FailedLoginEvents,
                    COUNT(DISTINCT UserId) as UniqueUsers,
                    COUNT(DISTINCT EntityType) as UniqueEntityTypes
                FROM AuditLogs {whereClause}";

            var stats = await connection.QuerySingleAsync<AuditStatistics>(sql, parameters);
            return stats;
        }

        /// <summary>
        /// حذف سجلات الأحداث القديمة
        /// </summary>
        public async Task<int> CleanupOldLogsAsync(DateTime beforeDate)
        {
            using var connection = _dbHelper.GetConnection();
            var sql = "DELETE FROM AuditLogs WHERE Timestamp < @BeforeDate";
            return await connection.ExecuteAsync(sql, new { BeforeDate = beforeDate });
        }

        /// <summary>
        /// تصدير سجلات الأحداث
        /// </summary>
        public async Task<string> ExportAuditLogsAsync(
            DateTime? fromDate = null,
            DateTime? toDate = null,
            string format = "JSON")
        {
            var logs = await GetAuditLogsAsync(
                fromDate: fromDate,
                toDate: toDate,
                pageSize: int.MaxValue);

            return format.ToUpper() switch
            {
                "JSON" => System.Text.Json.JsonSerializer.Serialize(logs, new JsonSerializerOptions { WriteIndented = true }),
                "CSV" => ConvertToCsv(logs),
                _ => throw new ArgumentException("تنسيق غير مدعوم")
            };
        }

        /// <summary>
        /// حفظ سجل الحدث
        /// </summary>
        private async Task SaveAuditLogAsync(AuditLog auditLog)
        {
            using var connection = _dbHelper.GetConnection();
            var sql = @"
                INSERT INTO AuditLogs (
                    UserId, Username, Action, EntityType, EntityId, EntityName,
                    OldValues, NewValues, Changes, IPAddress, UserAgent, SessionId,
                    Module, Severity, Success, ErrorMessage, Duration, AdditionalData, Timestamp
                ) VALUES (
                    @UserId, @Username, @Action, @EntityType, @EntityId, @EntityName,
                    @OldValues, @NewValues, @Changes, @IPAddress, @UserAgent, @SessionId,
                    @Module, @Severity, @Success, @ErrorMessage, @Duration, @AdditionalData, @Timestamp
                )";

            await connection.ExecuteAsync(sql, auditLog);
        }

        /// <summary>
        /// الحصول على اسم المستخدم
        /// </summary>
        private async Task<string?> GetUsernameAsync(int userId)
        {
            try
            {
                using var connection = _dbHelper.GetConnection();
                return await connection.QuerySingleOrDefaultAsync<string>(
                    "SELECT Username FROM Users WHERE Id = @UserId", new { UserId = userId });
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// حساب التغييرات بين القيم القديمة والجديدة
        /// </summary>
        private string? CalculateChanges(object oldValues, object newValues)
        {
            try
            {
                var oldJson = System.Text.Json.JsonSerializer.Serialize(oldValues);
                var newJson = System.Text.Json.JsonSerializer.Serialize(newValues);

                var oldDict = System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, object>>(oldJson);
                var newDict = System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, object>>(newJson);

                var changes = new Dictionary<string, object>();

                if (oldDict != null && newDict != null)
                {
                    foreach (var key in newDict.Keys)
                    {
                        if (!oldDict.ContainsKey(key) || !Equals(oldDict[key], newDict[key]))
                        {
                            changes[key] = new { Old = oldDict.GetValueOrDefault(key), New = newDict[key] };
                        }
                    }
                }

                return changes.Count > 0 ? System.Text.Json.JsonSerializer.Serialize(changes, new JsonSerializerOptions { WriteIndented = true }) : null;
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// تحويل إلى CSV
        /// </summary>
        private string ConvertToCsv(List<AuditLog> logs)
        {
            var csv = new StringBuilder();
            csv.AppendLine("Timestamp,Username,Action,EntityType,EntityName,Module,Severity,Success,ErrorMessage");

            foreach (var log in logs)
            {
                csv.AppendLine($"{log.Timestamp:yyyy-MM-dd HH:mm:ss},{log.Username},{log.Action},{log.EntityType},{log.EntityName},{log.Module},{log.SeverityText},{log.Success},{log.ErrorMessage}");
            }

            return csv.ToString();
        }
    }

    /// <summary>
    /// إحصائيات الأحداث
    /// </summary>
    public class AuditStatistics
    {
        public int TotalEvents { get; set; }
        public int SuccessfulEvents { get; set; }
        public int FailedEvents { get; set; }
        public int CriticalEvents { get; set; }
        public int ErrorEvents { get; set; }
        public int WarningEvents { get; set; }
        public int LoginEvents { get; set; }
        public int FailedLoginEvents { get; set; }
        public int UniqueUsers { get; set; }
        public int UniqueEntityTypes { get; set; }

        public double SuccessRate => TotalEvents > 0 ? (double)SuccessfulEvents / TotalEvents * 100 : 0;
        public double FailureRate => TotalEvents > 0 ? (double)FailedEvents / TotalEvents * 100 : 0;
        public double LoginSuccessRate => (LoginEvents + FailedLoginEvents) > 0 ? (double)LoginEvents / (LoginEvents + FailedLoginEvents) * 100 : 0;
    }

    public static class Extensions
    {
        public static TValue GetValueOrDefault<TKey, TValue>(this Dictionary<TKey, TValue> dict, TKey key)
          => dict.TryGetValue(key, out var value) ? value : default(TValue);
    }

}
