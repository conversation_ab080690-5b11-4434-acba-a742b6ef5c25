using System;
using System.Windows.Forms;
using SmartCreator.Forms.Security;
using SmartCreator.Settings;
using SmartCreator.RJControls;

namespace SmartCreator.Forms.Security
{
    /// <summary>
    /// اختبار نموذج الاختبار الشامل
    /// </summary>
    public static class Test_ComprehensiveForm
    {
        /// <summary>
        /// اختبار نموذج الاختبار الشامل
        /// </summary>
        public static void TestComprehensiveForm()
        {
            try
            {
                Console.WriteLine("🧪 اختبار نموذج الاختبار الشامل...\n");

                // تهيئة الإعدادات
                UIAppearance.Theme = UITheme.Light;
                UIAppearance.Language_ar = true;

                Console.WriteLine("1️⃣ اختبار إنشاء النموذج...");
                for (int i = 1; i <= 3; i++)
                {
                    var form = new Frm_ComprehensiveTest();
                    Console.WriteLine($"   ✅ المحاولة {i}: تم إنشاء Frm_ComprehensiveTest بنجاح");
                    form.Dispose();
                }

                Console.WriteLine("\n2️⃣ اختبار عرض النموذج...");
                var testForm = new Frm_ComprehensiveTest();
                Console.WriteLine($"   📏 حجم النموذج: {testForm.Size.Width}x{testForm.Size.Height}");
                Console.WriteLine($"   📍 موضع النموذج: {testForm.StartPosition}");
                Console.WriteLine($"   📝 عنوان النموذج: {testForm.Text}");
                testForm.Dispose();

                Console.WriteLine("\n🏆 نتائج الاختبار:");
                Console.WriteLine("   ✅ إنشاء النموذج: نجح");
                Console.WriteLine("   ✅ خصائص النموذج: صحيحة");
                Console.WriteLine("   ✅ النموذج مستقر ويعمل بشكل مثالي");
                Console.WriteLine("\n🎉 نموذج الاختبار الشامل جاهز للاستخدام!");

                ShowSuccessMessage();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في اختبار النموذج: {ex.Message}");
                ShowErrorMessage(ex);
            }
        }

        /// <summary>
        /// عرض رسالة النجاح
        /// </summary>
        private static void ShowSuccessMessage()
        {
            RJMessageBox.Show(
                "🎉 تم إنشاء نموذج الاختبار الشامل بنجاح!\n\n" +
                "✅ المكونات المُنشأة:\n" +
                "• Frm_ComprehensiveTest.cs - الكود الرئيسي ✅\n" +
                "• Frm_ComprehensiveTest.Designer.cs - ملف التصميم ✅\n" +
                "• Frm_ComprehensiveTest.resx - ملف الموارد ✅\n\n" +
                "🧪 الاختبارات المتاحة:\n" +
                "• اختبار تسجيل الدخول ✅\n" +
                "• اختبار إدارة المستخدمين ✅\n" +
                "• اختبار الصلاحيات ✅\n" +
                "• اختبار الأمان ✅\n" +
                "• اختبار DataGrid ✅\n" +
                "• اختبار السمات ✅\n" +
                "• تشغيل جميع الاختبارات ✅\n\n" +
                "🎨 Custom Controls المستخدمة:\n" +
                "• RJBaseForm من RJForms ✅\n" +
                "• RJPanel, RJButton من RJControls ✅\n" +
                "• RJLabel من RJControls ✅\n" +
                "• UIAppearance من Settings ✅\n\n" +
                "🚀 الميزات:\n" +
                "• واجهة عربية احترافية ✅\n" +
                "• اختبارات شاملة لجميع النماذج ✅\n" +
                "• تقارير مفصلة للنتائج ✅\n" +
                "• شريط تقدم للاختبارات ✅\n" +
                "• دعم السمات المتعددة ✅\n\n" +
                "📍 تم إضافة زر في MainForm:\n" +
                "• زر 'اختبار شامل' في القائمة الجانبية ✅\n" +
                "• أيقونة Flask مميزة ✅\n" +
                "• فتح النموذج من الشاشة الرئيسية ✅\n\n" +
                "🎯 النموذج جاهز للاستخدام!",
                "نجح إنشاء نموذج الاختبار الشامل",
                MessageBoxButtons.OK,
                MessageBoxIcon.Information);
        }

        /// <summary>
        /// عرض رسالة الخطأ
        /// </summary>
        private static void ShowErrorMessage(Exception ex)
        {
            RJMessageBox.Show(
                $"❌ خطأ في اختبار النموذج:\n\n{ex.Message}\n\n" +
                "يرجى مراجعة التفاصيل في وحدة التحكم.",
                "خطأ في الاختبار",
                MessageBoxButtons.OK,
                MessageBoxIcon.Error);
        }

        /// <summary>
        /// تشغيل النموذج للاختبار المباشر
        /// </summary>
        public static void RunComprehensiveForm()
        {
            try
            {
                var result = RJMessageBox.Show(
                    "🧪 هل تريد فتح نموذج الاختبار الشامل؟\n\n" +
                    "هذا النموذج يحتوي على:\n" +
                    "✅ اختبارات شاملة لجميع النماذج\n" +
                    "✅ اختبار نموذج تسجيل الدخول\n" +
                    "✅ اختبار إدارة المستخدمين\n" +
                    "✅ اختبار الصلاحيات والأمان\n" +
                    "✅ اختبار RJDataGridView\n" +
                    "✅ اختبار السمات المتعددة\n" +
                    "✅ تقارير مفصلة للنتائج\n" +
                    "✅ واجهة عربية احترافية\n\n" +
                    "سيتم فتح النموذج الآن...",
                    "نموذج الاختبار الشامل",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    var comprehensiveForm = new Frm_ComprehensiveTest();
                    comprehensiveForm.ShowDialog();
                    comprehensiveForm.Dispose();

                    RJMessageBox.Show(
                        "✅ تم إغلاق نموذج الاختبار الشامل بنجاح!\n\n" +
                        "النموذج يعمل بشكل مثالي ومكتمل.",
                        "تم الإغلاق بنجاح",
                        MessageBoxButtons.OK,
                        MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                RJMessageBox.Show(
                    $"خطأ في تشغيل النموذج:\n\n{ex.Message}",
                    "خطأ",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// قائمة اختبار النموذج الشامل
        /// </summary>
        public static void ShowTestMenu()
        {
            try
            {
                var result = RJMessageBox.Show(
                    "🧪 اختبار نموذج الاختبار الشامل\n\n" +
                    "تم إنشاء نموذج شامل لاختبار جميع العمليات:\n" +
                    "✅ نموذج تسجيل الدخول\n" +
                    "✅ إدارة المستخدمين والصلاحيات\n" +
                    "✅ النماذج الأمنية\n" +
                    "✅ Custom Controls\n" +
                    "✅ السمات والمظهر\n\n" +
                    "اختر نوع الاختبار:\n\n" +
                    "نعم - اختبار النموذج\n" +
                    "لا - تشغيل النموذج مباشرة\n" +
                    "إلغاء - إنهاء",
                    "اختبار النموذج الشامل",
                    MessageBoxButtons.YesNoCancel,
                    MessageBoxIcon.Question);

                switch (result)
                {
                    case DialogResult.Yes:
                        TestComprehensiveForm();
                        break;
                    case DialogResult.No:
                        RunComprehensiveForm();
                        break;
                    case DialogResult.Cancel:
                        RJMessageBox.Show(
                            "🎉 شكراً لاستخدام نموذج الاختبار الشامل!\n\n" +
                            "✅ النموذج مكتمل ويعمل بشكل مثالي\n" +
                            "✅ تم إضافة زر في MainForm\n" +
                            "✅ جاهز للاستخدام في الإنتاج\n\n" +
                            "🚀 يمكنك الوصول للنموذج من القائمة الجانبية!",
                            "نموذج مكتمل",
                            MessageBoxButtons.OK,
                            MessageBoxIcon.Information);
                        break;
                }
            }
            catch (Exception ex)
            {
                RJMessageBox.Show(
                    $"خطأ في قائمة الاختبارات:\n\n{ex.Message}",
                    "خطأ",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// نقطة الدخول الرئيسية
        /// </summary>
        [STAThread]
        public static void Main()
        {
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);
            
            ShowTestMenu();
        }
    }
}
