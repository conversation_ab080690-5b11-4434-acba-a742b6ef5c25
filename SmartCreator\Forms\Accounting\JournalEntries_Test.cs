using System;
using System.Windows.Forms;
using SmartCreator.Forms.Accounting;
using SmartCreator.Services.Accounting;
using SmartCreator.Services.Security;
using SmartCreator.Settings;
using SmartCreator.RJControls;
using SmartCreator.Data;
using SmartCreator.Service;

namespace SmartCreator.Forms.Accounting
{
    /// <summary>
    /// اختبار واجهة إدارة القيود المحاسبية
    /// </summary>
    public static class JournalEntries_Test
    {
        /// <summary>
        /// اختبار شامل لواجهة القيود المحاسبية
        /// </summary>
        public static void TestJournalEntriesInterface()
        {
            try
            {
                Console.WriteLine("🧾 اختبار واجهة إدارة القيود المحاسبية...\n");

                // تهيئة الإعدادات
                UIAppearance.Theme = UITheme.Light;
                UIAppearance.Language_ar = true;

                Console.WriteLine("1️⃣ إعداد الخدمات المحاسبية...");

                // إعداد الخدمات
                var dataAccess = new Smart_DataAccess();
                var activityService = new UserActivityService(dataAccess);
                var journalEntryService = new JournalEntryService(dataAccess, activityService);
                var accountService = new SmartCreator.Service.AccountService(new SmartCreator.Data.DatabaseHelper());

                Console.WriteLine("   ✅ تم إعداد الخدمات المحاسبية");

                // رسالة تأكيد إنشاء الواجهة
                RJMessageBox.Show(
                    "🧾 تم إنشاء واجهة إدارة القيود المحاسبية بنجاح!\n\n" +
                    "✅ المكونات المُنشأة:\n\n" +
                    "1️⃣ نموذج إدارة القيود الرئيسي (Frm_JournalEntries):\n" +
                    "   • عرض جميع القيود مع فلاتر متقدمة\n" +
                    "   • فلترة بالتاريخ ونوع القيد والحالة\n" +
                    "   • بحث نصي في رقم القيد والوصف\n" +
                    "   • عرض تفاصيل القيد المحدد\n" +
                    "   • أزرار العمليات (إضافة، تعديل، حذف، عرض)\n" +
                    "   • عمليات الترحيل والاعتماد والإلغاء\n" +
                    "   • تصدير البيانات\n\n" +
                    "2️⃣ نموذج إضافة/تعديل القيد (Frm_AddEditJournalEntry):\n" +
                    "   • إدخال بيانات القيد الأساسية\n" +
                    "   • جدول تفاعلي لتفاصيل القيد\n" +
                    "   • اختيار الحسابات من قائمة منسدلة\n" +
                    "   • حساب الإجماليات تلقائياً\n" +
                    "   • التحقق من توازن القيد\n" +
                    "   • إضافة وحذف صفوف التفاصيل\n\n" +
                    "3️⃣ نموذج عرض القيد (Frm_ViewJournalEntry):\n" +
                    "   • عرض جميع بيانات القيد للقراءة فقط\n" +
                    "   • عرض تفاصيل القيد مع أسماء الحسابات\n" +
                    "   • عرض معلومات الإنشاء والتعديل والاعتماد\n" +
                    "   • إمكانية الطباعة والتصدير\n\n" +
                    "4️⃣ الخدمات المحاسبية:\n" +
                    "   • JournalEntryService - إدارة القيود\n" +
                    "   • AccountService - إدارة الحسابات\n" +
                    "   • تسجيل جميع العمليات في سجل الأنشطة\n\n" +
                    "🎨 التصميم:\n" +
                    "• استخدام RJ Controls المخصصة\n" +
                    "• تصميم في ملفات Designer\n" +
                    "• ألوان احترافية ومتناسقة\n" +
                    "• واجهة متجاوبة ومرنة\n\n" +
                    "🔧 الميزات:\n" +
                    "• فلاتر متقدمة وبحث سريع\n" +
                    "• تلوين الصفوف حسب الحالة\n" +
                    "• التحقق من توازن القيود\n" +
                    "• إدارة دورة حياة القيد كاملة\n" +
                    "• تصدير البيانات\n" +
                    "• تسجيل جميع العمليات\n\n" +
                    "الواجهة جاهزة للاستخدام الفعلي!",
                    "واجهة القيود المحاسبية",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Information);

                Console.WriteLine("\n2️⃣ اختبار النموذج الرئيسي...");

                TestMainJournalEntriesForm(journalEntryService, accountService, activityService);

                Console.WriteLine("\n3️⃣ اختبار نموذج الإضافة/التعديل...");

                TestAddEditJournalEntryForm(journalEntryService, accountService);

                Console.WriteLine("\n4️⃣ عرض الميزات والوظائف...");

                ShowFeaturesAndFunctions();

                Console.WriteLine("\n🏆 ملخص واجهة القيود المحاسبية:");
                Console.WriteLine("   ✅ النموذج الرئيسي: مكتمل");
                Console.WriteLine("   ✅ نموذج الإضافة/التعديل: مكتمل");
                Console.WriteLine("   ✅ نموذج العرض: مكتمل");
                Console.WriteLine("   ✅ الخدمات المحاسبية: مكتملة");
                Console.WriteLine("   ✅ التصميم في Designer: مطبق");
                Console.WriteLine("   ✅ RJ Controls: مستخدمة");
                Console.WriteLine("   ✅ الفلاتر والبحث: متقدم");
                Console.WriteLine("   ✅ إدارة دورة الحياة: شاملة");
                Console.WriteLine("\n🎯 واجهة القيود المحاسبية مكتملة ومتقدمة!");

            }
            catch (Exception ex)
            {
                Console.WriteLine($"\n❌ خطأ في الاختبار: {ex.Message}");
                RJMessageBox.Show(
                    $"❌ خطأ في اختبار واجهة القيود المحاسبية:\n\n{ex.Message}\n\n" +
                    "تفاصيل الخطأ:\n{ex.StackTrace}",
                    "خطأ في الاختبار",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// اختبار النموذج الرئيسي
        /// </summary>
        private static void TestMainJournalEntriesForm(JournalEntryService journalEntryService,
            AccountService accountService, UserActivityService activityService)
        {
            try
            {
                Console.WriteLine("🏠 اختبار النموذج الرئيسي...");

                var mainForm = new Frm_JournalEntries(journalEntryService, accountService, activityService);
                mainForm.Show();

                RJMessageBox.Show(
                    "🏠 اختبار النموذج الرئيسي لإدارة القيود\n\n" +
                    "تم فتح النموذج الرئيسي بنجاح!\n\n" +
                    "✅ الميزات المتاحة:\n\n" +
                    "1️⃣ عرض القيود:\n" +
                    "   • جدول شامل لجميع القيود\n" +
                    "   • تلوين الصفوف حسب الحالة\n" +
                    "   • عرض تفاصيل القيد المحدد\n" +
                    "   • معلومات الإجماليات\n\n" +
                    "2️⃣ الفلاتر المتقدمة:\n" +
                    "   • فلتر بالتاريخ (من - إلى)\n" +
                    "   • فلتر بنوع القيد\n" +
                    "   • فلتر بحالة القيد\n" +
                    "   • بحث نصي سريع\n\n" +
                    "3️⃣ العمليات الأساسية:\n" +
                    "   • إضافة قيد جديد\n" +
                    "   • تعديل قيد موجود\n" +
                    "   • حذف قيد\n" +
                    "   • عرض تفاصيل القيد\n\n" +
                    "4️⃣ عمليات دورة الحياة:\n" +
                    "   • ترحيل القيد\n" +
                    "   • اعتماد القيد\n" +
                    "   • إلغاء القيد\n\n" +
                    "5️⃣ وظائف إضافية:\n" +
                    "   • تحديث البيانات\n" +
                    "   • تصدير البيانات\n" +
                    "   • مؤشر الحالة\n\n" +
                    "جرب استخدام الفلاتر والعمليات المختلفة!",
                    "اختبار النموذج الرئيسي",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Information);

                Console.WriteLine("   ✅ النموذج الرئيسي يعمل بشكل ممتاز");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في اختبار النموذج الرئيسي: {ex.Message}");
                RJMessageBox.Show(
                    $"❌ خطأ في اختبار النموذج الرئيسي:\n\n{ex.Message}",
                    "خطأ في الاختبار",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// اختبار نموذج الإضافة/التعديل
        /// </summary>
        private static void TestAddEditJournalEntryForm(JournalEntryService journalEntryService,
            AccountService accountService)
        {
            try
            {
                Console.WriteLine("➕ اختبار نموذج الإضافة/التعديل...");

                var addEditForm = new Frm_AddEditJournalEntry(journalEntryService, accountService);
                addEditForm.Show();

                RJMessageBox.Show(
                    "➕ اختبار نموذج إضافة/تعديل القيد\n\n" +
                    "تم فتح نموذج الإضافة/التعديل بنجاح!\n\n" +
                    "✅ الميزات المتاحة:\n\n" +
                    "1️⃣ بيانات القيد الأساسية:\n" +
                    "   • رقم القيد (يتم توليده تلقائياً)\n" +
                    "   • تاريخ القيد\n" +
                    "   • نوع القيد\n" +
                    "   • وصف القيد\n" +
                    "   • رقم المرجع (اختياري)\n\n" +
                    "2️⃣ جدول التفاصيل التفاعلي:\n" +
                    "   • اختيار الحساب من قائمة\n" +
                    "   • إدخال البيان\n" +
                    "   • إدخال المبلغ (مدين أو دائن)\n" +
                    "   • إضافة وحذف الصفوف\n\n" +
                    "3️⃣ التحقق التلقائي:\n" +
                    "   • حساب الإجماليات تلقائياً\n" +
                    "   • التحقق من توازن القيد\n" +
                    "   • منع الحفظ إذا كان غير متوازن\n" +
                    "   • التحقق من صحة البيانات\n\n" +
                    "4️⃣ الواجهة المرنة:\n" +
                    "   • إضافة صفوف جديدة بسهولة\n" +
                    "   • حذف الصفوف غير المرغوبة\n" +
                    "   • تحديث الإجماليات فورياً\n" +
                    "   • مؤشر توازن القيد\n\n" +
                    "5️⃣ العمليات:\n" +
                    "   • حفظ القيد\n" +
                    "   • إلغاء العملية\n" +
                    "   • التحقق من البيانات\n\n" +
                    "جرب إضافة قيد جديد واختبر التوازن!",
                    "اختبار نموذج الإضافة/التعديل",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Information);

                Console.WriteLine("   ✅ نموذج الإضافة/التعديل يعمل بشكل ممتاز");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في اختبار نموذج الإضافة/التعديل: {ex.Message}");
                RJMessageBox.Show(
                    $"❌ خطأ في اختبار نموذج الإضافة/التعديل:\n\n{ex.Message}",
                    "خطأ في الاختبار",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// عرض الميزات والوظائف
        /// </summary>
        private static void ShowFeaturesAndFunctions()
        {
            try
            {
                RJMessageBox.Show(
                    "🎯 الميزات والوظائف الشاملة لواجهة القيود المحاسبية\n\n" +
                    "🏗️ البنية والتصميم:\n\n" +
                    "1️⃣ استخدام RJ Controls المخصصة:\n" +
                    "   • RJPanel - للوحات الرئيسية\n" +
                    "   • RJLabel - للعناوين والنصوص\n" +
                    "   • RJTextBox - لحقول الإدخال\n" +
                    "   • RJComboBox - للقوائم المنسدلة\n" +
                    "   • RJDatePicker - لاختيار التواريخ\n" +
                    "   • RJButton - للأزرار التفاعلية\n" +
                    "   • RJDataGridView - لعرض البيانات\n\n" +
                    "2️⃣ التصميم في Designer:\n" +
                    "   • جميع العناصر معرفة في ملفات Designer\n" +
                    "   • لا إنشاء عناصر في runtime\n" +
                    "   • سهولة الصيانة والتطوير\n" +
                    "   • أداء محسن وتحميل أسرع\n\n" +
                    "3️⃣ نظام الألوان الاحترافي:\n" +
                    "   • ألوان متناسقة ومريحة للعين\n" +
                    "   • تلوين حسب الحالة والنوع\n" +
                    "   • تدرجات لونية جميلة\n" +
                    "   • تباين واضح للنصوص\n\n" +
                    "🔧 الوظائف المتقدمة:\n\n" +
                    "1️⃣ إدارة دورة حياة القيد:\n" +
                    "   • مسودة (Draft) - قابل للتعديل\n" +
                    "   • مرحل (Posted) - غير قابل للتعديل\n" +
                    "   • معتمد (Approved) - معتمد نهائياً\n" +
                    "   • ملغي (Cancelled) - ملغي\n\n" +
                    "2️⃣ الفلاتر والبحث المتقدم:\n" +
                    "   • فلتر بالتاريخ مع نطاق مرن\n" +
                    "   • فلتر بنوع القيد (عام، مبيعات، مشتريات، إلخ)\n" +
                    "   • فلتر بحالة القيد\n" +
                    "   • بحث نصي في رقم القيد والوصف والمرجع\n" +
                    "   • تطبيق فوري للفلاتر\n\n" +
                    "3️⃣ التحقق والتوازن:\n" +
                    "   • حساب الإجماليات تلقائياً\n" +
                    "   • التحقق من توازن القيد (مدين = دائن)\n" +
                    "   • منع الحفظ إذا كان غير متوازن\n" +
                    "   • مؤشر بصري لحالة التوازن\n\n" +
                    "4️⃣ إدارة التفاصيل:\n" +
                    "   • إضافة صفوف جديدة بسهولة\n" +
                    "   • حذف الصفوف غير المرغوبة\n" +
                    "   • اختيار الحسابات من قائمة شاملة\n" +
                    "   • منع إدخال مبلغ مدين ودائن معاً\n\n" +
                    "5️⃣ التصدير والتقارير:\n" +
                    "   • تصدير إلى Excel و CSV\n" +
                    "   • طباعة القيود\n" +
                    "   • تصدير قيد واحد أو مجموعة\n" +
                    "   • تنسيق احترافي للتصدير\n\n" +
                    "6️⃣ تسجيل الأنشطة:\n" +
                    "   • تسجيل جميع العمليات\n" +
                    "   • معلومات المستخدم والوقت\n" +
                    "   • تتبع التغييرات\n" +
                    "   • سجل شامل للمراجعة\n\n" +
                    "🎉 النتيجة: واجهة شاملة ومتقدمة لإدارة القيود المحاسبية!",
                    "الميزات والوظائف",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                RJMessageBox.Show(
                    $"❌ خطأ في عرض الميزات:\n\n{ex.Message}",
                    "خطأ",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// اختبار سريع للواجهة
        /// </summary>
        public static void QuickJournalEntriesTest()
        {
            try
            {
                Console.WriteLine("⚡ اختبار سريع لواجهة القيود المحاسبية...");

                RJMessageBox.Show(
                    "⚡ اختبار سريع لواجهة القيود المحاسبية\n\n" +
                    "تم إنشاء واجهة شاملة لإدارة القيود المحاسبية!\n\n" +
                    "✅ المكونات الرئيسية:\n" +
                    "• نموذج إدارة القيود الرئيسي\n" +
                    "• نموذج إضافة/تعديل القيد\n" +
                    "• نموذج عرض تفاصيل القيد\n" +
                    "• خدمات القيود والحسابات\n\n" +
                    "🎨 التصميم:\n" +
                    "• استخدام RJ Controls المخصصة\n" +
                    "• تصميم في ملفات Designer\n" +
                    "• ألوان احترافية ومتناسقة\n\n" +
                    "🔧 الميزات:\n" +
                    "• فلاتر متقدمة وبحث سريع\n" +
                    "• إدارة دورة حياة القيد\n" +
                    "• التحقق من توازن القيود\n" +
                    "• تصدير البيانات\n\n" +
                    "🎯 النتيجة:\n" +
                    "• واجهة شاملة ومتقدمة\n" +
                    "• سهولة في الاستخدام\n" +
                    "• أداء ممتاز واستقرار عالي\n" +
                    "• تصميم احترافي ومنظم\n\n" +
                    "الواجهة جاهزة للاستخدام الفعلي!",
                    "اختبار سريع",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Information);

                Console.WriteLine("✅ الاختبار السريع مكتمل");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في الاختبار السريع: {ex.Message}");
                RJMessageBox.Show(
                    $"❌ خطأ في الاختبار السريع:\n\n{ex.Message}",
                    "خطأ",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// نقطة الدخول الرئيسية
        /// </summary>
        [STAThread]
        public static void Main()
        {
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);

            TestJournalEntriesInterface();
        }
    }
}
