﻿using System;
using System.Collections.Generic;

namespace SmartCreator.Entities.Accounting
{
    /// <summary>
    /// نموذج الحساب في شجرة الحسابات
    /// </summary>
    public class Account : BaseEntity
    {
        //public int Id { get; set; }
        public string Code { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string NameEnglish { get; set; } = string.Empty;
        public AccountType Type { get; set; }
        public AccountNature Nature { get; set; }
        public int? ParentId { get; set; }
        public int? PartnerId { get; set; }
        public int Level { get; set; }
        public bool IsParent { get; set; }
        public bool IsActive { get; set; } = true;
        public decimal Balance { get; set; } = 0;
        public string Description { get; set; } = string.Empty;
        //public DateTime CreatedDate { get; set; }
        //public DateTime? UpdatedDate { get; set; }
        //public string Rb { get; set; }
        // Navigation Properties
        public Account Parent { get; set; }
        public List<Account> Children { get; set; } = new();

        // Computed Properties
        public string FullCode => GetFullCode();
        public string FullName => GetFullName();
        public decimal DebitBalance => Nature == AccountNature.Debit && Balance >= 0 ? Balance : 0;
        public decimal CreditBalance => Nature == AccountNature.Credit && Balance >= 0 ? Balance : 0;


        private string GetFullCode()
        {
            if (Parent == null) return Code;
            return $"{Parent.GetFullCode()}-{Code}";
        }

        private string GetFullName()
        {
            if (Parent == null) return Name;
            return $"{Parent.GetFullName()} / {Name}";
        }
    }

    /// <summary>
    /// أنواع الحسابات الرئيسية
    /// </summary>
    public enum AccountType
    {
        Assets = 1,         // الأصول
        Liabilities = 2,    // الخصوم
        Equity = 3,         // حقوق الملكية
        Revenue = 4,        // الإيرادات
        Expenses = 5,       // المصروفات
        OtherIncome = 6,    // إيرادات أخرى
        OtherExpenses = 7   // مصروفات أخرى
    }

    /// <summary>
    /// طبيعة الحساب (مدين أو دائن)
    /// </summary>
    public enum AccountNature
    {
        Debit = 1,   // مدين
        Credit = 2   // دائن
    }

    /// <summary>
    /// معلومات إضافية عن أنواع الحسابات
    /// </summary>
    public static class AccountTypeInfo
    {
        public static readonly Dictionary<AccountType, (string ArabicName, string EnglishName, AccountNature Nature)> Types =
            new()
            {
                { AccountType.Assets, ("الأصول", "Assets", AccountNature.Debit) },
                { AccountType.Liabilities, ("الخصوم", "Liabilities", AccountNature.Credit) },
                { AccountType.Equity, ("حقوق الملكية", "Equity", AccountNature.Credit) },
                { AccountType.Revenue, ("الإيرادات", "Revenue", AccountNature.Credit) },
                { AccountType.Expenses, ("المصروفات", "Expenses", AccountNature.Debit) },
                { AccountType.OtherIncome, ("إيرادات أخرى", "Other Income", AccountNature.Credit) },
                { AccountType.OtherExpenses, ("مصروفات أخرى", "Other Expenses", AccountNature.Debit) }
            };

        public static string GetArabicName(AccountType type) => Types[type].ArabicName;
        public static string GetEnglishName(AccountType type) => Types[type].EnglishName;
        public static AccountNature GetNature(AccountType type) => Types[type].Nature;
    }

}
