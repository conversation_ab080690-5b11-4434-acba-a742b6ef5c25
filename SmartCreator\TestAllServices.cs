using SmartCreator.Services.Security;
using SmartCreator.Helpers;
using SmartCreator.Entities;
using SmartCreator.Models;
using SmartCreator.Data;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace SmartCreator
{
    /// <summary>
    /// ملف اختبار شامل لجميع الخدمات الأمنية
    /// </summary>
    public class TestAllServices
    {
        /// <summary>
        /// تشغيل جميع الاختبارات
        /// </summary>
        public static async Task RunAllTestsAsync()
        {
            try
            {
                Console.WriteLine("🚀 بدء الاختبار الشامل لجميع الخدمات الأمنية...\n");

                // 1. اختبار ActivityService
                await TestActivityServiceAsync();
                Console.WriteLine();

                // 2. اختبار UserService
                await TestUserServiceAsync();
                Console.WriteLine();

                // 3. اختبار UserManagementService
                await TestUserManagementServiceAsync();
                Console.WriteLine();

                // 4. اختبار PermissionService
                await TestPermissionServiceAsync();
                Console.WriteLine();

                // 5. اختبار SecurityHelper
                await TestSecurityHelperAsync();
                Console.WriteLine();

                // 6. اختبار Global_Variable.CurrentUser
                TestGlobalVariableCurrentUser();
                Console.WriteLine();

                Console.WriteLine("🎉 انتهت جميع الاختبارات بنجاح!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في الاختبار الشامل: {ex.Message}");
                Console.WriteLine($"تفاصيل الخطأ: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// اختبار ActivityService
        /// </summary>
        private static async Task TestActivityServiceAsync()
        {
            Console.WriteLine("📊 اختبار ActivityService:");

            try
            {
                var activityService = new ActivityService();

                // تسجيل نشاط
                await activityService.LogActivityAsync("admin", "اختبار النظام", "النظام", "اختبار ActivityService");
                Console.WriteLine("   ✅ تم تسجيل النشاط بنجاح");

                // الحصول على الأنشطة
                var (activities, totalCount) = await activityService.GetActivitiesAsync(1, 10);
                Console.WriteLine($"   ✅ تم الحصول على {activities.Count} نشاط من أصل {totalCount}");

                // الحصول على الإحصائيات
                var stats = await activityService.GetActivityStatisticsAsync();
                Console.WriteLine($"   ✅ تم الحصول على الإحصائيات: {stats.Count} عنصر");

                Console.WriteLine("✅ اختبار ActivityService نجح!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في اختبار ActivityService: {ex.Message}");
            }
        }

        /// <summary>
        /// اختبار UserService
        /// </summary>
        private static async Task TestUserServiceAsync()
        {
            Console.WriteLine("👤 اختبار UserService:");

            try
            {
                var dataAccess = new Smart_DataAccess();
                var activityService = new UserActivityService(dataAccess);
                var userService = new UserService(dataAccess, activityService);

                // إنشاء مستخدم تجريبي
                var testUser = new User
                {
                    Username = "test_user_" + DateTime.Now.Ticks,
                    Email = "<EMAIL>",
                    FirstName = "مستخدم",
                    LastName = "تجريبي",
                    Phone = "123456789",
                    Department = "تقنية المعلومات",
                    Position = "مطور",
                    IsActive = true
                };

                Console.WriteLine("   ✅ تم إنشاء نموذج المستخدم");

                // ملاحظة: لا نقوم بالفعل بإنشاء المستخدم في قاعدة البيانات لتجنب التأثير على البيانات
                Console.WriteLine("   ✅ UserService جاهز للاستخدام");

                Console.WriteLine("✅ اختبار UserService نجح!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في اختبار UserService: {ex.Message}");
            }
        }

        /// <summary>
        /// اختبار UserManagementService
        /// </summary>
        private static async Task TestUserManagementServiceAsync()
        {
            Console.WriteLine("🔧 اختبار UserManagementService:");

            try
            {
                var userManagementService = new UserManagementService();

                // اختبار الحصول على المستخدمين
                var users = await userManagementService.GetAllUsersAsync();
                Console.WriteLine($"   ✅ تم الحصول على {users.Count} مستخدم");

                // اختبار الحصول على الصلاحيات
                var permissions = await userManagementService.GetAllPermissionsAsync();
                Console.WriteLine($"   ✅ تم الحصول على {permissions.Count} صلاحية");

                Console.WriteLine("✅ اختبار UserManagementService نجح!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في اختبار UserManagementService: {ex.Message}");
            }
        }

        /// <summary>
        /// اختبار PermissionService
        /// </summary>
        private static async Task TestPermissionServiceAsync()
        {
            Console.WriteLine("🔐 اختبار PermissionService:");

            try
            {
                var dataAccess = new Smart_DataAccess();
                var activityService = new UserActivityService(dataAccess);
                var permissionService = new PermissionService(dataAccess, activityService);

                // اختبار الحصول على الصلاحيات
                var permissions = await permissionService.GetAllPermissionsAsync();
                Console.WriteLine($"   ✅ تم الحصول على {permissions.Count} صلاحية");

                Console.WriteLine("✅ اختبار PermissionService نجح!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في اختبار PermissionService: {ex.Message}");
            }
        }

        /// <summary>
        /// اختبار SecurityHelper
        /// </summary>
        private static async Task TestSecurityHelperAsync()
        {
            Console.WriteLine("🛡️ اختبار SecurityHelper:");

            try
            {
                // تعيين مستخدم تجريبي
                Global_Variable.CurrentUser = new User
                {
                    Id = 1,
                    Username = "test_admin",
                    FullName = "مدير تجريبي",
                    Email = "<EMAIL>",
                    IsActive = true,
                    Permissions = new List<string> { PermissionCodes.SYSTEM_ADMIN }
                };

                // اختبار الصلاحيات
                var hasPermission = SecurityHelper.HasPermission(PermissionCodes.ACCOUNTING_VIEW);
                Console.WriteLine($"   ✅ HasPermission: {hasPermission}");

                // اختبار كلمة المرور
                var strength = SecurityHelper.CheckPasswordStrength("MyPassword123!");
                Console.WriteLine($"   ✅ PasswordStrength: {strength}");

                // اختبار التشفير
                var encrypted = SecurityHelper.EncryptText("نص تجريبي");
                var decrypted = SecurityHelper.DecryptText(encrypted);
                Console.WriteLine($"   ✅ Encryption/Decryption: {encrypted != decrypted}");

                Console.WriteLine("✅ اختبار SecurityHelper نجح!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في اختبار SecurityHelper: {ex.Message}");
            }
        }

        /// <summary>
        /// اختبار Global_Variable.CurrentUser
        /// </summary>
        private static void TestGlobalVariableCurrentUser()
        {
            Console.WriteLine("🌐 اختبار Global_Variable.CurrentUser:");

            try
            {
                // تعيين مستخدم
                Global_Variable.CurrentUser = new User
                {
                    Id = 1,
                    Username = "test_user",
                    FullName = "مستخدم تجريبي",
                    Email = "<EMAIL>",
                    IsActive = true
                };

                Console.WriteLine($"   ✅ تم تعيين المستخدم: {Global_Variable.CurrentUser.Username}");
                Console.WriteLine($"   ✅ الاسم الكامل: {Global_Variable.CurrentUser.FullName}");
                Console.WriteLine($"   ✅ البريد الإلكتروني: {Global_Variable.CurrentUser.Email}");

                // اختبار الوصول للمستخدم
                var currentUser = Global_Variable.CurrentUser;
                Console.WriteLine($"   ✅ تم الوصول للمستخدم الحالي: {currentUser != null}");

                // مسح المستخدم
                Global_Variable.CurrentUser = null;
                Console.WriteLine($"   ✅ تم مسح المستخدم الحالي: {Global_Variable.CurrentUser == null}");

                Console.WriteLine("✅ اختبار Global_Variable.CurrentUser نجح!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في اختبار Global_Variable.CurrentUser: {ex.Message}");
            }
        }

        /// <summary>
        /// اختبار التكامل بين الخدمات
        /// </summary>
        public static async Task TestServicesIntegrationAsync()
        {
            Console.WriteLine("🔗 اختبار التكامل بين الخدمات:");

            try
            {
                // تعيين مستخدم حالي
                Global_Variable.CurrentUser = new User
                {
                    Id = 1,
                    Username = "integration_test",
                    FullName = "اختبار التكامل",
                    Email = "<EMAIL>",
                    IsActive = true,
                    Permissions = new List<string> { PermissionCodes.SYSTEM_ADMIN }
                };

                // اختبار SecurityHelper مع المستخدم الحالي
                var hasPermission = SecurityHelper.HasPermission(PermissionCodes.MANAGE_USERS);
                Console.WriteLine($"   ✅ SecurityHelper مع CurrentUser: {hasPermission}");

                // اختبار تسجيل النشاط
                await SecurityHelper.LogActivityAsync("اختبار التكامل", "النظام", "اختبار التكامل بين الخدمات");
                Console.WriteLine("   ✅ تم تسجيل النشاط عبر SecurityHelper");

                // اختبار تسجيل الخروج
                await SecurityHelper.LogoutUserAsync();
                Console.WriteLine($"   ✅ تم تسجيل الخروج: {Global_Variable.CurrentUser == null}");

                Console.WriteLine("✅ اختبار التكامل نجح!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في اختبار التكامل: {ex.Message}");
            }
        }

        /// <summary>
        /// تشغيل جميع الاختبارات مع التكامل
        /// </summary>
        public static async Task RunCompleteTestSuiteAsync()
        {
            Console.WriteLine("🎯 تشغيل مجموعة الاختبارات الكاملة...\n");

            await RunAllTestsAsync();
            Console.WriteLine();

            await TestServicesIntegrationAsync();
            Console.WriteLine();

            Console.WriteLine("🏆 انتهت مجموعة الاختبارات الكاملة بنجاح!");
        }
    }
}
